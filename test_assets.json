[{"components": [{"product_level": "中间支撑层", "product_type": ["Web服务器", "内容分发网络(CDN)"], "product_vendor": "Cloudflare, Inc.", "product_name_cn": "Cloudflare", "product_catalog": ["网络安全设备", "Web系统与应用"]}], "org": "Cloudflare, Inc.", "port": 443, "service": {"response": "HTTP/1.1 404 Not Found\r\nDate: Mon, 02 Jun 2025 05:53:54 GMT\r\nContent-Type: text/html\r\nTransfer-Encoding: chunked\r\nConnection: keep-alive\r\nVary: Accept-Encoding\r\ncf-cache-status: DYNAMIC\r\nReport-To: {\"endpoints\":[{\"url\":\"https:\\/\\/a.nel.cloudflare.com\\/report\\/v4?s=88EZrDA3D4NfR1%2BdBYZtj7y0q5d4uRv5db7Eul5WIDKauNQpSYKW9QE3JJtsIjDp%2FxFozU1y6PliBY7uVUrnlB94SFU2%2Fsb4qLeaFP%2BPJHQXLMGzfZCmdUzP3Qye1fSXMkY7HNkp4zi8\"}],\"group\":\"cf-nel\",\"max_age\":604800}\r\nNEL: {\"success_fraction\":0,\"report_to\":\"cf-nel\",\"max_age\":604800}\r\nspeculation-rules: \"/cdn-cgi/speculation\"\r\nServer: cloudflare\r\nCF-RAY: 9494b168efb07ca1-LAX\r\nalt-svc: h3=\":443\"; ma=86400\r\nserver-timing: cfL4;desc=\"?proto=TCP&rtt=407375&min_rtt=406950&rtt_var=86483&sent=5&recv=9&lost=0&retrans=0&sent_bytes=4193&recv_bytes=1832&delivery_rate=10640&cwnd=252&unsent_bytes=0&cid=9d62dc979d13487d&ts=1690&x=0\"\r\n\r\n", "name": "http/ssl", "http": {"path": "/", "status_code": 404, "page_type": [], "host": "media-cdn.duizhang.fun", "meta_keywords": "", "title": "Not Found"}, "cert": "Certificate:\n    Data:\n        Version: 3 (0x2)\n        Serial Number:\n            ce:55:c5:ee:5c:aa:67:a6:0e:61:31:96:0d:3f:0a:10\n    Signature Algorithm: ECDSA-SHA256\n        Issuer: C=US,O=Google Trust Services,CN=WE1\n        Validity\n            Not Before: May 20 02:56:12 2025 UTC\n            Not After : Aug 18 03:56:10 2025 UTC\n        Subject: CN=media-cdn.duizhang.fun\n        Subject Public Key Info:\n            Public Key Algorithm: ECDSA\n                Public-Key: (256 bit)\n                X:\n                    73:4a:aa:6f:ab:a5:63:42:8c:9d:85:00:77:95:3d:\n                    74:7c:71:c9:f3:44:af:08:14:a7:f2:f7:39:da:c9:\n                    20:2f\n                Y:\n                    8a:c2:3f:53:a4:41:f7:7a:7d:1d:31:17:ec:e2:8c:\n                    25:6b:8f:15:8a:4f:2c:8a:84:dc:3b:d1:99:b3:cf:\n                    0c:b6\n                Curve: P-256\n        X509v3 extensions:\n            X509v3 Key Usage: critical\n                Digital Signature\n            X509v3 Extended Key Usage:\n                TLS Web Server Authentication\n            X509v3 Basic Constraints: critical\n                CA:FALSE\n            X509v3 Subject Key Identifier:\n                4D:F1:BD:A8:8E:03:F4:C0:4B:9D:28:FA:D8:F7:AA:2B:93:6E:1B:5B\n            X509v3 Authority Key Identifier:\n                keyid:90:77:92:35:67:C4:FF:A8:CC:A9:E6:7B:D9:80:79:7B:CC:93:F9:38\n            Authority Information Access:\n                OCSP - URI:http://o.pki.goog/s/we1/zlU\n                CA Issuers - URI:http://i.pki.goog/we1.crt\n\n            X509v3 Subject Alternative Name:\n                DNS:media-cdn.duizhang.fun\n            X509v3 Certificate Policies:\n                Policy: 2.23.140.1.2.1\n            X509v3 CRL Distribution Points:\n\n                Full Name:\n                  URI:http://c.pki.goog/we1/nXrN9meVWok.crl\n\n            Unknown extension 1.3.6.1.4.1.11129.2.4.2\n\n    Signature Algorithm: ECDSA-SHA256\n         30:45:02:21:00:ef:78:46:b8:1c:fd:97:c4:08:ad:df:e1:b5:\n         1d:33:e3:bf:0f:33:64:ee:ad:9a:33:73:8b:c0:8c:04:6d:69:\n         59:02:20:54:e2:10:33:6f:9d:1b:fa:2a:e4:4e:00:15:b2:a9:\n         78:63:d9:74:57:08:1b:9f:81:c9:07:3a:bd:4e:6e:77:01\n\n\nThumbprint MD5: FE:F6:BB:93:65:23:41:87:3B:7A:08:5F:33:8A:07:5C\nThumbprint SHA1: 9A:76:37:3E:A9:AA:73:FD:AC:25:D0:BE:10:0B:E6:44:6A:DA:EC:9D\nThumbprint SHA256: 70:A4:B9:C5:8C:95:39:66:FB:E3:45:5D:EC:80:4F:BA:3A:5B:AE:BE:9D:78:A1:DD:34:57:4D:8E:A8:55:C9:C1\n", "tls": {"handshake_log": {"server_certificates": {"certificate": {"parsed": {"issuer_dn": "CN=WE1, O=Google Trust Services, C=US", "subject_dn": "CN=media-cdn.duizhang.fun", "subject": {"common_name": ["media-cdn.duizhang.fun"]}, "serial_number": "274266326884341355700436860479129717264"}}}}}, "version": "1.1"}, "ip": "***********", "location": {"province_cn": "", "isp": "Cloudflare, Inc.", "province_en": "", "country_en": "United States", "district_cn": "", "city_en": "", "district_en": "", "country_cn": "美国", "city_cn": ""}, "transport": "tcp", "time": "2025-06-02T05:54:55.301Z", "asn": 13335, "url": "https://media-cdn.duizhang.fun:443"}, {"org": "Amazon.com, Inc.", "port": 80, "service": {"response": "HTTP/1.0 308 Permanent Redirect\r\nContent-Type: text/plain\r\nLocation: https://test.duizhang.fun/\r\nRefresh: 0;url=https://test.duizhang.fun/\r\nserver: Vercel\r\n\r\n", "name": "http", "http": {"path": "/", "status_code": 308, "page_type": [], "host": "test.duizhang.fun", "meta_keywords": "", "title": ""}, "version": "1.0"}, "ip": "************", "location": {"province_cn": "加利福尼亚州", "isp": "亚马逊", "province_en": "California", "country_en": "United States", "district_cn": "", "city_en": "Walnut", "district_en": "", "country_cn": "美国", "city_cn": "核桃市"}, "transport": "tcp", "time": "2025-05-17T01:48:37.851Z", "asn": 16509, "url": "http://test.duizhang.fun:80"}]