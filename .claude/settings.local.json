{"permissions": {"allow": ["Bash(find:*)", "Bash(ls:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(cd /home/<USER>/gdrive/code/bounty-ams/frontend)", "Bash(npx create-react-app . --template typescript)", "Bash(npx create-react-app:*)", "Bash(npm create:*)", "Bash(rm -rf /home/<USER>/gdrive/code/bounty-ams/frontend)", "<PERSON><PERSON>(docker-compose up:*)", "<PERSON><PERSON>(docker compose up -d)", "<PERSON><PERSON>(docker compose:*)", "Bash(python3 -m pip install:*)", "Bash(pip install:*)", "<PERSON><PERSON>(python3:*)", "<PERSON><PERSON>(pkill:*)", "Bash(npm:*)", "<PERSON><PERSON>(curl:*)", "Bash(rm:*)", "Bash(python -m pip install:*)", "Bash(npx tailwindcss init:*)", "Bash(python test_elasticsearch.py)", "Bash(pip3 install:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(python:*)", "<PERSON><PERSON>(uvicorn:*)", "<PERSON><PERSON>(cat:*)", "Bash(go mod init:*)", "Bash(export:*)", "<PERSON><PERSON>(go:*)", "Ba<PERSON>(./bounty-agent:*)", "Bash(BOUNTY_USERNAME=testuser BOUNTY_PASSWORD=testpass ./bounty-agent)", "Bash(/usr/local/go/bin/go build:*)", "Bash(PGPASSWORD=password psql:*)", "<PERSON><PERSON>(true)", "Bash(grep:*)", "<PERSON><PERSON>(timeout 10 npm run dev)", "Bash(ss:*)", "<PERSON><PERSON>(sudo apt-get:*)", "Bash(sudo apt-get install:*)", "<PERSON><PERSON>(sed:*)", "Bash(kill:*)", "<PERSON><PERSON>(timeout:*)", "Bash(npx tsc:*)", "<PERSON><PERSON>(touch:*)", "Bash(# 检查登录\nLOGIN_RESPONSE=$(curl -s -X POST \"\"http://localhost:8000/api/auth/login\"\" \\\n  -H \"\"Content-Type: application/json\"\" \\\n  -d ''{\"\"username\"\":\"\"admin\"\",\"\"password\"\":\"\"password\"\"}'')\n\necho \"\"Login response: $LOGIN_RESPONSE\"\"\n\nif echo \"\"$LOGIN_RESPONSE\"\" | jq -e ''.access_token'' > /dev/null 2>&1; then\n  TOKEN=$(echo \"\"$LOGIN_RESPONSE\"\" | jq -r ''.access_token'')\n  echo \"\"Token extracted: ${TOKEN:0:30}...\"\"\n  \n  echo -e \"\"\\n=== 现在测试密钥查询 ===\"\"\n  curl -s -X GET \"\"http://localhost:8000/api/agent-keys/?agent_id=test-agent-001\"\" \\\n    -H \"\"Authorization: Bearer $TOKEN\"\"\nelse\n  echo \"\"Login failed!\"\"\nfi)", "Bash(sudo kill:*)", "Bash(pkill -f \"python3 main.py\")", "Bash(rm /home/<USER>/code/code/bounty-ams/backend/test_delete_function.py)"], "deny": []}}