# Bounty AMS - 漏洞赏金资产管理系统

## 🚀 系统概览

Bounty AMS 是一个完整的漏洞赏金资产管理系统，结合了现代化的前端界面和强大的后端功能。

### 技术栈
- **前端**: React 18 + TypeScript + Vite + Ant Design
- **后端**: Python FastAPI + PostgreSQL + Elasticsearch
- **Agent**: Go 语言分布式代理
- **可视化**: ECharts 图表库

## 🔧 系统组件

### 1. 前端应用 (http://localhost:5173)
现代化的管理界面，包含：
- 响应式设计，适配各种设备
- 美观的 Ant Design 组件
- 实时数据可视化
- 多语言支持（中文界面）

### 2. 后端API (http://localhost:8000)
RESTful API 服务，提供：
- JWT 认证和授权
- 资产搜索和管理
- 工作流引擎
- Agent 管理
- 任务调度

### 3. 数据存储
- **PostgreSQL**: 用户、配置、关系数据
- **Elasticsearch**: 资产搜索和分析
- **Redis**: 缓存和消息队列

## 👤 默认账户

```
用户名: admin
密码: password
```

## 📱 功能模块

### 1. 仪表盘
- 系统概览统计
- 资产发现趋势图
- Agent 状态监控
- 最近活动追踪

### 2. 资产管理
- 高级搜索过滤
- 资产类型分类
- 详细信息查看
- 批量操作支持

### 3. 工作流管理
- 创建自定义工作流
- 任务串联执行
- 执行状态追踪
- 结果聚合分析

### 4. Agent管理
- Agent 注册和认证
- 实时状态监控
- 任务分发管理
- 性能指标追踪

### 5. 任务管理
- 任务创建和调度
- 执行状态监控
- 结果查看和分析
- 错误处理和重试

### 6. 搜索分析
- 多维度数据分析
- 可视化图表展示
- 导出功能支持
- 高级筛选条件

## 🔍 系统特性

### 安全性
- JWT Token 认证
- 角色权限控制
- API 访问控制
- 数据加密存储

### 性能
- 异步处理架构
- 数据库索引优化
- 缓存机制
- 分布式部署支持

### 可扩展性
- 微服务架构
- 水平扩展支持
- 插件化设计
- API 优先设计

## 🛠️ 开发指南

### 启动服务

1. **后端服务**:
   ```bash
   cd backend
   python3 -m uvicorn main:app --reload --host 0.0.0.0 --port 8000
   ```

2. **前端服务**:
   ```bash
   cd frontend
   npm run dev
   ```

3. **Agent服务**:
   ```bash
   cd agents/go-agent
   ./bounty-agent
   ```

### 测试流程

1. 访问 http://localhost:5173
2. 使用 admin/password 登录
3. 浏览各个功能模块
4. 创建工作流测试 Agent 功能
5. 查看资产发现结果

## 📊 系统监控

### 健康检查
- Backend: `GET /health`
- Frontend: 访问主页面
- Agent: 查看 Agent 管理页面

### 日志监控
- 后端日志：控制台输出
- 前端日志：浏览器开发者工具
- Agent日志：Agent 运行日志

## 🔄 系统流程

1. **用户登录** → 身份验证
2. **创建工作流** → 定义任务序列
3. **Agent注册** → 自动发现和注册
4. **任务分发** → 智能负载均衡
5. **执行任务** → 并行处理
6. **结果聚合** → 自动索引到ES
7. **可视化展示** → 实时数据呈现

## 🎯 使用建议

1. **初次使用**：先浏览仪表盘了解系统状态
2. **资产管理**：使用搜索功能查找特定资产
3. **工作流创建**：从简单的单任务开始
4. **Agent监控**：确保Agent正常运行
5. **数据分析**：利用搜索分析功能深入了解数据

## 🐛 故障排除

### 常见问题

1. **前端无法访问**
   - 检查 npm run dev 是否正常启动
   - 确认端口 5173 未被占用

2. **后端API错误**
   - 检查数据库连接
   - 确认 Elasticsearch 服务状态

3. **Agent连接失败**
   - 检查Agent配置文件
   - 确认网络连接正常

### 支持联系

- 查看系统日志获取详细错误信息
- 检查 API 文档：http://localhost:8000/docs
- 使用浏览器开发者工具调试前端问题

---

**🎉 Bounty AMS 系统已就绪，开始您的安全资产管理之旅！**