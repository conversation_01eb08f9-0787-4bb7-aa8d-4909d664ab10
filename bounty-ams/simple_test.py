#!/usr/bin/env python3
import asyncio
import aiohttp

async def test_apis():
    """简单测试API状态"""
    base_url = "http://localhost:8000"
    
    # 获取token
    async with aiohttp.ClientSession() as session:
        # 登录获取token
        login_data = {"username": "admin", "password": "password"}
        async with session.post(f"{base_url}/api/auth/login", json=login_data) as response:
            if response.status == 200:
                data = await response.json()
                token = data.get("access_token")
                print(f"✅ 登录成功")
            else:
                print(f"❌ 登录失败: {response.status}")
                return
        
        headers = {"Authorization": f"Bearer {token}"}
        
        # 测试各个端点
        endpoints = [
            "/health",
            "/api/vulnerabilities/severities/",
            "/api/vulnerabilities/stats/severity",
            "/api/asset-types/field-types/",
            "/api/dynamic-models/field-types/"
        ]
        
        for endpoint in endpoints:
            try:
                async with session.get(f"{base_url}{endpoint}", headers=headers) as response:
                    print(f"{endpoint}: {response.status}")
                    if response.status != 200:
                        text = await response.text()
                        print(f"  错误: {text[:100]}")
            except Exception as e:
                print(f"{endpoint}: 异常 - {str(e)}")

if __name__ == "__main__":
    asyncio.run(test_apis()) 