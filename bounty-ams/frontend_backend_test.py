#!/usr/bin/env python3
"""
前端后端集成测试
模拟前端的API调用流程
"""

import asyncio
import aiohttp
import json
from datetime import datetime

class FrontendBackendTest:
    def __init__(self):
        self.base_url = "http://localhost:8000"
        self.frontend_url = "http://localhost:5173"
        self.token = None
        
    async def test_login_flow(self):
        """测试登录流程"""
        print("🔐 测试登录流程...")
        
        try:
            async with aiohttp.ClientSession() as session:
                # 测试登录
                login_data = {
                    "username": "admin",
                    "password": "password"
                }
                
                async with session.post(
                    f"{self.base_url}/api/auth/login",
                    json=login_data,
                    headers={"Content-Type": "application/json"}
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        self.token = data.get("access_token")
                        print(f"✅ 登录成功，获得token: {self.token[:50]}...")
                        return True
                    else:
                        error_text = await response.text()
                        print(f"❌ 登录失败: {response.status} - {error_text}")
                        return False
                        
        except Exception as e:
            print(f"❌ 登录流程异常: {str(e)}")
            return False
    
    async def test_authenticated_endpoints(self):
        """测试需要身份验证的端点"""
        if not self.token:
            print("❌ 没有token，跳过身份验证测试")
            return False
            
        print("🔒 测试身份验证端点...")
        
        endpoints = [
            ("/api/auth/me", "GET", "获取用户信息"),
            ("/api/agents/", "GET", "获取Agent列表"),
            ("/api/tasks", "GET", "获取任务列表"),
            ("/api/workflows", "GET", "获取工作流列表"),
            ("/api/discovered-assets/stats", "GET", "获取资产统计"),
            ("/api/dynamic-models/types", "GET", "获取动态模型类型")
        ]
        
        success_count = 0
        total_count = len(endpoints)
        
        try:
            headers = {"Authorization": f"Bearer {self.token}"}
            
            async with aiohttp.ClientSession() as session:
                for endpoint, method, description in endpoints:
                    try:
                        url = f"{self.base_url}{endpoint}"
                        
                        if method == "GET":
                            async with session.get(url, headers=headers, timeout=5) as response:
                                if response.status == 200:
                                    data = await response.json()
                                    print(f"✅ {description}: 成功 (数据长度: {len(str(data))})")
                                    success_count += 1
                                else:
                                    error_text = await response.text()
                                    print(f"❌ {description}: 失败 ({response.status}) - {error_text[:100]}...")
                                    
                    except Exception as e:
                        print(f"❌ {description}: 异常 - {str(e)}")
                        
        except Exception as e:
            print(f"❌ 身份验证端点测试异常: {str(e)}")
            return False
            
        print(f"\n📊 身份验证端点测试结果: {success_count}/{total_count} 成功")
        return success_count == total_count
        
    async def test_frontend_accessibility(self):
        """测试前端可访问性"""
        print("🌐 测试前端可访问性...")
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(self.frontend_url, timeout=5) as response:
                    if response.status == 200:
                        content = await response.text()
                        if "root" in content and "main.tsx" in content:
                            print("✅ 前端页面正常加载")
                            return True
                        else:
                            print("❌ 前端页面内容异常")
                            return False
                    else:
                        print(f"❌ 前端页面访问失败: {response.status}")
                        return False
                        
        except Exception as e:
            print(f"❌ 前端可访问性测试异常: {str(e)}")
            return False
            
    async def test_cors_configuration(self):
        """测试CORS配置"""
        print("🌍 测试CORS配置...")
        
        try:
            async with aiohttp.ClientSession() as session:
                headers = {
                    "Origin": "http://localhost:5173",
                    "Access-Control-Request-Method": "GET",
                    "Access-Control-Request-Headers": "Content-Type,Authorization"
                }
                
                async with session.options(
                    f"{self.base_url}/api/auth/me",
                    headers=headers,
                    timeout=5
                ) as response:
                    cors_headers = {
                        "Access-Control-Allow-Origin": response.headers.get("Access-Control-Allow-Origin"),
                        "Access-Control-Allow-Methods": response.headers.get("Access-Control-Allow-Methods"),
                        "Access-Control-Allow-Headers": response.headers.get("Access-Control-Allow-Headers"),
                        "Access-Control-Allow-Credentials": response.headers.get("Access-Control-Allow-Credentials")
                    }
                    
                    print(f"CORS Headers: {cors_headers}")
                    
                    if cors_headers.get("Access-Control-Allow-Origin") == "http://localhost:5173":
                        print("✅ CORS配置正常")
                        return True
                    else:
                        print("❌ CORS配置可能有问题")
                        return False
                        
        except Exception as e:
            print(f"❌ CORS配置测试异常: {str(e)}")
            return False
            
    async def test_api_performance(self):
        """测试API性能"""
        print("⚡ 测试API性能...")
        
        if not self.token:
            print("❌ 没有token，跳过性能测试")
            return False
            
        try:
            headers = {"Authorization": f"Bearer {self.token}"}
            
            async with aiohttp.ClientSession() as session:
                start_time = datetime.now()
                
                # 并发请求测试
                tasks = []
                for i in range(5):
                    task = session.get(
                        f"{self.base_url}/api/auth/me",
                        headers=headers,
                        timeout=5
                    )
                    tasks.append(task)
                
                responses = await asyncio.gather(*tasks, return_exceptions=True)
                
                end_time = datetime.now()
                total_time = (end_time - start_time).total_seconds()
                
                success_count = sum(1 for r in responses if not isinstance(r, Exception) and r.status == 200)
                
                print(f"✅ 并发请求测试: {success_count}/5 成功，总时间: {total_time:.2f}秒")
                
                # 关闭所有响应
                for response in responses:
                    if not isinstance(response, Exception):
                        response.close()
                        
                return success_count >= 4  # 至少4个成功
                
        except Exception as e:
            print(f"❌ API性能测试异常: {str(e)}")
            return False
            
    async def run_full_test(self):
        """运行完整的集成测试"""
        print("🚀 开始前端后端集成测试...")
        print("=" * 50)
        
        test_results = {}
        
        # 测试登录流程
        test_results["login"] = await self.test_login_flow()
        
        # 测试身份验证端点
        test_results["auth_endpoints"] = await self.test_authenticated_endpoints()
        
        # 测试前端可访问性
        test_results["frontend"] = await self.test_frontend_accessibility()
        
        # 测试CORS配置
        test_results["cors"] = await self.test_cors_configuration()
        
        # 测试API性能
        test_results["performance"] = await self.test_api_performance()
        
        print("=" * 50)
        print("📊 集成测试总结:")
        print(f"登录流程: {'✅' if test_results['login'] else '❌'}")
        print(f"身份验证端点: {'✅' if test_results['auth_endpoints'] else '❌'}")
        print(f"前端可访问性: {'✅' if test_results['frontend'] else '❌'}")
        print(f"CORS配置: {'✅' if test_results['cors'] else '❌'}")
        print(f"API性能: {'✅' if test_results['performance'] else '❌'}")
        
        success_count = sum(1 for result in test_results.values() if result)
        total_count = len(test_results)
        
        print(f"\n整体测试结果: {success_count}/{total_count} 通过")
        
        if success_count == total_count:
            print("🎉 所有集成测试通过！前端后端连接正常。")
            return True
        else:
            print("⚠️  部分集成测试失败，需要进一步检查。")
            return False

async def main():
    """主测试函数"""
    tester = FrontendBackendTest()
    success = await tester.run_full_test()
    
    if success:
        print("\n✅ 系统状态：前端和后端都正常运行，可以正常使用。")
    else:
        print("\n❌ 系统状态：发现问题，需要进一步调试。")
    
    return success

if __name__ == "__main__":
    asyncio.run(main()) 