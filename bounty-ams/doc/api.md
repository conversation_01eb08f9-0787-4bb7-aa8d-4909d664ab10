| 文档版本<br> | v1.1<br> |
| --- | --- |
| **基于DB设计版本**<br> | v1.0<br> |
| **创建日期**<br> | 2025年7月10日<br> |
| **创建人**<br> | Gemini AI<br> |
| **核心目标**<br> | 为MVP版本定义清晰、一致且可扩展的后端API接口<br> |

### 1. 概述与通用约定

本文档定义了漏洞赏金资产管理平台的后端API。所有API都应遵循以下约定：
*   **基地址 (Base URL)**: `https://api.yourdomain.com/v1`
    
*   **认证 (Authentication)**: 所有需要认证的接口都必须在 HTTP Header 中包含 `Authorization: Bearer <JWT_TOKEN>`。
    
*   **请求/响应格式**: 所有请求和响应的 Body 均使用 `application/json` 格式。
    
*   **权限控制**: 每个接口都明确标明了所需的最低角色权限。
    
*   **错误处理**:
    *   `400 Bad Request`: 请求参数错误或格式不正确。
        
    *   `401 Unauthorized`: 未提供或提供了无效的JWT Token。
        
    *   `403 Forbidden`: 用户角色权限不足。
        
    *   `404 Not Found`: 请求的资源不存在。
        
    *   `500 Internal Server Error`: 服务器内部错误。
        
    *   错误响应体格式: `{ "detail": "错误描述信息" }`
        

### 2. 认证接口 (Auth)

#### `POST /auth/token`

*   **描述**: 用户登录，获取JWT Token。
    
*   **权限**: 公开访问
    
*   **请求体 (Body)**:
    
        {
          "username": "admin",
          "password": "your_password"
        }
        
    
*   **成功响应 (200 OK)**:
    
        {
          "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
          "token_type": "bearer"
        }
        
    

### 3. 用户与角色接口 (Users & Roles)

#### `GET /users/me`

*   **描述**: 获取当前登录用户的信息。
    
*   **权限**: `readonly`, `admin`
    
*   **成功响应 (200 OK)**:
    
        {
          "id": "c4a4a4a4-b4b4-c4c4-d4d4-e4e4e4e4e4e4",
          "username": "admin",
          "email": "<EMAIL>",
          "is_active": true,
          "roles": ["admin"]
        }
        
    

#### `POST /users`

*   **描述**: 创建一个新用户。
    
*   **权限**: `admin`
    
*   **请求体 (Body)**:
    
        {
          "username": "newuser",
          "email": "<EMAIL>",
          "password": "strong_password",
          "roles": ["readonly"]
        }
        
    
*   **成功响应 (201 Created)**: 返回新创建的用户信息（不含密码）。
    

### 4. 资产接口 (Assets)

此组接口与 Elasticsearch 交互。

#### `POST /assets/search`

*   **描述**: **核心接口**。根据复杂的查询条件搜索资产。支持高级搜索语法。
    
*   **权限**: `readonly`, `admin`
    
*   **请求体 (Body)**:
    
        {
          "query_string": "asset_type:service AND http.tech_stack.name:\"Nginx\" AND port:443",
          "page": 1,
          "size": 20,
          "sort_by": "last_seen_at",
          "sort_order": "desc"
        }
        
    
*   **成功响应 (200 OK)**:
    
        {
          "total": 125,
          "page": 1,
          "size": 20,
          "items": [
            {
              "_id": "es_document_id_1",
              "_source": {
                "asset_type": "service",
                "ip": "*******",
                "port": 443,
                "http": {
                  "url": "https://example.com",
                  "title": "Example Domain",
                  "tech_stack": [{"name": "Nginx", "version": "1.20.1"}]
                },
                "last_seen_at": "2025-07-10T12:00:00Z"
              }
            }
          ]
        }
        
    

#### `GET /assets/{asset_id}`

*   **描述**: 获取单个资产的详细信息。
    
*   **权限**: `readonly`, `admin`
    
*   **成功响应 (200 OK)**: 返回单个资产的完整文档。
    

#### `PUT /assets/{asset_id}`

*   **描述**: 手动更新一个资产的信息（如添加标签）。
    
*   **权限**: `admin`
    
*   **请求体 (Body)**:
    
        {
          "tags": ["critical", "production"]
        }
        
    
*   **成功响应 (200 OK)**: 返回更新后的资产文档。
    

#### `POST /assets/{asset_id}/vulnerabilities`

*   **描述**: 为某个资产手动关联一个潜在漏洞。
    
*   **权限**: `admin`
    
*   **请求体 (Body)**:
    
        {
          "vulnerability_kb_id": "a1b1a1b1-b1b1-c1c1-d1d1-e1e1e1e1e1e1"
        }
        
    
*   **成功响应 (200 OK)**: 返回更新后的资产文档。
    

#### 4.1 批量操作接口 (Bulk Operations)

#### `POST /assets/bulk-tag`

*   **描述**: 批量为多个资产添加或移除标签。
    
*   **权限**: `admin`
    
*   **请求体 (Body)**:
    
        {
          "asset_ids": ["es_doc_id_1", "es_doc_id_2"],
          "add_tags": ["production", "critical"],
          "remove_tags": ["staging"]
        }
        
    
*   **成功响应 (200 OK)**:
    
        {
          "status": "success",
          "updated_count": 2
        }
        
    

#### `POST /assets/bulk-associate-vuln`

*   **描述**: 批量为多个资产关联同一个潜在漏洞。
    
*   **权限**: `admin`
    
*   **请求体 (Body)**:
    
        {
          "asset_ids": ["es_doc_id_1", "es_doc_id_2"],
          "vulnerability_kb_id": "a1b1a1b1-b1b1-c1c1-d1d1-e1e1e1e1e1e1"
        }
        
    
*   **成功响应 (200 OK)**:
    
        {
          "status": "success",
          "associated_count": 2
        }
        
    

### 5. 漏洞知识库接口 (Vulnerability KB)

#### `POST /kb/vulnerabilities`

*   **描述**: 在知识库中创建一个新的漏洞条目。
    
*   **权限**: `admin`
    
*   **请求体 (Body)**:
    
        {
          "name": "Apache Struts2 S2-016 RCE",
          "vuln_id": "CVE-2013-2251",
          "severity": "Critical",
          "description": "A remote code execution vulnerability...",
          "affected_products": [{"name": "Struts", "version": "2.0.0 - 2.3.15"}],
          "poc_identifier": "struts2/s2-016.py"
        }
        
    
*   **成功响应 (201 Created)**: 返回新创建的漏洞条目。
    

#### `GET /kb/vulnerabilities`

*   **描述**: 获取漏洞知识库列表，支持分页和搜索。
    
*   **权限**: `readonly`, `admin`
    
*   **查询参数 (Query)**: `?search=struts&page=1&size=20`
    
*   **成功响应 (200 OK)**: 返回分页的漏洞列表。
    

#### `GET /kb/vulnerabilities/{vuln_id}/poc`

*   **描述**: 下载指定漏洞的PoC脚本内容。
    
*   **权限**: `admin` (或专用的Agent角色)
    
*   **成功响应 (200 OK)**:
    *   **Content-Type**: `text/plain`
        
    *   **Body**: 返回PoC脚本的原始内容。
        

### 6. Agent 与任务接口 (Agents & Tasks)

#### `GET /agents`

*   **描述**: 获取所有已注册的Agent列表及其状态。
    
*   **权限**: `admin`
    
*   **成功响应 (200 OK)**:
    
        [
          {
            "id": "b2c2b2c2-c2c2-d2d2-e2e2-f2f2f2f2f2f2",
            "name": "My-Lab-Agent",
            "hostname": "dev-box-01",
            "status": "online",
            "version": "1.0.0",
            "last_seen_at": "2025-07-10T12:00:00Z"
          }
        ]
        
    

#### `POST /tasks`

*   **描述**: 创建一个验证任务并下发给指定的Agent。
    
*   **权限**: `admin`
    
*   **请求体 (Body)**:
    
        {
          "asset_identifier": "es_document_id_for_asset",
          "vulnerability_kb_id": "a1b1a1b1-b1b1-c1c1-d1d1-e1e1e1e1e1e1",
          "agent_id": "b2c2b2c2-c2c2-d2d2-e2e2-f2f2f2f2f2f2"
        }
        
    
*   **成功响应 (202 Accepted)**: 表示任务已成功创建并进入队列。
    
        {
          "task_id": "d4e4d4e4-e4e4-f4f4-a4a4-b4b4b4b4b4b4",
          "status": "pending"
        }
        
    

#### `GET /tasks/{task_id}`

*   **描述**: 获取特定任务的状态和结果。
    
*   **权限**: `admin`
    
*   **成功响应 (200 OK)**:
    
        {
          "id": "d4e4d4e4-e4e4-f4f4-a4a4-b4b4b4b4b4b4",
          "status": "completed",
          "result": {
            "success": true,
            "output": "Vulnerability confirmed. Target is vulnerable."
          },
          "completed_at": "2025-07-10T12:05:00Z"
        }
        
    

### 7. AI 辅助接口 (AI)

#### `POST /ai/analyze`

*   **描述**: 将选定的文本发送给AI模型进行分析。
    
*   **权限**: `readonly`, `admin`
    
*   **请求体 (Body)**:
    
        {
          "text": "The server is running Apache/2.4.41 (Ubuntu).",
          "prompt_type": "vulnerability_analysis"
        }
        
    
*   **成功响应 (200 OK)**:
    
        {
          "analysis_result": "Apache/2.4.41 is known to be vulnerable to CVE-2021-41773 (Path Traversal) and CVE-2021-42013 (RCE). It is highly recommended to upgrade to the latest version..."
        }
        
    

### 8. Agent与平台通信协议 (Agent-Platform Protocol)

本章节定义了Agent与平台后端之间的交互流程。Agent应被视为一个特殊的API客户端。

#### 8.1 认证

*   每个Agent在部署时都应被分配一个长期有效的JWT Token，或者通过客户端凭证模式(Client Credentials Grant)获取Token。
    
*   Agent发起的**所有**后续请求都必须在HTTP Header中包含此Token。
    

#### 8.2 启动与注册流程

1.  **Agent启动时**，应调用以下接口向平台注册自己或更新已有信息。
    
2.  **接口**: `POST /agents/register`
    
3.  **请求体**:
    
        {
          "id": "b2c2b2c2-c2c2-d2d2-e2e2-f2f2f2f2f2f2", // Agent的UUID，应持久化在Agent本地
          "name": "My-Lab-Agent", // 可选的友好名称
          "hostname": "dev-box-01",
          "version": "1.0.1"
        }
        
    
4.  **成功响应 (200 OK)**: 平台确认注册成功。
    
        {
          "status": "registered",
          "agent_id": "b2c2b2c2-c2c2-d2d2-e2e2-f2f2f2f2f2f2"
        }
        
    

#### 8.3 心跳与任务拉取

*   Agent应**定期（如每30秒）**调用心跳接口，以表明自己在线并拉取新任务。
    
*   **接口**: `POST /agents/heartbeat`
    
*   **请求体**:
    
        {
          "agent_id": "b2c2b2c2-c2c2-d2d2-e2e2-f2f2f2f2f2f2",
          "status": "idle" // Agent当前状态: 'idle' 或 'busy'
        }
        
    
*   **成功响应 (200 OK)**: 平台返回心跳确认，并**可能**携带一个新任务和**最新配置**。
    
        {
          "status": "acknowledged",
          "task": {
            "task_id": "d4e4d4e4-e4e4-f4f4-a4a4-b4b4b4b4b4b4",
            "asset_info": {
                "ip": "*******",
                "port": 8080,
                "domain": "test.example.com"
            },
            "poc_identifier": "struts2/s2-016.py"
          },
          "config": {
            "subfinder_domains": ["example.com", "test.com"],
            "naabu_ports": "top-1000",
            "scan_interval_seconds": 3600
          }
        }
        
    

#### 8.4 资产数据上报

*   当Agent通过扫描发现新资产时，应调用此接口进行上报。建议批量上报以提高效率。
    
*   **接口**: `POST /agents/assets`
    
*   **请求体**:
    
        {
          "agent_id": "b2c2b2c2-c2c2-d2d2-e2e2-f2f2f2f2f2f2",
          "assets": [
            {
              "asset_type": "service",
              "source": "naabu",
              "ip": "*******",
              "port": 80,
              "service_name": "http",
              "raw_data": { "tool": "naabu", "output": "..." }
            },
            {
              "asset_type": "domain",
              "source": "subfinder",
              "domain": "sub.example.com",
              "raw_data": { "tool": "subfinder", "output": "..." }
            }
          ]
        }
        
    
*   **成功响应 (202 Accepted)**: 表示平台已接收数据并放入处理队列。
    

#### 8.5 任务结果上报

*   当Agent完成一个任务后，调用此接口上报结果。
    
*   **接口**: `PUT /tasks/{task_id}/result`
    
*   **URL参数**: `{task_id}` 是从心跳接口获取的任务ID。
    
*   **请求体**:
    
        {
          "agent_id": "b2c2b2c2-c2c2-d2d2-e2e2-f2f2f2f2f2f2",
          "status": "failed",
          "result": {
            "success": false,
            "reason": "connection_timeout",
            "output": "Connection timed out after 30 seconds.",
            "raw_output": "..."
          }
        }
        
    
*   **成功响应 (200 OK)**: 表示平台已成功接收并更新任务结果。
