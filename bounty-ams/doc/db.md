| 文档版本<br> | v1.0<br> |
| --- | --- |
| **基于PRD版本**<br> | v1.2 (精简版)<br> |
| **创建日期**<br> | 2025年7月10日<br> |
| **创建人**<br> | Gemini AI<br> |
| **核心目标**<br> | 为MVP版本定义清晰、高效、可扩展的数据库结构<br> |

### 1. 概述

本系统采用双数据库架构，以充分利用不同数据库的优势，满足平台对关系型数据管理和海量资产数据检索分析的需求。
*   **PostgreSQL 15+**: 作为关系型元数据存储。它负责存储结构化、需要事务一致性的数据，如用户信息、角色、任务队列、Agent状态以及漏洞知识库。
    
*   **Elasticsearch 8.x**: 作为核心资产数据存储与搜索引擎。它负责存储海量的、半结构化的资产数据，并提供强大的全文搜索、聚合分析和复杂查询能力，是平台数据消费的核心。
    

### 2. PostgreSQL 数据库设计

此数据库存储平台运行所需的核心元数据和关系数据。

#### 2.1 ER 图 (实体关系图)

    erDiagram
        users ||--o{ user_roles : "has"
        roles ||--o{ user_roles : "has"
        users ||--o{ tasks : "creates"
        users ||--o{ vulnerability_kb : "maintains"
        agents ||--o{ tasks : "executes"
        vulnerability_kb ||--o{ tasks : "is_verified_by"
    
        users {
            UUID id PK
            string username
            string hashed_password
            string email
            datetime created_at
            bool is_active
        }
    
        roles {
            int id PK
            string name
        }
    
        user_roles {
            UUID user_id FK
            int role_id FK
        }
    
        agents {
            UUID id PK
            string name
            string status
            datetime last_seen_at
        }
    
        tasks {
            UUID id PK
            string asset_identifier
            UUID vulnerability_kb_id FK
            UUID agent_id FK
            string status
            jsonb result
            UUID created_by_user_id FK
            datetime created_at
        }
    
        vulnerability_kb {
            UUID id PK
            string name
            string vuln_id
            text description
            string severity
            jsonb affected_products
            string poc_identifier
            UUID created_by_user_id FK
        }
    

#### 2.2 表结构定义

##### `users` - 用户表

存储平台的用户信息。
| 字段名<br> | 数据类型<br> | 约束<br> | 描述<br> |
| --- | --- | --- | --- |
| `id`<br> | UUID<br> | PRIMARY KEY<br> | 用户唯一标识符<br> |
| `username`<br> | VARCHAR(255)<br> | UNIQUE, NOT NULL<br> | 用户名<br> |
| `hashed_password`<br> | VARCHAR(255)<br> | NOT NULL<br> | 加密后的密码<br> |
| `email`<br> | VARCHAR(255)<br> | UNIQUE<br> | 电子邮箱<br> |
| `created_at`<br> | TIMESTAMPTZ<br> | NOT NULL, DEFAULT NOW()<br> | 创建时间<br> |
| `updated_at`<br> | TIMESTAMPTZ<br> | NOT NULL, DEFAULT NOW()<br> | 最后更新时间<br> |
| `is_active`<br> | BOOLEAN<br> | NOT NULL, DEFAULT TRUE<br> | 账户是否激活<br> |

##### `roles` - 角色表

存储用户角色（管理员、只读用户）。
| 字段名<br> | 数据类型<br> | 约束<br> | 描述<br> |
| --- | --- | --- | --- |
| `id`<br> | SERIAL<br> | PRIMARY KEY<br> | 角色ID<br> |
| `name`<br> | VARCHAR(50)<br> | UNIQUE, NOT NULL<br> | 角色名称 ('admin', 'readonly')<br> |

##### `user_roles` - 用户角色关联表

用户和角色的多对多关系。
| 字段名<br> | 数据类型<br> | 约束<br> | 描述<br> |
| --- | --- | --- | --- |
| `user_id`<br> | UUID<br> | FOREIGN KEY (users.id)<br> | 用户ID<br> |
| `role_id`<br> | INTEGER<br> | FOREIGN KEY (roles.id)<br> | 角色ID<br> |

##### `vulnerability_kb` - 漏洞知识库

存储分析师手动录入或维护的漏洞信息。
| 字段名<br> | 数据类型<br> | 约束<br> | 描述<br> |
| --- | --- | --- | --- |
| `id`<br> | UUID<br> | PRIMARY KEY<br> | 漏洞唯一标识符<br> |
| `name`<br> | VARCHAR(255)<br> | NOT NULL<br> | 漏洞名称，如 "Log4j2 JNDI RCE"<br> |
| `vuln_id`<br> | VARCHAR(100)<br> | UNIQUE<br> | 公共漏洞编号，如 "CVE-2021-44228"<br> |
| `description`<br> | TEXT<br> |   <br><br> | 漏洞的详细描述<br> |
| `severity`<br> | VARCHAR(50)<br> |   <br><br> | 严重等级 ('Critical', 'High', 'Medium', 'Low')<br> |
| `affected_products`<br> | JSONB<br> |   <br><br> | 受影响的产品和版本，格式灵活<br> |
| `poc_identifier`<br> | VARCHAR(255)<br> |   <br><br> | PoC脚本的标识符，供Agent调用<br> |
| `created_by_user_id`<br> | UUID<br> | FOREIGN KEY (users.id)<br> | 创建该记录的用户ID<br> |
| `created_at`<br> | TIMESTAMPTZ<br> | NOT NULL, DEFAULT NOW()<br> | 创建时间<br> |
| `updated_at`<br> | TIMESTAMPTZ<br> | NOT NULL, DEFAULT NOW()<br> | 最后更新时间<br> |

##### `agents` - Agent管理表

注册和管理所有在线的Agent。
| 字段名<br> | 数据类型<br> | 约束<br> | 描述<br> |
| --- | --- | --- | --- |
| `id`<br> | UUID<br> | PRIMARY KEY<br> | Agent唯一标识符<br> |
| `name`<br> | VARCHAR(255)<br> |   <br><br> | 用户为Agent设置的友好名称<br> |
| `hostname`<br> | VARCHAR(255)<br> |   <br><br> | Agent所在主机的名称<br> |
| `status`<br> | VARCHAR(50)<br> | NOT NULL<br> | Agent状态 ('online', 'offline', 'busy')<br> |
| `version`<br> | VARCHAR(50)<br> |   <br><br> | Agent版本号<br> |
| `last_seen_at`<br> | TIMESTAMPTZ<br> |   <br><br> | 最后心跳时间<br> |
| `created_at`<br> | TIMESTAMPTZ<br> | NOT NULL, DEFAULT NOW()<br> | 注册时间<br> |

##### `tasks` - 任务队列表

存储下发给Agent的验证任务。
| 字段名<br> | 数据类型<br> | 约束<br> | 描述<br> |
| --- | --- | --- | --- |
| `id`<br> | UUID<br> | PRIMARY KEY<br> | 任务唯一标识符<br> |
| `asset_identifier`<br> | VARCHAR(255)<br> | NOT NULL<br> | 目标资产在ES中的`_id`<br> |
| `vulnerability_kb_id`<br> | UUID<br> | FOREIGN KEY (vulnerability_kb.id)<br> | 待验证的漏洞ID<br> |
| `agent_id`<br> | UUID<br> | FOREIGN KEY (agents.id)<br> | 执行任务的Agent ID<br> |
| `status`<br> | VARCHAR(50)<br> | NOT NULL<br> | 任务状态 ('pending', 'running', 'completed', 'failed')<br> |
| `result`<br> | JSONB<br> |   <br><br> | Agent回传的结构化执行结果<br> |
| `created_by_user_id`<br> | UUID<br> | FOREIGN KEY (users.id)<br> | 下发任务的用户ID<br> |
| `created_at`<br> | TIMESTAMPTZ<br> | NOT NULL, DEFAULT NOW()<br> | 任务创建时间<br> |
| `completed_at`<br> | TIMESTAMPTZ<br> |   <br><br> | 任务完成时间<br> |

### 3. Elasticsearch 索引设计

此数据库存储所有发现的资产数据，为平台的搜索、分析和可视化提供支持。

#### 3.1 `assets` 索引映射 (Index Mapping)

建议使用索引模板来统一管理 `assets-*` 模式的索引。

    {
      "index_patterns": ["assets-*"],
      "template": {
        "settings": {
          "number_of_shards": 3,
          "number_of_replicas": 1
        },
        "mappings": {
          "properties": {
            "asset_type": { "type": "keyword" },
            "source": { "type": "keyword" },
            "tags": { "type": "keyword" },
            "created_at": { "type": "date" },
            "last_seen_at": { "type": "date" },
    
            "domain": { "type": "keyword" },
            "ip": { "type": "ip" },
            "port": { "type": "integer" },
            "service_name": { "type": "keyword" },
    
            "geo": {
              "properties": {
                "location": { "type": "geo_point" },
                "country_name": { "type": "keyword" },
                "city_name": { "type": "keyword" }
              }
            },
    
            "http": {
              "properties": {
                "url": { "type": "keyword" },
                "title": { "type": "text", "fields": { "keyword": { "type": "keyword", "ignore_above": 256 }}},
                "status_code": { "type": "short" },
                "tech_stack": {
                  "type": "nested",
                  "properties": {
                    "name": { "type": "keyword" },
                    "version": { "type": "keyword" },
                    "category": { "type": "keyword" }
                  }
                }
              }
            },
    
            "vulnerabilities": {
              "type": "nested",
              "properties": {
                "kb_id": { "type": "keyword" },
                "name": { "type": "keyword" },
                "severity": { "type": "keyword" },
                "status": { "type": "keyword" },
                "last_verified_at": { "type": "date" },
                "task_id": { "type": "keyword" }
              }
            },
            
            "raw_data": { "type": "flattened" }
          }
        }
      }
    }
    

#### 3.2 核心字段说明

*   `asset_type`: 资产类型，如 `domain`, `ip`, `service`。
    
*   `source`: 资产来源，如 `subfinder`, `naabu`, `manual`。
    
*   `tags`: 用户为资产打的标签，便于分类管理。
    
*   `domain`, `ip`, `port`, `service_name`: 资产的核心标识字段。
    
*   `http`: 存储Web资产的详细信息。
    *   `http.tech_stack`: **嵌套(nested)类型**，用于精确查询技术栈组合，如“同时使用Nginx 1.18和PHP 7.4的资产”。
        
*   `vulnerabilities`: **嵌套(nested)类型**，存储与该资产关联的漏洞列表。
    *   `kb_id`: 关联到PostgreSQL中`vulnerability_kb`表的ID。
        
    *   `status`: 漏洞在该资产上的状态，如 `potential` (潜在), `verified_true` (已验证存在), `verified_false` (已验证不存在)。
        
    *   `task_id`: 关联到PostgreSQL中`tasks`表的ID，用于追溯验证过程。
        
*   `raw_data`: **扁平化(flattened)类型**，用于存储来自扫描工具的、未提前定义结构的原始数据，提供灵活的扩展性。
    

### 4. 数据关联策略

*   **任务与资产的关联**: 当用户从平台下发一个验证任务时，`tasks`表会记录该任务信息，其中`asset_identifier`字段存储目标资产在Elasticsearch中的`_id`。
    
*   **漏洞与资产的关联**:
    1.  当分析师手动将一个漏洞（来自`vulnerability_kb`）关联给某个资产时，会在该资产文档的`vulnerabilities`数组中添加一条记录，状态为`potential`。
        
    2.  当验证任务完成后，平台根据结果更新对应资产文档中`vulnerabilities`数组里相应漏洞的`status`和`last_verified_at`等字段。
        
这种设计将关系型数据的事务性和一致性与搜索引擎的灵活性和强大查询能力结合起来，为平台提供了坚实的数据基础。
