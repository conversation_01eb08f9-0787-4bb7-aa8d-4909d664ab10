| 文档版本<br> | v1.2 (精简版)<br> |
| --- | --- |
| **修订日期**<br> | 2025年7月10日<br> |
| **修订人**<br> | Gemini AI<br> |
| **核心目标**<br> | 聚焦核心功能，快速迭代，实现MVP版本<br> |

### 1. 项目概述与背景

#### 1.1 项目背景

在漏洞赏金项目中，安全团队需要一个统一、高效的平台来发现、跟踪和管理海量的数字资产。当前的目标是构建一个核心功能完备的系统，解决资产管理和漏洞验证的核心痛点。

#### 1.2 核心价值主张

构建一个以资产为中心的安全管理平台。它能自动化发现和管理数字资产，并结合**人工知识库与Agent验证**的模式，高效地管理和验证潜在漏洞，为安全分析提供精准的数据支持和操作入口。

### 2. 项目目标

*   **首要目标**: 快速开发并交付一个可用的核心平台，实现资产的自动化发现、存储、检索和漏洞关联验证。
    
*   **技术目标**: 建立一个稳定、可扩展的后端基础架构，所有非开发组件（如数据库、探针）均通过容器化部署，前端和后端可在本地运行以方便调试。
    
*   **业务目标**:
    *   为安全分析师提供一个统一的资产视图和管理界面。
        
    *   将漏洞验证流程工具化，通过平台下发指令，由Agent执行，并回收结果。
        
    *   通过强大的搜索功能，快速从海量数据中定位目标资产。
        

### 3. 用户角色与认证

为简化初期开发，系统仅设立两种角色：
| 角色<br> | 核心职责<br> | 认证实现<br> |
| --- | --- | --- |
| **管理员 (Admin)**<br> | 拥有平台的全部操作权限，包括资产管理、漏洞信息录入、任务下发、用户管理等。<br> | 使用业界成熟的认证框架（如JWT）实现基础的登录和权限控制。<br> |
| **只读用户 (Read-only)**<br> | 只能查看平台上的所有数据（资产、漏洞等），不能进行任何修改或操作。<br> |   <br><br> |

### 4. 功能性需求 (核心 MVP)

#### 4.1 资产发现与管理模块

*   `4.1.1` **自动化资产发现**:
    *   核心功能，由Go语言开发的Agent执行。
        
    *   Agent集成ProjectDiscovery工具集 (subfinder, naabu, httpx, katana)，持续发现资产。
        
    *   Agent将发现的结构化数据上报至平台后端。
        
*   `4.1.2` **资产信息全景视图**:
    *   平台提供统一界面，展示资产的360度视图（IP、端口、服务、技术栈、关联漏洞等）。
        
*   `4.1.3` **资产手动管理**: 支持在前端界面对资产进行手动的增、删、改、查和标记。
    

#### 4.2 漏洞管理与验证模块 (核心流程变更)

*   `4.2.1` **漏洞信息聚合**:
    *   **主要方式**: 分析师在平台**手动录入**漏洞信息，或维护一个私有的漏洞知识库。
        
    *   **辅助方式**: 平台可根据资产的技术栈指纹（如 "Apache 2.4.41", "ThinkPHP 5.0.23"），自动从私有漏洞库中匹配并提示**可能存在**的漏洞。
        
*   `4.2.2` **漏洞验证任务下发**:
    *   这是本模块的核心。分析师可以针对某个特定资产和其关联的某个潜在漏洞，从平台**下发一个验证任务**。
        
    *   任务内容包含：目标资产信息、需要验证的漏洞PoC（或PoC标识）。
        
    *   该任务指令被发送到指定的Agent。
        
*   `4.2.3` **Agent执行与结果回传**:
    *   Agent接收到验证指令后，在本地执行对应的PoC脚本。
        
    *   Agent将执行结果（如：成功、失败、超时、原始返回信息）结构化地回传给平台。
        
    *   平台更新该漏洞在该资产上的验证状态（如 `验证成功-存在漏洞`, `验证失败-无法利用`）。
        

#### 4.3 数据检索与可视化模块

*   `4.3.1` **Elasticsearch高级搜索**:
    *   这是数据消费的核心。必须提供一个强大的高级搜索栏。
        
    *   支持**全文本搜索**。
        
    *   支持**字段精确匹配** (如 `asset.ip:"*******"`)。
        
    *   支持**组合逻辑查询** (AND, OR, NOT)。
        
    *   支持**范围查询** (如 `asset.ports.port:[8000 TO 9000]`)。
        
    *   支持**聚合分析** (Aggregation)，为仪表盘提供数据源，例如：统计开放端口Top 10，统计不同技术栈的资产数量等。
        
*   `4.3.2` **可定制仪表盘**:
    *   提供一个简洁的仪表盘。
        
    *   仪表盘上的图表和统计数据，其数据源**完全来自于用户通过高级搜索功能定义的查询**。用户保存一个查询，就可以将其作为一个图表组件添加到仪表盘。
        

#### 4.4 集成与自动化模块

*   `4.4.1` **平台API集成 (远期考虑)**:
    *   初期可手动导入数据。未来版本计划集成HackerOne, Bugcrowd等平台的API，用于同步资产范围。
        
*   `4.4.2` **Agent指令下发**:
    *   平台后端提供API端点，用于接收前端的指令。
        
    *   通过消息队列或gRPC等方式，将指令安全、可靠地分发给对应的在线Agent。
        
*   `4.4.3` **AI辅助决策模块**:
    *   提供一个“AI分析”功能区。
        
    *   用户可以框选一段资产信息或漏洞描述，点击“AI分析”按钮。
        
    *   平台将选定的文本，通过API发送给用户配置的**兼容OpenAI或Gemini的AI模型**。
        
    *   将AI返回的分析结果（如：漏洞原理、修复建议、潜在影响）展示给用户。
        

### 5. 技术架构与部署策略

#### 5.1 技术选型 (保持不变)

| 组件<br> | 技术<br> | 理由<br> |
| --- | --- | --- |
| **探针/Agent**<br> | Go 1.21+<br> | 高性能，易于打包和分发。<br> |
| **后端API**<br> | Python 3.11+ with FastAPI<br> | 开发效率高，生态丰富。<br> |
| **前端**<br> | React 18 + TypeScript<br> | 现代前端开发的事实标准。<br> |
| **主数据库**<br> | Elasticsearch 8.x<br> | 强大的搜索和分析能力是平台核心。<br> |
| **元数据存储**<br> | PostgreSQL 15+<br> | 存储用户、角色等关系型数据。<br> |
| **缓存/消息队列**<br> | Redis 7.x<br> | 可用于缓存和轻量级任务队列。<br> |
| **容器编排**<br> | Kubernetes / Docker Compose<br> | 实现环境一致性和快速部署。<br> |

#### 5.2 部署策略

*   **开发环境**:
    *   **前端 (React)**: 在开发者本地通过 `npm run start` 运行。
        
    *   **后端 (FastAPI)**: 在开发者本地通过 `uvicorn` 等工具运行。
        
    *   **其他所有组件 (ES, PG, Redis, Agent)**: **必须通过Docker或Docker Compose在本地容器化运行**。这确保了开发环境与生产环境的高度一致性。
        
*   **生产环境**:
    *   所有组件，包括前端和后端，都将被打包成Docker镜像，通过Kubernetes进行部署和管理。
