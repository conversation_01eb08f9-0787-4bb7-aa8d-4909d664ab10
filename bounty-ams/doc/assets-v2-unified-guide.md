# 资产管理 V2.0 统一平台使用指南

## 🎯 概述

资产管理 V2.0 统一平台是基于 Elasticsearch 的下一代资产管理系统，提供：

- **统一数据管道**：Agent发现 → 动态模型 → 手动导入 → ES统一存储
- **智能去重和数据清洗**：基于多维度特征的智能去重算法
- **大数据量支持**：优化的索引结构，支持海量数据存储和快速检索
- **深度Kibana集成**：强大的数据可视化和分析能力
- **高级搜索和分析**：复杂查询、聚合分析、趋势分析、异常检测

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   数据源层      │    │   处理层        │    │   存储层        │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ • Agent发现     │───▶│ • 数据清洗      │───▶│ • Elasticsearch │
│ • 动态模型      │    │ • 智能去重      │    │ • PostgreSQL    │
│ • 手动导入      │    │ • 格式标准化    │    │   (元数据)      │
│ • API同步       │    │ • 质量评估      │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                ▲
                                │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   应用层        │    │   分析层        │    │   可视化层      │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ • Web界面       │    │ • 趋势分析      │    │ • Kibana仪表板  │
│ • REST API      │    │ • 异常检测      │    │ • 自定义图表    │
│ • 批量操作      │    │ • 相关性分析    │    │ • 实时监控      │
│ • 数据导出      │    │ • 安全风险评估  │    │ • 报告生成      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🚀 快速开始

### 1. 启动服务

```bash
# 启动后端服务
cd bounty-ams/backend
python main.py

# 启动前端服务
cd bounty-ams/frontend
npm run dev

# 确保Elasticsearch运行
curl http://localhost:9200/_cluster/health
```

### 2. 访问系统

- **前端界面**: http://localhost:5175/assets-v2-unified
- **API文档**: http://localhost:8000/docs
- **Kibana**: http://localhost:5601

### 3. 初始化数据

系统会自动创建优化的Elasticsearch索引模板和别名。首次访问时会：

1. 创建 `unified-assets-template` 索引模板
2. 创建当前月份的索引（如 `unified-assets-2025-07`）
3. 设置 `unified-assets` 别名

## 📊 核心功能

### 1. 统一数据管道

#### 自动同步
```python
# 启动数据管道
from data_pipeline_v2 import create_data_pipeline

pipeline = await create_data_pipeline(asset_manager, db_session)
await pipeline.start_pipeline()
```

#### 手动同步
```bash
# 从动态模型同步
curl -X POST "http://localhost:8000/api/assets-v2-unified/sync/dynamic-models?model_type_name=discovered_asset"

# 批量导入
curl -X POST "http://localhost:8000/api/assets-v2-unified/import/bulk" \
  -H "Content-Type: application/json" \
  -d '{"assets": [...], "metadata": {...}}'
```

### 2. 智能去重和数据清洗

#### 去重分析
```bash
# 分析重复资产
curl "http://localhost:8000/api/assets-v2-unified/analysis/duplicates"

# 清理重复资产
curl -X DELETE "http://localhost:8000/api/assets-v2-unified/cleanup/duplicates?dry_run=false"
```

#### 数据质量提升
```bash
# 数据质量评估
curl "http://localhost:8000/api/assets-v2-unified/quality/assessment"

# 提升数据质量
curl -X POST "http://localhost:8000/api/assets-v2-unified/quality/enhance"
```

### 3. 高级搜索和分析

#### 复杂搜索
```json
{
  "query": "example.com",
  "filters": {
    "asset_type": "domain",
    "confidence": "high",
    "discovered_at_range": {
      "gte": "2025-01-01",
      "lte": "2025-07-14"
    }
  },
  "page": 1,
  "size": 20,
  "include_aggregations": true
}
```

#### 趋势分析
```bash
# 获取30天趋势
curl "http://localhost:8000/api/assets-v2-unified/analysis/trends?time_range=30d&interval=1d"
```

#### 异常检测
```bash
# 检测数据质量异常
curl "http://localhost:8000/api/assets-v2-unified/analysis/anomalies?field=data_quality_score&threshold_std=2.0"
```

#### 安全风险评估
```bash
# 获取安全风险评估
curl "http://localhost:8000/api/assets-v2-unified/analysis/security-risk"
```

### 4. Kibana深度集成

#### 设置步骤
1. 访问 Kibana: http://localhost:5601
2. 创建索引模式: `unified-assets-*`
3. 设置时间字段: `discovered_at`
4. 导入预配置仪表板

#### 可用仪表板
- **资产概览仪表板**: 总体概览和核心指标
- **安全分析仪表板**: 安全相关分析和风险评估
- **数据质量仪表板**: 数据质量监控和分析
- **运营监控仪表板**: 系统运营和性能监控

## 🔧 配置和优化

### 1. 索引优化

```bash
# 查看索引健康状态
curl "http://localhost:8000/api/assets-v2-unified/admin/index/health"

# 优化索引性能
curl -X POST "http://localhost:8000/api/assets-v2-unified/admin/index/optimize"

# 清理旧索引
curl -X DELETE "http://localhost:8000/api/assets-v2-unified/admin/index/cleanup?retention_days=90"
```

### 2. 性能调优

#### Elasticsearch设置
```yaml
# elasticsearch.yml
cluster.name: bounty-ams
node.name: node-1
network.host: 0.0.0.0
discovery.type: single-node

# 内存设置
-Xms2g
-Xmx2g

# 索引设置
index.refresh_interval: 30s
index.number_of_shards: 3
index.number_of_replicas: 1
```

#### 数据管道配置
```python
# 同步配置
sync_config = SyncConfig(
    source_model_type="discovered_asset",
    sync_mode=SyncMode.INCREMENTAL,
    batch_size=1000,
    sync_interval_minutes=30,
    enable_dedup=True,
    enable_cleaning=True
)
```

## 📈 监控和维护

### 1. 系统监控

- **集群健康**: 监控Elasticsearch集群状态
- **索引大小**: 跟踪索引增长和存储使用
- **查询性能**: 监控搜索和聚合查询响应时间
- **数据质量**: 跟踪数据质量分数和验证错误

### 2. 定期维护

- **索引优化**: 定期合并段，优化查询性能
- **数据清理**: 清理旧索引和重复数据
- **质量提升**: 运行数据质量增强任务
- **备份恢复**: 定期备份重要数据

## 🔍 故障排除

### 常见问题

1. **Elasticsearch连接失败**
   ```bash
   # 检查ES状态
   curl http://localhost:9200/_cluster/health
   
   # 重启ES服务
   sudo systemctl restart elasticsearch
   ```

2. **索引创建失败**
   ```bash
   # 检查索引模板
   curl http://localhost:9200/_index_template/unified-assets-template
   
   # 手动创建索引
   curl -X POST "http://localhost:8000/api/assets-v2-unified/admin/index/create-monthly"
   ```

3. **数据同步问题**
   ```bash
   # 检查同步状态
   curl "http://localhost:8000/api/assets-v2-unified/sync/status"
   
   # 手动触发同步
   curl -X POST "http://localhost:8000/api/assets-v2-unified/sync/manual"
   ```

### 日志查看

```bash
# 后端日志
tail -f bounty-ams/backend/logs/app.log

# Elasticsearch日志
tail -f /var/log/elasticsearch/elasticsearch.log

# 前端控制台
# 打开浏览器开发者工具查看
```

## 🎯 最佳实践

1. **数据导入**: 使用批量导入API，避免单条插入
2. **搜索优化**: 使用聚合查询减少数据传输
3. **索引管理**: 定期清理旧索引，保持系统性能
4. **监控告警**: 设置关键指标监控和告警
5. **备份策略**: 制定完整的数据备份和恢复计划

## 📚 API参考

详细的API文档请访问: http://localhost:8000/docs

主要API端点：
- `/api/assets-v2-unified/search` - 资产搜索
- `/api/assets-v2-unified/statistics` - 统计信息
- `/api/assets-v2-unified/analysis/*` - 各种分析功能
- `/api/assets-v2-unified/quality/*` - 数据质量管理
- `/api/assets-v2-unified/admin/*` - 系统管理
- `/api/assets-v2-unified/kibana/*` - Kibana集成

---

🎉 **恭喜！您已成功部署资产管理 V2.0 统一平台！**

如有问题，请查看日志或联系技术支持。
