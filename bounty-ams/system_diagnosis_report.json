{"timestamp": "2025-07-12T14:29:23.327611", "overall_status": "unhealthy", "detailed_results": {"数据库连接": {"status": "error", "message": "数据库检查异常: [Errno 2] No such file or directory: 'psql'"}, "Elasticsearch连接": {"status": "healthy", "cluster_status": "yellow", "number_of_nodes": 1, "message": "Elasticsearch连接正常"}, "Redis连接": {"status": "error", "message": "Redis检查异常: [Errno 2] No such file or directory: 'redis-cli'"}, "后端API": {"status": "healthy", "message": "后端API正常", "data": {"status": "healthy"}}, "前端服务": {"status": "healthy", "message": "前端服务正常"}, "API端点": {"status": "error", "message": "检查失败: 'message'"}, "运行进程": {"status": "info", "running_services": {"postgresql": true, "elasticsearch": true, "redis": true, "node_vite": true, "python_fastapi": true}}, "端口监听": {"status": "error", "message": "端口检查异常: [Errno 2] No such file or directory: 'netstat'"}}, "recommendations": ["启动PostgreSQL数据库服务: sudo systemctl start postgresql", "启动Redis服务: sudo systemctl start redis"]}