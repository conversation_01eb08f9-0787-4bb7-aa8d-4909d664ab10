#!/usr/bin/env python3
"""
连接测试脚本
使用Python原生方式测试数据库和Redis连接
"""

import asyncio
import sys
import os
sys.path.append('./backend')

async def test_database_connection():
    """测试数据库连接"""
    try:
        from database import get_db, engine
        from sqlalchemy import text
        
        # 测试数据库连接
        async with engine.begin() as conn:
            result = await conn.execute(text("SELECT 1"))
            row = result.fetchone()
            if row and row[0] == 1:
                print("✅ 数据库连接成功")
                return True
            else:
                print("❌ 数据库连接失败：查询结果异常")
                return False
                
    except Exception as e:
        print(f"❌ 数据库连接失败: {str(e)}")
        return False

async def test_redis_connection():
    """测试Redis连接"""
    try:
        import redis.asyncio as redis
        from config import settings
        
        # 创建Redis连接
        redis_client = redis.from_url(settings.REDIS_URL)
        
        # 测试ping
        pong = await redis_client.ping()
        if pong:
            print("✅ Redis连接成功")
            await redis_client.close()
            return True
        else:
            print("❌ Redis连接失败：ping无响应")
            await redis_client.close()
            return False
            
    except Exception as e:
        print(f"❌ Redis连接失败: {str(e)}")
        return False

async def test_elasticsearch_connection():
    """测试Elasticsearch连接"""
    try:
        from elasticsearch_client import get_es_client
        
        es_client = await get_es_client()
        
        # 测试集群健康状态
        health = await es_client.cluster.health()
        if health:
            print(f"✅ Elasticsearch连接成功，集群状态: {health.get('status', 'unknown')}")
            return True
        else:
            print("❌ Elasticsearch连接失败：健康检查无响应")
            return False
            
    except Exception as e:
        print(f"❌ Elasticsearch连接失败: {str(e)}")
        return False

async def test_api_endpoints():
    """测试关键API端点"""
    import aiohttp
    
    endpoints = [
        "/health",
        "/api/auth/me",
        "/api/agents/",
        "/api/tasks",
        "/api/workflows",
        "/api/discovered-assets/stats",
        "/api/dynamic-models/types"
    ]
    
    success_count = 0
    total_count = len(endpoints)
    
    try:
        async with aiohttp.ClientSession() as session:
            for endpoint in endpoints:
                try:
                    url = f"http://localhost:8000{endpoint}"
                    async with session.get(url, timeout=5) as response:
                        if response.status in [200, 401]:  # 401 for auth endpoints is expected
                            print(f"✅ {endpoint}: 状态码 {response.status}")
                            success_count += 1
                        else:
                            print(f"❌ {endpoint}: 状态码 {response.status}")
                except Exception as e:
                    print(f"❌ {endpoint}: 连接失败 - {str(e)}")
            
            print(f"\n📊 API端点测试结果: {success_count}/{total_count} 成功")
            return success_count == total_count
            
    except Exception as e:
        print(f"❌ API端点测试失败: {str(e)}")
        return False

async def main():
    """主测试函数"""
    print("🔍 开始连接测试...")
    
    # 测试各个服务
    db_ok = await test_database_connection()
    redis_ok = await test_redis_connection()
    es_ok = await test_elasticsearch_connection()
    api_ok = await test_api_endpoints()
    
    print("\n📊 测试总结:")
    print(f"数据库: {'✅' if db_ok else '❌'}")
    print(f"Redis: {'✅' if redis_ok else '❌'}")
    print(f"Elasticsearch: {'✅' if es_ok else '❌'}")
    print(f"API端点: {'✅' if api_ok else '❌'}")
    
    if db_ok and redis_ok and es_ok and api_ok:
        print("\n🎉 所有连接测试通过！系统状态正常。")
        return True
    else:
        print("\n⚠️  部分连接测试失败，需要进一步检查。")
        return False

if __name__ == "__main__":
    asyncio.run(main()) 