#!/usr/bin/env python3
"""
资产管理系统综合修复脚本

此脚本解决以下问题：
1. 资产无法与平台、项目关联
2. 导入的资产无法在资产管理页面展示
3. 数据存储不一致（Elasticsearch vs PostgreSQL）
4. 动态模型字段映射问题
"""

import os
import sys
import asyncio
import logging
from datetime import datetime

# 添加backend目录到Python路径
backend_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'backend')
if backend_dir not in sys.path:
    sys.path.insert(0, backend_dir)

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def main():
    """主修复流程"""
    logger.info("🚀 开始资产管理系统综合修复")
    logger.info("=" * 60)
    
    try:
        # 1. 运行资产管理修复
        logger.info("📊 步骤1: 修复资产数据一致性...")
        await run_asset_management_fix()
        
        # 2. 运行数据导入修复
        logger.info("📥 步骤2: 修复数据导入流程...")
        run_data_import_fix()
        
        # 3. 创建或更新必要的初始化数据
        logger.info("🔧 步骤3: 初始化平台项目模型...")
        await run_platform_project_init()
        
        # 4. 验证修复结果
        logger.info("✅ 步骤4: 验证修复结果...")
        await verify_fixes()
        
        logger.info("=" * 60)
        logger.info("🎉 资产管理系统修复完成！")
        
        # 提供使用建议
        print_usage_guide()
        
    except Exception as e:
        logger.error(f"❌ 修复过程中出现错误: {e}")
        logger.error("请检查错误信息并重试")
        return False
    
    return True

async def run_asset_management_fix():
    """运行资产管理修复"""
    try:
        # 尝试导入并运行资产管理修复
        from fix_asset_management import AssetManagementFixer
        
        fixer = AssetManagementFixer()
        await fixer.fix_all_issues()
        
        logger.info("✅ 资产数据一致性修复完成")
        
    except ImportError as e:
        logger.error(f"❌ 无法导入资产管理修复模块: {e}")
        logger.info("请确保fix_asset_management.py文件存在")
    except Exception as e:
        logger.error(f"❌ 资产管理修复失败: {e}")
        raise

def run_data_import_fix():
    """运行数据导入修复"""
    try:
        # 尝试导入并运行数据导入修复
        from fix_data_import import main as fix_data_import_main
        
        fix_data_import_main()
        
        logger.info("✅ 数据导入流程修复完成")
        
    except ImportError as e:
        logger.error(f"❌ 无法导入数据导入修复模块: {e}")
        logger.info("请确保fix_data_import.py文件存在")
    except Exception as e:
        logger.error(f"❌ 数据导入修复失败: {e}")
        raise

async def run_platform_project_init():
    """运行平台项目初始化"""
    try:
        # 检查是否需要初始化平台项目模型
        if os.path.exists('backend/init_platform_project_models.py'):
            logger.info("发现平台项目初始化脚本，正在运行...")
            
            # 导入并运行初始化脚本
            import subprocess
            result = subprocess.run(
                [sys.executable, 'backend/init_platform_project_models.py'],
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0:
                logger.info("✅ 平台项目模型初始化完成")
                logger.info(result.stdout)
            else:
                logger.warning(f"⚠️ 平台项目模型初始化出现警告: {result.stderr}")
        else:
            logger.info("未找到平台项目初始化脚本，跳过此步骤")
            
    except Exception as e:
        logger.error(f"❌ 平台项目初始化失败: {e}")
        # 不抛出异常，因为这不是致命错误

async def verify_fixes():
    """验证修复结果"""
    try:
        # 验证数据库连接
        from sqlalchemy.ext.asyncio import create_async_engine
        from config import settings
        
        engine = create_async_engine(settings.DATABASE_URL)
        
        async with engine.begin() as conn:
            # 检查关键表是否存在
            tables_to_check = [
                'model_types',
                'dynamic_entities',
                'users'
            ]
            
            for table in tables_to_check:
                result = await conn.execute(
                    f"SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = '{table}')"
                )
                exists = result.scalar()
                if exists:
                    logger.info(f"✅ 表 {table} 存在")
                else:
                    logger.warning(f"⚠️ 表 {table} 不存在")
        
        await engine.dispose()
        
        # 验证Elasticsearch连接
        try:
            from elasticsearch_client import get_es_client
            
            es_client = await get_es_client()
            
            # 检查索引是否存在
            indices_to_check = [
                'enhanced_asset-*',
                'dynamic_platform-*',
                'dynamic_project-*'
            ]
            
            for index_pattern in indices_to_check:
                try:
                    response = await es_client.indices.get(index=index_pattern)
                    if response:
                        logger.info(f"✅ 索引模式 {index_pattern} 存在")
                    else:
                        logger.info(f"ℹ️ 索引模式 {index_pattern} 不存在（这是正常的，会在使用时创建）")
                except:
                    logger.info(f"ℹ️ 索引模式 {index_pattern} 不存在（这是正常的，会在使用时创建）")
            
            await es_client.close()
            
        except Exception as e:
            logger.warning(f"⚠️ Elasticsearch连接验证失败: {e}")
        
        logger.info("✅ 系统验证完成")
        
    except Exception as e:
        logger.error(f"❌ 验证过程失败: {e}")
        # 不抛出异常，因为这不是致命错误

def print_usage_guide():
    """打印使用指南"""
    print("\n" + "=" * 60)
    print("📋 使用指南")
    print("=" * 60)
    
    print("\n1. 📊 创建平台和项目：")
    print("   - 访问前端Models页面")
    print("   - 创建平台（platform）实体")
    print("   - 为每个平台创建项目（project）实体")
    
    print("\n2. 📥 导入资产：")
    print("   - 使用数据导入功能")
    print("   - 确保选择正确的平台和项目")
    print("   - 资产将自动关联到选定的平台和项目")
    
    print("\n3. 🔍 查看资产：")
    print("   - 访问资产管理页面")
    print("   - 使用平台和项目筛选器")
    print("   - 资产现在应该正确显示和关联")
    
    print("\n4. 🔧 如果遇到问题：")
    print("   - 检查后端日志")
    print("   - 确认Elasticsearch服务正常运行")
    print("   - 重新运行此修复脚本")
    
    print("\n5. 📈 监控建议：")
    print("   - 定期检查资产数据一致性")
    print("   - 监控Elasticsearch索引大小")
    print("   - 备份重要的平台项目配置")
    
    print("\n" + "=" * 60)
    print("🚀 开始使用修复后的资产管理系统！")
    print("=" * 60)

def create_verification_script():
    """创建验证脚本"""
    verification_script = '''#!/usr/bin/env python3
"""
资产管理系统验证脚本
用于验证修复后的系统状态
"""

import asyncio
import sys
import os

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def verify_system():
    """验证系统状态"""
    print("🔍 开始验证资产管理系统状态...")
    
    try:
        # 验证数据库连接
        from sqlalchemy.ext.asyncio import create_async_engine, async_sessionmaker
        from sqlalchemy import select, text
        from config import settings
        from models_dynamic import ModelType, DynamicEntity
        
        engine = create_async_engine(settings.DATABASE_URL)
        async_session = async_sessionmaker(engine, expire_on_commit=False)
        
        async with async_session() as session:
            # 检查模型类型
            result = await session.execute(select(ModelType))
            model_types = result.scalars().all()
            
            print(f"📊 找到 {len(model_types)} 个模型类型:")
            for mt in model_types:
                print(f"  - {mt.name}: {mt.display_name}")
            
            # 检查动态实体
            result = await session.execute(select(DynamicEntity))
            entities = result.scalars().all()
            
            print(f"📦 找到 {len(entities)} 个动态实体")
            
            # 按模型类型分组统计
            stats = {}
            for entity in entities:
                model_name = next((mt.name for mt in model_types if mt.id == entity.model_type_id), 'unknown')
                stats[model_name] = stats.get(model_name, 0) + 1
            
            print("📈 按类型分布:")
            for model_name, count in stats.items():
                print(f"  - {model_name}: {count} 个")
        
        await engine.dispose()
        
        # 验证Elasticsearch
        try:
            from elasticsearch_client import get_es_client
            es_client = await get_es_client()
            
            # 检查索引
            indices = await es_client.cat.indices(format='json')
            asset_indices = [idx for idx in indices if 'asset' in idx.get('index', '').lower()]
            
            print(f"🔍 Elasticsearch中找到 {len(asset_indices)} 个资产相关索引:")
            for idx in asset_indices:
                print(f"  - {idx['index']}: {idx['docs.count']} 文档")
            
            await es_client.close()
            
        except Exception as e:
            print(f"⚠️ Elasticsearch验证失败: {e}")
        
        print("✅ 系统验证完成")
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    result = asyncio.run(verify_system())
    if result:
        print("🎉 系统状态正常！")
    else:
        print("❌ 系统存在问题，请查看错误信息")
'''
    
    # 写入验证脚本
    with open('verify_system.py', 'w', encoding='utf-8') as f:
        f.write(verification_script)
    
    logger.info("✅ 验证脚本已创建: verify_system.py")

if __name__ == "__main__":
    # 创建验证脚本
    create_verification_script()
    
    # 运行主修复流程
    success = asyncio.run(main())
    
    if success:
        print("\n🎊 修复成功完成！")
        print("运行 python verify_system.py 来验证系统状态")
        sys.exit(0)
    else:
        print("\n💥 修复失败！")
        sys.exit(1) 