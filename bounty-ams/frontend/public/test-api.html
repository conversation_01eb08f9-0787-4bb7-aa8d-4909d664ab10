<!DOCTYPE html>
<html>
<head>
    <title>Agent API Test</title>
</head>
<body>
    <h1>Agent API Test</h1>
    <button onclick="testAgentAPI()">Test Agent API</button>
    <div id="result"></div>

    <script>
        async function testAgentAPI() {
            try {
                // 首先登录获取token
                const loginResponse = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'password'
                    })
                });
                
                const loginData = await loginResponse.json();
                console.log('Login response:', loginData);
                
                if (loginData.access_token) {
                    // 使用token获取agents
                    const agentsResponse = await fetch('/api/agents/', {
                        headers: {
                            'Authorization': `Bearer ${loginData.access_token}`
                        }
                    });
                    
                    const agentsData = await agentsResponse.json();
                    console.log('Agents response:', agentsData);
                    
                    document.getElementById('result').innerHTML = `
                        <h2>Success!</h2>
                        <p>Found ${agentsData.agents ? agentsData.agents.length : 0} agents</p>
                        <pre>${JSON.stringify(agentsData, null, 2)}</pre>
                    `;
                } else {
                    document.getElementById('result').innerHTML = `<p>Login failed: ${JSON.stringify(loginData)}</p>`;
                }
            } catch (error) {
                console.error('Error:', error);
                document.getElementById('result').innerHTML = `<p>Error: ${error.message}</p>`;
            }
        }
    </script>
</body>
</html>