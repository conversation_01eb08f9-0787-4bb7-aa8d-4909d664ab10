# Agent控制功能实现说明

## 问题分析

你的Agent管理界面功能很完善，但控制功能（暂停、停止、重启）目前无法真正控制Agent，原因如下：

### 1. 当前工作流程
1. ✅ 前端发送控制命令到后端
2. ✅ 后端创建控制任务（如 `agent_pause`、`agent_stop`）
3. ❌ Go Agent接收到任务但无法处理（没有相应的执行器）
4. ❌ Agent状态没有改变

### 2. 根本原因
Go Agent的ExecutorManager只注册了三个执行器：
- `subdomain_discovery` (SubdomainExecutor)
- `port_scanning` (PortScanExecutor) 
- `service_detection` (ServiceExecutor)

缺少处理 `agent_pause`、`agent_stop`、`agent_restart`、`cancel_tasks` 的执行器。

## 完整解决方案

### 方案1: 快速修复 - 修改ExecutorManager支持控制命令

1. **修改 `agents/go-agent/executor/manager.go`**：
```go
// ExecuteTask 执行任务
func (em *ExecutorManager) ExecuteTask(ctx context.Context, task *client.TaskDefinition) (*client.TaskResult, error) {
	// 特殊处理Agent控制命令
	if isAgentControlTask(task.TaskType) {
		return em.executeAgentControl(ctx, task)
	}
	
	executor, exists := em.GetExecutor(task.TaskType)
	if !exists {
		return nil, fmt.Errorf("no executor found for task type: %s", task.TaskType)
	}
	// ... 原有逻辑
}

// 检查是否为Agent控制任务
func isAgentControlTask(taskType string) bool {
	controlTasks := []string{"agent_pause", "agent_resume", "agent_stop", "agent_restart", "cancel_tasks"}
	for _, ct := range controlTasks {
		if taskType == ct {
			return true
		}
	}
	return false
}

// 执行Agent控制命令
func (em *ExecutorManager) executeAgentControl(ctx context.Context, task *client.TaskDefinition) (*client.TaskResult, error) {
	result := &client.TaskResult{
		TaskID: task.TaskID,
		Status: "completed",
		ResultData: map[string]interface{}{},
	}

	em.logger.Infof("Executing agent control command: %s", task.TaskType)

	switch task.TaskType {
	case "agent_pause":
		// 暂停逻辑 - 可以设置一个全局标志
		result.ResultData["message"] = "Agent paused successfully"
		em.logger.Info("Agent paused")
		
	case "agent_resume":
		// 恢复逻辑
		result.ResultData["message"] = "Agent resumed successfully" 
		em.logger.Info("Agent resumed")
		
	case "agent_stop":
		result.ResultData["message"] = "Agent stop initiated"
		em.logger.Info("Agent stop command received")
		// 延迟停止，让结果先提交
		go func() {
			time.Sleep(2 * time.Second)
			os.Exit(0)
		}()
		
	case "agent_restart":
		result.ResultData["message"] = "Agent restart initiated"
		em.logger.Info("Agent restart command received")
		// 重启逻辑
		
	case "cancel_tasks":
		result.ResultData["message"] = "Tasks cancellation initiated"
		em.logger.Info("Task cancellation command received")
		
	default:
		return nil, fmt.Errorf("unknown agent control command: %s", task.TaskType)
	}

	return result, nil
}
```

2. **重新编译和重启Agent**：
```bash
cd agents/go-agent
go build -o bounty-agent
./bounty-agent
```

### 方案2: 完整实现 - 添加状态管理

如果要实现真正的暂停/恢复功能，需要：

1. **在Agent主结构中添加状态字段**
2. **在任务轮询中检查状态**
3. **实现状态持久化**

### 方案3: 简化方案 - 仅支持停止和重启

最实用的控制功能：
- **停止**: 直接退出进程
- **重启**: 退出后由进程管理器重启
- **取消任务**: 清空任务队列

## 立即可用的临时解决方案

1. **停止功能**: 修改ExecutorManager，接收到`agent_stop`时调用`os.Exit(0)`
2. **重启功能**: 需要系统级的进程管理器（如systemd、supervisor）
3. **任务取消**: 清空Agent的任务队列

## 建议

由于这涉及Go代码的修改和重新编译，建议：

1. **短期**: 使用当前版本，了解控制命令实际是创建任务
2. **中期**: 按方案1修改ExecutorManager，实现基本控制
3. **长期**: 实现完整的状态管理和持久化

当前你的Agent管理界面功能已经很完善了，只是Go Agent端需要相应的修改来真正处理控制命令。