import { message } from 'antd';

export interface ApiError {
  message: string;
  status?: number;
  detail?: string;
}

export function handleApiError(error: any): ApiError {
  console.error('API Error:', error);
  
  if (error.response) {
    // 服务器响应了错误状态码
    const status = error.response.status;
    const data = error.response.data;
    
    let errorMessage = '请求失败';
    
    if (data?.detail) {
      errorMessage = data.detail;
    } else if (data?.message) {
      errorMessage = data.message;
    } else {
      switch (status) {
        case 400:
          errorMessage = '请求参数错误';
          break;
        case 401:
          errorMessage = '未授权，请重新登录';
          break;
        case 403:
          errorMessage = '权限不足';
          break;
        case 404:
          errorMessage = '请求的资源不存在';
          break;
        case 500:
          errorMessage = '服务器内部错误';
          break;
        default:
          errorMessage = `请求失败 (${status})`;
      }
    }
    
    return {
      message: errorMessage,
      status: status,
      detail: data?.detail
    };
  } else if (error.request) {
    // 请求发出但没有响应
    console.error('No response received:', error.request);
    return {
      message: '网络连接失败，请检查网络设置',
      detail: 'Network error'
    };
  } else {
    // 其他错误
    return {
      message: error.message || '未知错误',
      detail: 'Unknown error'
    };
  }
}

export function showErrorMessage(error: any, defaultMessage?: string) {
  const apiError = handleApiError(error);
  message.error(defaultMessage || apiError.message);
  return apiError;
} 