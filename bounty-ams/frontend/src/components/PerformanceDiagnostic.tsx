import React, { useState, useEffect } from 'react';
import { Card, Statistic, Row, Col, Button, Collapse, Typography, Tag } from 'antd';
import { ClockCircleOutlined, DatabaseOutlined, ReloadOutlined } from '@ant-design/icons';
import { OptimizedApiService } from '../services/optimizedApi';

const { Panel } = Collapse;
const { Text } = Typography;

interface PerformanceStats {
  apiCacheStats: any;
  longTermCacheStats: any;
  shortTermCacheStats: any;
  lastLoadTime: number;
  networkStatus: 'good' | 'slow' | 'timeout' | 'unknown';
}

const PerformanceDiagnostic: React.FC = () => {
  const [stats, setStats] = useState<PerformanceStats>({
    apiCacheStats: { cacheSize: 0, pendingRequests: 0, hitRate: 0 },
    longTermCacheStats: { cacheSize: 0, pendingRequests: 0, hitRate: 0 },
    shortTermCacheStats: { cacheSize: 0, pendingRequests: 0, hitRate: 0 },
    lastLoadTime: 0,
    networkStatus: 'unknown'
  });

  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // 定期更新统计信息
    const interval = setInterval(() => {
      updateStats();
    }, 2000);

    return () => clearInterval(interval);
  }, []);

  const updateStats = () => {
    try {
      const cacheStats = OptimizedApiService.getCacheStats();
      setStats(prev => ({
        ...prev,
        apiCacheStats: cacheStats.apiCache,
        longTermCacheStats: cacheStats.longTermCache,
        shortTermCacheStats: cacheStats.shortTermCache,
      }));
    } catch (error) {
      console.error('更新性能统计失败:', error);
    }
  };

  const testNetworkSpeed = async () => {
    const startTime = Date.now();
    try {
      const response = await fetch('/api/health', { 
        method: 'GET',
        cache: 'no-cache'
      });
      const endTime = Date.now();
      const loadTime = endTime - startTime;
      
      let networkStatus: 'good' | 'slow' | 'timeout' = 'good';
      if (loadTime > 3000) {
        networkStatus = 'timeout';
      } else if (loadTime > 1000) {
        networkStatus = 'slow';
      }

      setStats(prev => ({
        ...prev,
        lastLoadTime: loadTime,
        networkStatus
      }));
    } catch (error) {
      setStats(prev => ({
        ...prev,
        networkStatus: 'timeout'
      }));
    }
  };

  const clearAllCache = () => {
    OptimizedApiService.clearAllCache();
    updateStats();
  };

  const getNetworkStatusColor = () => {
    switch (stats.networkStatus) {
      case 'good': return 'green';
      case 'slow': return 'orange';
      case 'timeout': return 'red';
      default: return 'default';
    }
  };

  const getNetworkStatusText = () => {
    switch (stats.networkStatus) {
      case 'good': return '良好';
      case 'slow': return '较慢';
      case 'timeout': return '超时';
      default: return '未知';
    }
  };

  if (!isVisible) {
    return (
      <div style={{ position: 'fixed', bottom: 20, right: 20, zIndex: 1000 }}>
        <Button 
          size="small" 
          icon={<ClockCircleOutlined />}
          onClick={() => setIsVisible(true)}
        >
          性能
        </Button>
      </div>
    );
  }

  return (
    <div style={{ position: 'fixed', bottom: 20, right: 20, zIndex: 1000, width: 400 }}>
      <Card 
        title="性能诊断" 
        size="small"
        extra={
          <Button 
            size="small" 
            type="text"
            onClick={() => setIsVisible(false)}
          >
            ×
          </Button>
        }
      >
        <Row gutter={[8, 8]}>
          <Col span={12}>
            <Statistic 
              title="API缓存" 
              value={stats.apiCacheStats.cacheSize}
              suffix="项"
              prefix={<DatabaseOutlined />}
            />
          </Col>
          <Col span={12}>
            <Statistic 
              title="pending请求" 
              value={stats.apiCacheStats.pendingRequests}
              suffix="个"
            />
          </Col>
          <Col span={12}>
            <div>
              <Text type="secondary" style={{ fontSize: 12 }}>网络状态</Text>
              <div>
                <Tag color={getNetworkStatusColor()}>
                  {getNetworkStatusText()}
                </Tag>
              </div>
            </div>
          </Col>
          <Col span={12}>
            <div>
              <Text type="secondary" style={{ fontSize: 12 }}>响应时间</Text>
              <div>
                <Text strong>{stats.lastLoadTime}ms</Text>
              </div>
            </div>
          </Col>
        </Row>

        <Collapse size="small" style={{ marginTop: 8 }}>
          <Panel header="详细统计" key="details">
            <div style={{ fontSize: 12 }}>
              <div><strong>短期缓存:</strong> {stats.shortTermCacheStats.cacheSize} 项</div>
              <div><strong>长期缓存:</strong> {stats.longTermCacheStats.cacheSize} 项</div>
              <div style={{ marginTop: 8 }}>
                <Button 
                  size="small" 
                  icon={<ReloadOutlined />}
                  onClick={testNetworkSpeed}
                  style={{ marginRight: 8 }}
                >
                  测试网络
                </Button>
                <Button 
                  size="small" 
                  onClick={clearAllCache}
                  danger
                >
                  清除缓存
                </Button>
              </div>
            </div>
          </Panel>
        </Collapse>
      </Card>
    </div>
  );
};

export default PerformanceDiagnostic; 