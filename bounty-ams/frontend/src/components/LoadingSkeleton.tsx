import React from 'react';
import { Skeleton, Card, Row, Col } from 'antd';

interface LoadingSkeletonProps {
  type?: 'dashboard' | 'table' | 'card' | 'form' | 'chart';
  rows?: number;
  loading?: boolean;
  children?: React.ReactNode;
}

const LoadingSkeleton: React.FC<LoadingSkeletonProps> = ({
  type = 'card',
  rows = 4,
  loading = true,
  children
}) => {
  if (!loading) {
    return <>{children}</>;
  }

  const renderSkeleton = () => {
    switch (type) {
      case 'dashboard':
        return (
          <div>
            <Row gutter={[16, 16]}>
              <Col span={6}>
                <Card>
                  <Skeleton.Button active size="large" style={{ width: '100%', height: 60 }} />
                  <Skeleton.Button active size="small" style={{ width: '80%', marginTop: 8 }} />
                </Card>
              </Col>
              <Col span={6}>
                <Card>
                  <Skeleton.Button active size="large" style={{ width: '100%', height: 60 }} />
                  <Skeleton.Button active size="small" style={{ width: '80%', marginTop: 8 }} />
                </Card>
              </Col>
              <Col span={6}>
                <Card>
                  <Skeleton.Button active size="large" style={{ width: '100%', height: 60 }} />
                  <Skeleton.Button active size="small" style={{ width: '80%', marginTop: 8 }} />
                </Card>
              </Col>
              <Col span={6}>
                <Card>
                  <Skeleton.Button active size="large" style={{ width: '100%', height: 60 }} />
                  <Skeleton.Button active size="small" style={{ width: '80%', marginTop: 8 }} />
                </Card>
              </Col>
            </Row>
            <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
              <Col span={12}>
                <Card title={<Skeleton.Button active size="small" style={{ width: 120 }} />}>
                  <Skeleton.Button active size="large" style={{ width: '100%', height: 300 }} />
                </Card>
              </Col>
              <Col span={12}>
                <Card title={<Skeleton.Button active size="small" style={{ width: 120 }} />}>
                  <Skeleton active paragraph={{ rows: 8 }} />
                </Card>
              </Col>
            </Row>
          </div>
        );

      case 'table':
        return (
          <Card>
            <div style={{ marginBottom: 16 }}>
              <Skeleton.Button active size="small" style={{ width: 100, marginRight: 8 }} />
              <Skeleton.Button active size="small" style={{ width: 80, marginRight: 8 }} />
              <Skeleton.Button active size="small" style={{ width: 120 }} />
            </div>
            <Skeleton active paragraph={{ rows: rows }} />
          </Card>
        );

      case 'chart':
        return (
          <Card>
            <div style={{ marginBottom: 16 }}>
              <Skeleton.Button active size="small" style={{ width: 150 }} />
            </div>
            <Skeleton.Button active size="large" style={{ width: '100%', height: 300 }} />
          </Card>
        );

      case 'form':
        return (
          <Card>
            <div style={{ maxWidth: 600 }}>
              <Skeleton.Input active size="large" style={{ width: '100%', marginBottom: 16 }} />
              <Skeleton.Input active size="large" style={{ width: '100%', marginBottom: 16 }} />
              <Skeleton.Input active size="large" style={{ width: '100%', marginBottom: 16 }} />
              <Skeleton.Button active size="large" style={{ width: 100, marginTop: 16 }} />
            </div>
          </Card>
        );

      default:
        return (
          <Card>
            <Skeleton active paragraph={{ rows: rows }} />
          </Card>
        );
    }
  };

  return renderSkeleton();
};

export default LoadingSkeleton; 