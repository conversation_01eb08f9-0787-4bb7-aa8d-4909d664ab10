/*
可配置分析组件
为高级搜索、安全分析等页面提供可自定义的聚合和可视化配置
*/

import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Select,
  Input,
  Button,
  Space,
  Row,
  Col,
  Typography,
  Divider,
  Modal,
  List,
  Tag,
  message,
  Switch,
  InputNumber,
  Tooltip
} from 'antd';
import {
  PlusOutlined,
  SettingOutlined,
  DeleteOutlined,
  EyeOutlined,
  SaveOutlined,
  LoadingOutlined
} from '@ant-design/icons';
import { Pie, Column, Line, Area } from '@ant-design/plots';
import { dashboardService } from '../services/api';

const { Option } = Select;
const { TextArea } = Input;
const { Title, Text } = Typography;

interface AggregationConfig {
  id: string;
  name: string;
  description?: string;
  field: string;
  type: 'terms' | 'date_histogram' | 'stats' | 'range';
  size?: number;
  interval?: string;
  ranges?: Array<{ from?: number; to?: number; key: string }>;
  filters?: Record<string, any>;
  visualization: 'pie' | 'column' | 'line' | 'table' | 'statistic';
  chart_config?: {
    show_legend?: boolean;
    show_labels?: boolean;
    color_scheme?: string[];
    height?: number;
  };
}

interface ConfigurableAnalysisProps {
  title: string;
  defaultConfigs?: AggregationConfig[];
  onConfigChange?: (configs: AggregationConfig[]) => void;
  showSaveLoad?: boolean;
}

const ConfigurableAnalysis: React.FC<ConfigurableAnalysisProps> = ({
  title,
  defaultConfigs = [],
  onConfigChange,
  showSaveLoad = true
}) => {
  const [configs, setConfigs] = useState<AggregationConfig[]>(defaultConfigs);
  const [showConfigModal, setShowConfigModal] = useState(false);
  const [editingConfig, setEditingConfig] = useState<AggregationConfig | null>(null);
  const [showSavedConfigs, setShowSavedConfigs] = useState(false);
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState<Map<string, any>>(new Map());
  const [availableFields, setAvailableFields] = useState<any[]>([]);

  useEffect(() => {
    loadAvailableFields();
  }, []);

  useEffect(() => {
    if (onConfigChange) {
      onConfigChange(configs);
    }
  }, [configs, onConfigChange]);

  const loadAvailableFields = async () => {
    try {
      const response = await dashboardService.getAggregatableFields();
      const allFields = [
        ...response.data.fields.categorical,
        ...response.data.fields.temporal,
        ...response.data.fields.textual
      ];
      setAvailableFields(allFields);
    } catch (error) {
      console.error('加载字段列表失败:', error);
    }
  };

  const executeAnalysis = async () => {
    if (configs.length === 0) {
      message.warning('请先添加分析配置');
      return;
    }

    setLoading(true);
    const newResults = new Map();

    try {
      for (const config of configs) {
        const widgetConfig = {
          query_type: 'aggregation',
          aggregation_type: config.type,
          field: config.field,
          size: config.size || 10,
          interval: config.interval,
          time_range: '30d',
          filters: config.filters || {}
        };

        const response = await dashboardService.getWidgetData(widgetConfig);
        newResults.set(config.id, response.data.data);
      }

      setResults(newResults);
      message.success('分析执行完成');
    } catch (error) {
      message.error('分析执行失败');
      console.error('Analysis execution failed:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddConfig = () => {
    setEditingConfig(null);
    form.resetFields();
    setShowConfigModal(true);
  };

  const handleEditConfig = (config: AggregationConfig) => {
    setEditingConfig(config);
    form.setFieldsValue(config);
    setShowConfigModal(true);
  };

  const handleSaveConfig = async (values: any) => {
    const newConfig: AggregationConfig = {
      id: editingConfig?.id || Date.now().toString(),
      name: values.name,
      description: values.description,
      field: values.field,
      type: values.type,
      size: values.size,
      interval: values.interval,
      filters: values.filters ? JSON.parse(values.filters) : {},
      visualization: values.visualization,
      chart_config: {
        show_legend: values.show_legend,
        show_labels: values.show_labels,
        height: values.height || 300
      }
    };

    if (editingConfig) {
      setConfigs(prev => prev.map(c => c.id === editingConfig.id ? newConfig : c));
    } else {
      setConfigs(prev => [...prev, newConfig]);
    }

    setShowConfigModal(false);
    form.resetFields();
    message.success(editingConfig ? '配置已更新' : '配置已添加');
  };

  const handleDeleteConfig = (configId: string) => {
    setConfigs(prev => prev.filter(c => c.id !== configId));
    setResults(prev => {
      const newResults = new Map(prev);
      newResults.delete(configId);
      return newResults;
    });
    message.success('配置已删除');
  };

  const renderVisualization = (config: AggregationConfig, data: any) => {
    if (!data) return <div>暂无数据</div>;

    const { visualization, chart_config = {} } = config;

    switch (visualization) {
      case 'pie':
        const pieData = data.type === 'terms' 
          ? data.data.map((item: any) => ({ type: item.name, value: item.value }))
          : [];
        return (
          <Pie
            data={pieData}
            angleField="value"
            colorField="type"
            radius={0.8}
            label={{ type: 'outer', content: '{name}: {percentage}' }}
            height={chart_config.height || 250}
            legend={chart_config.show_legend !== false ? { position: 'bottom' } : false}
          />
        );

      case 'column':
        const columnData = data.type === 'terms'
          ? data.data.map((item: any) => ({ category: item.name, count: item.value }))
          : [];
        return (
          <Column
            data={columnData}
            xField="category"
            yField="count"
            height={chart_config.height || 250}
            label={chart_config.show_labels ? { position: 'middle' } : false}
          />
        );

      case 'line':
        const lineData = data.type === 'time_series'
          ? data.data.map((item: any) => ({ date: item.date, count: item.value }))
          : [];
        return (
          <Line
            data={lineData}
            xField="date"
            yField="count"
            height={chart_config.height || 250}
            point={{ size: 5, shape: 'diamond' }}
          />
        );

      case 'statistic':
        const statValue = data.type === 'statistics' 
          ? data.data.count 
          : data.total || 0;
        return (
          <div style={{ textAlign: 'center', padding: 40 }}>
            <div style={{ fontSize: 48, fontWeight: 'bold', color: '#1890ff' }}>
              {statValue.toLocaleString()}
            </div>
            <div style={{ color: '#666', marginTop: 8, fontSize: 16 }}>
              {config.name}
            </div>
          </div>
        );

      case 'table':
        const tableData = data.type === 'terms'
          ? data.data.map((item: any, index: number) => ({
              key: index,
              name: item.name,
              value: item.value,
              percentage: ((item.value / data.total) * 100).toFixed(1) + '%'
            }))
          : [];
        return (
          <table style={{ width: '100%', borderCollapse: 'collapse' }}>
            <thead>
              <tr style={{ backgroundColor: '#fafafa' }}>
                <th style={{ padding: 8, border: '1px solid #d9d9d9' }}>名称</th>
                <th style={{ padding: 8, border: '1px solid #d9d9d9' }}>数量</th>
                <th style={{ padding: 8, border: '1px solid #d9d9d9' }}>占比</th>
              </tr>
            </thead>
            <tbody>
              {tableData.map((row: any) => (
                <tr key={row.key}>
                  <td style={{ padding: 8, border: '1px solid #d9d9d9' }}>{row.name}</td>
                  <td style={{ padding: 8, border: '1px solid #d9d9d9' }}>{row.value}</td>
                  <td style={{ padding: 8, border: '1px solid #d9d9d9' }}>{row.percentage}</td>
                </tr>
              ))}
            </tbody>
          </table>
        );

      default:
        return <div>不支持的可视化类型</div>;
    }
  };

  const saveConfigsToLocal = () => {
    const savedConfigs = JSON.parse(localStorage.getItem('analysis_configs') || '[]');
    const timestamp = new Date().toISOString();
    const configSet = {
      id: Date.now().toString(),
      name: `${title}_${timestamp.split('T')[0]}`,
      configs: configs,
      created_at: timestamp
    };
    
    savedConfigs.push(configSet);
    localStorage.setItem('analysis_configs', JSON.stringify(savedConfigs));
    message.success('配置已保存到本地');
  };

  const loadConfigsFromLocal = (configSet: any) => {
    setConfigs(configSet.configs);
    setShowSavedConfigs(false);
    message.success(`已加载配置: ${configSet.name}`);
  };

  return (
    <div>
      {/* 配置管理 */}
      <Card style={{ marginBottom: 16 }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Title level={4} style={{ margin: 0 }}>
              {title} - 可配置分析
            </Title>
            <Text type="secondary">
              {configs.length} 个分析配置
            </Text>
          </Col>
          <Col>
            <Space>
              <Button 
                type="primary" 
                icon={<PlusOutlined />} 
                onClick={handleAddConfig}
              >
                添加分析
              </Button>
              <Button 
                icon={<EyeOutlined />} 
                onClick={executeAnalysis}
                loading={loading}
              >
                执行分析
              </Button>
              {showSaveLoad && (
                <>
                  <Button 
                    icon={<SaveOutlined />} 
                    onClick={saveConfigsToLocal}
                    disabled={configs.length === 0}
                  >
                    保存配置
                  </Button>
                  <Button 
                    icon={<SettingOutlined />} 
                    onClick={() => setShowSavedConfigs(true)}
                  >
                    加载配置
                  </Button>
                </>
              )}
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 配置列表 */}
      {configs.length > 0 && (
        <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
          {configs.map(config => (
            <Col span={24} key={config.id}>
              <Card
                size="small"
                title={config.name}
                extra={
                  <Space>
                    <Tag color="blue">{config.type}</Tag>
                    <Tag color="green">{config.visualization}</Tag>
                    <Button 
                      type="text" 
                      size="small" 
                      icon={<SettingOutlined />}
                      onClick={() => handleEditConfig(config)}
                    />
                    <Button 
                      type="text" 
                      size="small" 
                      danger
                      icon={<DeleteOutlined />}
                      onClick={() => handleDeleteConfig(config.id)}
                    />
                  </Space>
                }
              >
                <Row>
                  <Col span={12}>
                    <Text strong>字段:</Text> {config.field}
                    <br />
                    <Text strong>类型:</Text> {config.type}
                    {config.size && (
                      <>
                        <br />
                        <Text strong>大小:</Text> {config.size}
                      </>
                    )}
                  </Col>
                  <Col span={12}>
                    {results.has(config.id) && (
                      <div style={{ minHeight: 200 }}>
                        {renderVisualization(config, results.get(config.id))}
                      </div>
                    )}
                  </Col>
                </Row>
              </Card>
            </Col>
          ))}
        </Row>
      )}

      {/* 配置编辑模态框 */}
      <Modal
        title={editingConfig ? "编辑分析配置" : "添加分析配置"}
        open={showConfigModal}
        onCancel={() => {
          setShowConfigModal(false);
          form.resetFields();
        }}
        footer={null}
        width={800}
      >
        <Form form={form} onFinish={handleSaveConfig} layout="vertical">
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="配置名称"
                rules={[{ required: true, message: '请输入配置名称' }]}
              >
                <Input placeholder="输入分析配置名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="description" label="描述">
                <Input placeholder="可选描述" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="field"
                label="聚合字段"
                rules={[{ required: true, message: '请选择聚合字段' }]}
              >
                <Select placeholder="选择要聚合的字段" showSearch>
                  {availableFields.map(field => (
                    <Option key={field.field} value={field.field}>
                      {field.display_name} ({field.field})
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="type"
                label="聚合类型"
                rules={[{ required: true, message: '请选择聚合类型' }]}
              >
                <Select placeholder="选择聚合类型">
                  <Option value="terms">分组统计</Option>
                  <Option value="date_histogram">时间分布</Option>
                  <Option value="stats">统计信息</Option>
                  <Option value="range">范围分组</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item name="size" label="结果数量" initialValue={10}>
                <InputNumber min={1} max={100} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="visualization" label="可视化类型" initialValue="column">
                <Select>
                  <Option value="pie">饼图</Option>
                  <Option value="column">柱状图</Option>
                  <Option value="line">折线图</Option>
                  <Option value="table">表格</Option>
                  <Option value="statistic">数值统计</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="height" label="图表高度" initialValue={300}>
                <InputNumber min={200} max={600} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) => prevValues.type !== currentValues.type}
          >
            {({ getFieldValue }) =>
              getFieldValue('type') === 'date_histogram' && (
                <Form.Item name="interval" label="时间间隔">
                  <Select placeholder="选择时间间隔">
                    <Option value="hour">小时</Option>
                    <Option value="day">天</Option>
                    <Option value="week">周</Option>
                    <Option value="month">月</Option>
                  </Select>
                </Form.Item>
              )
            }
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="show_legend" valuePropName="checked" initialValue={true}>
                <Space>
                  <Switch size="small" />
                  <span>显示图例</span>
                </Space>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="show_labels" valuePropName="checked" initialValue={false}>
                <Space>
                  <Switch size="small" />
                  <span>显示标签</span>
                </Space>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="filters" label="过滤条件">
            <TextArea 
              placeholder='{"platform_id": "hackerone", "confidence": "high"}'
              rows={3}
            />
          </Form.Item>

          <Divider />

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                {editingConfig ? '更新配置' : '添加配置'}
              </Button>
              <Button onClick={() => setShowConfigModal(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 已保存配置列表 */}
      <Modal
        title="加载已保存的配置"
        open={showSavedConfigs}
        onCancel={() => setShowSavedConfigs(false)}
        footer={null}
        width={600}
      >
        <List
          dataSource={JSON.parse(localStorage.getItem('analysis_configs') || '[]')}
          locale={{ emptyText: '暂无已保存的配置' }}
          renderItem={(item: any) => (
            <List.Item
              actions={[
                <Button
                  key="load"
                  type="primary"
                  size="small"
                  onClick={() => loadConfigsFromLocal(item)}
                >
                  加载
                </Button>,
                <Button
                  key="delete"
                  danger
                  size="small"
                  onClick={() => {
                    const saved = JSON.parse(localStorage.getItem('analysis_configs') || '[]');
                    const filtered = saved.filter((config: any) => config.id !== item.id);
                    localStorage.setItem('analysis_configs', JSON.stringify(filtered));
                    setShowSavedConfigs(false);
                    message.success('配置已删除');
                  }}
                >
                  删除
                </Button>
              ]}
            >
              <List.Item.Meta
                title={item.name}
                description={`${item.configs?.length || 0} 个分析配置 - 创建于 ${new Date(item.created_at).toLocaleDateString()}`}
              />
            </List.Item>
          )}
        />
      </Modal>
    </div>
  );
};

export default ConfigurableAnalysis;