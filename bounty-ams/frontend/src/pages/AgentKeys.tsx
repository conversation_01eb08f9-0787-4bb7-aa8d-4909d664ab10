import React, { useState, useEffect } from 'react';
import {
  Row,
  Col,
  Card,
  Table,
  Button,
  Space,
  Tag,
  Typography,
  Modal,
  Form,
  Input,
  DatePicker,
  Select,
  message,
  Popconfirm,
  Statistic,
  Alert,
  Tooltip,
  Badge,
} from 'antd';
import {
  PlusOutlined,
  KeyOutlined,
  DeleteOutlined,
  StopOutlined,
  PlayCircleOutlined,
  ExclamationCircleOutlined,
  CopyOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  ReloadOutlined,
} from '@ant-design/icons';
import { agentKeyService } from '../services/api';
import dayjs from 'dayjs';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;
const { TextArea } = Input;

interface AgentKey {
  id: string;
  key_id: string;
  agent_id?: string;
  name: string;
  description?: string;
  status: 'active' | 'suspended' | 'revoked' | 'expired';
  expires_at?: string;
  created_at: string;
  updated_at?: string;
  last_used_at?: string;
  usage_count: number;
  created_by_user_id?: string;
}

interface AgentKeyStats {
  total_keys: number;
  active_keys: number;
  suspended_keys: number;
  revoked_keys: number;
  expired_keys: number;
  soon_expire_keys: number;
  recently_used_keys: number;
}

const AgentKeys: React.FC = () => {
  const [keys, setKeys] = useState<AgentKey[]>([]);
  const [stats, setStats] = useState<AgentKeyStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [keyDetailModalVisible, setKeyDetailModalVisible] = useState(false);
  const [selectedKey, setSelectedKey] = useState<AgentKey | null>(null);
  const [newKeyValue, setNewKeyValue] = useState<string>('');
  const [newKeyVisible, setNewKeyVisible] = useState(false);
  const [form] = Form.useForm();

  useEffect(() => {
    loadKeys();
    loadStats();
  }, []);

  const loadKeys = async () => {
    try {
      setLoading(true);
      const response = await agentKeyService.getKeys();
      setKeys(response.data);
    } catch (error) {
      console.error('Load keys error:', error);
      message.error('加载密钥列表失败');
    } finally {
      setLoading(false);
    }
  };

  const loadStats = async () => {
    try {
      const response = await agentKeyService.getStats();
      setStats(response.data);
    } catch (error) {
      console.error('Load stats error:', error);
    }
  };

  const handleCreateKey = async (values: any) => {
    try {
      const response = await agentKeyService.createKey(values);
      setNewKeyValue(response.data.key_value);
      setNewKeyVisible(true);
      
      message.success('密钥创建成功');
      setCreateModalVisible(false);
      form.resetFields();
      loadKeys();
      loadStats();
    } catch (error: any) {
      console.error('Create key error:', error);
      message.error(`创建密钥失败: ${error.response?.data?.detail || '未知错误'}`);
    }
  };

  const handleRevokeKey = async (keyId: string) => {
    try {
      await agentKeyService.revokeKey(keyId);
      message.success('密钥已撤销');
      loadKeys();
      loadStats();
    } catch (error: any) {
      console.error('Revoke key error:', error);
      message.error(`撤销密钥失败: ${error.response?.data?.detail || '未知错误'}`);
    }
  };

  const handleDeleteKey = async (keyId: string) => {
    try {
      await agentKeyService.deleteKey(keyId);
      message.success('密钥已删除');
      loadKeys();
      loadStats();
    } catch (error: any) {
      console.error('Delete key error:', error);
      message.error(`删除密钥失败: ${error.response?.data?.detail || '未知错误'}`);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    message.success('已复制到剪贴板');
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'success';
      case 'suspended':
        return 'warning';
      case 'revoked':
        return 'error';
      case 'expired':
        return 'default';
      default:
        return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return '活跃';
      case 'suspended':
        return '暂停';
      case 'revoked':
        return '已撤销';
      case 'expired':
        return '已过期';
      default:
        return status;
    }
  };

  const columns = [
    {
      title: '密钥ID',
      dataIndex: 'key_id',
      key: 'key_id',
      render: (keyId: string) => (
        <Space>
          <Text code>{keyId}</Text>
          <Button
            type="text"
            size="small"
            icon={<CopyOutlined />}
            onClick={() => copyToClipboard(keyId)}
          />
        </Space>
      ),
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Badge
          status={getStatusColor(status) as any}
          text={getStatusText(status)}
        />
      ),
    },
    {
      title: '绑定Agent',
      dataIndex: 'agent_id',
      key: 'agent_id',
      render: (agentId?: string) => (
        agentId ? <Text code>{agentId}</Text> : <Text type="secondary">未绑定</Text>
      ),
    },
    {
      title: '使用次数',
      dataIndex: 'usage_count',
      key: 'usage_count',
    },
    {
      title: '最后使用',
      dataIndex: 'last_used_at',
      key: 'last_used_at',
      render: (date?: string) => (
        date ? (
          <Text type="secondary">
            {dayjs(date).format('YYYY-MM-DD HH:mm')}
          </Text>
        ) : (
          <Text type="secondary">从未使用</Text>
        )
      ),
    },
    {
      title: '过期时间',
      dataIndex: 'expires_at',
      key: 'expires_at',
      render: (date?: string) => {
        if (!date) {
          return <Text type="secondary">永不过期</Text>;
        }
        
        const expireDate = dayjs(date);
        const now = dayjs();
        const isExpired = expireDate.isBefore(now);
        const isSoonExpire = expireDate.diff(now, 'days') <= 7;
        
        return (
          <Text type={isExpired ? 'danger' : isSoonExpire ? 'warning' : 'secondary'}>
            {expireDate.format('YYYY-MM-DD HH:mm')}
          </Text>
        );
      },
    },
    {
      title: '操作',
      key: 'action',
      render: (record: AgentKey) => (
        <Space>
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => {
              setSelectedKey(record);
              setKeyDetailModalVisible(true);
            }}
          >
            详情
          </Button>
          {record.status === 'active' && (
            <Popconfirm
              title="撤销密钥"
              description="确定要撤销此密钥吗？撤销后无法恢复。"
              onConfirm={() => handleRevokeKey(record.key_id)}
              okText="确定"
              cancelText="取消"
              okType="danger"
            >
              <Button
                type="text"
                danger
                icon={<StopOutlined />}
              >
                撤销
              </Button>
            </Popconfirm>
          )}
          {['revoked', 'expired'].includes(record.status) && (
            <Popconfirm
              title="删除密钥"
              description="确定要删除此密钥吗？此操作不可恢复。"
              onConfirm={() => handleDeleteKey(record.key_id)}
              okText="确定"
              cancelText="取消"
              okType="danger"
            >
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
              >
                删除
              </Button>
            </Popconfirm>
          )}
        </Space>
      ),
    },
  ];

  return (
    <div>
      {/* 统计卡片 */}
      {stats && (
        <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
          <Col span={6}>
            <Card>
              <Statistic
                title="总密钥数"
                value={stats.total_keys}
                prefix={<KeyOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="活跃密钥"
                value={stats.active_keys}
                valueStyle={{ color: '#3f8600' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="即将过期"
                value={stats.soon_expire_keys}
                valueStyle={{ color: stats.soon_expire_keys > 0 ? '#cf1322' : undefined }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="最近使用"
                value={stats.recently_used_keys}
                suffix="/ 24h"
              />
            </Card>
          </Col>
        </Row>
      )}

      {/* 警告信息 */}
      {stats && stats.soon_expire_keys > 0 && (
        <Alert
          message="密钥即将过期"
          description={`有 ${stats.soon_expire_keys} 个密钥将在7天内过期，请及时处理。`}
          type="warning"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}

      {/* 主要操作区域 */}
      <Row gutter={[16, 16]}>
        <Col span={24}>
          <Space align="center" style={{ marginBottom: 16 }}>
            <Title level={3} style={{ margin: 0 }}>
              Agent密钥管理
            </Title>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setCreateModalVisible(true)}
            >
              创建密钥
            </Button>
            <Button
              icon={<ReloadOutlined />}
              onClick={() => {
                loadKeys();
                loadStats();
              }}
              loading={loading}
            >
              刷新
            </Button>
          </Space>
        </Col>
      </Row>

      {/* 密钥列表 */}
      <Card>
        <Table
          columns={columns}
          dataSource={keys}
          loading={loading}
          rowKey="id"
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
        />
      </Card>

      {/* 创建密钥模态框 */}
      <Modal
        title="创建Agent密钥"
        open={createModalVisible}
        onOk={() => form.submit()}
        onCancel={() => {
          setCreateModalVisible(false);
          form.resetFields();
        }}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleCreateKey}
        >
          <Form.Item
            name="name"
            label="密钥名称"
            rules={[{ required: true, message: '请输入密钥名称' }]}
          >
            <Input placeholder="请输入密钥名称" />
          </Form.Item>
          
          <Form.Item
            name="description"
            label="描述"
          >
            <TextArea
              placeholder="请输入密钥描述（可选）"
              rows={3}
            />
          </Form.Item>
          
          <Form.Item
            name="expires_at"
            label="过期时间"
          >
            <DatePicker
              showTime
              placeholder="选择过期时间（可选，不选择则永不过期）"
              style={{ width: '100%' }}
            />
          </Form.Item>
        </Form>
      </Modal>

      {/* 新密钥显示模态框 */}
      <Modal
        title="密钥创建成功"
        open={newKeyVisible}
        onOk={() => setNewKeyVisible(false)}
        onCancel={() => setNewKeyVisible(false)}
        footer={[
          <Button
            key="copy"
            type="primary"
            icon={<CopyOutlined />}
            onClick={() => copyToClipboard(newKeyValue)}
          >
            复制密钥
          </Button>,
          <Button key="close" onClick={() => setNewKeyVisible(false)}>
            关闭
          </Button>,
        ]}
      >
        <Alert
          message="请立即保存密钥"
          description="出于安全考虑，密钥值只会显示一次。请立即复制并妥善保管。"
          type="warning"
          showIcon
          style={{ marginBottom: 16 }}
        />
        <Paragraph>
          <Text strong>密钥值：</Text>
        </Paragraph>
        <Paragraph>
          <Text code copyable>{newKeyValue}</Text>
        </Paragraph>
      </Modal>

      {/* 密钥详情模态框 */}
      <Modal
        title="密钥详情"
        open={keyDetailModalVisible}
        onCancel={() => setKeyDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setKeyDetailModalVisible(false)}>
            关闭
          </Button>,
        ]}
        width={600}
      >
        {selectedKey && (
          <div>
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <Text strong>密钥ID：</Text>
                <br />
                <Text code>{selectedKey.key_id}</Text>
              </Col>
              <Col span={12}>
                <Text strong>名称：</Text>
                <br />
                <Text>{selectedKey.name}</Text>
              </Col>
              <Col span={12}>
                <Text strong>状态：</Text>
                <br />
                <Badge
                  status={getStatusColor(selectedKey.status) as any}
                  text={getStatusText(selectedKey.status)}
                />
              </Col>
              <Col span={12}>
                <Text strong>绑定Agent：</Text>
                <br />
                <Text>{selectedKey.agent_id || '未绑定'}</Text>
              </Col>
              <Col span={12}>
                <Text strong>使用次数：</Text>
                <br />
                <Text>{selectedKey.usage_count}</Text>
              </Col>
              <Col span={12}>
                <Text strong>创建时间：</Text>
                <br />
                <Text>{dayjs(selectedKey.created_at).format('YYYY-MM-DD HH:mm:ss')}</Text>
              </Col>
              {selectedKey.last_used_at && (
                <Col span={12}>
                  <Text strong>最后使用：</Text>
                  <br />
                  <Text>{dayjs(selectedKey.last_used_at).format('YYYY-MM-DD HH:mm:ss')}</Text>
                </Col>
              )}
              {selectedKey.expires_at && (
                <Col span={12}>
                  <Text strong>过期时间：</Text>
                  <br />
                  <Text>{dayjs(selectedKey.expires_at).format('YYYY-MM-DD HH:mm:ss')}</Text>
                </Col>
              )}
              {selectedKey.description && (
                <Col span={24}>
                  <Text strong>描述：</Text>
                  <br />
                  <Text>{selectedKey.description}</Text>
                </Col>
              )}
            </Row>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default AgentKeys;