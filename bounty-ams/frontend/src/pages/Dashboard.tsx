import React, { useState, useEffect } from 'react';
import {
  Row,
  Col,
  Card,
  Statistic,
  Tag,
  Typography,
  Space,
  Button,
  Progress,
  List,
  Avatar,
  Spin,
} from 'antd';
import {
  DatabaseOutlined,
  SecurityScanOutlined,
  RobotOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  SyncOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';
import ReactECharts from 'echarts-for-react';
import { assetService, agentService, taskService } from '../services/api';
import { OptimizedApiService } from '../services/optimizedApi';
import LoadingSkeleton from '../components/LoadingSkeleton';

const { Title, Text } = Typography;

const Dashboard: React.FC = () => {
  const [assetStats, setAssetStats] = useState<any>({});
  const [agentStats, setAgentStats] = useState<any>({});
  const [taskStats, setTaskStats] = useState<any>({});
  const [recentAssets, setRecentAssets] = useState<any[]>([]);
  const [recentTasks, setRecentTasks] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      console.log('开始加载仪表板数据...');
      
      // 添加超时控制
      const timeoutPromise = new Promise((_, reject) => 
        setTimeout(() => reject(new Error('仪表板数据加载超时')), 4000)
      );
      
      try {
        // 首先尝试使用优化的批量加载API
        const dashboardData = await Promise.race([
          OptimizedApiService.loadDashboardData(),
          timeoutPromise
        ]) as any;
        
        console.log('批量加载成功:', dashboardData);
        setAssetStats(dashboardData.assetStats || {});
        setAgentStats(dashboardData.agentStats || {});
        setTaskStats(dashboardData.taskStats || {});
        setRecentAssets(dashboardData.recentAssets || []);
        setRecentTasks(dashboardData.recentTasks || []);
        
      } catch (batchError) {
        console.warn('批量加载失败，回退到单独加载:', batchError);
        
        // 如果批量加载失败，回退到快速单独加载
        const quickTimeoutPromise = new Promise((_, reject) => 
          setTimeout(() => reject(new Error('单独加载超时')), 2000)
        );
        
        const [assetResponse, agentResponse, taskResponse] = await Promise.allSettled([
          Promise.race([assetService.getAssetStats(), quickTimeoutPromise]).catch(() => ({ data: {} })),
          Promise.race([agentService.getAgents(), quickTimeoutPromise]).catch(() => ({ data: { agents: [] } })),
          Promise.race([taskService.getTaskStats(), quickTimeoutPromise]).catch(() => ({ data: {} }))
        ]);
        
        console.log('单独加载结果:', { assetResponse, agentResponse, taskResponse });
        
        if (assetResponse.status === 'fulfilled') {
          setAssetStats((assetResponse.value as any).data || {});
        } else {
          setAssetStats({});
        }
        
        if (agentResponse.status === 'fulfilled') {
          const agents = (agentResponse.value as any).data?.agents || [];
          setAgentStats({
            total: agents.length,
            active: agents.filter((a: any) => a.status === 'active').length,
            idle: agents.filter((a: any) => a.status === 'idle').length,
            offline: agents.filter((a: any) => a.status === 'offline').length,
          });
        } else {
          setAgentStats({ total: 0, active: 0, idle: 0, offline: 0 });
        }
        
        if (taskResponse.status === 'fulfilled') {
          setTaskStats((taskResponse.value as any).data || {});
        } else {
          setTaskStats({});
        }
        
        // 设置空的recent数据
        setRecentAssets([]);
        setRecentTasks([]);
      }
      
    } catch (error) {
      console.error('仪表板数据加载完全失败:', error);
      // 设置默认空数据，确保页面能正常显示
      setAssetStats({});
      setAgentStats({ total: 0, active: 0, idle: 0, offline: 0 });
      setTaskStats({});
      setRecentAssets([]);
      setRecentTasks([]);
    } finally {
      setLoading(false);
      console.log('仪表板数据加载完成');
    }
  };

  const getAssetTypeChartOption = () => {
    const data = Object.entries(assetStats.by_type || {}).map(([key, value]) => ({
      name: key,
      value: value,
    }));

    return {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left',
      },
      series: [
        {
          name: '资产类型',
          type: 'pie',
          radius: ['40%', '70%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 10,
            borderColor: '#fff',
            borderWidth: 2
          },
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: 16,
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          data: data
        }
      ]
    };
  };

  const getTaskTrendChartOption = () => {
    const dates = Object.keys(assetStats.by_date || {}).sort();
    const data = dates.map(date => assetStats.by_date[date]);

    return {
      tooltip: {
        trigger: 'axis'
      },
      xAxis: {
        type: 'category',
        data: dates
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: '发现资产',
          type: 'line',
          smooth: true,
          itemStyle: {
            color: '#1890ff'
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: 'rgba(24, 144, 255, 0.3)' },
                { offset: 1, color: 'rgba(24, 144, 255, 0.1)' }
              ]
            }
          },
          data: data
        }
      ]
    };
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'running':
        return <SyncOutlined spin style={{ color: '#1890ff' }} />;
      case 'pending':
        return <ClockCircleOutlined style={{ color: '#faad14' }} />;
      case 'failed':
        return <ExclamationCircleOutlined style={{ color: '#f5222d' }} />;
      default:
        return <ClockCircleOutlined style={{ color: '#d9d9d9' }} />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'running':
        return 'processing';
      case 'pending':
        return 'warning';
      case 'failed':
        return 'error';
      default:
        return 'default';
    }
  };

  if (loading) {
    return (
      <div style={{ padding: 24 }}>
        <LoadingSkeleton type="dashboard" loading={loading} />
      </div>
    );
  }

  return (
    <div>
      <Row gutter={[16, 16]}>
        <Col span={24}>
          <Space align="center" style={{ marginBottom: 16 }}>
            <Title level={3} style={{ margin: 0 }}>
              系统概览
            </Title>
            <Button 
              icon={<SyncOutlined />} 
              onClick={loadDashboardData}
              loading={loading}
            >
              刷新
            </Button>
          </Space>
        </Col>
      </Row>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="总资产数量"
              value={assetStats.total_assets || 0}
              prefix={<DatabaseOutlined />}
              suffix={<ArrowUpOutlined style={{ color: '#3f8600' }} />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="活跃Agent"
              value={agentStats.active || 0}
              prefix={<RobotOutlined />}
              suffix={`/${agentStats.total || 0}`}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="运行中任务"
              value={taskStats.running || 0}
              prefix={<SecurityScanOutlined />}
              suffix={<SyncOutlined spin />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="完成任务"
              value={taskStats.completed || 0}
              prefix={<CheckCircleOutlined />}
              suffix={<ArrowUpOutlined style={{ color: '#3f8600' }} />}
            />
          </Card>
        </Col>
      </Row>

      {/* 图表区域 */}
      <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
        <Col xs={24} lg={12}>
          <Card title="资产类型分布">
            <ReactECharts 
              option={getAssetTypeChartOption()} 
              style={{ height: 300 }}
            />
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card title="资产发现趋势">
            <ReactECharts 
              option={getTaskTrendChartOption()} 
              style={{ height: 300 }}
            />
          </Card>
        </Col>
      </Row>

      {/* 最近活动 */}
      <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
        <Col xs={24} lg={12}>
          <Card title="最近发现的资产" extra={<Button type="link">查看更多</Button>}>
            <List
              dataSource={recentAssets.slice(0, 5)}
              renderItem={(item) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={<Avatar icon={<DatabaseOutlined />} />}
                    title={
                      <Space>
                        <Text strong>{item.asset_value}</Text>
                        <Tag color="blue">{item.asset_type}</Tag>
                      </Space>
                    }
                    description={
                      <Space>
                        <Text type="secondary">
                          发现时间: {new Date(item.discovered_at).toLocaleString()}
                        </Text>
                        <Tag color="green">置信度: {item.confidence}</Tag>
                      </Space>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card title="最近任务" extra={<Button type="link">查看更多</Button>}>
            <List
              dataSource={recentTasks.slice(0, 5)}
              renderItem={(item) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={<Avatar icon={getStatusIcon(item.status)} />}
                    title={
                      <Space>
                        <Text strong>{item.task_id}</Text>
                        <Tag color={getStatusColor(item.status)}>{item.status}</Tag>
                      </Space>
                    }
                    description={
                      <Space>
                        <Text type="secondary">
                          类型: {item.task_type}
                        </Text>
                        <Text type="secondary">
                          目标: {item.target}
                        </Text>
                        <Text type="secondary">
                          创建时间: {new Date(item.created_at).toLocaleString()}
                        </Text>
                      </Space>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>
      </Row>

      {/* Agent状态 */}
      <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
        <Col span={24}>
          <Card title="Agent状态">
            <Row gutter={[16, 16]}>
              <Col xs={24} sm={8}>
                <div style={{ textAlign: 'center' }}>
                  <Progress 
                    type="circle" 
                    percent={agentStats.total > 0 ? Math.round((agentStats.active / agentStats.total) * 100) : 0}
                    format={(percent) => `${agentStats.active}/${agentStats.total}`}
                    strokeColor="#52c41a"
                  />
                  <div style={{ marginTop: 8 }}>
                    <Text>活跃Agent</Text>
                  </div>
                </div>
              </Col>
              <Col xs={24} sm={8}>
                <div style={{ textAlign: 'center' }}>
                  <Progress 
                    type="circle" 
                    percent={agentStats.total > 0 ? Math.round((agentStats.idle / agentStats.total) * 100) : 0}
                    format={(percent) => `${agentStats.idle}/${agentStats.total}`}
                    strokeColor="#faad14"
                  />
                  <div style={{ marginTop: 8 }}>
                    <Text>空闲Agent</Text>
                  </div>
                </div>
              </Col>
              <Col xs={24} sm={8}>
                <div style={{ textAlign: 'center' }}>
                  <Progress 
                    type="circle" 
                    percent={agentStats.total > 0 ? Math.round((agentStats.offline / agentStats.total) * 100) : 0}
                    format={(percent) => `${agentStats.offline}/${agentStats.total}`}
                    strokeColor="#f5222d"
                  />
                  <div style={{ marginTop: 8 }}>
                    <Text>离线Agent</Text>
                  </div>
                </div>
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Dashboard;