import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Form,
  Input,
  Select,
  Row,
  Col,
  Tag,
  message,
  Upload,
  Modal,
  Typography,
  Statistic,
  Alert,
  DatePicker,
  Drawer,
  Descriptions,
  Checkbox,
  Popover
} from 'antd';
import {
  SearchOutlined,
  UploadOutlined,
  ReloadOutlined,
  BarChartOutlined,
  ClearOutlined,
  EyeOutlined,
  ExportOutlined,
  ApiOutlined,
  GlobalOutlined,
  SettingOutlined
} from '@ant-design/icons';
import { assetsV2Service, assetDataTransformer } from '../services/assetsV2';
import type { AssetSearchParams } from '../services/assetsV2';
import { assetService, api } from '../services/api';
import { useLocation } from 'react-router-dom';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { Option } = Select;
const { RangePicker } = DatePicker;

const AssetsV2Test: React.FC = () => {
  // 状态管理
  const [assets, setAssets] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [stats, setStats] = useState<any>(null);
  const [platforms, setPlatforms] = useState<any[]>([]);
  const [projects, setProjects] = useState<any[]>([]);
  const [searchForm] = Form.useForm();
  const [importModalVisible, setImportModalVisible] = useState(false);
  const [importForm] = Form.useForm();
  const [fileList, setFileList] = useState<any[]>([]);

  // 分页状态
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [total, setTotal] = useState(0);

  // 新增状态变量
  const [selectedAsset, setSelectedAsset] = useState<any>(null);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [assetTypes, setAssetTypes] = useState<any[]>([]);
  const [assetSources, setAssetSources] = useState<any[]>([]);
  const [aggregations, setAggregations] = useState<any>(null);
  const location = useLocation();

  // 获取模型类型ID
  const [platformModelId, setPlatformModelId] = useState<string>('');
  const [projectModelId, setProjectModelId] = useState<string>('');

  // 筛选状态
  const [filters, setFilters] = useState({
    q: '',
    asset_type: '',
    source: '',
    confidence: '',
    status: '',
    date_from: '',
    date_to: '',
    platform_id: '',
    project_id: '',
  });

  // 可选择的列配置
  const [visibleColumns, setVisibleColumns] = useState<string[]>([
    'platform_project', 'asset_type', 'asset_value', 'status', 'confidence', 'action'
  ]);
  
  // 加载初始数据
  useEffect(() => {
    loadModelTypes();
  }, []);

  useEffect(() => {
    // 处理URL参数 - 只在模型ID加载完成后处理
    if (!platformModelId || !projectModelId) return;

    const searchParams = new URLSearchParams(location.search);
    const platformId = searchParams.get('platform_id');
    const projectId = searchParams.get('project_id');

    if (platformId || projectId) {
      const urlFilters = {
        ...filters,
        platform_id: platformId || '',
        project_id: projectId || '',
      };
      setFilters(urlFilters);
      setCurrentPage(1);

      // 设置表单值
      searchForm.setFieldsValue({
        platform_id: platformId || undefined,
        project_id: projectId || undefined,
      });

      // 如果有平台筛选，加载该平台的项目
      if (platformId && projectModelId) {
        assetService.getDynamicEntities(projectModelId, {
          platform_id: platformId
        }).then(response => {
          setProjects(response.data || []);
        }).catch(error => {
          console.error('加载项目列表失败:', error);
        });
      }
    } else {
      // 没有URL参数时，清除平台和项目筛选
      if (filters.platform_id || filters.project_id) {
        setFilters(prev => ({ ...prev, platform_id: '', project_id: '' }));
        searchForm.setFieldsValue({
          platform_id: undefined,
          project_id: undefined,
        });
      }
    }
  }, [location.search, platformModelId, projectModelId]);

  useEffect(() => {
    if (platformModelId) {
      loadPlatforms();
    }
    if (projectModelId) {
      loadProjects();
    }
  }, [platformModelId, projectModelId]);

  useEffect(() => {
    const init = async () => {
      const esStatus = await checkElasticsearchStatus();
      if (esStatus) {
        loadAssets();
        loadStats();
        loadAssetTypes();
        loadAssetSources();
      }
    };

    init();
  }, [currentPage, pageSize, filters]);

  const loadModelTypes = async () => {
    try {
      const response = await assetService.getDynamicModelTypes();
      const types = response.data;

      const platformType = types.find((t: any) => t.name === 'platform');
      const projectType = types.find((t: any) => t.name === 'project');

      if (platformType) setPlatformModelId(platformType.id);
      if (projectType) setProjectModelId(projectType.id);
    } catch (error) {
      console.error('加载模型类型失败:', error);
      message.error('加载模型类型失败');
    }
  };

  const loadPlatforms = async () => {
    try {
      const response = await assetService.getDynamicEntities(platformModelId);
      setPlatforms(response.data || []);
    } catch (error) {
      console.error('加载平台失败:', error);
    }
  };

  const loadProjects = async () => {
    try {
      const response = await assetService.getDynamicEntities(projectModelId);
      setProjects(response.data || []);
    } catch (error) {
      console.error('加载项目失败:', error);
    }
  };

  const loadAssetTypes = async () => {
    try {
      const response = await assetService.getAssetTypes();
      setAssetTypes(response.data.types || []);
    } catch (error) {
      console.error('加载资产类型失败:', error);
    }
  };

  const loadAssetSources = async () => {
    try {
      const response = await assetService.getAssetSources();
      setAssetSources(response.data.sources || []);
    } catch (error) {
      console.error('加载资产来源失败:', error);
    }
  };

  // 移除useCallback，直接定义函数避免循环依赖
  const loadProjectsByPlatform = async (platformId: string) => {
    try {
      const response = await assetService.getDynamicEntities(projectModelId, {
        platform_id: platformId
      });
      setProjects(response.data || []);
    } catch (error) {
      console.error('加载项目列表失败:', error);
    }
  };

  const loadAssets = async (searchParams?: AssetSearchParams) => {
    try {
      setLoading(true);

      // 构建筛选参数，优先使用传入的searchParams，否则使用当前filters
      const currentFilters = searchParams || {
        q: filters.q || '*',
        platform_id: filters.platform_id || undefined,
        project_id: filters.project_id || undefined,
        asset_type: filters.asset_type || undefined,
        confidence: filters.confidence || undefined,
        status: filters.status || undefined,
        page: currentPage,
        size: pageSize,
        include_aggs: true
      };

      console.log('搜索参数:', currentFilters);

      const response = await assetsV2Service.searchAssets(currentFilters);
      console.log('API响应:', response);

      const result = response.data;

      // 检查返回的数据结构
      if (!result) {
        console.error('API返回空数据');
        setAssets([]);
        setTotal(0);
        message.info('暂无数据');
        return;
      }

      // 处理hits数据
      const hits = result.hits || [];
      const total = result.total || 0;

      // 转换数据格式
      const transformedAssets = assetDataTransformer.transformAssetsForDisplay(hits);

      console.log('转换后的资产数据:', transformedAssets);

      setAssets(transformedAssets);
      setTotal(total);

      // 设置聚合数据
      if (result.aggregations) {
        const processedAggs: any = {
          asset_types: {},
          confidence: {},
          platforms: {},
          projects: {}
        };

        // 处理聚合数据
        if (result.aggregations.asset_types?.buckets) {
          result.aggregations.asset_types.buckets.forEach((bucket: any) => {
            processedAggs.asset_types[bucket.key] = bucket.doc_count;
          });
        }

        if (result.aggregations.confidence_levels?.buckets) {
          result.aggregations.confidence_levels.buckets.forEach((bucket: any) => {
            processedAggs.confidence[bucket.key] = bucket.doc_count;
          });
        }

        setAggregations(processedAggs);
      }

      // 显示结果消息
      if (total > 0) {
        console.log(`成功加载 ${transformedAssets.length} / ${total} 个资产`);
      } else {
        message.info('没有找到匹配的资产数据');
      }
    } catch (error: any) {
      console.error('加载资产失败:', error);

      // 更详细的错误处理
      if (error.response?.status === 401) {
        message.error('认证失败，请重新登录');
      } else if (error.response?.status === 403) {
        message.error('权限不足');
      } else if (error.response?.status >= 500) {
        message.error('服务器错误，请稍后重试');
      } else {
        message.error(`加载资产失败: ${error.response?.data?.detail || error.message}`);
      }

      setAssets([]);
      setTotal(0);
    } finally {
      setLoading(false);
    }
  };

  const loadStats = async () => {
    try {
      const response = await assetsV2Service.getAssetStats();
      setStats(response.data);
    } catch (error: any) {
      console.error('加载统计失败:', error);
      // 不显示错误消息，因为可能是初次运行没有数据
    }
  };

  const handlePlatformChange = (platformId: string) => {
    setFilters(prev => ({ ...prev, platform_id: platformId, project_id: '' }));
    searchForm.setFieldsValue({ project_id: undefined });
    if (platformId) {
      loadProjectsByPlatform(platformId);
    } else {
      loadProjects();
    }
  };

  const handleSearch = (values: any) => {
    const newFilters = {
      ...filters,
      ...values,
      date_from: values.dateRange?.[0]?.format('YYYY-MM-DD') || '',
      date_to: values.dateRange?.[1]?.format('YYYY-MM-DD') || '',
    };
    delete newFilters.dateRange;
    setFilters(newFilters);
    setCurrentPage(1);
  };

  const handleReset = () => {
    const resetFilters = {
      q: '',
      asset_type: '',
      source: '',
      confidence: '',
      status: '',
      date_from: '',
      date_to: '',
      platform_id: '',
      project_id: '',
    };
    setFilters(resetFilters);
    searchForm.resetFields();
    setCurrentPage(1);
    loadProjects(); // 重置项目列表
  };

  const handleViewDetails = (asset: any) => {
    setSelectedAsset(asset);
    setDrawerVisible(true);
  };

  const handleExport = async () => {
    try {
      message.info('正在导出资产数据...');

      const searchParams = {
        q: filters.q || '*',
        platform_id: filters.platform_id || undefined,
        project_id: filters.project_id || undefined,
        asset_type: filters.asset_type || undefined,
        confidence: filters.confidence || undefined,
        status: filters.status || undefined,
        page: 1,
        size: 10000, // 导出更多数据
        include_aggs: false
      };

      const response = await assetsV2Service.searchAssets(searchParams);
      const result = response.data;

      if (!result || !result.hits) {
        message.error('没有数据可导出');
        return;
      }

      // 转换数据格式
      const exportData = result.hits.map((asset: any) => ({
        资产类型: asset.asset_type,
        资产值: asset.asset_value,
        主机: asset.asset_host,
        状态: asset.status,
        置信度: asset.confidence,
        来源: asset.source,
        发现时间: asset.discovered_at,
        平台ID: asset.platform_id,
        项目ID: asset.project_id,
        标签: asset.tags?.join(', ') || '',
      }));

      // 创建CSV内容
      const headers = Object.keys(exportData[0] || {});
      const csvContent = [
        headers.join(','),
        ...exportData.map(row =>
          headers.map(header => `"${(row as any)[header] || ''}"`).join(',')
        )
      ].join('\n');

      // 下载文件
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `assets_export_${new Date().toISOString().split('T')[0]}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      message.success(`成功导出 ${exportData.length} 条资产数据`);
    } catch (error: any) {
      console.error('导出失败:', error);
      message.error(`导出失败: ${error.response?.data?.detail || error.message}`);
    }
  };

  // 添加API测试功能
  const testApiConnection = async () => {
    try {
      message.info('正在测试API连接...');

      // 测试基础搜索API
      const response = await assetsV2Service.searchAssets({
        q: '*',
        page: 1,
        size: 1,
        include_aggs: false
      });

      console.log('API测试响应:', response);

      if (response.data) {
        message.success(`API连接正常！找到 ${response.data.total || 0} 条资产数据`);
      } else {
        message.warning('API连接正常，但返回数据为空');
      }
    } catch (error: any) {
      console.error('API测试失败:', error);
      message.error(`API测试失败: ${error.response?.data?.detail || error.message}`);
    }
  };

  const handleImport = async () => {
    try {
      const values = await importForm.validateFields();
      
      if (!fileList.length) {
        message.error('请选择文件');
        return;
      }
      
      const file = fileList[0].originFileObj;
      
      // 简单的字段映射（实际应用中需要更复杂的映射逻辑）
      const mappings = [
        { source_field: 'domain', target_field: 'asset_value', target_type: 'text' },
        { source_field: 'subdomain', target_field: 'asset_value', target_type: 'text' },
        { source_field: 'ip', target_field: 'asset_value', target_type: 'text' },
        { source_field: 'url', target_field: 'asset_value', target_type: 'text' },
        { source_field: 'type', target_field: 'asset_type', target_type: 'text' },
        { source_field: 'status', target_field: 'status', target_type: 'text' },
        { source_field: 'confidence', target_field: 'confidence', target_type: 'text' },
      ];
      
      const response = await assetsV2Service.importAssets(
        file,
        mappings,
        values.platform_id,
        values.project_id,
        true // 自动清洗
      );
      
      const result = response.data;
      
      message.success(result.message);
      
      // 重新加载数据
      loadAssets();
      loadStats();
      setImportModalVisible(false);
      importForm.resetFields();
      setFileList([]);
      
    } catch (error: any) {
      message.error(`导入失败: ${error.response?.data?.detail || error.message}`);
    }
  };

  const handleCleanup = async () => {
    try {
      const response = await assetsV2Service.cleanupDuplicates(false); // 实际清理
      message.success(response.data.message);
      loadAssets();
      loadStats();
    } catch (error: any) {
      message.error(`清理失败: ${error.response?.data?.detail || error.message}`);
    }
  };



  // 所有可用的列定义
  const allColumnOptions = [
    { key: 'platform_project', title: '平台/项目', essential: true },
    { key: 'asset_type', title: '资产类型', essential: true },
    { key: 'asset_value', title: '资产值', essential: true },
    { key: 'status', title: '状态', essential: false },
    { key: 'confidence', title: '置信度', essential: false },
    { key: 'tags', title: '标签', essential: false },
    { key: 'source', title: '来源', essential: false },
    { key: 'discovered_at', title: '发现时间', essential: false },
    { key: 'asset_host', title: '主机', essential: false },
    { key: 'action', title: '操作', essential: true },
  ];

  // 动态生成列配置
  const getColumnConfig = (key: string) => {
    switch (key) {
      case 'platform_project':
        return {
          title: '平台/项目',
          key: 'platform_project',
          width: 180,
          fixed: 'left' as const,
          render: (record: any) => {
            const platform = platforms.find(p => p.id === record.platform_id);
            const project = projects.find(p => p.id === record.project_id);
            return (
              <div>
                {platform && (
                  <Tag color="blue" icon={<GlobalOutlined />} style={{ marginBottom: 2, fontSize: '11px' }}>
                    {platform.entity_data.display_name}
                  </Tag>
                )}
                {project && (
                  <Tag color="green" icon={<ApiOutlined />} style={{ fontSize: '11px' }}>
                    {project.entity_data.name}
                  </Tag>
                )}
              </div>
            );
          },
        };

      case 'asset_type':
        return {
          title: '资产类型',
          dataIndex: 'asset_type',
          key: 'asset_type',
          width: 100,
          render: (type: string) => (
            <Tag color="orange">{type}</Tag>
          ),
        };

      case 'asset_value':
        return {
          title: '资产值',
          dataIndex: 'asset_value',
          key: 'asset_value',
          width: 250,
          ellipsis: true,
          render: (value: string) => (
            <Text code style={{ fontSize: '12px' }}>{value}</Text>
          ),
        };

      case 'status':
        return {
          title: '状态',
          dataIndex: 'status',
          key: 'status',
          width: 80,
          render: (status: string) => (
            <Tag color={status === 'active' ? 'green' : 'default'}>
              {status}
            </Tag>
          ),
        };

      case 'confidence':
        return {
          title: '置信度',
          dataIndex: 'confidence',
          key: 'confidence',
          width: 80,
          render: (confidence: string) => (
            <Tag color={confidence === 'high' ? 'red' : confidence === 'medium' ? 'orange' : 'default'}>
              {confidence}
            </Tag>
          ),
        };

      case 'tags':
        return {
          title: '标签',
          dataIndex: 'tags',
          key: 'tags',
          width: 150,
          render: (tags: string[]) => (
            <div>
              {tags?.slice(0, 2).map((tag: string, index: number) => (
                <Tag key={index}>{tag}</Tag>
              ))}
              {tags?.length > 2 && <Text type="secondary">+{tags.length - 2}</Text>}
            </div>
          ),
        };

      case 'source':
        return {
          title: '来源',
          dataIndex: 'source',
          key: 'source',
          width: 100,
        };

      case 'discovered_at':
        return {
          title: '发现时间',
          dataIndex: 'discovered_at',
          key: 'discovered_at',
          width: 120,
          render: (date: string) => date ? (
            <Text type="secondary" style={{ fontSize: '11px' }}>
              {dayjs(date).format('MM-DD HH:mm')}
            </Text>
          ) : '-',
        };

      case 'asset_host':
        return {
          title: '主机',
          dataIndex: 'asset_host',
          key: 'asset_host',
          width: 150,
          ellipsis: true,
        };

      case 'action':
        return {
          title: '操作',
          key: 'action',
          width: 60,
          fixed: 'right' as const,
          render: (record: any) => (
            <Button
              type="text"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => handleViewDetails(record)}
            />
          ),
        };

      default:
        return null;
    }
  };

  const columns = visibleColumns.map(getColumnConfig).filter(Boolean) as any[];

  // 添加初始化检查
  const checkElasticsearchStatus = async () => {
    try {
      // 使用仪表板健康检查API
      const response = await api.get('/dashboard/health');
      console.log('ES状态检查:', response.data);
      
      if (response.data.elasticsearch_connected) {
        message.success('Elasticsearch连接正常');
      } else {
        message.warning('Elasticsearch连接异常，可能影响数据显示');
      }
      
      return response.data.elasticsearch_connected;
    } catch (error) {
      console.error('ES状态检查失败:', error);
      message.warning('无法检查Elasticsearch状态，可能影响数据显示');
      return false;
    }
  };

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>资产管理 V2.0 测试页面</Title>
      
      <Alert
        message="这是新的ES架构测试页面"
        description="基于Elasticsearch的统一资产管理，支持大数据量、数据清洗、去重和Kibana集成。"
        type="info"
        showIcon
        style={{ marginBottom: 24 }}
      />

      {/* 统计卡片 */}
      {stats && (
        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col span={6}>
            <Card>
              <Statistic title="总资产数" value={stats.total_assets} />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic 
                title="资产类型" 
                value={stats.asset_types?.length || 0} 
                suffix="种"
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic 
                title="关联平台" 
                value={stats.platforms?.length || 0} 
                suffix="个"
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic 
                title="关联项目" 
                value={stats.projects?.length || 0} 
                suffix="个"
              />
            </Card>
          </Col>
        </Row>
      )}

      {/* 列显示配置 */}
      <Row style={{ marginBottom: 16 }}>
        <Col span={24}>
          <Space style={{ float: 'right' }}>
            <Popover
              content={
                <div style={{ width: 300 }}>
                  <div style={{ marginBottom: 8 }}>
                    <Text strong>选择显示列</Text>
                  </div>
                  {allColumnOptions.map(option => (
                    <div key={option.key} style={{ marginBottom: 4 }}>
                      <Checkbox
                        checked={visibleColumns.includes(option.key)}
                        disabled={option.essential}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setVisibleColumns([...visibleColumns, option.key]);
                          } else {
                            setVisibleColumns(visibleColumns.filter(col => col !== option.key));
                          }
                        }}
                      >
                        {option.title}
                        {option.essential && <Text type="secondary"> (必需)</Text>}
                      </Checkbox>
                    </div>
                  ))}
                  <div style={{ marginTop: 8, paddingTop: 8, borderTop: '1px solid #f0f0f0' }}>
                    <Button
                      size="small"
                      onClick={() => setVisibleColumns(['platform_project', 'asset_type', 'asset_value', 'action'])}
                    >
                      最小化
                    </Button>
                    <Button
                      size="small"
                      style={{ marginLeft: 8 }}
                      onClick={() => setVisibleColumns(allColumnOptions.map(opt => opt.key))}
                    >
                      全部显示
                    </Button>
                  </div>
                </div>
              }
              title="列显示设置"
              trigger="click"
            >
              <Button icon={<SettingOutlined />}>列设置</Button>
            </Popover>
          </Space>
        </Col>
      </Row>

      {/* 搜索表单 */}
      <Card style={{ marginBottom: 16 }}>
        <Form
          form={searchForm}
          onFinish={handleSearch}
          layout="vertical"
        >
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12} md={8}>
              <Form.Item name="q" label="搜索关键词">
                <Input
                  placeholder="搜索资产值、主机或目标..."
                  prefix={<SearchOutlined />}
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={8}>
              <Form.Item name="platform_id" label="平台">
                <Select
                  placeholder="选择平台"
                  allowClear
                  onChange={handlePlatformChange}
                >
                  {platforms.map(platform => (
                    <Option key={platform.id} value={platform.id}>
                      <Space>
                        <GlobalOutlined />
                        {platform.entity_data.display_name}
                      </Space>
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={8}>
              <Form.Item name="project_id" label="项目">
                <Select
                  placeholder="选择项目"
                  allowClear
                  disabled={!filters.platform_id}
                >
                  {projects.map(project => (
                    <Option key={project.id} value={project.id}>
                      <Space>
                        <ApiOutlined />
                        {project.entity_data.name}
                      </Space>
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={8}>
              <Form.Item name="asset_type" label="资产类型">
                <Select placeholder="选择资产类型" allowClear>
                  {assetTypes.map(type => (
                    <Option key={type.type} value={type.type}>
                      {type.type} ({type.count})
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={8}>
              <Form.Item name="source" label="发现来源">
                <Select placeholder="选择发现来源" allowClear>
                  {assetSources.map(source => (
                    <Option key={source.source} value={source.source}>
                      {source.source} ({source.count})
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={8}>
              <Form.Item name="confidence" label="置信度">
                <Select placeholder="选择置信度" allowClear>
                  <Option value="high">高</Option>
                  <Option value="medium">中</Option>
                  <Option value="low">低</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={8}>
              <Form.Item name="status" label="状态">
                <Select placeholder="选择状态" allowClear>
                  <Option value="active">活跃</Option>
                  <Option value="inactive">非活跃</Option>
                  <Option value="pending">待验证</Option>
                  <Option value="verified">已验证</Option>
                  <Option value="false_positive">误报</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={8}>
              <Form.Item name="dateRange" label="发现时间">
                <RangePicker
                  style={{ width: '100%' }}
                  format="YYYY-MM-DD"
                />
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col span={24}>
              <Space>
                <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
                  搜索
                </Button>
                <Button onClick={handleReset} icon={<ReloadOutlined />}>
                  重置
                </Button>
              </Space>
            </Col>
          </Row>
        </Form>
      </Card>

      {/* 聚合数据展示 */}
      {aggregations && (
        <Card title="数据概览" style={{ marginBottom: 16 }}>
          <Row gutter={16}>
            <Col span={12}>
              <div>
                <Text strong>资产类型分布</Text>
                <div style={{ marginTop: 8 }}>
                  {Object.entries(aggregations.asset_types || {}).map(([type, count]) => (
                    <Tag key={type} style={{ margin: '2px' }}>
                      {type}: {count as number}
                    </Tag>
                  ))}
                </div>
              </div>
            </Col>
            <Col span={12}>
              <div>
                <Text strong>置信度分布</Text>
                <div style={{ marginTop: 8 }}>
                  {Object.entries(aggregations.confidence || {}).map(([confidence, count]) => (
                    <Tag key={confidence} style={{ margin: '2px' }}>
                      {confidence}: {count as number}
                    </Tag>
                  ))}
                </div>
              </div>
            </Col>
          </Row>
        </Card>
      )}

      {/* 操作按钮 */}
      <Card style={{ marginBottom: 16 }}>
        <Space>
          <Button
            type="primary"
            icon={<UploadOutlined />}
            onClick={() => setImportModalVisible(true)}
          >
            导入资产
          </Button>
          <Button
            icon={<ExportOutlined />}
            onClick={handleExport}
          >
            导出资产
          </Button>
          <Button
            icon={<ReloadOutlined />}
            onClick={() => {
              loadAssets();
              loadStats();
            }}
            loading={loading}
          >
            刷新
          </Button>
          <Button
            icon={<BarChartOutlined />}
            onClick={loadStats}
          >
            更新统计
          </Button>
          <Button
            icon={<SearchOutlined />}
            onClick={testApiConnection}
          >
            测试API
          </Button>
          <Button
            icon={<ClearOutlined />}
            onClick={handleCleanup}
            danger
          >
            清理重复
          </Button>
        </Space>
      </Card>

      {/* 资产列表 */}
      <Card>
        {/* 显示当前筛选状态 */}
        {(filters.platform_id || filters.project_id) && (
          <div style={{ marginBottom: 16 }}>
            {filters.platform_id && (
              <Tag color="blue" closable onClose={() => {
                const newFilters = { ...filters, platform_id: '', project_id: '' };
                setFilters(newFilters);
                searchForm.setFieldsValue({ platform_id: undefined, project_id: undefined });
                loadProjects(); // 重新加载所有项目
              }}>
                平台: {platforms.find(p => p.id === filters.platform_id)?.entity_data?.display_name || '未知平台'}
              </Tag>
            )}
            {filters.project_id && (
              <Tag color="green" closable onClose={() => {
                const newFilters = { ...filters, project_id: '' };
                setFilters(newFilters);
                searchForm.setFieldsValue({ project_id: undefined });
              }} style={{ marginLeft: 8 }}>
                项目: {projects.find(p => p.id === filters.project_id)?.entity_data?.name || '未知项目'}
              </Tag>
            )}
          </div>
        )}
        <Table
          columns={columns}
          dataSource={assets}
          loading={loading}
          rowKey="_id"
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            onChange: (page, size) => {
              setCurrentPage(page);
              setPageSize(size!);
            },
          }}
          scroll={{
            x: Math.max(800, visibleColumns.length * 120),
            y: 600
          }}
          locale={{
            emptyText: '暂无数据，请尝试导入资产或检查Elasticsearch连接'
          }}
        />
      </Card>

      {/* 导入对话框 */}
      <Modal
        title="导入资产 V2.0"
        open={importModalVisible}
        onOk={handleImport}
        onCancel={() => {
          setImportModalVisible(false);
          importForm.resetFields();
          setFileList([]);
        }}
        width={600}
      >
        <Form form={importForm} layout="vertical">
          <Form.Item name="platform_id" label="关联平台" rules={[{ required: true }]}>
            <Select placeholder="选择平台">
              {platforms.map(platform => (
                <Option key={platform.id} value={platform.id}>
                  {platform.entity_data?.display_name || platform.entity_data?.name}
                </Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item name="project_id" label="关联项目" rules={[{ required: true }]}>
            <Select placeholder="选择项目">
              {projects.map(project => (
                <Option key={project.id} value={project.id}>
                  {project.entity_data?.name}
                </Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item label="上传文件">
            <Upload
              beforeUpload={() => false}
              fileList={fileList}
              onChange={({ fileList }) => setFileList(fileList)}
              accept=".csv,.json"
            >
              <Button icon={<UploadOutlined />}>选择文件</Button>
            </Upload>
            <Text type="secondary">支持CSV和JSON格式</Text>
          </Form.Item>
        </Form>
      </Modal>

      {/* 资产详情抽屉 */}
      <Drawer
        title="资产详情"
        width={700}
        onClose={() => setDrawerVisible(false)}
        open={drawerVisible}
      >
        {selectedAsset && (
          <div>
            <Descriptions title="基本信息" bordered size="small" column={2}>
              <Descriptions.Item label="资产值" span={2}>
                <Text code style={{ fontSize: '14px' }}>{selectedAsset.asset_value}</Text>
              </Descriptions.Item>
              <Descriptions.Item label="资产类型">
                <Tag color="orange">
                  {selectedAsset.asset_type}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="置信度">
                <Tag color={selectedAsset.confidence === 'high' ? 'red' : selectedAsset.confidence === 'medium' ? 'orange' : 'default'}>
                  {selectedAsset.confidence}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="状态">
                {selectedAsset.status ? (
                  <Tag color={selectedAsset.status === 'active' ? 'green' : 'default'}>
                    {selectedAsset.status}
                  </Tag>
                ) : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="主机">
                {selectedAsset.asset_host || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="发现时间" span={2}>
                {selectedAsset.discovered_at ?
                  dayjs(selectedAsset.discovered_at).format('YYYY-MM-DD HH:mm:ss') : '-'}
              </Descriptions.Item>
            </Descriptions>

            <Descriptions title="发现信息" bordered size="small" column={2} style={{ marginTop: 16 }}>
              <Descriptions.Item label="发现来源">
                {selectedAsset.source ? (
                  <Tag color="blue">{selectedAsset.source}</Tag>
                ) : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="发现次数">
                {selectedAsset.discovery_count || 1}
              </Descriptions.Item>
              <Descriptions.Item label="源任务ID" span={2}>
                {selectedAsset.source_task_id ? (
                  <Text code>{selectedAsset.source_task_id}</Text>
                ) : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="Agent ID" span={2}>
                {selectedAsset.source_agent_id || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="工作流ID" span={2}>
                {selectedAsset.workflow_id || '-'}
              </Descriptions.Item>
            </Descriptions>

            {/* 平台项目信息 */}
            {(selectedAsset.platform_id || selectedAsset.project_id) && (
              <Descriptions title="关联信息" bordered size="small" column={1} style={{ marginTop: 16 }}>
                {selectedAsset.platform_id && (
                  <Descriptions.Item label="所属平台">
                    {(() => {
                      const platform = platforms.find(p => p.id === selectedAsset.platform_id);
                      return platform ? (
                        <Tag color="blue" icon={<GlobalOutlined />}>
                          {platform.entity_data.display_name}
                        </Tag>
                      ) : selectedAsset.platform_id;
                    })()}
                  </Descriptions.Item>
                )}
                {selectedAsset.project_id && (
                  <Descriptions.Item label="所属项目">
                    {(() => {
                      const project = projects.find(p => p.id === selectedAsset.project_id);
                      return project ? (
                        <Tag color="green" icon={<ApiOutlined />}>
                          {project.entity_data.name}
                        </Tag>
                      ) : selectedAsset.project_id;
                    })()}
                  </Descriptions.Item>
                )}
              </Descriptions>
            )}

            {selectedAsset.tags && selectedAsset.tags.length > 0 && (
              <div style={{ marginTop: 16 }}>
                <Title level={5}>标签</Title>
                <Space wrap>
                  {selectedAsset.tags.map((tag: string, index: number) => (
                    <Tag key={index} color="processing">{tag}</Tag>
                  ))}
                </Space>
              </div>
            )}

            {selectedAsset.metadata && Object.keys(selectedAsset.metadata).length > 0 && (
              <div style={{ marginTop: 16 }}>
                <Title level={5}>元数据</Title>
                <Card size="small" style={{ background: '#fafafa' }}>
                  <pre style={{
                    margin: 0,
                    fontSize: '12px',
                    lineHeight: '1.4',
                    maxHeight: '200px',
                    overflow: 'auto'
                  }}>
                    {JSON.stringify(selectedAsset.metadata, null, 2)}
                  </pre>
                </Card>
              </div>
            )}
          </div>
        )}
      </Drawer>
    </div>
  );
};

export default AssetsV2Test; 
