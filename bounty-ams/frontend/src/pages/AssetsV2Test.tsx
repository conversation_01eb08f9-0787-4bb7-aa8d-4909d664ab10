import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Form,
  Input,
  Select,
  Row,
  Col,
  Tag,
  message,
  Upload,
  Modal,
  Typography,
  Statistic,
  Progress,
  Alert
} from 'antd';
import {
  SearchOutlined,
  UploadOutlined,
  ReloadOutlined,
  Bar<PERSON><PERSON>Outlined,
  ClearOutlined,
  DatabaseOutlined
} from '@ant-design/icons';
import { assetsV2Service, assetDataTransformer } from '../services/assetsV2';
import type { AssetSearchParams } from '../services/assetsV2';
import { assetService, api } from '../services/api';

const { Title, Text } = Typography;
const { Option } = Select;

const AssetsV2Test: React.FC = () => {
  // 状态管理
  const [assets, setAssets] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [stats, setStats] = useState<any>(null);
  const [platforms, setPlatforms] = useState<any[]>([]);
  const [projects, setProjects] = useState<any[]>([]);
  const [searchForm] = Form.useForm();
  const [importModalVisible, setImportModalVisible] = useState(false);
  const [importForm] = Form.useForm();
  const [fileList, setFileList] = useState<any[]>([]);
  
  // 分页状态
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [total, setTotal] = useState(0);
  
  // 加载初始数据
  useEffect(() => {
    const init = async () => {
      const esStatus = await checkElasticsearchStatus();
      if (esStatus) {
        loadPlatforms();
        loadProjects();
        loadAssets();
        loadStats();
      }
    };
    
    init();
  }, []);

  const loadPlatforms = async () => {
    try {
      const response = await assetService.getDynamicModelTypes();
      const platformType = response.data.find((t: any) => t.name === 'platform');
      if (platformType) {
        const platformsResponse = await assetService.getDynamicEntities(platformType.id);
        setPlatforms(platformsResponse.data || []);
      }
    } catch (error) {
      console.error('加载平台失败:', error);
    }
  };

  const loadProjects = async () => {
    try {
      const response = await assetService.getDynamicModelTypes();
      const projectType = response.data.find((t: any) => t.name === 'project');
      if (projectType) {
        const projectsResponse = await assetService.getDynamicEntities(projectType.id);
        setProjects(projectsResponse.data || []);
      }
    } catch (error) {
      console.error('加载项目失败:', error);
    }
  };

  const loadAssets = async (searchParams?: AssetSearchParams) => {
    try {
      setLoading(true);
      
      const params: AssetSearchParams = {
        q: '*', // 确保默认查询所有
        page: currentPage,
        size: pageSize,
        include_aggs: true,
        ...searchParams
      };
      
      console.log('搜索参数:', params); // 添加日志
      
      const response = await assetsV2Service.searchAssets(params);
      console.log('API响应:', response); // 添加日志查看响应
      
      const result = response.data;
      
      // 检查返回的数据结构
      if (!result || !result.hits) {
        console.error('API返回数据结构异常:', result);
        message.error('返回数据结构异常');
        setAssets([]);
        setTotal(0);
        return;
      }
      
      // 转换数据格式
      const transformedAssets = assetDataTransformer.transformAssetsForDisplay(result.hits);
      
      console.log('转换后的资产数据:', transformedAssets); // 添加日志
      
      setAssets(transformedAssets);
      setTotal(result.total || 0);
      
      if (transformedAssets.length > 0) {
        message.success(`加载了 ${transformedAssets.length} 个资产`);
      } else {
        message.info('没有找到资产数据');
      }
    } catch (error: any) {
      console.error('加载资产失败:', error);
      message.error(`加载资产失败: ${error.response?.data?.detail || error.message}`);
      setAssets([]);
      setTotal(0);
    } finally {
      setLoading(false);
    }
  };

  const loadStats = async () => {
    try {
      const response = await assetsV2Service.getAssetStats();
      setStats(response.data);
    } catch (error: any) {
      console.error('加载统计失败:', error);
      // 不显示错误消息，因为可能是初次运行没有数据
    }
  };

  const handleSearch = (values: any) => {
    const searchParams: AssetSearchParams = {
      q: values.q || '*',
      platform_id: values.platform_id,
      project_id: values.project_id,
      asset_type: values.asset_type,
      confidence: values.confidence,
      status: values.status,
      page: 1
    };
    
    setCurrentPage(1);
    loadAssets(searchParams);
  };

  const handleImport = async () => {
    try {
      const values = await importForm.validateFields();
      
      if (!fileList.length) {
        message.error('请选择文件');
        return;
      }
      
      const file = fileList[0].originFileObj;
      
      // 简单的字段映射（实际应用中需要更复杂的映射逻辑）
      const mappings = [
        { source_field: 'domain', target_field: 'asset_value', target_type: 'text' },
        { source_field: 'subdomain', target_field: 'asset_value', target_type: 'text' },
        { source_field: 'ip', target_field: 'asset_value', target_type: 'text' },
        { source_field: 'url', target_field: 'asset_value', target_type: 'text' },
        { source_field: 'type', target_field: 'asset_type', target_type: 'text' },
        { source_field: 'status', target_field: 'status', target_type: 'text' },
        { source_field: 'confidence', target_field: 'confidence', target_type: 'text' },
      ];
      
      const response = await assetsV2Service.importAssets(
        file,
        mappings,
        values.platform_id,
        values.project_id,
        true // 自动清洗
      );
      
      const result = response.data;
      
      message.success(result.message);
      
      // 重新加载数据
      loadAssets();
      loadStats();
      setImportModalVisible(false);
      importForm.resetFields();
      setFileList([]);
      
    } catch (error: any) {
      message.error(`导入失败: ${error.response?.data?.detail || error.message}`);
    }
  };

  const handleCleanup = async () => {
    try {
      const response = await assetsV2Service.cleanupDuplicates(false); // 实际清理
      message.success(response.data.message);
      loadAssets();
      loadStats();
    } catch (error: any) {
      message.error(`清理失败: ${error.response?.data?.detail || error.message}`);
    }
  };

  // 添加测试数据导入功能
  const importTestData = async () => {
    try {
      setLoading(true);
      message.info('正在导入测试数据...');
      
      // 创建测试数据
      const testData = [
        { asset_value: 'example.com', asset_type: 'domain' },
        { asset_value: '***********', asset_type: 'ip' },
        { asset_value: 'https://test.example.com', asset_type: 'url' },
        { asset_value: 'subdomain.example.com', asset_type: 'subdomain' },
        { asset_value: 'test-app', asset_type: 'application' }
      ];
      
      // 创建测试文件
      const blob = new Blob([JSON.stringify(testData)], { type: 'application/json' });
      const testFile = new File([blob], 'test_assets.json', { type: 'application/json' });
      
      // 创建测试映射
      const testMappings = [
        { source_field: 'asset_value', target_field: 'asset_value' },
        { source_field: 'asset_type', target_field: 'asset_type' }
      ];
      
      // 调用导入API
      const response = await assetsV2Service.importAssets(
        testFile,
        testMappings,
        undefined, // platform_id
        undefined, // project_id
        true // auto_clean
      );
      
      console.log('导入响应:', response);
      message.success(`导入测试数据成功: ${response.data.message}`);
      
      // 重新加载数据
      loadAssets();
      loadStats();
      
    } catch (error: any) {
      console.error('导入测试数据失败:', error);
      message.error(`导入测试数据失败: ${error.response?.data?.detail || error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // 表格列定义
  const columns = [
    {
      title: '平台/项目',
      key: 'platform_project',
      width: 200,
      render: (record: any) => (
        <div>
          {record.platform_name && (
            <Tag color="blue" style={{ marginBottom: 4 }}>
              {record.platform_name}
            </Tag>
          )}
          <br />
          {record.project_name && (
            <Tag color="green">
              {record.project_name}
            </Tag>
          )}
        </div>
      ),
    },
    {
      title: '类型',
      dataIndex: 'asset_type',
      key: 'asset_type',
      width: 80,
      render: (type: string) => (
        <Tag color="orange">{type}</Tag>
      ),
    },
    {
      title: '资产值',
      dataIndex: 'asset_value',
      key: 'asset_value',
      width: 250,
      ellipsis: true,
    },
    {
      title: '主机',
      dataIndex: 'asset_host',
      key: 'asset_host',
      width: 200,
      ellipsis: true,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status: string) => (
        <Tag color={status === 'active' ? 'green' : 'default'}>
          {status}
        </Tag>
      ),
    },
    {
      title: '置信度',
      dataIndex: 'confidence',
      key: 'confidence',
      width: 80,
      render: (confidence: string) => (
        <Tag color={confidence === 'high' ? 'red' : confidence === 'medium' ? 'orange' : 'default'}>
          {confidence}
        </Tag>
      ),
    },
    {
      title: '来源',
      dataIndex: 'source',
      key: 'source',
      width: 100,
    },
    {
      title: '发现时间',
      dataIndex: 'discovered_at',
      key: 'discovered_at',
      width: 150,
      render: (time: string) => time ? new Date(time).toLocaleString() : '-',
    },
    {
      title: '标签',
      dataIndex: 'tags',
      key: 'tags',
      width: 150,
      render: (tags: string[]) => (
        <div>
          {tags?.slice(0, 2).map((tag: string, index: number) => (
            <Tag key={index}>{tag}</Tag>
          ))}
          {tags?.length > 2 && <Text type="secondary">+{tags.length - 2}</Text>}
        </div>
      ),
    },
  ];

  // 添加初始化检查
  const checkElasticsearchStatus = async () => {
    try {
      // 使用仪表板健康检查API
      const response = await api.get('/dashboard/health');
      console.log('ES状态检查:', response.data);
      
      if (response.data.elasticsearch_connected) {
        message.success('Elasticsearch连接正常');
      } else {
        message.warning('Elasticsearch连接异常，可能影响数据显示');
      }
      
      return response.data.elasticsearch_connected;
    } catch (error) {
      console.error('ES状态检查失败:', error);
      message.warning('无法检查Elasticsearch状态，可能影响数据显示');
      return false;
    }
  };

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>资产管理 V2.0 测试页面</Title>
      
      <Alert
        message="这是新的ES架构测试页面"
        description="基于Elasticsearch的统一资产管理，支持大数据量、数据清洗、去重和Kibana集成。"
        type="info"
        showIcon
        style={{ marginBottom: 24 }}
      />

      {/* 统计卡片 */}
      {stats && (
        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col span={6}>
            <Card>
              <Statistic title="总资产数" value={stats.total_assets} />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic 
                title="资产类型" 
                value={stats.asset_types?.length || 0} 
                suffix="种"
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic 
                title="关联平台" 
                value={stats.platforms?.length || 0} 
                suffix="个"
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic 
                title="关联项目" 
                value={stats.projects?.length || 0} 
                suffix="个"
              />
            </Card>
          </Col>
        </Row>
      )}

      {/* 搜索表单 */}
      <Card style={{ marginBottom: 16 }}>
        <Form form={searchForm} onFinish={handleSearch} layout="inline">
          <Form.Item name="q">
            <Input 
              placeholder="搜索资产..." 
              prefix={<SearchOutlined />}
              style={{ width: 200 }}
            />
          </Form.Item>
          <Form.Item name="platform_id">
            <Select placeholder="选择平台" style={{ width: 150 }} allowClear>
              {platforms.map(platform => (
                <Option key={platform.id} value={platform.id}>
                  {platform.entity_data?.display_name || platform.entity_data?.name}
                </Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item name="project_id">
            <Select placeholder="选择项目" style={{ width: 150 }} allowClear>
              {projects.map(project => (
                <Option key={project.id} value={project.id}>
                  {project.entity_data?.name}
                </Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item name="asset_type">
            <Select placeholder="资产类型" style={{ width: 120 }} allowClear>
              <Option value="domain">域名</Option>
              <Option value="subdomain">子域名</Option>
              <Option value="ip">IP地址</Option>
              <Option value="url">URL</Option>
              <Option value="api_endpoint">API端点</Option>
            </Select>
          </Form.Item>
          <Form.Item>
            <Button type="primary" htmlType="submit" loading={loading}>
              搜索
            </Button>
          </Form.Item>
          <Form.Item>
            <Button onClick={() => {
              searchForm.resetFields();
              loadAssets();
            }}>
              重置
            </Button>
          </Form.Item>
        </Form>
      </Card>

      {/* 操作按钮 */}
      <Card style={{ marginBottom: 16 }}>
        <Space>
          <Button 
            type="primary" 
            icon={<UploadOutlined />}
            onClick={() => setImportModalVisible(true)}
          >
            导入资产
          </Button>
          <Button 
            icon={<ReloadOutlined />}
            onClick={() => {
              loadAssets();
              loadStats();
            }}
            loading={loading}
          >
            刷新
          </Button>
          <Button 
            icon={<BarChartOutlined />}
            onClick={loadStats}
          >
            更新统计
          </Button>
          <Button 
            icon={<ClearOutlined />}
            onClick={handleCleanup}
            danger
          >
            清理重复
          </Button>
        </Space>
      </Card>

      {/* 资产表格 */}
      <Card>
        <Table
          columns={columns}
          dataSource={assets}
          loading={loading}
          rowKey="_id"
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条`,
            onChange: (page, pageSize) => {
              setCurrentPage(page);
              setPageSize(pageSize || 20);
              loadAssets({ page, size: pageSize });
            }
          }}
          locale={{
            emptyText: '暂无数据，请尝试导入资产或检查Elasticsearch连接'
          }}
        />
      </Card>

      {/* 导入对话框 */}
      <Modal
        title="导入资产 V2.0"
        open={importModalVisible}
        onOk={handleImport}
        onCancel={() => {
          setImportModalVisible(false);
          importForm.resetFields();
          setFileList([]);
        }}
        width={600}
      >
        <Form form={importForm} layout="vertical">
          <Form.Item name="platform_id" label="关联平台" rules={[{ required: true }]}>
            <Select placeholder="选择平台">
              {platforms.map(platform => (
                <Option key={platform.id} value={platform.id}>
                  {platform.entity_data?.display_name || platform.entity_data?.name}
                </Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item name="project_id" label="关联项目" rules={[{ required: true }]}>
            <Select placeholder="选择项目">
              {projects.map(project => (
                <Option key={project.id} value={project.id}>
                  {project.entity_data?.name}
                </Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item label="上传文件">
            <Upload
              beforeUpload={() => false}
              fileList={fileList}
              onChange={({ fileList }) => setFileList(fileList)}
              accept=".csv,.json"
            >
              <Button icon={<UploadOutlined />}>选择文件</Button>
            </Upload>
            <Text type="secondary">支持CSV和JSON格式</Text>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default AssetsV2Test; 
