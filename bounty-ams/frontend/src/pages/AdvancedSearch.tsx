import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { 
  Card, 
  Form, 
  Input, 
  Select, 
  DatePicker, 
  Button, 
  Table, 
  Tabs, 
  Space, 
  Tag, 
  Progress, 
  Statistic, 
  Row, 
  Col,
  Alert,
  Tooltip,
  Badge,
  message,
  Spin,
  Collapse,
  Tree,
  AutoComplete,
  Modal,
  List,
  Typography,
  Dropdown,
  Popconfirm
} from 'antd';
import { 
  SearchOutlined, 
  FilterOutlined, 
  ExportOutlined, 
  SecurityScanOutlined, 
  NodeIndexOutlined,
  FunnelPlotOutlined,
  BugOutlined,
  SafetyCertificateOutlined,
  RadarChartOutlined,
  ThunderboltOutlined,
  SaveOutlined,
  FolderOpenOutlined,
  DeleteOutlined,
  StarOutlined,
  SettingOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import { enhancedSearchService, assetService } from '../services/api';
import { OptimizedApiService } from '../services/optimizedApi';
import ConfigurableAnalysis from '../components/ConfigurableAnalysis';

const { RangePicker } = DatePicker;
const { Option } = Select;
const { TabPane } = Tabs;
const { Panel } = Collapse;
const { TreeNode } = Tree;
const { Text } = Typography;
const { TextArea } = Input;

interface SearchParams {
  query?: string;
  platform_id?: string;
  project_id?: string;
  asset_types?: string[];
  confidence?: string;
  date_from?: string;
  date_to?: string;
  tags?: string[];
  sort_by?: string;
  sort_order?: string;
  page?: number;
  size?: number;
  include_aggregations?: boolean;
}

interface Asset {
  id: string;
  source: {
    entity_data: {
      platform_id: string;
      project_id: string;
      asset_type: string;
      asset_value: string;
      asset_host?: string;
      confidence: string;
      status: string;
      discovered_at: string;
      tags: string[];
      metadata?: any;
    };
  };
  score: number;
  risk_score?: number;
  security_tags?: string[];
}

interface SavedSearch {
  id: string;
  name: string;
  description?: string;
  search_params: SearchParams;
  created_at: string;
  is_favorite: boolean;
}

const AdvancedSearch: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [searchResults, setSearchResults] = useState<Asset[]>([]);
  const [aggregations, setAggregations] = useState<any>({});
  const [total, setTotal] = useState(0);
  const [queryTime, setQueryTime] = useState(0);
  const [activeTab, setActiveTab] = useState('search');
  const [searchSuggestions, setSearchSuggestions] = useState<string[]>([]);
  const [platforms, setPlatforms] = useState<any[]>([]);
  const [projects, setProjects] = useState<any[]>([]);
  
  // 保存的搜索相关状态
  const [savedSearches, setSavedSearches] = useState<SavedSearch[]>([]);
  const [showSaveModal, setShowSaveModal] = useState(false);
  const [showLoadModal, setShowLoadModal] = useState(false);
  const [saveForm] = Form.useForm();

  // 搜索参数状态
  const [searchParams, setSearchParams] = useState<SearchParams>({
    page: 1,
    size: 20,
    include_aggregations: true,
    sort_by: 'discovered_at',
    sort_order: 'desc'
  });

  useEffect(() => {
    loadPlatformsAndProjects();
    loadSavedSearches();
  }, []);

  const loadPlatformsAndProjects = async () => {
    try {
      // 使用优化的批量加载API
      const { platforms, projects } = await OptimizedApiService.loadPlatformsAndProjects();
      
      // 转换平台数据格式以适配UI
      const transformedPlatforms = platforms.map((platform: any) => ({
        id: platform.id,
        name: platform.entity_data.name || platform.entity_data.platform_name
      }));
      setPlatforms(transformedPlatforms);
      
      // 转换项目数据格式以适配UI
      const transformedProjects = projects.map((project: any) => ({
        id: project.id,
        name: project.entity_data.name || project.entity_data.project_name,
        platform_id: project.entity_data.platform_id
      }));
      setProjects(transformedProjects);
      
    } catch (error) {
      console.error('加载平台和项目数据失败:', error);
      message.warning('平台和项目数据加载失败，可能服务未启动');
      // 如果API调用失败，使用空数组
      setPlatforms([]);
      setProjects([]);
    }
  };

  // 加载保存的搜索
  const loadSavedSearches = () => {
    const stored = localStorage.getItem('bounty_ams_saved_searches');
    if (stored) {
      try {
        setSavedSearches(JSON.parse(stored));
      } catch (error) {
        console.error('Failed to load saved searches:', error);
      }
    }
  };

  // 保存搜索
  const handleSaveSearch = async (values: any) => {
    const currentFormValues = form.getFieldsValue();
    const newSavedSearch: SavedSearch = {
      id: Date.now().toString(),
      name: values.name,
      description: values.description,
      search_params: {
        ...searchParams,
        ...currentFormValues
      },
      created_at: new Date().toISOString(),
      is_favorite: values.is_favorite || false
    };

    const updatedSearches = [...savedSearches, newSavedSearch];
    setSavedSearches(updatedSearches);
    localStorage.setItem('bounty_ams_saved_searches', JSON.stringify(updatedSearches));
    
    setShowSaveModal(false);
    saveForm.resetFields();
    message.success('搜索保存成功');
  };

  // 加载保存的搜索
  const handleLoadSearch = (savedSearch: SavedSearch) => {
    const { search_params } = savedSearch;
    
    // 设置表单值
    form.setFieldsValue({
      query: search_params.query,
      platform_id: search_params.platform_id,
      project_id: search_params.project_id,
      asset_types: search_params.asset_types,
      confidence: search_params.confidence,
      tags: search_params.tags,
      date_range: search_params.date_from && search_params.date_to ? [
        dayjs(search_params.date_from),
        dayjs(search_params.date_to)
      ] : undefined
    });
    
    setSearchParams(search_params);
    setShowLoadModal(false);
    message.success(`加载搜索 "${savedSearch.name}" 成功`);
  };

  // 删除保存的搜索
  const handleDeleteSearch = (searchId: string) => {
    const updatedSearches = savedSearches.filter(search => search.id !== searchId);
    setSavedSearches(updatedSearches);
    localStorage.setItem('bounty_ams_saved_searches', JSON.stringify(updatedSearches));
    message.success('搜索已删除');
  };

  // 切换收藏状态
  const handleToggleFavorite = (searchId: string) => {
    const updatedSearches = savedSearches.map(search => 
      search.id === searchId 
        ? { ...search, is_favorite: !search.is_favorite }
        : search
    );
    setSavedSearches(updatedSearches);
    localStorage.setItem('bounty_ams_saved_searches', JSON.stringify(updatedSearches));
  };

  // 执行搜索
  const handleSearch = async (values: any) => {
    setLoading(true);
    try {
      const params: SearchParams = {
        ...searchParams,
        query: values.query,
        platform_id: values.platform_id,
        project_id: values.project_id,
        asset_types: values.asset_types,
        confidence: values.confidence,
        tags: values.tags,
        page: 1
      };

      if (values.date_range && values.date_range.length === 2) {
        params.date_from = values.date_range[0].toISOString();
        params.date_to = values.date_range[1].toISOString();
      }

      const response = await enhancedSearchService.advancedSearch(params);
      const result = response.data;

      setSearchResults(result.hits || []);
      setAggregations(result.aggregations || {});
      setTotal(result.total || 0);
      setQueryTime(result.query_time || 0);
      setSearchParams(params);

      message.success(`找到 ${result.total} 个资产，耗时 ${result.query_time.toFixed(3)}s`);
    } catch (error) {
      message.error('搜索失败，请检查参数');
      console.error('Search error:', error);
    } finally {
      setLoading(false);
    }
  };

  // 获取搜索建议
  const handleSearchSuggestions = async (query: string) => {
    if (query.length < 2) return;
    
    try {
      const response = await enhancedSearchService.getSearchSuggestions(query);
      setSearchSuggestions(response.data.suggestions || []);
    } catch (error) {
      console.error('Failed to get suggestions:', error);
    }
  };

  // 执行安全分析
  const handleSecurityAnalysis = async () => {
    setLoading(true);
    try {
      const response = await enhancedSearchService.securityAnalysis(
        searchParams.platform_id,
        'high'
      );
      
      setActiveTab('security');
      setSearchResults(response.data.hits || []);
      setAggregations(response.data.aggregations || {});
      setTotal(response.data.total || 0);
      
      message.success('安全分析完成');
    } catch (error) {
      message.error('安全分析失败');
    } finally {
      setLoading(false);
    }
  };

  // 导出结果
  const handleExport = async (format: string = 'json') => {
    try {
      const response = await enhancedSearchService.exportAssets(searchParams, format);
      message.success(`成功导出 ${response.data.data.total_exported} 个资产`);
      
      // 创建下载链接
      const blob = new Blob([JSON.stringify(response.data.data.assets, null, 2)], {
        type: format === 'json' ? 'application/json' : 'text/csv'
      });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `assets_export_${dayjs().format('YYYY-MM-DD_HH-mm-ss')}.${format}`;
      a.click();
      window.URL.revokeObjectURL(url);
    } catch (error) {
      message.error('导出失败');
    }
  };

  // 表格列定义
  const columns: ColumnsType<Asset> = useMemo(() => [
    {
      title: '资产值',
      dataIndex: ['source', 'entity_data', 'asset_value'],
      key: 'asset_value',
      render: (value: string, record: Asset) => (
        <div>
          <strong>{value}</strong>
          {record.risk_score !== undefined && (
            <div>
              <Progress 
                percent={record.risk_score * 100} 
                size="small" 
                status={record.risk_score > 0.7 ? 'exception' : record.risk_score > 0.4 ? 'active' : 'success'}
                showInfo={false}
              />
              <span style={{ fontSize: '12px', color: '#666' }}>
                风险: {(record.risk_score * 100).toFixed(1)}%
              </span>
            </div>
          )}
        </div>
      ),
    },
    {
      title: '类型',
      dataIndex: ['source', 'entity_data', 'asset_type'],
      key: 'asset_type',
      render: (type: string) => <Tag color="blue">{type}</Tag>,
    },
    {
      title: '置信度',
      dataIndex: ['source', 'entity_data', 'confidence'],
      key: 'confidence',
      render: (confidence: string) => (
        <Tag color={confidence === 'high' ? 'green' : confidence === 'medium' ? 'orange' : 'red'}>
          {confidence}
        </Tag>
      ),
    },
    {
      title: '状态',
      dataIndex: ['source', 'entity_data', 'status'],
      key: 'status',
      render: (status: string) => (
        <Badge 
          status={status === 'active' ? 'success' : status === 'verified' ? 'processing' : 'default'} 
          text={status} 
        />
      ),
    },
    {
      title: '标签',
      dataIndex: ['source', 'entity_data', 'tags'],
      key: 'tags',
      render: (tags: string[], record: Asset) => (
        <div>
          {tags?.map((tag: string) => (
            <Tag key={tag}>{tag}</Tag>
          ))}
          {record.security_tags?.map((tag: string) => (
            <Tag key={tag} color="red" icon={<BugOutlined />}>
              {tag}
            </Tag>
          ))}
        </div>
      ),
    },
    {
      title: '发现时间',
      dataIndex: ['source', 'entity_data', 'discovered_at'],
      key: 'discovered_at',
      render: (date: string) => dayjs(date).format('YYYY-MM-DD HH:mm'),
    },
    {
      title: '相关性评分',
      dataIndex: 'score',
      key: 'score',
      render: (score: number) => (
        <Tooltip title={`ElasticSearch相关性评分: ${score.toFixed(3)}`}>
          <Progress 
            type="circle" 
            percent={Math.min(score * 20, 100)} 
            size={40}
            format={() => score.toFixed(1)}
          />
        </Tooltip>
      ),
    },
  ], []);

  // 渲染聚合统计
  const renderAggregations = useCallback(() => {
    if (!aggregations || Object.keys(aggregations).length === 0) {
      return <Alert message="暂无聚合数据" type="info" />;
    }

    return (
      <Row gutter={[16, 16]}>
        {/* 平台分布 */}
        {aggregations.platforms && (
          <Col span={8}>
            <Card title="平台分布" size="small">
              {aggregations.platforms.map((item: any) => (
                <div key={item.key} style={{ marginBottom: 8 }}>
                  <Tag color="blue">{item.key}</Tag>
                  <span>{item.doc_count}</span>
                </div>
              ))}
            </Card>
          </Col>
        )}

        {/* 资产类型分布 */}
        {aggregations.asset_types && (
          <Col span={8}>
            <Card title="资产类型分布" size="small">
              {aggregations.asset_types.map((item: any) => (
                <div key={item.key} style={{ marginBottom: 8 }}>
                  <Tag color="green">{item.key}</Tag>
                  <span>{item.doc_count}</span>
                </div>
              ))}
            </Card>
          </Col>
        )}

        {/* 置信度分布 */}
        {aggregations.confidence_levels && (
          <Col span={8}>
            <Card title="置信度分布" size="small">
              {aggregations.confidence_levels.map((item: any) => (
                <div key={item.key} style={{ marginBottom: 8 }}>
                  <Tag color={item.key === 'high' ? 'green' : item.key === 'medium' ? 'orange' : 'red'}>
                    {item.key}
                  </Tag>
                  <span>{item.doc_count}</span>
                </div>
              ))}
            </Card>
          </Col>
        )}
      </Row>
    );
  }, [aggregations]);

  return (
    <div style={{ padding: 24 }}>
      <Card title="高级资产搜索" style={{ marginBottom: 24 }}>
        <Form form={form} onFinish={handleSearch} layout="vertical">
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item label="搜索查询" name="query">
                <AutoComplete
                  placeholder="输入资产值、域名、IP等"
                  options={searchSuggestions.map(item => ({ value: item }))}
                  onSearch={handleSearchSuggestions}
                  onSelect={(value) => form.setFieldsValue({ query: value })}
                />
              </Form.Item>
            </Col>
            <Col span={4}>
              <Form.Item label="平台" name="platform_id">
                <Select placeholder="选择平台" allowClear>
                  {platforms.map(platform => (
                    <Option key={platform.id} value={platform.id}>
                      {platform.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={4}>
              <Form.Item label="项目" name="project_id">
                <Select placeholder="选择项目" allowClear>
                  {projects.map(project => (
                    <Option key={project.id} value={project.id}>
                      {project.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={4}>
              <Form.Item label="资产类型" name="asset_types">
                <Select mode="multiple" placeholder="选择类型" allowClear>
                  <Option value="domain">域名</Option>
                  <Option value="subdomain">子域名</Option>
                  <Option value="ip">IP地址</Option>
                  <Option value="url">URL</Option>
                  <Option value="port">端口</Option>
                  <Option value="service">服务</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={4}>
              <Form.Item label="置信度" name="confidence">
                <Select placeholder="选择置信度" allowClear>
                  <Option value="high">高</Option>
                  <Option value="medium">中</Option>
                  <Option value="low">低</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item label="时间范围" name="date_range">
                <RangePicker 
                  showTime 
                  placeholder={['开始时间', '结束时间']}
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="标签" name="tags">
                <Select mode="tags" placeholder="输入或选择标签" allowClear>
                  <Option value="verified">已验证</Option>
                  <Option value="vulnerable">有漏洞</Option>
                  <Option value="critical">关键</Option>
                  <Option value="in_scope">在范围内</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="操作" style={{ marginTop: 30 }}>
                <Space wrap>
                  <Button 
                    type="primary" 
                    icon={<SearchOutlined />} 
                    htmlType="submit"
                    loading={loading}
                  >
                    搜索
                  </Button>
                  <Button 
                    icon={<SecurityScanOutlined />} 
                    onClick={handleSecurityAnalysis}
                    loading={loading}
                  >
                    安全分析
                  </Button>
                  <Button 
                    icon={<SaveOutlined />} 
                    onClick={() => setShowSaveModal(true)}
                  >
                    保存搜索
                  </Button>
                  <Button 
                    icon={<FolderOpenOutlined />} 
                    onClick={() => setShowLoadModal(true)}
                  >
                    加载搜索
                  </Button>
                  <Button 
                    icon={<ExportOutlined />} 
                    onClick={() => handleExport('json')}
                  >
                    导出
                  </Button>
                </Space>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Card>

      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab={`搜索结果 (${total})`} key="search">
          <Card>
            <div style={{ marginBottom: 16 }}>
              <Row gutter={16}>
                <Col span={6}>
                  <Statistic title="总资产数" value={total} />
                </Col>
                <Col span={6}>
                  <Statistic 
                    title="查询耗时" 
                    value={queryTime} 
                    suffix="秒"
                    precision={3}
                  />
                </Col>
                <Col span={6}>
                  <Statistic 
                    title="当前页" 
                    value={`${searchParams.page}/${Math.ceil(total / (searchParams.size || 20))}`} 
                  />
                </Col>
                <Col span={6}>
                  <Statistic title="每页大小" value={searchParams.size} />
                </Col>
              </Row>
            </div>

            <Table
              columns={columns}
              dataSource={searchResults}
              rowKey="id"
              loading={loading}
              pagination={{
                current: searchParams.page,
                pageSize: searchParams.size,
                total: total,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) => `${range[0]}-${range[1]} / ${total}`,
                onChange: (page, size) => {
                  setSearchParams({ ...searchParams, page, size });
                  // 这里可以触发新的搜索
                },
              }}
            />
          </Card>
        </TabPane>

        <TabPane tab={<span><FunnelPlotOutlined />聚合分析</span>} key="aggregations">
          <Card title="数据聚合统计">
            {renderAggregations()}
          </Card>
        </TabPane>

        <TabPane tab={<span><SecurityScanOutlined />安全分析</span>} key="security">
          <Card title="安全风险分析">
            <Alert
              message="安全分析功能"
              description="基于资产特征、标签和历史数据进行智能风险评估，识别高风险资产和潜在威胁。"
              type="info"
              showIcon
              style={{ marginBottom: 16 }}
            />
            
            {searchResults.length > 0 && (
              <Table
                columns={columns}
                dataSource={searchResults}
                rowKey="id"
                loading={loading}
                pagination={false}
                size="small"
              />
            )}
          </Card>
        </TabPane>

        <TabPane tab={<span><RadarChartOutlined />关联分析</span>} key="correlations">
          <Card title="资产关联分析">
            <Alert
              message="关联分析功能"
              description="分析资产间的关系和依赖，识别域名组、IP集群和技术栈关联。"
              type="info"
              showIcon
            />
            {/* 这里可以添加关联分析的可视化组件 */}
          </Card>
        </TabPane>
        
        <TabPane tab={<span><SettingOutlined />自定义分析</span>} key="custom-analysis">
          <ConfigurableAnalysis 
            title="高级搜索"
            defaultConfigs={[
              {
                id: 'asset_types',
                name: '资产类型分布',
                field: 'entity_data.asset_type.keyword',
                type: 'terms' as const,
                size: 10,
                visualization: 'pie' as const,
                chart_config: { show_legend: true, height: 300 }
              },
              {
                id: 'confidence_stats',
                name: '置信度统计',
                field: 'entity_data.confidence.keyword',
                type: 'terms' as const,
                size: 5,
                visualization: 'column' as const,
                chart_config: { show_labels: true, height: 250 }
              }
            ]}
          />
        </TabPane>
      </Tabs>

      {/* 保存搜索模态框 */}
      <Modal
        title="保存当前搜索"
        open={showSaveModal}
        onCancel={() => {
          setShowSaveModal(false);
          saveForm.resetFields();
        }}
        footer={null}
      >
        <Form form={saveForm} onFinish={handleSaveSearch} layout="vertical">
          <Form.Item
            name="name"
            label="搜索名称"
            rules={[{ required: true, message: '请输入搜索名称' }]}
          >
            <Input placeholder="输入搜索名称" />
          </Form.Item>
          <Form.Item name="description" label="描述">
            <TextArea placeholder="输入搜索描述（可选）" rows={3} />
          </Form.Item>
          <Form.Item name="is_favorite" valuePropName="checked">
            <Space>
              <StarOutlined />
              <span>设为收藏</span>
            </Space>
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                保存
              </Button>
              <Button onClick={() => setShowSaveModal(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 加载搜索模态框 */}
      <Modal
        title="加载已保存的搜索"
        open={showLoadModal}
        onCancel={() => setShowLoadModal(false)}
        footer={[
          <Button key="close" onClick={() => setShowLoadModal(false)}>
            关闭
          </Button>
        ]}
        width={800}
      >
        <List
          dataSource={savedSearches}
          locale={{ emptyText: '暂无保存的搜索' }}
          renderItem={(item) => (
            <List.Item
              actions={[
                <Button
                  key="favorite"
                  type="text"
                  icon={<StarOutlined />}
                  style={{ color: item.is_favorite ? '#faad14' : '#d9d9d9' }}
                  onClick={() => handleToggleFavorite(item.id)}
                />,
                <Button
                  key="load"
                  type="primary"
                  size="small"
                  onClick={() => handleLoadSearch(item)}
                >
                  加载
                </Button>,
                <Popconfirm
                  key="delete"
                  title="确定删除这个搜索吗？"
                  onConfirm={() => handleDeleteSearch(item.id)}
                >
                  <Button type="text" danger icon={<DeleteOutlined />} size="small" />
                </Popconfirm>
              ]}
            >
              <List.Item.Meta
                title={
                  <Space>
                    {item.is_favorite && <StarOutlined style={{ color: '#faad14' }} />}
                    <Text strong>{item.name}</Text>
                  </Space>
                }
                description={
                  <div>
                    {item.description && <div>{item.description}</div>}
                    <div style={{ fontSize: '12px', color: '#999' }}>
                      创建时间: {dayjs(item.created_at).format('YYYY-MM-DD HH:mm')}
                    </div>
                    <div style={{ marginTop: 4 }}>
                      {item.search_params.query && (
                        <Tag>查询: {item.search_params.query}</Tag>
                      )}
                      {item.search_params.platform_id && (
                        <Tag>平台: {item.search_params.platform_id}</Tag>
                      )}
                      {item.search_params.asset_types && item.search_params.asset_types.length > 0 && (
                        <Tag>类型: {item.search_params.asset_types.join(', ')}</Tag>
                      )}
                    </div>
                  </div>
                }
              />
            </List.Item>
          )}
        />
      </Modal>
    </div>
  );
};

export default AdvancedSearch;