import React, { useState, useEffect } from 'react';
import { Card, Button, Space, Typography, message } from 'antd';
import { agentService, authService } from '../services/api';

const { Text, Paragraph } = Typography;

const DebugAgents: React.FC = () => {
  const [debugInfo, setDebugInfo] = useState<any>({});
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = () => {
    const token = localStorage.getItem('token');
    const user = localStorage.getItem('user');
    
    setDebugInfo({
      hasToken: !!token,
      hasUser: !!user,
      tokenPreview: token ? token.substring(0, 50) + '...' : 'none',
      userInfo: user ? JSON.parse(user) : null,
    });
  };

  const testLogin = async () => {
    try {
      setLoading(true);
      const response = await authService.login('admin', 'password');
      console.log('Login test response:', response);
      message.success('Login test successful');
      checkAuthStatus();
    } catch (error) {
      console.error('Login test failed:', error);
      message.error('Login test failed');
    } finally {
      setLoading(false);
    }
  };

  const testAgentsAPI = async () => {
    try {
      setLoading(true);
      const response = await agentService.getAgents();
      console.log('Agents API response:', response);
      message.success(`Got ${response.data?.agents?.length || 0} agents`);
    } catch (error: any) {
      console.error('Agents API failed:', error);
      message.error(`Agents API failed: ${error.message || 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ padding: 24 }}>
      <Card title="Authentication Debug" style={{ marginBottom: 16 }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <Text strong>Has Token: {debugInfo.hasToken ? 'Yes' : 'No'}</Text>
          <Text strong>Has User: {debugInfo.hasUser ? 'Yes' : 'No'}</Text>
          <Paragraph>
            <Text strong>Token Preview: </Text>
            <Text code>{debugInfo.tokenPreview}</Text>
          </Paragraph>
          <Paragraph>
            <Text strong>User Info: </Text>
            <Text code>{JSON.stringify(debugInfo.userInfo, null, 2)}</Text>
          </Paragraph>
        </Space>
      </Card>

      <Card title="API Tests">
        <Space>
          <Button onClick={testLogin} loading={loading}>
            Test Login
          </Button>
          <Button onClick={testAgentsAPI} loading={loading}>
            Test Agents API
          </Button>
          <Button onClick={checkAuthStatus}>
            Refresh Auth Status
          </Button>
        </Space>
      </Card>
    </div>
  );
};

export default DebugAgents;