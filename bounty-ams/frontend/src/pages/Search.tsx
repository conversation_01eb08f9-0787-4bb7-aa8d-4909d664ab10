import React, { useState, useEffect } from 'react';
import {
  Row,
  Col,
  Card,
  Input,
  Button,
  Select,
  Space,
  Table,
  Tag,
  Typography,
  DatePicker,
  Form,
  message,
  Statistic,
} from 'antd';
import {
  SearchOutlined,
  FilterOutlined,
  ReloadOutlined,
  Bar<PERSON><PERSON>Outlined,
  <PERSON><PERSON><PERSON>Outlined,
  Line<PERSON>hartOutlined,
} from '@ant-design/icons';
import ReactECharts from 'echarts-for-react';
import { assetService } from '../services/api';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;

const Search: React.FC = () => {
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [searchForm] = Form.useForm();
  const [assetStats, setAssetStats] = useState<any>({});
  const [timeline, setTimeline] = useState<any[]>([]);

  useEffect(() => {
    loadAssetStats();
    loadTimeline();
  }, []);

  const loadAssetStats = async () => {
    try {
      const response = await assetService.getAssetStats();
      setAssetStats(response.data);
    } catch (error) {
      console.error('加载统计数据失败:', error);
    }
  };

  const loadTimeline = async () => {
    try {
      const response = await assetService.getAssetTimeline(30);
      setTimeline(response.data.timeline || []);
    } catch (error) {
      console.error('加载时间线数据失败:', error);
    }
  };

  const handleSearch = async (values: any) => {
    try {
      setLoading(true);
      const params = {
        ...values,
        date_from: values.dateRange?.[0]?.format('YYYY-MM-DD') || '',
        date_to: values.dateRange?.[1]?.format('YYYY-MM-DD') || '',
        size: pageSize,
        offset: (currentPage - 1) * pageSize,
      };
      delete params.dateRange;
      
      const response = await assetService.searchAssets(params);
      setSearchResults(response.data.assets || []);
      setTotal(response.data.total || 0);
    } catch (error) {
      message.error('搜索失败');
    } finally {
      setLoading(false);
    }
  };

  const getAssetTypeDistribution = () => {
    const data = Object.entries(assetStats.by_type || {}).map(([key, value]) => ({
      name: key,
      value: value,
    }));

    return {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        bottom: 0,
        left: 'center',
        data: data.map(item => item.name)
      },
      series: [
        {
          name: '资产类型',
          type: 'pie',
          radius: ['40%', '70%'],
          center: ['50%', '40%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 10,
            borderColor: '#fff',
            borderWidth: 2
          },
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: 16,
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          data: data
        }
      ]
    };
  };

  const getSourceDistribution = () => {
    const data = Object.entries(assetStats.by_source || {}).map(([key, value]) => ({
      name: key,
      value: value,
    }));

    return {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      xAxis: {
        type: 'category',
        data: data.map(item => item.name)
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: '资产数量',
          type: 'bar',
          data: data.map(item => item.value),
          itemStyle: {
            color: '#1890ff'
          }
        }
      ]
    };
  };

  const getTimelineChart = () => {
    const dates = timeline.map(item => item.date);
    const totals = timeline.map(item => item.total);

    return {
      tooltip: {
        trigger: 'axis'
      },
      xAxis: {
        type: 'category',
        data: dates
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: '发现资产数',
          type: 'line',
          smooth: true,
          data: totals,
          itemStyle: {
            color: '#1890ff'
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: 'rgba(24, 144, 255, 0.3)' },
                { offset: 1, color: 'rgba(24, 144, 255, 0.1)' }
              ]
            }
          }
        }
      ]
    };
  };

  const columns = [
    {
      title: '资产类型',
      dataIndex: 'asset_type',
      key: 'asset_type',
      render: (type: string) => (
        <Tag color="blue">{type}</Tag>
      ),
    },
    {
      title: '资产值',
      dataIndex: 'asset_value',
      key: 'asset_value',
      render: (value: string) => (
        <Text code>{value}</Text>
      ),
    },
    {
      title: '主机',
      dataIndex: 'asset_host',
      key: 'asset_host',
      render: (host: string) => host || '-',
    },
    {
      title: '置信度',
      dataIndex: 'confidence',
      key: 'confidence',
      render: (confidence: string) => (
        <Tag color={confidence === 'high' ? 'green' : confidence === 'medium' ? 'orange' : 'red'}>
          {confidence}
        </Tag>
      ),
    },
    {
      title: '发现时间',
      dataIndex: 'discovered_at',
      key: 'discovered_at',
      render: (date: string) => (
        <Text type="secondary">
          {dayjs(date).format('YYYY-MM-DD HH:mm')}
        </Text>
      ),
    },
    {
      title: '得分',
      dataIndex: '_score',
      key: '_score',
      render: (score: number) => score?.toFixed(2) || '-',
    },
  ];

  return (
    <div>
      <Row gutter={[16, 16]}>
        <Col span={24}>
          <Title level={3} style={{ margin: 0, marginBottom: 16 }}>
            搜索分析
          </Title>
        </Col>
      </Row>

      {/* 统计概览 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="总资产数"
              value={assetStats.total_assets || 0}
              prefix={<BarChartOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="资产类型"
              value={Object.keys(assetStats.by_type || {}).length}
              prefix={<PieChartOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="发现来源"
              value={Object.keys(assetStats.by_source || {}).length}
              prefix={<FilterOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="今日新增"
              value={timeline.length > 0 ? timeline[timeline.length - 1]?.total || 0 : 0}
              prefix={<LineChartOutlined />}
            />
          </Card>
        </Col>
      </Row>

      {/* 搜索表单 */}
      <Card style={{ marginBottom: 16 }}>
        <Form
          form={searchForm}
          onFinish={handleSearch}
          layout="vertical"
        >
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12} md={8}>
              <Form.Item name="q" label="搜索关键词">
                <Input
                  placeholder="输入搜索关键词..."
                  prefix={<SearchOutlined />}
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={8}>
              <Form.Item name="asset_type" label="资产类型">
                <Select placeholder="选择资产类型" allowClear>
                  {Object.keys(assetStats.by_type || {}).map(type => (
                    <Option key={type} value={type}>
                      {type}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={8}>
              <Form.Item name="confidence" label="置信度">
                <Select placeholder="选择置信度" allowClear>
                  <Option value="high">高</Option>
                  <Option value="medium">中</Option>
                  <Option value="low">低</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={8}>
              <Form.Item name="target" label="目标">
                <Input placeholder="输入目标" />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={8}>
              <Form.Item name="dateRange" label="时间范围">
                <RangePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={8}>
              <Form.Item label=" ">
                <Space>
                  <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
                    搜索
                  </Button>
                  <Button onClick={() => searchForm.resetFields()}>
                    重置
                  </Button>
                </Space>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Card>

      {/* 图表展示 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
        <Col xs={24} lg={8}>
          <Card title="资产类型分布">
            <ReactECharts 
              option={getAssetTypeDistribution()} 
              style={{ height: 300 }}
            />
          </Card>
        </Col>
        <Col xs={24} lg={8}>
          <Card title="发现来源分布">
            <ReactECharts 
              option={getSourceDistribution()} 
              style={{ height: 300 }}
            />
          </Card>
        </Col>
        <Col xs={24} lg={8}>
          <Card title="发现趋势">
            <ReactECharts 
              option={getTimelineChart()} 
              style={{ height: 300 }}
            />
          </Card>
        </Col>
      </Row>

      {/* 搜索结果 */}
      {searchResults.length > 0 && (
        <Card title={`搜索结果 (${total})`}>
          <Table
            columns={columns}
            dataSource={searchResults}
            loading={loading}
            rowKey="_id"
            pagination={{
              current: currentPage,
              pageSize: pageSize,
              total: total,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) =>
                `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
              onChange: (page, size) => {
                setCurrentPage(page);
                setPageSize(size!);
              },
            }}
          />
        </Card>
      )}
    </div>
  );
};

export default Search;