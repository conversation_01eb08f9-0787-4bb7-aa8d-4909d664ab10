import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Select,
  Switch,
  Tag,
  Typography,
  Row,
  Col,
  Divider,
  InputNumber,
  message,
  Tabs,
  List,
  Avatar,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  SettingOutlined,
  DatabaseOutlined,
  FormOutlined,
  BgColorsOutlined,
  SyncOutlined,
} from '@ant-design/icons';
import { dynamicModelService } from '../services/api';

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;
const { TabPane } = Tabs;

interface ModelField {
  id?: string;
  field_name: string;
  field_type: string;
  display_name: string;
  description?: string;
  is_required: boolean;
  is_searchable: boolean;
  is_filterable: boolean;
  is_unique: boolean;
  default_value?: any;
  validation_rules?: Record<string, any>;
  field_options?: any[];
  sort_order: number;
}

interface ModelType {
  id: string;
  name: string;
  display_name: string;
  description?: string;
  icon?: string;
  color?: string;
  is_active: boolean;
  is_system: boolean;
  fields: ModelField[];
  created_at: string;
  updated_at?: string;
  entity_count?: number;
}

interface DynamicEntity {
  id: string;
  model_type_id: string;
  entity_data: Record<string, any>;
  created_at: string;
  updated_at?: string;
}

const Models: React.FC = () => {
  const [modelTypes, setModelTypes] = useState<ModelType[]>([]);
  const [entities, setEntities] = useState<DynamicEntity[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [entityModalVisible, setEntityModalVisible] = useState(false);
  const [editingModel, setEditingModel] = useState<ModelType | null>(null);
  const [selectedModelType, setSelectedModelType] = useState<ModelType | null>(null);
  const [activeTab, setActiveTab] = useState('models');
  const [form] = Form.useForm();
  const [entityForm] = Form.useForm();

  // 支持的字段类型
  const [fieldTypes, setFieldTypes] = useState<any[]>([]);

  useEffect(() => {
    loadModelTypes();
    loadFieldTypes();
  }, []);

  useEffect(() => {
    if (selectedModelType) {
      loadEntities(selectedModelType.id);
    }
  }, [selectedModelType]);

  const loadModelTypes = async () => {
    setLoading(true);
    try {
      const response = await dynamicModelService.getModelTypes();
      // API返回的是直接的数组，不是包装在data属性中
      const modelTypes = response.data || response || [];
      
      // 为每个模型类型加载实体数量
      const modelTypesWithCounts = await Promise.all(
        modelTypes.map(async (modelType: ModelType) => {
          try {
            const entitiesResponse = await dynamicModelService.getEntities({ model_type_id: modelType.id });
            const entities = entitiesResponse.data || entitiesResponse || [];
            return {
              ...modelType,
              entity_count: entities.length
            };
          } catch (error) {
            console.error(`Error loading entity count for ${modelType.name}:`, error);
            return {
              ...modelType,
              entity_count: 0
            };
          }
        })
      );
      
      setModelTypes(modelTypesWithCounts);
    } catch (error) {
      console.error('Error loading model types:', error);
      message.error('加载模型类型失败');
    } finally {
      setLoading(false);
    }
  };

  const loadFieldTypes = async () => {
    try {
      const response = await dynamicModelService.getFieldTypes();
      // API返回的是直接的数组，不是包装在data属性中
      setFieldTypes(response.data || response || []);
    } catch (error) {
      console.error('Error loading field types:', error);
    }
  };

  const loadEntities = async (modelTypeId: string) => {
    try {
      console.log('Loading entities for model type:', modelTypeId);
      const response = await dynamicModelService.getEntities({ model_type_id: modelTypeId });
      // API返回的是直接的数组，不是包装在data属性中
      const entities = response.data || response || [];
      console.log('Loaded entities:', entities);
      setEntities(entities);
    } catch (error) {
      console.error('Error loading entities:', error);
      message.error('加载实体数据失败');
    }
  };

  const handleAddModel = () => {
    setEditingModel(null);
    form.resetFields();
    form.setFieldsValue({
      fields: [{
        field_name: '',
        field_type: 'text',
        display_name: '',
        is_required: true,
        is_searchable: true,
        is_filterable: false,
        is_unique: false,
        sort_order: 1,
      }]
    });
    setModalVisible(true);
  };

  const handleEditModel = (record: ModelType) => {
    setEditingModel(record);
    form.setFieldsValue(record);
    setModalVisible(true);
  };

  const handleDeleteModel = (id: string) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个模型类型吗？这将同时删除所有相关的实体数据。',
      onOk: async () => {
        try {
          await dynamicModelService.deleteModelType(id);
          setModelTypes(modelTypes.filter(m => m.id !== id));
          message.success('删除成功');
        } catch (error) {
          console.error('Error deleting model type:', error);
          message.error('删除失败');
        }
      },
    });
  };

  const handleSubmitModel = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);
      
      if (editingModel) {
        // 更新
        const response = await dynamicModelService.updateModelType(editingModel.id, values);
        const updatedModel = response.data || response;
        setModelTypes(modelTypes.map(m => 
          m.id === editingModel.id ? updatedModel : m
        ));
        message.success('更新成功');
      } else {
        // 新增
        const response = await dynamicModelService.createModelType(values);
        const newModel = response.data || response;
        setModelTypes([...modelTypes, newModel]);
        message.success('创建成功');
      }
      
      setModalVisible(false);
    } catch (error) {
      console.error('提交失败:', error);
      message.error(editingModel ? '更新失败' : '创建失败');
    } finally {
      setLoading(false);
    }
  };

  const handleAddEntity = () => {
    if (!selectedModelType) return;
    entityForm.resetFields();
    setEntityModalVisible(true);
  };

  const handleSubmitEntity = async () => {
    try {
      const values = await entityForm.validateFields();
      setLoading(true);
      
      const entityData = {
        model_type_id: selectedModelType!.id,
        entity_data: values,
      };
      
      const response = await dynamicModelService.createEntity(entityData);
      const newEntity = response.data || response;
      setEntities([...entities, newEntity]);
      setEntityModalVisible(false);
      message.success('创建实体成功');
    } catch (error) {
      console.error('创建实体失败:', error);
      message.error('创建实体失败');
    } finally {
      setLoading(false);
    }
  };

  const modelColumns = [
    {
      title: '模型名称',
      dataIndex: 'display_name',
      key: 'display_name',
      render: (text: string, record: ModelType) => (
        <Space>
          <Avatar 
            style={{ backgroundColor: record.color || '#1890ff' }}
            icon={<DatabaseOutlined />}
          />
          <div>
            <Text strong>{text}</Text>
            <br />
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {record.name}
            </Text>
          </div>
        </Space>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      render: (text: string) => text || '暂无描述',
    },
    {
      title: '字段信息',
      dataIndex: 'fields',
      key: 'field_info',
      render: (fields: ModelField[]) => (
        <div>
          <div style={{ marginBottom: 4 }}>
            <Tag color="blue">{fields?.length || 0} 个字段</Tag>
          </div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {fields?.slice(0, 3).map(field => field.display_name).join(', ')}
            {fields?.length > 3 && '...'}
          </div>
        </div>
      ),
    },
    {
      title: '实体数量',
      dataIndex: 'entity_count',
      key: 'entity_count',
      render: (count: number) => (
        <Tag color="green">{count || 0} 个实体</Tag>
      ),
    },
    {
      title: '状态',
      dataIndex: 'is_active',
      key: 'is_active',
      render: (isActive: boolean, record: ModelType) => (
        <Space>
          <Tag color={isActive ? 'success' : 'default'}>
            {isActive ? '启用' : '禁用'}
          </Tag>
          {record.is_system && <Tag color="orange">系统</Tag>}
        </Space>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date: string) => new Date(date).toLocaleString(),
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: ModelType) => (
        <Space>
          <Button
            type="link"
            icon={<EyeOutlined />}
            onClick={() => {
              setSelectedModelType(record);
              setActiveTab('entities');
            }}
          >
            查看实体
          </Button>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEditModel(record)}
            disabled={record.is_system}
          >
            编辑
          </Button>
          <Button
            type="link"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDeleteModel(record.id)}
            disabled={record.is_system}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  const entityColumns = [...(selectedModelType?.fields.map(field => ({
    title: field.display_name,
    dataIndex: ['entity_data', field.field_name],
    key: field.field_name,
    render: (value: any) => {
      if (field.field_type === 'boolean') {
        return <Tag color={value ? 'success' : 'default'}>{value ? '是' : '否'}</Tag>;
      }
      if (field.field_type === 'select' && field.field_options) {
        const option = field.field_options.find((opt: any) => opt.value === value);
        return option ? option.label : value || '-';
      }
      if (field.field_type === 'date') {
        return value ? new Date(value).toLocaleDateString() : '-';
      }
      if (field.field_type === 'datetime') {
        return value ? new Date(value).toLocaleString() : '-';
      }
      return value || '-';
    },
  })) || []), {
    title: '创建时间',
    dataIndex: 'created_at',
    key: 'created_at',
    render: (date: string) => new Date(date).toLocaleString(),
  }, {
    title: '操作',
    key: 'action',
    render: (_: any, record: DynamicEntity) => (
      <Space>
        <Button
          type="link"
          icon={<EditOutlined />}
          size="small"
        >
          编辑
        </Button>
        <Button
          type="link"
          danger
          icon={<DeleteOutlined />}
          size="small"
          onClick={() => {
            Modal.confirm({
              title: '确认删除',
              content: '确定要删除这个实体吗？',
              onOk: async () => {
                try {
                  await dynamicModelService.deleteEntity(record.id);
                  setEntities(entities.filter(e => e.id !== record.id));
                  message.success('删除成功');
                } catch (error) {
                  console.error('Error deleting entity:', error);
                  message.error('删除失败');
                }
              },
            });
          }}
        >
          删除
        </Button>
      </Space>
    ),
  }];

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px' }}>
        <Title level={2}>
          <FormOutlined /> 动态模型管理
        </Title>
        <Paragraph type="secondary">
          创建和管理自定义数据模型，支持动态字段定义和实体数据管理。
        </Paragraph>
      </div>

      <Card>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab="模型类型" key="models">
            <div style={{ marginBottom: '16px' }}>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleAddModel}
              >
                创建模型类型
              </Button>
            </div>

            <Table
              columns={modelColumns}
              dataSource={modelTypes}
              rowKey="id"
              loading={loading}
              pagination={{
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 个模型类型`,
              }}
            />
          </TabPane>

          <TabPane 
            tab={
              <span>
                实体数据
                {selectedModelType && (
                  <Tag style={{ marginLeft: 8 }} color="blue">
                    {selectedModelType.display_name} ({entities.length})
                  </Tag>
                )}
              </span>
            } 
            key="entities"
          >
            {selectedModelType ? (
              <>
                <div style={{ marginBottom: '16px' }}>
                  <Row justify="space-between" align="middle">
                    <Col>
                      <Space>
                        <Button
                          type="primary"
                          icon={<PlusOutlined />}
                          onClick={handleAddEntity}
                        >
                          添加实体
                        </Button>
                        <Text type="secondary">
                          当前模型: {selectedModelType.display_name} | 共 {entities.length} 个实体
                        </Text>
                      </Space>
                    </Col>
                    <Col>
                      <Space>
                        <Button
                          icon={<SyncOutlined />}
                          onClick={() => loadEntities(selectedModelType.id)}
                        >
                          刷新
                        </Button>
                      </Space>
                    </Col>
                  </Row>
                </div>

                <Table
                  columns={entityColumns}
                  dataSource={entities}
                  rowKey="id"
                  loading={loading}
                  locale={{
                    emptyText: (
                      <div style={{ textAlign: 'center', padding: '40px' }}>
                        <Text type="secondary">
                          {selectedModelType ? 
                            `暂无${selectedModelType.display_name}实体数据，点击上方"添加实体"按钮创建第一个实体` : 
                            '暂无数据'
                          }
                        </Text>
                      </div>
                    )
                  }}
                  pagination={{
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 个实体`,
                  }}
                />
              </>
            ) : (
              <div style={{ textAlign: 'center', padding: '50px' }}>
                <Text type="secondary">请先选择一个模型类型查看实体数据</Text>
              </div>
            )}
          </TabPane>
        </Tabs>
      </Card>

      {/* 创建/编辑模型类型弹窗 */}
      <Modal
        title={editingModel ? '编辑模型类型' : '创建模型类型'}
        open={modalVisible}
        onOk={handleSubmitModel}
        onCancel={() => setModalVisible(false)}
        confirmLoading={loading}
        width={800}
      >
        <Form form={form} layout="vertical">
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="模型名称(英文)"
                rules={[
                  { required: true, message: '请输入模型名称' },
                  { pattern: /^[a-z_][a-z0-9_]*$/, message: '只能使用小写字母、数字和下划线' }
                ]}
              >
                <Input placeholder="例如: custom_asset" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="display_name"
                label="显示名称"
                rules={[{ required: true, message: '请输入显示名称' }]}
              >
                <Input placeholder="例如: 自定义资产" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="description" label="描述">
            <TextArea rows={3} placeholder="模型类型的详细描述" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="icon" label="图标">
                <Input placeholder="例如: database" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="color" label="颜色">
                <Input type="color" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="is_active" label="启用状态" valuePropName="checked">
            <Switch />
          </Form.Item>

          <Divider orientation="left">字段定义</Divider>

          <Form.List name="fields">
            {(fields, { add, remove }) => (
              <>
                {fields.map(({ key, name, ...restField }) => (
                  <Card key={key} size="small" style={{ marginBottom: 16 }}>
                    <Row gutter={16}>
                      <Col span={8}>
                        <Form.Item
                          {...restField}
                          name={[name, 'field_name']}
                          label="字段名"
                          rules={[{ required: true, message: '请输入字段名' }]}
                        >
                          <Input placeholder="字段名(英文)" />
                        </Form.Item>
                      </Col>
                      <Col span={8}>
                        <Form.Item
                          {...restField}
                          name={[name, 'display_name']}
                          label="显示名"
                          rules={[{ required: true, message: '请输入显示名' }]}
                        >
                          <Input placeholder="显示名" />
                        </Form.Item>
                      </Col>
                      <Col span={8}>
                        <Form.Item
                          {...restField}
                          name={[name, 'field_type']}
                          label="字段类型"
                          rules={[{ required: true, message: '请选择字段类型' }]}
                        >
                          <Select>
                            {fieldTypes.map(type => (
                              <Select.Option key={type.type} value={type.type}>
                                {type.display_name}
                              </Select.Option>
                            ))}
                          </Select>
                        </Form.Item>
                      </Col>
                    </Row>

                    <Row gutter={16}>
                      <Col span={12}>
                        <Form.Item
                          {...restField}
                          name={[name, 'description']}
                          label="描述"
                        >
                          <Input placeholder="字段描述" />
                        </Form.Item>
                      </Col>
                      <Col span={12}>
                        <Form.Item
                          {...restField}
                          name={[name, 'sort_order']}
                          label="排序"
                        >
                          <InputNumber min={1} style={{ width: '100%' }} />
                        </Form.Item>
                      </Col>
                    </Row>

                    <Row gutter={16}>
                      <Col span={6}>
                        <Form.Item
                          {...restField}
                          name={[name, 'is_required']}
                          valuePropName="checked"
                        >
                          <Switch checkedChildren="必填" unCheckedChildren="可选" />
                        </Form.Item>
                      </Col>
                      <Col span={6}>
                        <Form.Item
                          {...restField}
                          name={[name, 'is_searchable']}
                          valuePropName="checked"
                        >
                          <Switch checkedChildren="可搜索" unCheckedChildren="不可搜索" />
                        </Form.Item>
                      </Col>
                      <Col span={6}>
                        <Form.Item
                          {...restField}
                          name={[name, 'is_filterable']}
                          valuePropName="checked"
                        >
                          <Switch checkedChildren="可筛选" unCheckedChildren="不可筛选" />
                        </Form.Item>
                      </Col>
                      <Col span={6}>
                        <Button
                          type="link"
                          danger
                          onClick={() => remove(name)}
                        >
                          删除字段
                        </Button>
                      </Col>
                    </Row>
                  </Card>
                ))}
                <Form.Item>
                  <Button
                    type="dashed"
                    onClick={() => add()}
                    block
                    icon={<PlusOutlined />}
                  >
                    添加字段
                  </Button>
                </Form.Item>
              </>
            )}
          </Form.List>
        </Form>
      </Modal>

      {/* 添加实体弹窗 */}
      <Modal
        title={`添加${selectedModelType?.display_name}实体`}
        open={entityModalVisible}
        onOk={handleSubmitEntity}
        onCancel={() => setEntityModalVisible(false)}
        confirmLoading={loading}
      >
        <Form form={entityForm} layout="vertical">
          {selectedModelType?.fields.map(field => (
            <Form.Item
              key={field.field_name}
              name={field.field_name}
              label={field.display_name}
              rules={[
                { required: field.is_required, message: `请输入${field.display_name}` }
              ]}
            >
              {field.field_type === 'select' ? (
                <Select placeholder={`请选择${field.display_name}`}>
                  {field.field_options?.map(option => (
                    <Select.Option key={option} value={option}>
                      {option}
                    </Select.Option>
                  ))}
                </Select>
              ) : field.field_type === 'boolean' ? (
                <Switch />
              ) : field.field_type === 'number' ? (
                <InputNumber style={{ width: '100%' }} />
              ) : field.field_type === 'textarea' ? (
                <TextArea rows={3} />
              ) : (
                <Input placeholder={field.description} />
              )}
            </Form.Item>
          ))}
        </Form>
      </Modal>
    </div>
  );
};

export default Models;