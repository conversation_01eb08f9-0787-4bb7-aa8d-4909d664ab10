import React from 'react';
import { Card, Typography, Table, Tag } from 'antd';

const { Title } = Typography;

const AgentsSimple: React.FC = () => {
  const mockData = [
    {
      key: '1',
      id: 'agent-001',
      name: 'Go Agent 1',
      status: 'online',
      lastSeen: '2024-01-10 10:30:00',
      tasks: 3,
    },
    {
      key: '2', 
      id: 'agent-002',
      name: 'Go Agent 2',
      status: 'offline',
      lastSeen: '2024-01-10 09:45:00',
      tasks: 0,
    },
  ];

  const columns = [
    {
      title: 'Agent ID',
      dataIndex: 'id',
      key: 'id',
    },
    {
      title: 'Agent名称',
      dataIndex: 'name', 
      key: 'name',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={status === 'online' ? 'green' : 'red'}>
          {status === 'online' ? '在线' : '离线'}
        </Tag>
      ),
    },
    {
      title: '最后见到时间',
      dataIndex: 'lastSeen',
      key: 'lastSeen',
    },
    {
      title: '当前任务数',
      dataIndex: 'tasks',
      key: 'tasks',
    },
  ];

  return (
    <div>
      <Title level={2}>Agent管理</Title>
      <Card>
        <Table 
          columns={columns} 
          dataSource={mockData}
          pagination={false}
        />
      </Card>
    </div>
  );
};

export default AgentsSimple;