import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Statistic, Typography, Spin, message } from 'antd';
import { DatabaseOutlined, SecurityScanOutlined, ClusterOutlined, ApiOutlined } from '@ant-design/icons';
import { assetService, taskService, agentService } from '../services/api';

const { Title } = Typography;

interface DashboardStats {
  totalAssets: number;
  totalTasks: number;
  activeAgents: number;
  highConfidenceAssets: number;
}

const SimpleDashboard: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState<DashboardStats>({
    totalAssets: 0,
    totalTasks: 0,
    activeAgents: 0,
    highConfidenceAssets: 0
  });

  useEffect(() => {
    loadDashboardStats();
  }, []);

  const loadDashboardStats = async () => {
    try {
      setLoading(true);
      
      // 并行加载各种统计数据
      const [assetStatsResponse, taskStatsResponse, agentsResponse] = await Promise.all([
        assetService.getAssetStats().catch(() => ({ data: {} })),
        taskService.getTaskStats().catch(() => ({ data: {} })),
        agentService.getAgents().catch(() => ({ data: { agents: [] } }))
      ]);

      const assetStats = assetStatsResponse.data || {};
      const taskStats = taskStatsResponse.data || {};
      const agents = agentsResponse.data?.agents || [];

      setStats({
        totalAssets: assetStats.total || 0,
        totalTasks: taskStats.total || 0,
        activeAgents: agents.filter((agent: any) => agent.status === 'active').length,
        highConfidenceAssets: assetStats.by_confidence?.high || 0
      });

    } catch (error) {
      console.error('加载仪表板统计数据失败:', error);
      message.error('加载数据失败');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div style={{ padding: '24px', textAlign: 'center' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>加载中...</div>
      </div>
    );
  }

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>简单仪表板</Title>
      
      <Row gutter={[16, 16]}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总资产数"
              value={stats.totalAssets}
              prefix={<DatabaseOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="高置信度资产"
              value={stats.highConfidenceAssets}
              prefix={<SecurityScanOutlined />}
              valueStyle={{ color: '#cf1322' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="活跃Agent"
              value={stats.activeAgents}
              prefix={<ClusterOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="任务总数"
              value={stats.totalTasks}
              prefix={<ApiOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>
      
      <Row gutter={[16, 16]} style={{ marginTop: '24px' }}>
        <Col span={12}>
          <Card title="资产概览">
            <div style={{ 
              height: '300px', 
              display: 'flex', 
              alignItems: 'center', 
              justifyContent: 'center',
              flexDirection: 'column'
            }}>
              <DatabaseOutlined style={{ fontSize: 48, color: '#1890ff', marginBottom: 16 }} />
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: 24, fontWeight: 'bold', marginBottom: 8 }}>
                  {stats.totalAssets}
                </div>
                <div style={{ color: '#666' }}>个资产已发现</div>
              </div>
            </div>
          </Card>
        </Col>
        <Col span={12}>
          <Card title="系统状态">
            <div style={{ 
              height: '300px', 
              display: 'flex', 
              alignItems: 'center', 
              justifyContent: 'center',
              flexDirection: 'column'
            }}>
              <ClusterOutlined style={{ fontSize: 48, color: '#52c41a', marginBottom: 16 }} />
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: 24, fontWeight: 'bold', marginBottom: 8 }}>
                  {stats.activeAgents}
                </div>
                <div style={{ color: '#666' }}>个Agent在线</div>
              </div>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default SimpleDashboard;