import React, { useState, useEffect } from 'react';
import {
  Row,
  Col,
  Card,
  Table,
  Button,
  Space,
  Tag,
  Typography,
  Modal,
  Form,
  Input,
  Select,
  message,
  Drawer,
  Timeline,
  Progress,
} from 'antd';
import {
  PlusOutlined,
  PlayCircleOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
  NodeIndexOutlined,
  CheckCircleOutlined,
  SyncOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';
import { workflowService } from '../services/api';
import LoadingSkeleton from '../components/LoadingSkeleton';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { Option } = Select;

const Workflows: React.FC = () => {
  const [workflows, setWorkflows] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [selectedWorkflow, setSelectedWorkflow] = useState<any>(null);
  const [executeModalVisible, setExecuteModalVisible] = useState(false);
  const [form] = Form.useForm();
  const [executeForm] = Form.useForm();

  useEffect(() => {
    loadWorkflows();
  }, []);

  const loadWorkflows = async () => {
    try {
      setLoading(true);
      // 使用Promise.race实现快速失败
      const timeoutPromise = new Promise((_, reject) => 
        setTimeout(() => reject(new Error('请求超时')), 3000)
      );
      
      const response = await Promise.race([
        workflowService.getWorkflows(),
        timeoutPromise
      ]);
      
      // API可能直接返回workflows数组或包装在data中
      const workflowsData = (response as any).data?.workflows || (response as any).data || (response as any) || [];
      setWorkflows(workflowsData);
      
      if (workflowsData.length === 0) {
        console.log('No workflows found');
      }
    } catch (error: any) {
      console.error('Error loading workflows:', error);
      if (error.message === '请求超时' || error.code === 'ECONNABORTED') {
        message.warning('连接超时，可能后端服务未启动或网络问题');
      } else {
        message.error('加载工作流失败');
      }
      // 即使失败也设置空数组，避免页面一直loading
      setWorkflows([]);
    } finally {
      setLoading(false);
    }
  };

  const handleCreate = async (values: any) => {
    try {
      await workflowService.createWorkflow(values);
      message.success('创建工作流成功');
      setModalVisible(false);
      form.resetFields();
      loadWorkflows();
    } catch (error) {
      message.error('创建工作流失败');
    }
  };

  const handleExecute = async (values: any) => {
    try {
      await workflowService.executeWorkflow(selectedWorkflow.workflow_id, values.target);
      message.success('工作流执行成功');
      setExecuteModalVisible(false);
      executeForm.resetFields();
      loadWorkflows();
    } catch (error) {
      message.error('工作流执行失败');
    }
  };

  const handleViewDetails = async (workflow: any) => {
    try {
      const response = await workflowService.getWorkflowDetails(workflow.workflow_id);
      setSelectedWorkflow(response.data);
      setDrawerVisible(true);
    } catch (error) {
      message.error('加载工作流详情失败');
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'running':
        return <SyncOutlined spin style={{ color: '#1890ff' }} />;
      case 'pending':
        return <ClockCircleOutlined style={{ color: '#faad14' }} />;
      case 'failed':
        return <ExclamationCircleOutlined style={{ color: '#f5222d' }} />;
      default:
        return <ClockCircleOutlined style={{ color: '#d9d9d9' }} />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'running':
        return 'processing';
      case 'pending':
        return 'warning';
      case 'failed':
        return 'error';
      default:
        return 'default';
    }
  };

  const columns = [
    {
      title: '工作流ID',
      dataIndex: 'workflow_id',
      key: 'workflow_id',
      render: (id: string) => (
        <Text code>{id}</Text>
      ),
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      render: (desc: string) => desc || '-',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={getStatusColor(status)} icon={getStatusIcon(status)}>
          {status}
        </Tag>
      ),
    },
    {
      title: '任务数量',
      dataIndex: 'task_count',
      key: 'task_count',
      render: (count: number) => count || 0,
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date: string) => (
        <Text type="secondary">
          {dayjs(date).format('YYYY-MM-DD HH:mm')}
        </Text>
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: (record: any) => (
        <Space>
          <Button
            type="text"
            icon={<PlayCircleOutlined />}
            onClick={() => {
              setSelectedWorkflow(record);
              setExecuteModalVisible(true);
            }}
          >
            执行
          </Button>
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => handleViewDetails(record)}
          >
            详情
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Row gutter={[16, 16]}>
        <Col span={24}>
          <Space align="center" style={{ marginBottom: 16 }}>
            <Title level={3} style={{ margin: 0 }}>
              工作流管理
            </Title>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setModalVisible(true)}
            >
              创建工作流
            </Button>
          </Space>
        </Col>
      </Row>

      <LoadingSkeleton type="table" loading={loading} rows={6}>
        <Card>
          <Table
            columns={columns}
            dataSource={workflows}
            loading={false}
            rowKey="workflow_id"
            pagination={{
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) =>
                `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            }}
            locale={{
              emptyText: workflows.length === 0 ? '暂无工作流数据，可能后端服务未配置' : '暂无数据'
            }}
          />
        </Card>
      </LoadingSkeleton>

      {/* 创建工作流弹窗 */}
      <Modal
        title="创建工作流"
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
      >
        <Form form={form} onFinish={handleCreate} layout="vertical">
          <Form.Item
            name="name"
            label="工作流名称"
            rules={[{ required: true, message: '请输入工作流名称' }]}
          >
            <Input placeholder="输入工作流名称" />
          </Form.Item>
          <Form.Item
            name="description"
            label="描述"
          >
            <Input.TextArea placeholder="输入工作流描述" rows={3} />
          </Form.Item>
          <Form.Item
            name="tasks"
            label="任务配置"
            rules={[{ required: true, message: '请配置任务' }]}
          >
            <Select
              mode="multiple"
              placeholder="选择任务类型"
              style={{ width: '100%' }}
            >
              <Option value="subdomain_discovery">子域名发现</Option>
              <Option value="port_scanning">端口扫描</Option>
              <Option value="service_detection">服务检测</Option>
            </Select>
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                创建
              </Button>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 执行工作流弹窗 */}
      <Modal
        title="执行工作流"
        open={executeModalVisible}
        onCancel={() => setExecuteModalVisible(false)}
        footer={null}
      >
        <Form form={executeForm} onFinish={handleExecute} layout="vertical">
          <Form.Item
            name="target"
            label="目标"
            rules={[{ required: true, message: '请输入目标' }]}
          >
            <Input placeholder="输入目标域名或IP" />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                执行
              </Button>
              <Button onClick={() => setExecuteModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 工作流详情抽屉 */}
      <Drawer
        title="工作流详情"
        width={600}
        onClose={() => setDrawerVisible(false)}
        open={drawerVisible}
      >
        {selectedWorkflow && (
          <div>
            <Card title="基本信息" size="small" style={{ marginBottom: 16 }}>
              <Row gutter={[16, 16]}>
                <Col span={12}>
                  <Text strong>工作流ID:</Text>
                  <br />
                  <Text code>{selectedWorkflow.workflow_id}</Text>
                </Col>
                <Col span={12}>
                  <Text strong>名称:</Text>
                  <br />
                  <Text>{selectedWorkflow.name}</Text>
                </Col>
                <Col span={12}>
                  <Text strong>状态:</Text>
                  <br />
                  <Tag color={getStatusColor(selectedWorkflow.status)}>
                    {selectedWorkflow.status}
                  </Tag>
                </Col>
                <Col span={12}>
                  <Text strong>任务数量:</Text>
                  <br />
                  <Text>{selectedWorkflow.task_count || 0}</Text>
                </Col>
                <Col span={24}>
                  <Text strong>描述:</Text>
                  <br />
                  <Text>{selectedWorkflow.description || '无描述'}</Text>
                </Col>
              </Row>
            </Card>

            {selectedWorkflow.tasks && selectedWorkflow.tasks.length > 0 && (
              <Card title="任务执行时间线" size="small">
                <Timeline>
                  {selectedWorkflow.tasks.map((task: any, index: number) => (
                    <Timeline.Item
                      key={task.task_id}
                      color={getStatusColor(task.status)}
                      dot={getStatusIcon(task.status)}
                    >
                      <div>
                        <Text strong>{task.task_type}</Text>
                        <br />
                        <Text type="secondary">
                          {task.status} - {dayjs(task.created_at).format('YYYY-MM-DD HH:mm:ss')}
                        </Text>
                        {task.execution_time && (
                          <>
                            <br />
                            <Text type="secondary">
                              执行时间: {task.execution_time}s
                            </Text>
                          </>
                        )}
                      </div>
                    </Timeline.Item>
                  ))}
                </Timeline>
              </Card>
            )}
          </div>
        )}
      </Drawer>
    </div>
  );
};

export default Workflows;