import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { 
  Card, 
  Row, 
  Col, 
  Button, 
  Modal, 
  Form, 
  Input, 
  Select, 
  Space, 
  Table, 
  Tag, 
  message,
  Popconfirm,
  Divider,
  Drawer,
  Switch,
  Slider,
  ColorPicker,
  Typography,
  Spin
} from 'antd';
import { 
  PlusOutlined, 
  EditOutlined, 
  DeleteOutlined, 
  CopyOutlined,
  SettingOutlined,
  <PERSON>Outlined,
  <PERSON><PERSON><PERSON>Outlined,
  <PERSON><PERSON><PERSON>Outlined,
  Line<PERSON><PERSON>Outlined,
  TableOutlined,
  NumberOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { Pie, Column, Line, Area } from '@ant-design/plots';
import { enhancedSearchService, dashboardService } from '../services/api';
import { OptimizedApiService } from '../services/optimizedApi';

// Widget内容组件 - 正确实现避免性能问题
const WidgetContent = React.memo<{ 
  widget: Widget; 
  widgetTemplates: any;
}>(({ widget, widgetTemplates }) => {
  const { type, config } = widget;
  const [widgetData, setWidgetData] = useState<any>(null);
  const [widgetLoading, setWidgetLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 使用useMemo优化配置生成
  const widgetConfig = useMemo(() => {
    if (widget.data_source.query?.template) {
      const template = widgetTemplates[widget.data_source.query.template];
      if (!template) {
        return null;
      }
      return {
        ...template,
        ...widget.data_source.query
      };
    }
    return widget.data_source.query || {};
  }, [widget.data_source.query, widgetTemplates]);

  // 使用useCallback优化数据获取函数
  const fetchData = useCallback(async () => {
    if (!widgetConfig) {
      setError('配置无效');
      return;
    }

    setWidgetLoading(true);
    setError(null);
    try {
      const response = await OptimizedApiService.loadWidgetData(widgetConfig);
      setWidgetData(response.data);
    } catch (err) {
      console.error('获取组件数据失败:', err);
      setError('数据加载失败');
    } finally {
      setWidgetLoading(false);
    }
  }, [widgetConfig]);

  // 数据加载副作用
  useEffect(() => {
    if (widgetConfig && Object.keys(widgetTemplates).length > 0) {
      fetchData();
    }
  }, [fetchData, widgetTemplates]);

  if (widgetLoading) {
    return (
      <div style={{ textAlign: 'center', padding: 40 }}>
        <Spin size="large" />
      </div>
    );
  }

  if (error) {
    return (
      <div style={{ textAlign: 'center', padding: 40, color: '#ff4d4f' }}>
        {error}
      </div>
    );
  }

  if (!widgetData) {
    return (
      <div style={{ textAlign: 'center', padding: 40, color: '#999' }}>
        暂无数据
      </div>
    );
  }

  switch (type) {
    case 'statistic':
      const statValue = widgetData.type === 'statistics' 
        ? widgetData.data.count 
        : widgetData.total || 0;
      return (
        <div style={{ textAlign: 'center', padding: 20 }}>
          <div style={{ fontSize: 32, fontWeight: 'bold', color: '#1890ff' }}>
            {statValue.toLocaleString()}
          </div>
          <div style={{ color: '#666', marginTop: 8 }}>
            {widget.title}
          </div>
        </div>
      );

    case 'chart':
      switch (config.chart_type) {
        case 'pie':
          const pieData = widgetData.type === 'terms' 
            ? widgetData.data.map((item: any) => ({ type: item.name, value: item.value }))
            : [];
          return (
            <Pie
              data={pieData}
              angleField="value"
              colorField="type"
              radius={0.8}
              label={{ type: 'outer', content: '{name}: {percentage}' }}
              height={config.height || 250}
              legend={config.show_legend !== false ? { position: 'bottom' } : false}
            />
          );
        case 'column':
          const columnData = widgetData.type === 'terms'
            ? widgetData.data.map((item: any) => ({ category: item.name, count: item.value }))
            : [];
          return (
            <Column
              data={columnData}
              xField="category"
              yField="count"
              height={config.height || 250}
              label={config.show_label ? { position: 'middle' } : false}
            />
          );
        case 'line':
          const lineData = widgetData.type === 'time_series'
            ? widgetData.data.map((item: any) => ({ date: item.date, count: item.value }))
            : [];
          return (
            <Line
              data={lineData}
              xField="date"
              yField="count"
              height={config.height || 250}
              point={{ size: 5, shape: 'diamond' }}
            />
          );
        default:
          return <div>暂不支持的图表类型</div>;
      }

    case 'table':
      const tableData = widgetData.type === 'search_results' 
        ? widgetData.data.slice(0, 10)
        : [];
      const tableColumns = [
        { title: '资产', dataIndex: 'asset_value', key: 'asset_value' },
        { title: '类型', dataIndex: 'asset_type', key: 'asset_type' },
        { title: '置信度', dataIndex: 'confidence', key: 'confidence' }
      ];
      return <Table columns={tableColumns} dataSource={tableData} size="small" pagination={false} />;

    default:
      return <div>未知组件类型</div>;
  }
});

const { Option } = Select;
const { TextArea } = Input;
const { Title, Text } = Typography;

interface Dashboard {
  id: string;
  name: string;
  description: string;
  widgets: Widget[];
  layout: LayoutConfig;
  created_at: string;
  updated_at: string;
  is_default: boolean;
}

interface Widget {
  id: string;
  type: 'chart' | 'table' | 'statistic' | 'text';
  title: string;
  position: { x: number; y: number; w: number; h: number };
  config: WidgetConfig;
  data_source: DataSource;
}

interface WidgetConfig {
  chart_type?: 'pie' | 'column' | 'line' | 'area';
  color_scheme?: string[];
  show_legend?: boolean;
  show_label?: boolean;
  height?: number;
}

interface DataSource {
  type: 'aggregation' | 'search' | 'custom';
  query: any;
  refresh_interval?: number;
}

interface LayoutConfig {
  columns: number;
  row_height: number;
  margin: [number, number];
}

const CustomDashboard: React.FC = () => {
  const [dashboards, setDashboards] = useState<Dashboard[]>([]);
  const [currentDashboard, setCurrentDashboard] = useState<Dashboard | null>(null);
  const [showDashboardModal, setShowDashboardModal] = useState(false);
  const [showWidgetDrawer, setShowWidgetDrawer] = useState(false);
  const [editingWidget, setEditingWidget] = useState<Widget | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  const [widgetForm] = Form.useForm();
  
  // 组件模板
  const [widgetTemplates, setWidgetTemplates] = useState<any>({});

  useEffect(() => {
    loadDashboards();
    loadWidgetTemplates();
  }, []);

  const loadDashboards = async () => {
    try {
      // 尝试从localStorage加载保存的仪表板
      const stored = localStorage.getItem('bounty_ams_dashboards');
      if (stored) {
        try {
          const storedDashboards = JSON.parse(stored);
          if (Array.isArray(storedDashboards) && storedDashboards.length > 0) {
            setDashboards(storedDashboards);
            if (storedDashboards.length > 0) {
              setCurrentDashboard(storedDashboards[0]);
            }
            return;
          }
        } catch (error) {
          console.error('解析保存的仪表板失败:', error);
        }
      }
      
      // 如果没有保存的仪表板，创建一个默认的
      const defaultDashboard: Dashboard = {
        id: 'default',
        name: '默认仪表板',
        description: '系统默认仪表板，展示核心指标',
        widgets: [
          {
            id: 'w1',
            type: 'statistic',
            title: '总资产数',
            position: { x: 0, y: 0, w: 3, h: 2 },
            config: { height: 120 },
            data_source: { type: 'aggregation', query: { template: 'total_assets_count' } }
          },
          {
            id: 'w2',
            type: 'chart',
            title: '资产类型分布',
            position: { x: 3, y: 0, w: 6, h: 4 },
            config: { chart_type: 'pie', show_legend: true, height: 300 },
            data_source: { type: 'aggregation', query: { template: 'asset_type_pie' } }
          },
          {
            id: 'w3',
            type: 'chart',
            title: '置信度分布',
            position: { x: 9, y: 0, w: 3, h: 4 },
            config: { chart_type: 'column', show_legend: false, height: 300 },
            data_source: { type: 'aggregation', query: { template: 'confidence_distribution' } }
          }
        ],
        layout: { columns: 12, row_height: 60, margin: [16, 16] },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        is_default: true
      };
      
      setDashboards([defaultDashboard]);
      setCurrentDashboard(defaultDashboard);
      
      // 保存到localStorage
      localStorage.setItem('bounty_ams_dashboards', JSON.stringify([defaultDashboard]));
      
    } catch (error) {
      console.error('加载仪表板失败:', error);
      message.error('加载仪表板失败');
    }
  };

  // 加载组件模板
  const loadWidgetTemplates = async () => {
    try {
      const response = await dashboardService.getWidgetTemplates();
      setWidgetTemplates(response.data.templates);
    } catch (error) {
      console.error('加载组件模板失败:', error);
    }
  };

  // 创建新仪表板
  const handleCreateDashboard = async (values: any) => {
    const newDashboard: Dashboard = {
      id: Date.now().toString(),
      name: values.name,
      description: values.description,
      widgets: [],
      layout: { columns: 12, row_height: 60, margin: [16, 16] },
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      is_default: false
    };

    const updatedDashboards = [...dashboards, newDashboard];
    setDashboards(updatedDashboards);
    setCurrentDashboard(newDashboard);
    
    // 保存到localStorage
    localStorage.setItem('bounty_ams_dashboards', JSON.stringify(updatedDashboards));
    
    setShowDashboardModal(false);
    form.resetFields();
    message.success('仪表板创建成功');
  };

  // 添加组件
  const handleAddWidget = async (values: any) => {
    if (!currentDashboard) return;

    const newWidget: Widget = {
      id: Date.now().toString(),
      type: values.type,
      title: values.title,
      position: { x: 0, y: 0, w: values.width || 6, h: values.height || 4 },
      config: {
        chart_type: values.chart_type,
        color_scheme: values.color_scheme,
        show_legend: values.show_legend,
        show_label: values.show_label,
        height: values.chart_height || 300
      },
      data_source: {
        type: values.data_source_type,
        query: values.template 
          ? { template: values.template, ...( values.query ? JSON.parse(values.query) : {}) }
          : ( values.query ? JSON.parse(values.query) : {} ),
        refresh_interval: values.refresh_interval
      }
    };

    const updatedDashboard = {
      ...currentDashboard,
      widgets: [...currentDashboard.widgets, newWidget],
      updated_at: new Date().toISOString()
    };

    setCurrentDashboard(updatedDashboard);
    const updatedDashboards = dashboards.map(d => d.id === updatedDashboard.id ? updatedDashboard : d);
    setDashboards(updatedDashboards);
    
    // 保存到localStorage
    localStorage.setItem('bounty_ams_dashboards', JSON.stringify(updatedDashboards));
    
    setShowWidgetDrawer(false);
    widgetForm.resetFields();
    message.success('组件添加成功');
  };


  // 仪表板表格列
  const dashboardColumns: ColumnsType<Dashboard> = useMemo(() => [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      render: (name: string, record: Dashboard) => (
        <div>
          <strong>{name}</strong>
          {record.is_default && <Tag color="blue" style={{ marginLeft: 8 }}>默认</Tag>}
        </div>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: '组件数量',
      key: 'widget_count',
      render: (_, record: Dashboard) => record.widgets.length,
    },
    {
      title: '更新时间',
      dataIndex: 'updated_at',
      key: 'updated_at',
      render: (date: string) => new Date(date).toLocaleDateString(),
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record: Dashboard) => (
        <Space>
          <Button 
            type="text" 
            icon={<EyeOutlined />} 
            onClick={() => setCurrentDashboard(record)}
          >
            查看
          </Button>
          <Button 
            type="text" 
            icon={<EditOutlined />} 
            onClick={() => {
              setCurrentDashboard(record);
              setIsEditing(true);
            }}
          >
            编辑
          </Button>
          <Button 
            type="text" 
            icon={<CopyOutlined />} 
            onClick={() => {
              const copied = {
                ...record,
                id: Date.now().toString(),
                name: record.name + ' (副本)',
                is_default: false
              };
              setDashboards(prev => [...prev, copied]);
              message.success('仪表板复制成功');
            }}
          >
            复制
          </Button>
          {!record.is_default && (
            <Popconfirm
              title="确定删除此仪表板吗？"
              onConfirm={() => {
                setDashboards(prev => {
                  const newDashboards = prev.filter(d => d.id !== record.id);
                  // 使用函数式更新确保我们有最新的状态
                  if (currentDashboard?.id === record.id) {
                    setCurrentDashboard(newDashboards.length > 0 ? newDashboards[0] : null);
                  }
                  return newDashboards;
                });
                message.success('仪表板删除成功');
              }}
            >
              <Button type="text" danger icon={<DeleteOutlined />}>
                删除
              </Button>
            </Popconfirm>
          )}
        </Space>
      ),
    },
  ], [currentDashboard?.id]); // 添加依赖项以确保正确更新

  return (
    <div style={{ padding: 24 }}>
      {/* 头部操作栏 */}
      <Card style={{ marginBottom: 24 }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Title level={4} style={{ margin: 0 }}>
              自定义仪表板
            </Title>
            {currentDashboard && (
              <Text type="secondary">
                当前：{currentDashboard.name} ({currentDashboard.widgets.length} 个组件)
              </Text>
            )}
          </Col>
          <Col>
            <Space>
              <Button 
                type="primary" 
                icon={<PlusOutlined />} 
                onClick={() => setShowDashboardModal(true)}
              >
                新建仪表板
              </Button>
              {currentDashboard && (
                <Button 
                  icon={<PlusOutlined />} 
                  onClick={() => setShowWidgetDrawer(true)}
                >
                  添加组件
                </Button>
              )}
              <Button 
                icon={<SettingOutlined />} 
                onClick={() => setIsEditing(!isEditing)}
              >
                {isEditing ? '完成编辑' : '编辑模式'}
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 仪表板内容 */}
      {currentDashboard ? (
        <div>
          {/* 组件网格 */}
          <Row gutter={[16, 16]}>
            {currentDashboard.widgets.map((widget) => (
              <Col 
                key={widget.id} 
                span={widget.position.w} 
                style={{ minHeight: widget.position.h * 60 }}
              >
                <Card
                  title={widget.title}
                  size="small"
                  extra={
                    isEditing && (
                      <Space>
                        <Button 
                          type="text" 
                          size="small" 
                          icon={<EditOutlined />}
                          onClick={() => {
                            setEditingWidget(widget);
                            setShowWidgetDrawer(true);
                            widgetForm.setFieldsValue({
                              title: widget.title,
                              type: widget.type,
                              chart_type: widget.config.chart_type,
                              width: widget.position.w,
                              height: widget.position.h
                            });
                          }}
                        />
                        <Popconfirm
                          title="确定删除此组件吗？"
                          onConfirm={() => {
                            const updatedDashboard = {
                              ...currentDashboard,
                              widgets: currentDashboard.widgets.filter(w => w.id !== widget.id)
                            };
                            setCurrentDashboard(updatedDashboard);
                            setDashboards(prev => prev.map(d => d.id === updatedDashboard.id ? updatedDashboard : d));
                            message.success('组件删除成功');
                          }}
                        >
                          <Button type="text" size="small" danger icon={<DeleteOutlined />} />
                        </Popconfirm>
                      </Space>
                    )
                  }
                >
                  <WidgetContent 
                    widget={widget} 
                    widgetTemplates={widgetTemplates}
                  />
                </Card>
              </Col>
            ))}
          </Row>

          {currentDashboard.widgets.length === 0 && (
            <Card>
              <div style={{ textAlign: 'center', padding: 60 }}>
                <BarChartOutlined style={{ fontSize: 64, color: '#d9d9d9' }} />
                <div style={{ marginTop: 16, fontSize: 16, color: '#666' }}>
                  暂无组件，点击"添加组件"开始构建仪表板
                </div>
              </div>
            </Card>
          )}
        </div>
      ) : (
        <Card>
          <Table
            columns={dashboardColumns}
            dataSource={dashboards}
            rowKey="id"
            pagination={false}
            locale={{ emptyText: '暂无仪表板，点击"新建仪表板"开始' }}
          />
        </Card>
      )}

      {/* 新建仪表板对话框 */}
      <Modal
        title="新建仪表板"
        open={showDashboardModal}
        onCancel={() => {
          setShowDashboardModal(false);
          form.resetFields();
        }}
        footer={null}
      >
        <Form form={form} onFinish={handleCreateDashboard} layout="vertical">
          <Form.Item
            name="name"
            label="仪表板名称"
            rules={[{ required: true, message: '请输入仪表板名称' }]}
          >
            <Input placeholder="输入仪表板名称" />
          </Form.Item>
          <Form.Item
            name="description"
            label="描述"
          >
            <TextArea placeholder="输入仪表板描述" rows={3} />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                创建
              </Button>
              <Button onClick={() => setShowDashboardModal(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 添加/编辑组件抽屉 */}
      <Drawer
        title={editingWidget ? "编辑组件" : "添加组件"}
        placement="right"
        width={400}
        open={showWidgetDrawer}
        onClose={() => {
          setShowWidgetDrawer(false);
          setEditingWidget(null);
          widgetForm.resetFields();
        }}
      >
        <Form form={widgetForm} onFinish={handleAddWidget} layout="vertical">
          <Form.Item
            name="title"
            label="组件标题"
            rules={[{ required: true, message: '请输入组件标题' }]}
          >
            <Input placeholder="输入组件标题" />
          </Form.Item>

          <Form.Item
            name="type"
            label="组件类型"
            rules={[{ required: true, message: '请选择组件类型' }]}
          >
            <Select placeholder="选择组件类型">
              <Option value="chart">图表</Option>
              <Option value="table">表格</Option>
              <Option value="statistic">统计数值</Option>
              <Option value="text">文本</Option>
            </Select>
          </Form.Item>

          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) => prevValues.type !== currentValues.type}
          >
            {({ getFieldValue }) =>
              getFieldValue('type') === 'chart' && (
                <Form.Item
                  name="chart_type"
                  label="图表类型"
                  rules={[{ required: true, message: '请选择图表类型' }]}
                >
                  <Select placeholder="选择图表类型">
                    <Option value="pie">饼图</Option>
                    <Option value="column">柱状图</Option>
                    <Option value="line">折线图</Option>
                    <Option value="area">面积图</Option>
                  </Select>
                </Form.Item>
              )
            }
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="width" label="宽度" initialValue={6}>
                <Slider min={1} max={12} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="height" label="高度" initialValue={4}>
                <Slider min={2} max={8} />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="data_source_type" label="数据源类型" initialValue="aggregation">
            <Select>
              <Option value="aggregation">聚合数据</Option>
              <Option value="search">搜索结果</Option>
              <Option value="custom">自定义</Option>
            </Select>
          </Form.Item>

          <Form.Item name="template" label="使用模板">
            <Select placeholder="选择预定义模板（可选）" allowClear>
              <Option value="total_assets_count">总资产数量</Option>
              <Option value="asset_type_pie">资产类型分布</Option>
              <Option value="confidence_distribution">置信度分布</Option>
              <Option value="platform_distribution">平台分布</Option>
              <Option value="discovery_timeline">发现时间线</Option>
            </Select>
          </Form.Item>

          <Form.Item name="query" label="查询配置">
            <TextArea 
              placeholder='{"template": "asset_type_pie"}或自定义配置'
              rows={4}
            />
          </Form.Item>

          <Form.Item name="refresh_interval" label="刷新间隔(秒)" initialValue={30}>
            <Select>
              <Option value={0}>不自动刷新</Option>
              <Option value={30}>30秒</Option>
              <Option value={60}>1分钟</Option>
              <Option value={300}>5分钟</Option>
              <Option value={900}>15分钟</Option>
            </Select>
          </Form.Item>

          <Divider />

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                {editingWidget ? '更新' : '添加'}
              </Button>
              <Button onClick={() => setShowWidgetDrawer(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Drawer>
    </div>
  );
};

export default CustomDashboard;