import React, { useState, useEffect } from 'react';
import {
  Row,
  Col,
  Card,
  Table,
  Button,
  Space,
  Tag,
  Typography,
  Select,
  message,
  Drawer,
  Progress,
  Badge,
  Modal,
  Form,
  Input,
  InputNumber,
  Radio,
  Checkbox,
  Divider,
  Alert,
  Tooltip,
  Dropdown,
} from 'antd';
import {
  ReloadOutlined,
  EyeOutlined,
  CheckCircleOutlined,
  SyncOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  SecurityScanOutlined,
  PlusOutlined,
  MoreOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  StopOutlined,
  DeleteOutlined,
} from '@ant-design/icons';
import { taskService, agentService } from '../services/api';
import LoadingSkeleton from '../components/LoadingSkeleton';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;

// 类型定义
interface ScanParameter {
  key: string;
  label: string;
  type: 'select' | 'multiselect' | 'number' | 'boolean' | 'input';
  options?: string[];
  min?: number;
  max?: number;
  default: any;
  placeholder?: string;
}

interface ScannerConfig {
  name: string;
  icon: React.ReactNode;
  color: string;
  parameters: ScanParameter[];
}

interface ScannerConfigs {
  [key: string]: ScannerConfig;
}

// 扫描器配置定义
const SCANNER_CONFIGS: ScannerConfigs = {
  subdomain_discovery: {
    name: '子域名发现',
    icon: <SecurityScanOutlined />,
    color: 'blue',
    parameters: [
      { key: 'wordlist', label: '字典文件', type: 'select', options: ['common.txt', 'big.txt', 'dns.txt'], default: 'common.txt' },
      { key: 'threads', label: '线程数', type: 'number', min: 1, max: 100, default: 20 },
      { key: 'timeout', label: '超时时间(秒)', type: 'number', min: 1, max: 300, default: 10 },
      { key: 'recursive', label: '递归扫描', type: 'boolean', default: false },
    ]
  },
  port_scanning: {
    name: '端口扫描',
    icon: <SecurityScanOutlined />,
    color: 'green',
    parameters: [
      { key: 'ports', label: '端口范围', type: 'input', placeholder: '1-1000,3389,8080-8090', default: '1-1000' },
      { key: 'scan_type', label: '扫描类型', type: 'select', options: ['syn', 'tcp', 'udp'], default: 'syn' },
      { key: 'threads', label: '线程数', type: 'number', min: 1, max: 1000, default: 100 },
      { key: 'timeout', label: '超时时间(秒)', type: 'number', min: 1, max: 60, default: 5 },
    ]
  },
  service_detection: {
    name: '服务识别',
    icon: <SecurityScanOutlined />,
    color: 'orange',
    parameters: [
      { key: 'probe_type', label: '探测类型', type: 'select', options: ['basic', 'aggressive', 'stealth'], default: 'basic' },
      { key: 'timeout', label: '超时时间(秒)', type: 'number', min: 1, max: 60, default: 10 },
      { key: 'max_retries', label: '重试次数', type: 'number', min: 0, max: 5, default: 1 },
    ]
  },
  vulnerability_testing: {
    name: '漏洞扫描',
    icon: <SecurityScanOutlined />,
    color: 'red',
    parameters: [
      { key: 'templates', label: '漏洞模板', type: 'multiselect', options: ['sql-injection', 'xss', 'lfi', 'rfi', 'ssrf'], default: ['sql-injection'] },
      { key: 'severity', label: '严重等级', type: 'multiselect', options: ['critical', 'high', 'medium', 'low'], default: ['critical', 'high'] },
      { key: 'rate_limit', label: '请求频率(r/s)', type: 'number', min: 1, max: 100, default: 10 },
      { key: 'timeout', label: '超时时间(秒)', type: 'number', min: 1, max: 300, default: 30 },
    ]
  },
  dns_resolution: {
    name: 'DNS解析',
    icon: <SecurityScanOutlined />,
    color: 'purple',
    parameters: [
      { key: 'record_types', label: '记录类型', type: 'multiselect', options: ['A', 'AAAA', 'CNAME', 'MX', 'TXT', 'NS'], default: ['A', 'AAAA'] },
      { key: 'dns_server', label: 'DNS服务器', type: 'input', placeholder: '*******', default: '' },
      { key: 'timeout', label: '超时时间(秒)', type: 'number', min: 1, max: 30, default: 5 },
    ]
  }
};

// 任务模板定义
const TASK_TEMPLATES = [
  {
    id: 'full_domain_recon',
    name: '完整域名侦察',
    description: '子域名发现 + DNS解析 + 端口扫描 + 服务识别',
    icon: <SecurityScanOutlined style={{ color: '#1890ff' }} />,
    tasks: [
      {
        task_type: 'subdomain_discovery',
        parameters: {
          wordlist: 'big.txt',
          threads: 50,
          timeout: 30,
          recursive: true
        },
        priority: 'high'
      },
      {
        task_type: 'dns_resolution', 
        parameters: {
          record_types: ['A', 'AAAA', 'CNAME', 'MX'],
          timeout: 10
        },
        priority: 'medium'
      },
      {
        task_type: 'port_scanning',
        parameters: {
          ports: '1-1000,3389,8080-8090',
          scan_type: 'syn',
          threads: 100,
          timeout: 5
        },
        priority: 'medium'
      },
      {
        task_type: 'service_detection',
        parameters: {
          probe_type: 'basic',
          timeout: 10,
          max_retries: 1
        },
        priority: 'low'
      }
    ]
  },
  {
    id: 'quick_scan',
    name: '快速扫描',
    description: '基础子域名发现 + 常用端口扫描',
    icon: <SecurityScanOutlined style={{ color: '#52c41a' }} />,
    tasks: [
      {
        task_type: 'subdomain_discovery',
        parameters: {
          wordlist: 'common.txt',
          threads: 20,
          timeout: 10,
          recursive: false
        },
        priority: 'medium'
      },
      {
        task_type: 'port_scanning',
        parameters: {
          ports: '21,22,23,25,53,80,110,143,443,993,995,8080,8443',
          scan_type: 'syn',
          threads: 50,
          timeout: 3
        },
        priority: 'medium'
      }
    ]
  },
  {
    id: 'vuln_scan',
    name: '漏洞快速检测',
    description: '端口扫描 + 服务识别 + 漏洞扫描',
    icon: <SecurityScanOutlined style={{ color: '#f5222d' }} />,
    tasks: [
      {
        task_type: 'port_scanning',
        parameters: {
          ports: '1-1000',
          scan_type: 'syn',
          threads: 100,
          timeout: 5
        },
        priority: 'high'
      },
      {
        task_type: 'service_detection',
        parameters: {
          probe_type: 'aggressive',
          timeout: 15,
          max_retries: 2
        },
        priority: 'high'
      },
      {
        task_type: 'vulnerability_testing',
        parameters: {
          templates: ['sql-injection', 'xss', 'lfi'],
          severity: ['critical', 'high'],
          rate_limit: 20,
          timeout: 60
        },
        priority: 'high'
      }
    ]
  },
  {
    id: 'dns_deep_dive',
    name: 'DNS深度分析',
    description: '全面DNS记录查询和子域名发现',
    icon: <SecurityScanOutlined style={{ color: '#722ed1' }} />,
    tasks: [
      {
        task_type: 'dns_resolution',
        parameters: {
          record_types: ['A', 'AAAA', 'CNAME', 'MX', 'TXT', 'NS'],
          timeout: 15
        },
        priority: 'medium'
      },
      {
        task_type: 'subdomain_discovery',
        parameters: {
          wordlist: 'dns.txt',
          threads: 30,
          timeout: 20,
          recursive: true
        },
        priority: 'medium'
      }
    ]
  }
];

const Tasks: React.FC = () => {
  const [tasks, setTasks] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedTask, setSelectedTask] = useState<any>(null);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [statusFilter, setStatusFilter] = useState('');
  
  // 任务创建相关状态
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [createForm] = Form.useForm();
  const [selectedScanType, setSelectedScanType] = useState('');
  const [targetAssets, setTargetAssets] = useState<string[]>([]);
  const [scanParameters, setScanParameters] = useState<any>({});
  const [availableAgents, setAvailableAgents] = useState<any[]>([]);
  const [selectedAgent, setSelectedAgent] = useState<string>('');
  
  // 模板相关状态
  const [templateModalVisible, setTemplateModalVisible] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<any>(null);

  useEffect(() => {
    loadTasks();
    loadAvailableAgents();
    // 从URL参数或localStorage获取传递的资产
    const urlParams = new URLSearchParams(window.location.search);
    const assetsParam = urlParams.get('assets');
    if (assetsParam) {
      try {
        const assets = JSON.parse(decodeURIComponent(assetsParam));
        setTargetAssets(assets);
        // 如果有资产传递过来，自动打开创建任务对话框
        setCreateModalVisible(true);
      } catch (error) {
        console.error('Error parsing assets parameter:', error);
      }
    }
  }, [statusFilter]);

  const loadAvailableAgents = async () => {
    try {
      const response = await agentService.getAgents();
      const agents = response.data?.agents || [];
      // 只显示在线的agents
      const onlineAgents = agents.filter((agent: any) => agent.status === 'online');
      setAvailableAgents(onlineAgents);
    } catch (error) {
      console.error('Error loading agents:', error);
    }
  };

  const loadTasks = async () => {
    try {
      setLoading(true);
      // 增加超时时间到15秒
      const timeoutPromise = new Promise((_, reject) => 
        setTimeout(() => reject(new Error('请求超时')), 15000)
      );
      
      const response = await Promise.race([
        taskService.getTasks({
          status: statusFilter,
          limit: 100,  // 修改参数名
        }),
        timeoutPromise
      ]);
      
      // API返回格式：{tasks: [...]}
      const tasksData = (response as any).data?.tasks || [];
      setTasks(tasksData);
      
      console.log(`Loaded ${tasksData.length} tasks`, tasksData);
      
      if (tasksData.length === 0) {
        console.log('No tasks found');
      }
    } catch (error: any) {
      console.error('Error loading tasks:', error);
      if (error.message === '请求超时' || error.code === 'ECONNABORTED') {
        message.warning('连接超时，任务服务响应较慢');
      } else {
        message.error('加载任务失败: ' + (error.response?.data?.detail || error.message));
      }
      // 即使失败也设置空数组，避免页面一直loading
      setTasks([]);
    } finally {
      setLoading(false);
    }
  };

  const handleViewDetails = async (task: any) => {
    try {
      const response = await taskService.getTaskDetails(task.task_id);
      // API可能直接返回task对象或包装在data中
      const taskData = response.data || response;
      setSelectedTask(taskData);
      setDrawerVisible(true);
    } catch (error) {
      console.error('Error loading task details:', error);
      message.error('加载任务详情失败');
    }
  };

  // 任务创建处理函数
  const handleCreateTask = () => {
    setCreateModalVisible(true);
    setSelectedScanType('');
    setScanParameters({});
    setSelectedAgent('');
    createForm.resetFields();
    // 重新加载agents以获取最新状态
    loadAvailableAgents();
  };

  const handleScanTypeChange = (scanType: string) => {
    setSelectedScanType(scanType);
    // 初始化默认参数
    const config = SCANNER_CONFIGS[scanType];
    if (config) {
      const defaultParams: { [key: string]: any } = {};
      config.parameters.forEach((param: ScanParameter) => {
        defaultParams[param.key] = param.default;
      });
      setScanParameters(defaultParams);
      createForm.setFieldsValue({
        task_type: scanType,
        parameters: defaultParams
      });
    }
  };

  const handleParameterChange = (key: string, value: any) => {
    const newParams = { ...scanParameters, [key]: value };
    setScanParameters(newParams);
    createForm.setFieldsValue({
      parameters: newParams
    });
  };

  const handleCreateSubmit = async () => {
    try {
      const values = await createForm.validateFields();
      
      // 获取目标列表
      const targets = targetAssets.length > 0 ? targetAssets : values.targets.split('\n').filter((t: string) => t.trim());
      
      if (targets.length === 0) {
        message.error('请输入至少一个扫描目标');
        return;
      }

      // 优先级映射
      const priorityMap: { [key: string]: number } = {
        'low': 1,
        'medium': 2, 
        'high': 3,
        'urgent': 4
      };

      if (targets.length === 1) {
        // 单个目标，使用TaskDefinition格式
        const taskData = {
          task_id: `${selectedScanType}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          task_type: selectedScanType,
          target: targets[0],
          parameters: scanParameters,
          priority: priorityMap[values.priority || 'medium'],
          timeout: values.timeout || 300,
          retry_count: values.retry_count || 1,
          ...(selectedAgent && { preferred_agent_id: selectedAgent })
        };

        console.log('Creating single task:', taskData);
        await taskService.createTask(taskData);
      } else {
        // 多个目标，使用批量创建格式
        const batchData = {
          task_type: selectedScanType,
          targets: targets,
          parameters: scanParameters,
          priority: values.priority || 'medium',
          timeout: values.timeout || 300,
          retry_count: values.retry_count || 1,
          ...(selectedAgent && { preferred_agent_id: selectedAgent })
        };

        console.log('Creating batch tasks:', batchData);
        await taskService.createTask(batchData);
      }
      
      message.success(`任务创建成功，共 ${targets.length} 个目标`);
      
      // 重置状态
      setCreateModalVisible(false);
      setSelectedScanType('');
      setScanParameters({});
      setTargetAssets([]);
      setSelectedAgent('');
      createForm.resetFields();
      
      // 刷新任务列表
      loadTasks();
    } catch (error) {
      console.error('Error creating task:', error);
      message.error('任务创建失败');
    }
  };

  const handleTaskAction = async (action: string, taskId: string) => {
    try {
      switch (action) {
        case 'cancel':
          await taskService.cancelTask(taskId);
          message.success('任务已取消');
          break;
        case 'retry':
          await taskService.retryTask(taskId);
          message.success('任务已重试');
          break;
        case 'delete':
          await taskService.deleteTask(taskId);
          message.success('任务已删除');
          break;
      }
      loadTasks();
    } catch (error) {
      console.error(`Error ${action} task:`, error);
      message.error(`任务${action}失败`);
    }
  };

  // 模板处理函数
  const handleTemplateSelect = (template: any) => {
    setSelectedTemplate(template);
    setTemplateModalVisible(false);
    
    // 根据模板创建任务
    createTasksFromTemplate(template);
  };

  const createTasksFromTemplate = async (template: any) => {
    if (targetAssets.length === 0) {
      message.warning('请先选择目标资产或在创建任务时输入目标');
      setCreateModalVisible(true);
      return;
    }

    try {
      message.loading('正在根据模板创建任务...', 0);
      
      // 为每个任务类型创建任务
      const createdTasks = [];
      for (const taskTemplate of template.tasks) {
        const taskData = {
          task_type: taskTemplate.task_type,
          targets: targetAssets,
          parameters: taskTemplate.parameters,
          priority: taskTemplate.priority,
          timeout: 300,
          retry_count: 1,
        };
        
        const response = await taskService.createTask(taskData);
        createdTasks.push(response);
      }
      
      message.destroy();
      message.success(`成功创建 ${createdTasks.length} 个任务`);
      
      // 清理状态
      setTargetAssets([]);
      
      // 刷新任务列表
      loadTasks();
    } catch (error) {
      message.destroy();
      console.error('Error creating tasks from template:', error);
      message.error('模板任务创建失败');
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'running':
        return <SyncOutlined spin style={{ color: '#1890ff' }} />;
      case 'pending':
        return <ClockCircleOutlined style={{ color: '#faad14' }} />;
      case 'failed':
        return <ExclamationCircleOutlined style={{ color: '#f5222d' }} />;
      default:
        return <ClockCircleOutlined style={{ color: '#d9d9d9' }} />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'running':
        return 'processing';
      case 'pending':
        return 'warning';
      case 'failed':
        return 'error';
      default:
        return 'default';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'red';
      case 'medium':
        return 'orange';
      case 'low':
        return 'green';
      default:
        return 'default';
    }
  };

  const columns = [
    {
      title: '任务ID',
      dataIndex: 'task_id',
      key: 'task_id',
      render: (id: string) => (
        <Text code style={{ fontSize: '12px' }}>
          {id}
        </Text>
      ),
    },
    {
      title: '类型',
      dataIndex: 'task_type',
      key: 'task_type',
      render: (type: string) => (
        <Tag color="blue">{type}</Tag>
      ),
    },
    {
      title: '目标',
      dataIndex: 'target',
      key: 'target',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Badge 
          status={getStatusColor(status) as any}
          text={status}
        />
      ),
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      key: 'priority',
      render: (priority: string) => (
        <Tag color={getPriorityColor(priority)}>
          {priority}
        </Tag>
      ),
    },
    {
      title: 'Agent',
      dataIndex: 'agent_id',
      key: 'agent_id',
      render: (agentId: string) => agentId || '-',
    },
    {
      title: '执行时间',
      dataIndex: 'execution_time',
      key: 'execution_time',
      render: (time: number) => time ? `${time}s` : '-',
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date: string) => (
        <Text type="secondary">
          {dayjs(date).format('MM-DD HH:mm')}
        </Text>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      render: (record: any) => (
        <Space>
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => handleViewDetails(record)}
            size="small"
          >
            详情
          </Button>
          <Dropdown
            menu={{
              items: [
                ...(record.status === 'pending' ? [
                  { key: 'cancel', label: '取消任务', icon: <StopOutlined /> }
                ] : []),
                ...(record.status === 'failed' ? [
                  { key: 'retry', label: '重试任务', icon: <ReloadOutlined /> }
                ] : []),
                { key: 'delete', label: '删除任务', icon: <DeleteOutlined />, danger: true }
              ],
              onClick: ({ key }) => handleTaskAction(key, record.task_id)
            }}
          >
            <Button type="text" icon={<MoreOutlined />} size="small" />
          </Dropdown>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Row gutter={[16, 16]}>
        <Col span={24}>
          <Space align="center" style={{ marginBottom: 16 }}>
            <Title level={3} style={{ margin: 0 }}>
              任务管理
            </Title>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleCreateTask}
            >
              创建任务
            </Button>
            <Button
              icon={<SecurityScanOutlined />}
              onClick={() => setTemplateModalVisible(true)}
            >
              使用模板
            </Button>
            <Select
              placeholder="筛选状态"
              style={{ width: 120 }}
              value={statusFilter}
              onChange={setStatusFilter}
              allowClear
            >
              <Option value="">全部</Option>
              <Option value="pending">待执行</Option>
              <Option value="running">运行中</Option>
              <Option value="completed">已完成</Option>
              <Option value="failed">失败</Option>
            </Select>
            <Button
              icon={<ReloadOutlined />}
              onClick={loadTasks}
              loading={loading}
            >
              刷新
            </Button>
          </Space>
          
          {targetAssets.length > 0 && (
            <Alert
              message={`已选择 ${targetAssets.length} 个资产目标`}
              description={targetAssets.slice(0, 3).join(', ') + (targetAssets.length > 3 ? '...' : '')}
              type="info"
              style={{ marginBottom: 16 }}
              closable
              onClose={() => setTargetAssets([])}
            />
          )}
        </Col>
      </Row>

      <LoadingSkeleton type="table" loading={loading} rows={6}>
        <Card>
          <Table
            columns={columns}
            dataSource={tasks}
            loading={false}
            rowKey="task_id"
            pagination={{
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) =>
                `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            }}
            scroll={{ x: 800 }}
            locale={{
              emptyText: tasks.length === 0 ? '暂无任务数据，可能任务系统未配置' : '暂无数据'
            }}
          />
        </Card>
      </LoadingSkeleton>

      {/* 任务详情抽屉 */}
      <Drawer
        title="任务详情"
        width={600}
        onClose={() => setDrawerVisible(false)}
        open={drawerVisible}
      >
        {selectedTask && (
          <div>
            <Card title="基本信息" size="small" style={{ marginBottom: 16 }}>
              <Row gutter={[16, 16]}>
                <Col span={12}>
                  <Text strong>任务ID:</Text>
                  <br />
                  <Text code>{selectedTask.task_id}</Text>
                </Col>
                <Col span={12}>
                  <Text strong>类型:</Text>
                  <br />
                  <Tag color="blue">{selectedTask.task_type}</Tag>
                </Col>
                <Col span={12}>
                  <Text strong>目标:</Text>
                  <br />
                  <Text>{selectedTask.target}</Text>
                </Col>
                <Col span={12}>
                  <Text strong>状态:</Text>
                  <br />
                  <Badge 
                    status={getStatusColor(selectedTask.status) as any}
                    text={selectedTask.status}
                  />
                </Col>
                <Col span={12}>
                  <Text strong>优先级:</Text>
                  <br />
                  <Tag color={getPriorityColor(selectedTask.priority)}>
                    {selectedTask.priority}
                  </Tag>
                </Col>
                <Col span={12}>
                  <Text strong>Agent:</Text>
                  <br />
                  <Text code>{selectedTask.agent_id || '未分配'}</Text>
                </Col>
              </Row>
            </Card>

            <Card title="执行信息" size="small" style={{ marginBottom: 16 }}>
              <Row gutter={[16, 16]}>
                <Col span={12}>
                  <Text strong>执行时间:</Text>
                  <br />
                  <Text>{selectedTask.execution_time ? `${selectedTask.execution_time}s` : '-'}</Text>
                </Col>
                <Col span={12}>
                  <Text strong>重试次数:</Text>
                  <br />
                  <Text>{selectedTask.current_retry || 0}/{selectedTask.retry_count || 0}</Text>
                </Col>
                <Col span={12}>
                  <Text strong>超时时间:</Text>
                  <br />
                  <Text>{selectedTask.timeout}s</Text>
                </Col>
                <Col span={12}>
                  <Text strong>工作流ID:</Text>
                  <br />
                  <Text code>{selectedTask.workflow_id || '-'}</Text>
                </Col>
              </Row>
            </Card>

            <Card title="时间信息" size="small" style={{ marginBottom: 16 }}>
              <Row gutter={[16, 16]}>
                <Col span={12}>
                  <Text strong>创建时间:</Text>
                  <br />
                  <Text type="secondary">
                    {dayjs(selectedTask.created_at).format('YYYY-MM-DD HH:mm:ss')}
                  </Text>
                </Col>
                <Col span={12}>
                  <Text strong>分配时间:</Text>
                  <br />
                  <Text type="secondary">
                    {selectedTask.assigned_at ? dayjs(selectedTask.assigned_at).format('YYYY-MM-DD HH:mm:ss') : '-'}
                  </Text>
                </Col>
                <Col span={12}>
                  <Text strong>开始时间:</Text>
                  <br />
                  <Text type="secondary">
                    {selectedTask.started_at ? dayjs(selectedTask.started_at).format('YYYY-MM-DD HH:mm:ss') : '-'}
                  </Text>
                </Col>
                <Col span={12}>
                  <Text strong>完成时间:</Text>
                  <br />
                  <Text type="secondary">
                    {selectedTask.completed_at ? dayjs(selectedTask.completed_at).format('YYYY-MM-DD HH:mm:ss') : '-'}
                  </Text>
                </Col>
              </Row>
            </Card>

            {selectedTask.parameters && (
              <Card title="参数" size="small" style={{ marginBottom: 16 }}>
                <pre style={{ 
                  background: '#f5f5f5', 
                  padding: '12px', 
                  borderRadius: '4px',
                  fontSize: '12px'
                }}>
                  {JSON.stringify(selectedTask.parameters, null, 2)}
                </pre>
              </Card>
            )}

            {selectedTask.result_data && (
              <Card title="执行结果" size="small" style={{ marginBottom: 16 }}>
                <pre style={{ 
                  background: '#f5f5f5', 
                  padding: '12px', 
                  borderRadius: '4px',
                  fontSize: '12px'
                }}>
                  {JSON.stringify(selectedTask.result_data, null, 2)}
                </pre>
              </Card>
            )}

            {selectedTask.assets_discovered && selectedTask.assets_discovered.length > 0 && (
              <Card title="发现资产" size="small">
                <Space wrap>
                  {selectedTask.assets_discovered.map((asset: any, index: number) => (
                    <Tag key={index} color="green">
                      {asset.type}: {asset.value}
                    </Tag>
                  ))}
                </Space>
              </Card>
            )}

            {selectedTask.error_message && (
              <Card title="错误信息" size="small" style={{ marginBottom: 16 }}>
                <Text type="danger">{selectedTask.error_message}</Text>
              </Card>
            )}
          </div>
        )}
      </Drawer>

      {/* 任务创建对话框 */}
      <Modal
        title="创建扫描任务"
        open={createModalVisible}
        onOk={handleCreateSubmit}
        onCancel={() => setCreateModalVisible(false)}
        width={800}
        destroyOnClose
      >
        <Form
          form={createForm}
          layout="vertical"
          initialValues={{
            priority: 'medium',
            timeout: 300,
            retry_count: 1
          }}
        >
          {/* 扫描类型选择 */}
          <Form.Item
            label="扫描类型"
            name="task_type"
            rules={[{ required: true, message: '请选择扫描类型' }]}
          >
            <Row gutter={[8, 8]}>
              {Object.entries(SCANNER_CONFIGS).map(([key, config]) => (
                <Col span={8} key={key}>
                  <Card
                    size="small"
                    hoverable
                    className={selectedScanType === key ? 'selected-scan-type' : ''}
                    onClick={() => handleScanTypeChange(key)}
                    style={{
                      cursor: 'pointer',
                      border: selectedScanType === key ? '2px solid #1890ff' : '1px solid #d9d9d9'
                    }}
                  >
                    <Space>
                      {config.icon}
                      <div>
                        <Text strong>{config.name}</Text>
                        <br />
                        <Tag color={config.color}>{key}</Tag>
                      </div>
                    </Space>
                  </Card>
                </Col>
              ))}
            </Row>
          </Form.Item>

          {/* 目标资产 */}
          <Form.Item
            label="扫描目标"
            name="targets"
            rules={targetAssets.length === 0 ? [{ required: true, message: '请输入扫描目标' }] : []}
          >
            {targetAssets.length > 0 ? (
              <div>
                <Alert
                  message={`已选择 ${targetAssets.length} 个资产`}
                  description={
                    <div style={{ maxHeight: 100, overflow: 'auto' }}>
                      {targetAssets.map((asset, index) => (
                        <Tag key={index} style={{ margin: 2 }}>{asset}</Tag>
                      ))}
                    </div>
                  }
                  type="info"
                />
              </div>
            ) : (
              <TextArea
                rows={4}
                placeholder="请输入扫描目标，每行一个：&#10;example.com&#10;***********&#10;https://test.com"
              />
            )}
          </Form.Item>

          {/* 扫描参数配置 */}
          {selectedScanType && SCANNER_CONFIGS[selectedScanType] && (
            <Form.Item label="扫描参数">
              <Card size="small">
                <Row gutter={[16, 16]}>
                  {SCANNER_CONFIGS[selectedScanType].parameters.map((param: ScanParameter) => (
                    <Col span={12} key={param.key}>
                      <Form.Item
                        label={param.label}
                        style={{ marginBottom: 8 }}
                      >
                        {param.type === 'select' ? (
                          <Select
                            value={scanParameters[param.key]}
                            onChange={(value) => handleParameterChange(param.key, value)}
                            style={{ width: '100%' }}
                          >
                            {param.options?.map((option: string) => (
                              <Option key={option} value={option}>{option}</Option>
                            ))}
                          </Select>
                        ) : param.type === 'multiselect' ? (
                          <Select
                            mode="multiple"
                            value={scanParameters[param.key]}
                            onChange={(value) => handleParameterChange(param.key, value)}
                            style={{ width: '100%' }}
                          >
                            {param.options?.map((option: string) => (
                              <Option key={option} value={option}>{option}</Option>
                            ))}
                          </Select>
                        ) : param.type === 'number' ? (
                          <InputNumber
                            value={scanParameters[param.key]}
                            onChange={(value) => handleParameterChange(param.key, value)}
                            min={param.min}
                            max={param.max}
                            style={{ width: '100%' }}
                          />
                        ) : param.type === 'boolean' ? (
                          <Checkbox
                            checked={scanParameters[param.key]}
                            onChange={(e) => handleParameterChange(param.key, e.target.checked)}
                          />
                        ) : (
                          <Input
                            value={scanParameters[param.key]}
                            onChange={(e) => handleParameterChange(param.key, e.target.value)}
                            placeholder={param.placeholder}
                          />
                        )}
                      </Form.Item>
                    </Col>
                  ))}
                </Row>
              </Card>
            </Form.Item>
          )}

          <Divider />

          {/* Agent选择 */}
          <Form.Item
            label="指定Agent (可选)"
            help="不选择则系统自动分配给合适的在线Agent"
          >
            <Select
              placeholder="选择Agent执行任务 (可选)"
              value={selectedAgent}
              onChange={setSelectedAgent}
              allowClear
              style={{ width: '100%' }}
            >
              {availableAgents
                .filter(agent => 
                  selectedScanType === '' || 
                  agent.capabilities.includes(selectedScanType)
                )
                .map(agent => (
                  <Option key={agent.agent_id} value={agent.agent_id}>
                    <Space>
                      <Badge 
                        status={agent.status === 'online' ? 'success' : 'default'} 
                        text={agent.name || agent.agent_id}
                      />
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        ({agent.current_tasks}/{agent.max_concurrent_tasks} 任务)
                      </Text>
                    </Space>
                  </Option>
                ))
              }
            </Select>
          </Form.Item>

          {/* 任务配置 */}
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                label="优先级"
                name="priority"
              >
                <Select>
                  <Option value="low">低</Option>
                  <Option value="medium">中</Option>
                  <Option value="high">高</Option>
                  <Option value="urgent">紧急</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="超时时间(秒)"
                name="timeout"
              >
                <InputNumber min={60} max={3600} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="重试次数"
                name="retry_count"
              >
                <InputNumber min={0} max={5} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>

      {/* 任务模板选择对话框 */}
      <Modal
        title="选择任务模板"
        open={templateModalVisible}
        onCancel={() => setTemplateModalVisible(false)}
        footer={null}
        width={1000}
      >
        <div>
          <Row gutter={[16, 16]}>
            {TASK_TEMPLATES.map((template) => (
              <Col span={12} key={template.id}>
                <Card
                  hoverable
                  onClick={() => handleTemplateSelect(template)}
                  style={{
                    cursor: 'pointer',
                    transition: 'all 0.3s ease',
                  }}
                  bodyStyle={{ padding: '16px' }}
                >
                  <Space direction="vertical" size="small" style={{ width: '100%' }}>
                    <Space>
                      {template.icon}
                      <Text strong style={{ fontSize: '16px' }}>
                        {template.name}
                      </Text>
                    </Space>
                    
                    <Text type="secondary" style={{ fontSize: '14px' }}>
                      {template.description}
                    </Text>
                    
                    <Divider style={{ margin: '8px 0' }} />
                    
                    <div>
                      <Text strong style={{ fontSize: '12px' }}>包含任务:</Text>
                      <div style={{ marginTop: '4px' }}>
                        {template.tasks.map((task, index) => (
                          <Tag 
                            key={index} 
                            color={SCANNER_CONFIGS[task.task_type]?.color || 'default'}
                            style={{ margin: '2px', fontSize: '11px' }}
                          >
                            {SCANNER_CONFIGS[task.task_type]?.name || task.task_type}
                          </Tag>
                        ))}
                      </div>
                    </div>
                    
                    <div style={{ marginTop: '8px' }}>
                      <Space size="small">
                        <Text type="secondary" style={{ fontSize: '11px' }}>
                          任务数: {template.tasks.length}
                        </Text>
                        <Text type="secondary" style={{ fontSize: '11px' }}>
                          预计时间: {template.tasks.length * 2}-{template.tasks.length * 5}分钟
                        </Text>
                      </Space>
                    </div>
                  </Space>
                </Card>
              </Col>
            ))}
          </Row>
          
          {targetAssets.length === 0 && (
            <Alert
              message="提示"
              description="选择模板后需要指定目标资产，建议先从资产管理页面选择资产后再使用模板。"
              type="info"
              style={{ marginTop: 16 }}
              showIcon
            />
          )}
        </div>
      </Modal>
    </div>
  );
};

export default Tasks;