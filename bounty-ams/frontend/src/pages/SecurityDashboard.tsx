import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Row, 
  Col, 
  Statistic, 
  Progress, 
  Alert, 
  Table, 
  Tag, 
  Badge,
  Tooltip,
  Button,
  Select,
  Space,
  Timeline,
  List,
  Avatar,
  message,
  Spin,
  Empty,
  Descriptions
} from 'antd';
import { 
  SecurityScanOutlined,
  BugOutlined,
  SafetyCertificateOutlined,
  WarningOutlined,
  <PERSON>boltOutlined,
  Radar<PERSON>hartOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { Pie, Column, Line } from '@ant-design/plots';
import dayjs from 'dayjs';
import { enhancedSearchService, assetService } from '../services/api';

const { Option } = Select;

interface RiskAsset {
  id: string;
  source: {
    entity_data: {
      asset_value: string;
      asset_type: string;
      platform_id: string;
      project_id: string;
      confidence: string;
      tags: string[];
      discovered_at: string;
    };
  };
  risk_score: number;
  security_tags: string[];
}

interface RiskAssessment {
  overall_risk_level: string;
  risk_score: number;
  asset_statistics: {
    total_assets: number;
    high_risk_assets: number;
    medium_risk_assets: number;
    risk_distribution: {
      high: number;
      medium: number;
    };
  };
  threat_vectors: {
    exposed_admin_interfaces: number;
    api_endpoints: number;
    development_assets: number;
  };
  insights: any;
  remediation_recommendations?: {
    immediate_actions: string[];
    monitoring_recommendations: string[];
    governance_actions: string[];
  };
}

const SecurityDashboard: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [riskAssessment, setRiskAssessment] = useState<RiskAssessment | null>(null);
  const [highRiskAssets, setHighRiskAssets] = useState<RiskAsset[]>([]);
  const [mediumRiskAssets, setMediumRiskAssets] = useState<RiskAsset[]>([]);
  const [correlations, setCorrelations] = useState<any>(null);
  const [selectedPlatform, setSelectedPlatform] = useState<string>('');
  const [selectedProject, setSelectedProject] = useState<string>('');
  const [platforms, setPlatforms] = useState<any[]>([]);

  useEffect(() => {
    loadPlatforms();
    loadSecurityData();
  }, [selectedPlatform, selectedProject]);

  const loadPlatforms = async () => {
    try {
      const modelTypesResponse = await assetService.getDynamicModelTypes();
      const modelTypes = modelTypesResponse.data;
      
      const platformType = modelTypes.find((t: any) => t.name === 'platform');
      if (platformType) {
        const platformsResponse = await assetService.getDynamicEntities(platformType.id);
        const platformsData = platformsResponse.data || [];
        const transformedPlatforms = platformsData.map((platform: any) => ({
          id: platform.id,
          name: platform.entity_data.name || platform.entity_data.platform_name
        }));
        setPlatforms(transformedPlatforms);
      }
    } catch (error) {
      console.error('加载平台数据失败:', error);
      // 如果API调用失败，设置为空数组
      setPlatforms([]);
    }
  };

  const loadSecurityData = async () => {
    setLoading(true);
    try {
      // 并行加载数据
      const [riskResponse, highRiskResponse, mediumRiskResponse, correlationsResponse] = await Promise.all([
        enhancedSearchService.comprehensiveRiskAssessment(selectedPlatform, selectedProject, true),
        enhancedSearchService.securityAnalysis(selectedPlatform, 'high'),
        enhancedSearchService.securityAnalysis(selectedPlatform, 'medium'),
        enhancedSearchService.getAssetCorrelations(selectedPlatform, selectedProject, 'all')
      ]);

      setRiskAssessment(riskResponse.data.assessment);
      setHighRiskAssets(highRiskResponse.data.hits || []);
      setMediumRiskAssets(mediumRiskResponse.data.hits || []);
      setCorrelations(correlationsResponse.data.data.correlations);

    } catch (error) {
      message.error('加载安全数据失败');
      console.error('Failed to load security data:', error);
    } finally {
      setLoading(false);
    }
  };

  // 获取风险级别颜色
  const getRiskColor = (level: string) => {
    switch (level) {
      case 'critical': return '#ff4d4f';
      case 'high': return '#ff7a45';
      case 'medium': return '#ffa940';
      case 'low': return '#52c41a';
      default: return '#d9d9d9';
    }
  };

  // 获取风险级别图标
  const getRiskIcon = (level: string) => {
    switch (level) {
      case 'critical': return <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />;
      case 'high': return <WarningOutlined style={{ color: '#ff7a45' }} />;
      case 'medium': return <ClockCircleOutlined style={{ color: '#ffa940' }} />;
      case 'low': return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      default: return <SafetyCertificateOutlined style={{ color: '#d9d9d9' }} />;
    }
  };

  // 风险资产表格列
  const riskAssetColumns: ColumnsType<RiskAsset> = [
    {
      title: '资产',
      dataIndex: ['source', 'entity_data', 'asset_value'],
      key: 'asset_value',
      render: (value: string, record: RiskAsset) => (
        <div>
          <strong>{value}</strong>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {record.source.entity_data.asset_type}
          </div>
        </div>
      ),
    },
    {
      title: '风险评分',
      dataIndex: 'risk_score',
      key: 'risk_score',
      render: (score: number) => (
        <div>
          <Progress 
            percent={score * 100} 
            size="small"
            status={score > 0.7 ? 'exception' : score > 0.4 ? 'active' : 'success'}
          />
          <span style={{ fontSize: '12px' }}>{(score * 100).toFixed(1)}%</span>
        </div>
      ),
      sorter: (a: RiskAsset, b: RiskAsset) => b.risk_score - a.risk_score,
    },
    {
      title: '安全标签',
      dataIndex: 'security_tags',
      key: 'security_tags',
      render: (tags: string[]) => (
        <div>
          {tags?.map((tag: string) => (
            <Tag key={tag} color="red" icon={<BugOutlined />}>
              {tag}
            </Tag>
          ))}
        </div>
      ),
    },
    {
      title: '置信度',
      dataIndex: ['source', 'entity_data', 'confidence'],
      key: 'confidence',
      render: (confidence: string) => (
        <Tag color={confidence === 'high' ? 'green' : confidence === 'medium' ? 'orange' : 'red'}>
          {confidence}
        </Tag>
      ),
    },
    {
      title: '发现时间',
      dataIndex: ['source', 'entity_data', 'discovered_at'],
      key: 'discovered_at',
      render: (date: string) => dayjs(date).format('MM-DD HH:mm'),
    },
  ];

  // 风险分布饼图数据
  const riskDistributionData = riskAssessment ? [
    {
      type: '高风险',
      value: riskAssessment.asset_statistics.risk_distribution.high,
      color: '#ff4d4f'
    },
    {
      type: '中等风险',
      value: riskAssessment.asset_statistics.risk_distribution.medium,
      color: '#ffa940'
    },
    {
      type: '低风险',
      value: 100 - riskAssessment.asset_statistics.risk_distribution.high - riskAssessment.asset_statistics.risk_distribution.medium,
      color: '#52c41a'
    }
  ] : [];

  // 威胁向量柱状图数据
  const threatVectorData = riskAssessment ? [
    {
      category: '管理接口',
      count: riskAssessment.threat_vectors.exposed_admin_interfaces,
      color: '#ff4d4f'
    },
    {
      category: 'API端点',
      count: riskAssessment.threat_vectors.api_endpoints,
      color: '#ff7a45'
    },
    {
      category: '开发环境',
      count: riskAssessment.threat_vectors.development_assets,
      color: '#ffa940'
    }
  ] : [];

  if (loading && !riskAssessment) {
    return (
      <div style={{ padding: 24, textAlign: 'center' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>加载安全数据中...</div>
      </div>
    );
  }

  return (
    <div style={{ padding: 24 }}>
      {/* 页面标题和过滤器 */}
      <Card style={{ marginBottom: 24 }}>
        <Row justify="space-between" align="middle">
          <Col>
            <h2>
              <SecurityScanOutlined style={{ marginRight: 8 }} />
              安全分析仪表板
            </h2>
          </Col>
          <Col>
            <Space>
              <Select
                placeholder="选择平台"
                style={{ width: 150 }}
                value={selectedPlatform}
                onChange={setSelectedPlatform}
                allowClear
              >
                {platforms.map(platform => (
                  <Option key={platform.id} value={platform.id}>
                    {platform.name}
                  </Option>
                ))}
              </Select>
              <Button 
                type="primary" 
                icon={<RadarChartOutlined />} 
                onClick={loadSecurityData}
                loading={loading}
              >
                刷新分析
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {riskAssessment ? (
        <>
          {/* 总体风险概览 */}
          <Card title="总体风险概览" style={{ marginBottom: 24 }}>
            <Row gutter={24}>
              <Col span={6}>
                <Card size="small">
                  <Statistic
                    title="总体风险级别"
                    value={riskAssessment.overall_risk_level.toUpperCase()}
                    prefix={getRiskIcon(riskAssessment.overall_risk_level)}
                    valueStyle={{ color: getRiskColor(riskAssessment.overall_risk_level) }}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card size="small">
                  <Statistic
                    title="风险评分"
                    value={riskAssessment.risk_score}
                    precision={3}
                    suffix="/1.000"
                    valueStyle={{ color: getRiskColor(riskAssessment.overall_risk_level) }}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card size="small">
                  <Statistic
                    title="总资产数"
                    value={riskAssessment.asset_statistics.total_assets}
                    prefix={<SafetyCertificateOutlined />}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card size="small">
                  <Statistic
                    title="高风险资产"
                    value={riskAssessment.asset_statistics.high_risk_assets}
                    prefix={<WarningOutlined />}
                    valueStyle={{ color: '#ff4d4f' }}
                  />
                </Card>
              </Col>
            </Row>
          </Card>

          {/* 图表分析 */}
          <Row gutter={24} style={{ marginBottom: 24 }}>
            <Col span={12}>
              <Card title="风险分布" size="small">
                <Pie
                  data={riskDistributionData}
                  angleField="value"
                  colorField="type"
                  radius={0.8}
                  label={{
                    type: 'outer',
                    content: '{name}: {percentage}'
                  }}
                  height={300}
                />
              </Card>
            </Col>
            <Col span={12}>
              <Card title="威胁向量分析" size="small">
                <Column
                  data={threatVectorData}
                  xField="category"
                  yField="count"
                  colorField="category"
                  height={300}
                  label={{
                    position: 'middle',
                    style: {
                      fill: '#FFFFFF',
                      opacity: 0.6,
                    },
                  }}
                />
              </Card>
            </Col>
          </Row>

          {/* 高风险资产和修复建议 */}
          <Row gutter={24} style={{ marginBottom: 24 }}>
            <Col span={16}>
              <Card 
                title={
                  <span>
                    <WarningOutlined style={{ color: '#ff4d4f', marginRight: 8 }} />
                    高风险资产 ({highRiskAssets.length})
                  </span>
                } 
                size="small"
              >
                <Table
                  columns={riskAssetColumns}
                  dataSource={highRiskAssets}
                  rowKey="id"
                  size="small"
                  pagination={{ pageSize: 5, size: 'small' }}
                />
              </Card>
            </Col>
            <Col span={8}>
              <Card title="修复建议" size="small">
                {riskAssessment.remediation_recommendations ? (
                  <div>
                    <Alert
                      message="立即行动"
                      type="error"
                      showIcon
                      style={{ marginBottom: 12 }}
                    />
                    <List
                      size="small"
                      dataSource={riskAssessment.remediation_recommendations.immediate_actions}
                      renderItem={(item: string, index: number) => (
                        <List.Item>
                          <List.Item.Meta
                            avatar={<Avatar size="small" style={{ backgroundColor: '#ff4d4f' }}>{index + 1}</Avatar>}
                            description={item}
                          />
                        </List.Item>
                      )}
                    />
                  </div>
                ) : (
                  <Empty description="暂无修复建议" />
                )}
              </Card>
            </Col>
          </Row>

          {/* 威胁时间线和关联分析 */}
          <Row gutter={24}>
            <Col span={12}>
              <Card title="安全事件时间线" size="small">
                <Timeline>
                  {highRiskAssets.slice(0, 5).map((asset, index) => (
                    <Timeline.Item
                      key={asset.id}
                      color={index === 0 ? 'red' : 'orange'}
                      dot={<BugOutlined style={{ fontSize: '16px' }} />}
                    >
                      <div>
                        <strong>{asset.source.entity_data.asset_value}</strong>
                        <div style={{ fontSize: '12px', color: '#666' }}>
                          风险评分: {(asset.risk_score * 100).toFixed(1)}% 
                          | {dayjs(asset.source.entity_data.discovered_at).format('YYYY-MM-DD HH:mm')}
                        </div>
                        <div>
                          {asset.security_tags?.map(tag => (
                            <Tag key={tag} color="red">{tag}</Tag>
                          ))}
                        </div>
                      </div>
                    </Timeline.Item>
                  ))}
                </Timeline>
              </Card>
            </Col>
            <Col span={12}>
              <Card title="资产关联分析" size="small">
                {correlations ? (
                  <Descriptions column={1} size="small">
                    <Descriptions.Item label="域名组数量">
                      {Object.keys(correlations.domain_groups || {}).length}
                    </Descriptions.Item>
                    <Descriptions.Item label="IP集群数量">
                      {Object.keys(correlations.ip_clusters || {}).length}
                    </Descriptions.Item>
                    <Descriptions.Item label="技术栈关联">
                      {Object.keys(correlations.technology_stacks || {}).length} 种技术
                    </Descriptions.Item>
                    <Descriptions.Item label="网络关系">
                      {correlations.network_relationships?.length || 0} 个关系
                    </Descriptions.Item>
                  </Descriptions>
                ) : (
                  <Empty description="暂无关联数据" />
                )}
              </Card>
            </Col>
          </Row>
        </>
      ) : (
        <Card>
          <Empty 
            description="暂无安全数据"
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          >
            <Button type="primary" onClick={loadSecurityData} loading={loading}>
              开始安全分析
            </Button>
          </Empty>
        </Card>
      )}
    </div>
  );
};

export default SecurityDashboard;