import React, { useState, useEffect } from 'react';
import ErrorBoundary from '../components/ErrorBoundary';

// 尝试直接导入原始Agents组件来获取具体错误
const AgentsWrapper: React.FC = () => {
  const [error, setError] = useState<string | null>(null);
  const [useOriginal, setUseOriginal] = useState(true);

  // 尝试导入并捕获错误
  useEffect(() => {
    const testImport = async () => {
      try {
        console.log('尝试导入原始Agents组件...');
        const AgentsModule = await import('./Agents');
        console.log('Agents组件导入成功:', AgentsModule);
      } catch (importError: any) {
        console.error('Agents组件导入失败:', importError);
        setError(`导入错误: ${importError.message}\n堆栈: ${importError.stack}`);
      }
    };
    
    if (useOriginal) {
      testImport();
    }
  }, [useOriginal]);

  // 简化版组件作为后备
  const AgentsFallback = () => {
    const mockData = [
      {
        key: '1',
        agent_id: 'go-agent-code',
        name: 'Go Agent Main',
        status: 'online',
        last_seen_at: new Date().toISOString(),
        current_tasks: 0,
        capabilities: ['subdomain_discovery', 'port_scanning', 'service_detection'],
        version: '1.0.0',
        hostname: 'localhost',
        ip_address: '127.0.0.1',
      },
    ];

    return (
      <div style={{ padding: '20px' }}>
        <div style={{ marginBottom: '16px', background: '#fff7e6', padding: '12px', borderRadius: '6px', border: '1px solid #ffd591' }}>
          <p><strong>Agent管理 - 简化模式</strong></p>
          <p>原始组件加载失败，正在使用简化版本。</p>
          {error && (
            <details style={{ marginTop: '8px' }}>
              <summary>错误详情</summary>
              <pre style={{ fontSize: '12px', background: '#f5f5f5', padding: '8px', marginTop: '8px', whiteSpace: 'pre-wrap' }}>
                {error}
              </pre>
            </details>
          )}
          <button onClick={() => setUseOriginal(true)} style={{ marginTop: '8px' }}>
            重试加载完整版本
          </button>
        </div>
        
        <table style={{ width: '100%', border: '1px solid #d9d9d9', borderCollapse: 'collapse' }}>
          <thead>
            <tr style={{ background: '#fafafa' }}>
              <th style={{ padding: '12px', border: '1px solid #d9d9d9' }}>Agent ID</th>
              <th style={{ padding: '12px', border: '1px solid #d9d9d9' }}>名称</th>
              <th style={{ padding: '12px', border: '1px solid #d9d9d9' }}>状态</th>
              <th style={{ padding: '12px', border: '1px solid #d9d9d9' }}>最后心跳</th>
              <th style={{ padding: '12px', border: '1px solid #d9d9d9' }}>当前任务</th>
            </tr>
          </thead>
          <tbody>
            {mockData.map(agent => (
              <tr key={agent.key}>
                <td style={{ padding: '12px', border: '1px solid #d9d9d9' }}>{agent.agent_id}</td>
                <td style={{ padding: '12px', border: '1px solid #d9d9d9' }}>{agent.name}</td>
                <td style={{ padding: '12px', border: '1px solid #d9d9d9' }}>
                  <span style={{ 
                    background: agent.status === 'online' ? '#52c41a' : '#ff4d4f',
                    color: 'white',
                    padding: '2px 8px',
                    borderRadius: '4px',
                    fontSize: '12px'
                  }}>
                    {agent.status === 'online' ? '在线' : '离线'}
                  </span>
                </td>
                <td style={{ padding: '12px', border: '1px solid #d9d9d9' }}>刚刚</td>
                <td style={{ padding: '12px', border: '1px solid #d9d9d9' }}>{agent.current_tasks}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    );
  };

  if (!useOriginal || error) {
    return <AgentsFallback />;
  }

  // 如果没有错误，尝试渲染原始组件
  const AgentsOriginal = React.lazy(() => import('./Agents'));

  return (
    <ErrorBoundary
      fallback={
        <div style={{ padding: '20px' }}>
          <div style={{ background: '#fff2f0', padding: '16px', borderRadius: '6px', border: '1px solid #ffccc7', marginBottom: '16px' }}>
            <h3>原始Agent组件渲染失败</h3>
            <p>组件已加载但渲染时出错...</p>
            <button onClick={() => setUseOriginal(false)}>
              切换到简化版本
            </button>
          </div>
        </div>
      }
    >
      <React.Suspense fallback={<div style={{ padding: '20px' }}>加载Agent管理中...</div>}>
        <AgentsOriginal />
      </React.Suspense>
    </ErrorBoundary>
  );
};

export default AgentsWrapper;