import React, { useState, useEffect } from 'react';
import {
  Row,
  Col,
  Card,
  Table,
  Input,
  Button,
  Select,
  Space,
  Tag,
  Typography,
  DatePicker,
  Drawer,
  Descriptions,
  message,
  Form,
  Spin,
  Checkbox,
  Tooltip,
  Popover,
} from 'antd';
import {
  SearchOutlined,
  ReloadOutlined,
  EyeOutlined,
  ExportOutlined,
  ApiOutlined,
  GlobalOutlined,
  SettingOutlined,
} from '@ant-design/icons';
import { useLocation, useNavigate } from 'react-router-dom';
import { assetService } from '../services/api';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;

const Assets: React.FC = () => {
  const [assets, setAssets] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [selectedAsset, setSelectedAsset] = useState<any>(null);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [assetTypes, setAssetTypes] = useState<any[]>([]);
  const [assetSources, setAssetSources] = useState<any[]>([]);
  const [platforms, setPlatforms] = useState<any[]>([]);
  const [projects, setProjects] = useState<any[]>([]);
  const [aggregations, setAggregations] = useState<any>(null);
  const [searchForm] = Form.useForm();
  const location = useLocation();
  const navigate = useNavigate();
  
  // 获取模型类型ID
  const [platformModelId, setPlatformModelId] = useState<string>('');
  const [projectModelId, setProjectModelId] = useState<string>('');

  const [filters, setFilters] = useState({
    q: '',
    asset_type: '',
    source: '',
    confidence: '',
    status: '',
    date_from: '',
    date_to: '',
    platform_id: '',
    project_id: '',
  });

  useEffect(() => {
    loadModelTypes();
  }, []);

  useEffect(() => {
    // 处理URL参数 - 只在模型ID加载完成后处理
    if (!platformModelId || !projectModelId) return;
    
    const searchParams = new URLSearchParams(location.search);
    const platformId = searchParams.get('platform_id');
    const projectId = searchParams.get('project_id');
    
    if (platformId || projectId) {
      const urlFilters = {
        ...filters,
        platform_id: platformId || '',
        project_id: projectId || '',
      };
      setFilters(urlFilters);
      setCurrentPage(1);
      
      // 设置表单值
      searchForm.setFieldsValue({
        platform_id: platformId || undefined,
        project_id: projectId || undefined,
      });
      
      // 如果有平台筛选，加载该平台的项目
      if (platformId && projectModelId) {
        assetService.getDynamicEntities(projectModelId, {
          platform_id: platformId
        }).then(response => {
          setProjects(response.data || []);
        }).catch(error => {
          console.error('加载项目列表失败:', error);
        });
      }
    } else {
      // 没有URL参数时，清除平台和项目筛选
      if (filters.platform_id || filters.project_id) {
        setFilters(prev => ({ ...prev, platform_id: '', project_id: '' }));
        searchForm.setFieldsValue({
          platform_id: undefined,
          project_id: undefined,
        });
      }
    }
  }, [location.search, platformModelId, projectModelId]);
  
  useEffect(() => {
    if (platformModelId) {
      loadPlatforms();
    }
    if (projectModelId) {
      loadProjects();
    }
  }, [platformModelId, projectModelId]);
  
  useEffect(() => {
    loadAssets();
    loadAssetTypes();
    loadAssetSources();
  }, [currentPage, pageSize, filters]);

  const loadModelTypes = async () => {
    try {
      const response = await assetService.getDynamicModelTypes();
      const types = response.data;
      
      const platformType = types.find((t: any) => t.name === 'platform');
      const projectType = types.find((t: any) => t.name === 'project');
      
      if (platformType) setPlatformModelId(platformType.id);
      if (projectType) setProjectModelId(projectType.id);
    } catch (error) {
      console.error('加载模型类型失败:', error);
      message.error('加载模型类型失败');
    }
  };

  const loadAssets = async () => {
    try {
      setLoading(true);
      
      // 构建筛选参数
      const filterParams: any = {
        skip: (currentPage - 1) * pageSize,
        limit: pageSize,
      };
      
      // 添加筛选条件
      if (filters.platform_id) filterParams.platform_id = filters.platform_id;
      if (filters.project_id) filterParams.project_id = filters.project_id;
      if (filters.asset_type) filterParams.asset_type = filters.asset_type;
      if (filters.confidence) filterParams.confidence = filters.confidence;
      if (filters.status) filterParams.status = filters.status;
      if (filters.q) filterParams.search = filters.q;
      
      // 使用新的聚合查询API，获取所有资产类型的数据
      const [entitiesResponse, countResponse] = await Promise.all([
        assetService.getAllAssets(filterParams),
        assetService.getAllAssetsCount({
          platform_id: filters.platform_id || undefined,
          project_id: filters.project_id || undefined,
          asset_type: filters.asset_type || undefined,
          confidence: filters.confidence || undefined,
          status: filters.status || undefined,
          search: filters.q || undefined,
        })
      ]);
      
      const assets = entitiesResponse.data || [];
      const total = countResponse.data.count || 0;
      
      // 转换数据格式以匹配现有的表格结构
      const transformedAssets = assets.map((asset: any) => ({
        _id: asset.id,
        ...asset.entity_data,
        // 添加模型类型信息用于显示
        _model_type_id: asset.model_type_id,
        _created_at: asset.created_at,
        _updated_at: asset.updated_at,
        // 确保平台项目ID正确传递
        platform_id: asset.entity_data?.platform_id || asset.platform_id,
        project_id: asset.entity_data?.project_id || asset.project_id,
      }));
      
      setAssets(transformedAssets);
      setTotal(total);
      
      // 简单聚合统计（基于当前页数据）
      const typeStats: {[key: string]: number} = {};
      const confidenceStats: {[key: string]: number} = {};
      
      transformedAssets.forEach((asset: any) => {
        const type = asset.asset_type;
        const confidence = asset.confidence;
        
        typeStats[type] = (typeStats[type] || 0) + 1;
        confidenceStats[confidence] = (confidenceStats[confidence] || 0) + 1;
      });
      
      setAggregations({
        asset_types: typeStats,
        confidence: confidenceStats,
      });
      
    } catch (error) {
      console.error('加载资产失败:', error);
      message.error('加载资产失败');
    } finally {
      setLoading(false);
    }
  };

  const loadAssetTypes = async () => {
    try {
      const response = await assetService.getAssetTypes();
      setAssetTypes(response.data.types || []);
    } catch (error) {
      console.error('加载资产类型失败:', error);
    }
  };

  const loadAssetSources = async () => {
    try {
      const response = await assetService.getAssetSources();
      setAssetSources(response.data.sources || []);
    } catch (error) {
      console.error('加载资产来源失败:', error);
    }
  };
  
  const loadPlatforms = async () => {
    try {
      const response = await assetService.getDynamicEntities(platformModelId);
      setPlatforms(response.data || []);
    } catch (error) {
      console.error('加载平台列表失败:', error);
    }
  };
  
  const loadProjects = async () => {
    try {
      const response = await assetService.getDynamicEntities(projectModelId);
      setProjects(response.data || []);
    } catch (error) {
      console.error('加载项目列表失败:', error);
    }
  };
  
  // 移除useCallback，直接定义函数避免循环依赖
  const loadProjectsByPlatform = async (platformId: string) => {
    try {
      const response = await assetService.getDynamicEntities(projectModelId, {
        platform_id: platformId
      });
      setProjects(response.data || []);
    } catch (error) {
      console.error('加载项目列表失败:', error);
    }
  };
  
  const handlePlatformChange = (platformId: string) => {
    setFilters(prev => ({ ...prev, platform_id: platformId, project_id: '' }));
    searchForm.setFieldsValue({ project_id: undefined });
    if (platformId) {
      loadProjectsByPlatform(platformId);
    } else {
      loadProjects();
    }
  };

  const handleSearch = (values: any) => {
    const newFilters = {
      ...filters,
      ...values,
      date_from: values.dateRange?.[0]?.format('YYYY-MM-DD') || '',
      date_to: values.dateRange?.[1]?.format('YYYY-MM-DD') || '',
    };
    delete newFilters.dateRange;
    setFilters(newFilters);
    setCurrentPage(1);
  };

  const handleReset = () => {
    const resetFilters = {
      q: '',
      asset_type: '',
      source: '',
      confidence: '',
      status: '',
      date_from: '',
      date_to: '',
      platform_id: '',
      project_id: '',
    };
    setFilters(resetFilters);
    searchForm.resetFields();
    setCurrentPage(1);
    loadProjects(); // 重置项目列表
  };

  const handleViewDetails = async (asset: any) => {
    try {
      const response = await assetService.getAssetDetails(asset._id);
      setSelectedAsset(response.data);
      setDrawerVisible(true);
    } catch (error) {
      message.error('加载资产详情失败');
    }
  };

  const handleExport = async () => {
    try {
      const searchParams = {
        query: filters.q || '',
        platform_id: filters.platform_id || undefined,
        project_id: filters.project_id || undefined,
        asset_types: filters.asset_type ? [filters.asset_type] : undefined,
        confidence: filters.confidence || undefined,
        date_from: filters.date_from || undefined,
        date_to: filters.date_to || undefined,
        filters: {
          source: filters.source || undefined,
          status: filters.status || undefined,
        }
      };
      
      const response = await fetch('/api/enhanced-search/assets/export', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify(searchParams),
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      
      // 创建并下载文件
      const blob = new Blob([JSON.stringify(data.data.assets, null, 2)], {
        type: 'application/json',
      });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `assets_export_${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      message.success(`成功导出 ${data.data.total_exported} 条资产`);
    } catch (error) {
      console.error('导出失败:', error);
      message.error('导出失败，请重试');
    }
  };

  const getAssetTypeColor = (type: string) => {
    const colorMap: { [key: string]: string } = {
      'subdomain': 'blue',
      'ip': 'green',
      'port': 'orange',
      'url': 'purple',
      'service': 'cyan',
    };
    return colorMap[type] || 'default';
  };

  const getConfidenceColor = (confidence: string) => {
    const colorMap: { [key: string]: string } = {
      'high': 'green',
      'medium': 'orange',
      'low': 'red',
    };
    return colorMap[confidence] || 'default';
  };

  // 可选择的列配置
  const [visibleColumns, setVisibleColumns] = useState<string[]>([
    'platform_project', 'asset_type', 'asset_value', 'status', 'confidence', 'action'
  ]);

  // 所有可用的列定义
  const allColumnOptions = [
    { key: 'platform_project', title: '平台/项目', essential: true },
    { key: 'asset_type', title: '资产类型', essential: true },
    { key: 'asset_value', title: '资产值', essential: true },
    { key: 'status', title: '状态', essential: false },
    { key: 'confidence', title: '置信度', essential: false },
    { key: 'tags', title: '标签', essential: false },
    { key: 'source_task_type', title: '发现来源', essential: false },
    { key: 'discovered_at', title: '发现时间', essential: false },
    { key: 'asset_host', title: '主机', essential: false },
    { key: 'action', title: '操作', essential: true },
  ];

  const getStatusColor = (status: string) => {
    const colorMap: { [key: string]: string } = {
      'active': 'green',
      'inactive': 'red',
      'pending': 'orange',
      'verified': 'blue',
      'false_positive': 'gray',
    };
    return colorMap[status] || 'default';
  };

  // 动态生成列配置
  const getColumnConfig = (key: string) => {
    switch (key) {
      case 'platform_project':
        return {
          title: '平台/项目',
          key: 'platform_project',
          width: 180,
          fixed: 'left' as const,
          render: (record: any) => {
            const platform = platforms.find(p => p.id === record.platform_id);
            const project = projects.find(p => p.id === record.project_id);
            return (
              <div>
                {platform && (
                  <Tag color="blue" icon={<GlobalOutlined />} style={{ marginBottom: 2, fontSize: '11px' }}>
                    {platform.entity_data.display_name}
                  </Tag>
                )}
                {project && (
                  <Tag color="green" icon={<ApiOutlined />} style={{ fontSize: '11px' }}>
                    {project.entity_data.name}
                  </Tag>
                )}
              </div>
            );
          },
        };
      
      case 'asset_type':
        return {
          title: '类型',
          dataIndex: 'asset_type',
          key: 'asset_type',
          width: 80,
          render: (type: string) => type ? (
            <Tag color={getAssetTypeColor(type)} style={{ fontSize: '11px' }}>
              {type}
            </Tag>
          ) : '-',
        };
      
      case 'asset_value':
        return {
          title: '资产值',
          dataIndex: 'asset_value',
          key: 'asset_value',
          width: 200,
          ellipsis: true,
          render: (value: string) => value ? (
            <Text code style={{ fontSize: '12px' }} title={value}>
              {value}
            </Text>
          ) : '-',
        };
      
      case 'status':
        return {
          title: '状态',
          dataIndex: 'status',
          key: 'status',
          width: 70,
          render: (status: string) => status ? (
            <Tag color={getStatusColor(status)} style={{ fontSize: '11px' }}>
              {status}
            </Tag>
          ) : '-',
        };
      
      case 'confidence':
        return {
          title: '置信度',
          dataIndex: 'confidence',
          key: 'confidence',
          width: 70,
          render: (confidence: string) => confidence ? (
            <Tag color={getConfidenceColor(confidence)} style={{ fontSize: '11px' }}>
              {confidence}
            </Tag>
          ) : '-',
        };
      
      case 'tags':
        return {
          title: '标签',
          dataIndex: 'tags',
          key: 'tags',
          width: 120,
          render: (tags: string[]) => {
            if (!tags || !Array.isArray(tags) || tags.length === 0) return '-';
            
            const displayTag = tags[0];
            const remainingCount = tags.length - 1;
            
            return (
              <div>
                <Tag style={{ fontSize: '11px', padding: '1px 4px' }}>
                  {displayTag}
                </Tag>
                {remainingCount > 0 && (
                  <Tag color="default" style={{ fontSize: '10px', padding: '0px 3px' }}>
                    +{remainingCount}
                  </Tag>
                )}
              </div>
            );
          },
        };
      
      case 'source_task_type':
        return {
          title: '来源',
          dataIndex: 'source_task_type',
          key: 'source_task_type',
          width: 100,
          render: (source: string) => source ? (
            <Tag color="blue" style={{ fontSize: '11px' }}>{source}</Tag>
          ) : '-',
        };
      
      case 'discovered_at':
        return {
          title: '发现时间',
          dataIndex: 'discovered_at',
          key: 'discovered_at',
          width: 120,
          render: (date: string) => date ? (
            <Text type="secondary" style={{ fontSize: '11px' }}>
              {dayjs(date).format('MM-DD HH:mm')}
            </Text>
          ) : '-',
        };
      
      case 'asset_host':
        return {
          title: '主机',
          dataIndex: 'asset_host',
          key: 'asset_host',
          width: 150,
          ellipsis: true,
          render: (host: string) => host || '-',
        };
      
      case 'action':
        return {
          title: '操作',
          key: 'action',
          width: 60,
          fixed: 'right' as const,
          render: (record: any) => (
            <Button
              type="text"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => handleViewDetails(record)}
            />
          ),
        };
      
      default:
        return null;
    }
  };

  const columns = visibleColumns.map(getColumnConfig).filter(Boolean) as any[];

  return (
    <div>
      <Row gutter={[16, 16]}>
        <Col span={24}>
          <Space align="center" style={{ marginBottom: 16 }}>
            <Title level={3} style={{ margin: 0 }}>
              资产管理
            </Title>
            <Text type="secondary">
              共 {total} 项资产
            </Text>
            <Popover
              content={
                <div style={{ width: 200 }}>
                  <div style={{ marginBottom: 8, fontWeight: 'bold' }}>选择显示列</div>
                  {allColumnOptions.map(option => (
                    <div key={option.key} style={{ marginBottom: 4 }}>
                      <Checkbox
                        checked={visibleColumns.includes(option.key)}
                        disabled={option.essential}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setVisibleColumns([...visibleColumns, option.key]);
                          } else {
                            setVisibleColumns(visibleColumns.filter(col => col !== option.key));
                          }
                        }}
                      >
                        <span style={{ fontSize: '12px' }}>
                          {option.title}
                          {option.essential && <Text type="secondary"> (必须)</Text>}
                        </span>
                      </Checkbox>
                    </div>
                  ))}
                  <div style={{ marginTop: 8, paddingTop: 8, borderTop: '1px solid #f0f0f0' }}>
                    <Button 
                      size="small" 
                      onClick={() => setVisibleColumns(['platform_project', 'asset_type', 'asset_value', 'action'])}
                    >
                      最小化
                    </Button>
                    <Button 
                      size="small" 
                      style={{ marginLeft: 8 }}
                      onClick={() => setVisibleColumns(allColumnOptions.map(col => col.key))}
                    >
                      全部显示
                    </Button>
                  </div>
                </div>
              }
              trigger="click"
              placement="bottomRight"
            >
              <Tooltip title="列设置">
                <Button icon={<SettingOutlined />} size="small" />
              </Tooltip>
            </Popover>
            <Button
              icon={<ExportOutlined />}
              onClick={handleExport}
            >
              导出
            </Button>
          </Space>
        </Col>
      </Row>

      {/* 搜索表单 */}
      <Card style={{ marginBottom: 16 }}>
        <Form
          form={searchForm}
          onFinish={handleSearch}
          layout="vertical"
        >
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12} md={8}>
              <Form.Item name="q" label="搜索关键词">
                <Input
                  placeholder="搜索资产值、主机或目标..."
                  prefix={<SearchOutlined />}
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={8}>
              <Form.Item name="platform_id" label="平台">
                <Select 
                  placeholder="选择平台" 
                  allowClear
                  onChange={handlePlatformChange}
                >
                  {platforms.map(platform => (
                    <Option key={platform.id} value={platform.id}>
                      <Space>
                        <GlobalOutlined />
                        {platform.entity_data.display_name}
                      </Space>
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={8}>
              <Form.Item name="project_id" label="项目">
                <Select 
                  placeholder="选择项目" 
                  allowClear
                  disabled={!filters.platform_id}
                >
                  {projects.map(project => (
                    <Option key={project.id} value={project.id}>
                      <Space>
                        <ApiOutlined />
                        {project.entity_data.name}
                      </Space>
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={8}>
              <Form.Item name="asset_type" label="资产类型">
                <Select placeholder="选择资产类型" allowClear>
                  {assetTypes.map(type => (
                    <Option key={type.type} value={type.type}>
                      {type.type} ({type.count})
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={8}>
              <Form.Item name="source" label="发现来源">
                <Select placeholder="选择发现来源" allowClear>
                  {assetSources.map(source => (
                    <Option key={source.source} value={source.source}>
                      {source.source} ({source.count})
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={8}>
              <Form.Item name="confidence" label="置信度">
                <Select placeholder="选择置信度" allowClear>
                  <Option value="high">高</Option>
                  <Option value="medium">中</Option>
                  <Option value="low">低</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={8}>
              <Form.Item name="status" label="状态">
                <Select placeholder="选择状态" allowClear>
                  <Option value="active">活跃</Option>
                  <Option value="inactive">非活跃</Option>
                  <Option value="pending">待验证</Option>
                  <Option value="verified">已验证</Option>
                  <Option value="false_positive">误报</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={8}>
              <Form.Item name="dateRange" label="发现时间">
                <RangePicker 
                  style={{ width: '100%' }}
                  format="YYYY-MM-DD"
                />
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col span={24}>
              <Space>
                <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
                  搜索
                </Button>
                <Button onClick={handleReset} icon={<ReloadOutlined />}>
                  重置
                </Button>
              </Space>
            </Col>
          </Row>
        </Form>
      </Card>

      {/* 聚合数据展示 */}
      {aggregations && (
        <Card title="数据概览" style={{ marginBottom: 16 }}>
          <Row gutter={[16, 16]}>
            {aggregations.asset_types && (
              <Col xs={24} sm={12} md={8}>
                <div>
                  <Text strong>资产类型分布</Text>
                  <div style={{ marginTop: 8 }}>
                    {Object.entries(aggregations.asset_types).slice(0, 5).map(([key, count]) => (
                      <div key={key} style={{ display: 'flex', justifyContent: 'space-between' }}>
                        <span>{key}</span>
                        <span>{count as number}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </Col>
            )}
            {aggregations.confidence && (
              <Col xs={24} sm={12} md={8}>
                <div>
                  <Text strong>置信度分布</Text>
                  <div style={{ marginTop: 8 }}>
                    {Object.entries(aggregations.confidence).map(([key, count]) => (
                      <div key={key} style={{ display: 'flex', justifyContent: 'space-between' }}>
                        <span>{key}</span>
                        <span>{count as number}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </Col>
            )}
          </Row>
        </Card>
      )}

      {/* 资产列表 */}
      <Card>
        {/* 显示当前筛选状态 */}
        {(filters.platform_id || filters.project_id) && (
          <div style={{ marginBottom: 16 }}>
            {filters.platform_id && (
              <Tag color="blue" closable onClose={() => {
                const newFilters = { ...filters, platform_id: '', project_id: '' };
                setFilters(newFilters);
                searchForm.setFieldsValue({ platform_id: undefined, project_id: undefined });
                loadProjects(); // 重新加载所有项目
              }}>
                平台: {platforms.find(p => p.id === filters.platform_id)?.entity_data?.display_name || '未知平台'}
              </Tag>
            )}
            {filters.project_id && (
              <Tag color="green" closable onClose={() => {
                const newFilters = { ...filters, project_id: '' };
                setFilters(newFilters);
                searchForm.setFieldsValue({ project_id: undefined });
              }} style={{ marginLeft: 8 }}>
                项目: {projects.find(p => p.id === filters.project_id)?.entity_data?.name || '未知项目'}
              </Tag>
            )}
          </div>
        )}
        <Table
          columns={columns}
          dataSource={assets}
          loading={loading}
          rowKey="_id"
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            onChange: (page, size) => {
              setCurrentPage(page);
              setPageSize(size!);
            },
          }}
          scroll={{ 
            x: Math.max(800, visibleColumns.length * 120),
            y: 600
          }}
        />
      </Card>

      {/* 资产详情抽屉 */}
      <Drawer
        title="资产详情"
        width={700}
        onClose={() => setDrawerVisible(false)}
        open={drawerVisible}
      >
        {selectedAsset && (
          <div>
            <Descriptions title="基本信息" bordered size="small" column={2}>
              <Descriptions.Item label="资产值" span={2}>
                <Text code style={{ fontSize: '14px' }}>{selectedAsset.asset_value}</Text>
              </Descriptions.Item>
              <Descriptions.Item label="资产类型">
                <Tag color={getAssetTypeColor(selectedAsset.asset_type)}>
                  {selectedAsset.asset_type}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="置信度">
                <Tag color={getConfidenceColor(selectedAsset.confidence)}>
                  {selectedAsset.confidence}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="状态">
                {selectedAsset.status ? (
                  <Tag color={getStatusColor(selectedAsset.status)}>
                    {selectedAsset.status}
                  </Tag>
                ) : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="主机">
                {selectedAsset.asset_host || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="发现时间" span={2}>
                {selectedAsset.discovered_at ? 
                  dayjs(selectedAsset.discovered_at).format('YYYY-MM-DD HH:mm:ss') : '-'}
              </Descriptions.Item>
            </Descriptions>

            <Descriptions title="发现信息" bordered size="small" column={2} style={{ marginTop: 16 }}>
              <Descriptions.Item label="发现来源">
                {selectedAsset.source_task_type ? (
                  <Tag color="blue">{selectedAsset.source_task_type}</Tag>
                ) : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="发现次数">
                {selectedAsset.discovery_count || 1}
              </Descriptions.Item>
              <Descriptions.Item label="源任务ID" span={2}>
                {selectedAsset.source_task_id ? (
                  <Text code>{selectedAsset.source_task_id}</Text>
                ) : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="Agent ID" span={2}>
                {selectedAsset.source_agent_id || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="工作流ID" span={2}>
                {selectedAsset.workflow_id || '-'}
              </Descriptions.Item>
            </Descriptions>

            {/* 平台项目信息 */}
            {(selectedAsset.platform_id || selectedAsset.project_id) && (
              <Descriptions title="关联信息" bordered size="small" column={1} style={{ marginTop: 16 }}>
                {selectedAsset.platform_id && (
                  <Descriptions.Item label="所属平台">
                    {(() => {
                      const platform = platforms.find(p => p.id === selectedAsset.platform_id);
                      return platform ? (
                        <Tag color="blue" icon={<GlobalOutlined />}>
                          {platform.entity_data.display_name}
                        </Tag>
                      ) : selectedAsset.platform_id;
                    })()}
                  </Descriptions.Item>
                )}
                {selectedAsset.project_id && (
                  <Descriptions.Item label="所属项目">
                    {(() => {
                      const project = projects.find(p => p.id === selectedAsset.project_id);
                      return project ? (
                        <Tag color="green" icon={<ApiOutlined />}>
                          {project.entity_data.name}
                        </Tag>
                      ) : selectedAsset.project_id;
                    })()}
                  </Descriptions.Item>
                )}
              </Descriptions>
            )}

            {selectedAsset.tags && selectedAsset.tags.length > 0 && (
              <div style={{ marginTop: 16 }}>
                <Title level={5}>标签</Title>
                <Space wrap>
                  {selectedAsset.tags.map((tag: string, index: number) => (
                    <Tag key={index} color="processing">{tag}</Tag>
                  ))}
                </Space>
              </div>
            )}

            {selectedAsset.metadata && Object.keys(selectedAsset.metadata).length > 0 && (
              <div style={{ marginTop: 16 }}>
                <Title level={5}>元数据</Title>
                <Card size="small" style={{ background: '#fafafa' }}>
                  <pre style={{ 
                    margin: 0,
                    fontSize: '12px',
                    lineHeight: '1.4',
                    maxHeight: '200px',
                    overflow: 'auto'
                  }}>
                    {JSON.stringify(selectedAsset.metadata, null, 2)}
                  </pre>
                </Card>
              </div>
            )}
          </div>
        )}
      </Drawer>
    </div>
  );
};

export default Assets;