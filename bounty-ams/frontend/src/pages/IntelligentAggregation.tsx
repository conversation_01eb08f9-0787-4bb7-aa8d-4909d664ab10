import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Row, 
  Col, 
  Select, 
  DatePicker, 
  Button, 
  Space, 
  Statistic, 
  Progress, 
  Table,
  Tag,
  Tooltip,
  Switch,
  message,
  Spin,
  Empty
} from 'antd';
import { 
  <PERSON><PERSON><PERSON>Outlined, 
  <PERSON><PERSON><PERSON>Outlined, 
  <PERSON><PERSON><PERSON>Outlined, 
  <PERSON><PERSON><PERSON>Outlined,
  Fun<PERSON><PERSON>lotOutlined,
  <PERSON>deIndexOutlined,
  <PERSON><PERSON>Outlined,
  ReloadOutlined
} from '@ant-design/icons';
import { 
  Pie, 
  Column, 
  Line, 
  Area, 
  Heatmap, 
  Sankey, 
  Treemap,
  Radar,
  Funnel
} from '@ant-design/plots';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import { enhancedSearchService, assetService } from '../services/api';

const { RangePicker } = DatePicker;
const { Option } = Select;

interface AggregationData {
  platforms?: any[];
  projects?: any[];
  asset_types?: any[];
  confidence_levels?: any[];
  discovery_timeline?: any[];
  platform_project_analysis?: any[];
  risk_analysis?: any;
  technology_analysis?: any;
  discovery_efficiency?: any[];
  asset_correlation?: any[];
}

interface ChartData {
  [key: string]: any;
}

const IntelligentAggregation: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [aggregations, setAggregations] = useState<AggregationData>({});
  const [selectedPlatform, setSelectedPlatform] = useState<string>('');
  const [selectedProject, setSelectedProject] = useState<string>('');
  const [timeRange, setTimeRange] = useState<[dayjs.Dayjs, dayjs.Dayjs] | null>(null);
  const [refreshInterval, setRefreshInterval] = useState<number>(0);
  const [autoRefresh, setAutoRefresh] = useState(false);
  const [platforms, setPlatforms] = useState<any[]>([]);

  useEffect(() => {
    loadPlatforms();
    loadAggregationData();
  }, [selectedPlatform, selectedProject, timeRange]);

  useEffect(() => {
    let interval: number | null = null;
    if (autoRefresh && refreshInterval > 0) {
      interval = setInterval(() => {
        loadAggregationData();
      }, refreshInterval * 1000);
    }
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [autoRefresh, refreshInterval, selectedPlatform, selectedProject, timeRange]);

  const loadPlatforms = async () => {
    try {
      const modelTypesResponse = await assetService.getDynamicModelTypes();
      const modelTypes = modelTypesResponse.data;
      
      const platformType = modelTypes.find((t: any) => t.name === 'platform');
      if (platformType) {
        const platformsResponse = await assetService.getDynamicEntities(platformType.id);
        const platformsData = platformsResponse.data || [];
        const transformedPlatforms = platformsData.map((platform: any) => ({
          id: platform.id,
          name: platform.entity_data.name || platform.entity_data.platform_name
        }));
        setPlatforms(transformedPlatforms);
      }
    } catch (error) {
      console.error('加载平台数据失败:', error);
      // 如果API调用失败，设置为空数组
      setPlatforms([]);
    }
  };

  const loadAggregationData = async () => {
    setLoading(true);
    try {
      // 构建搜索参数
      const searchParams: any = {
        platform_id: selectedPlatform,
        project_id: selectedProject,
        size: 0, // 只需要聚合数据
        include_aggregations: true,
      };

      if (timeRange) {
        searchParams.date_from = timeRange[0].toISOString();
        searchParams.date_to = timeRange[1].toISOString();
      }

      const response = await enhancedSearchService.advancedSearch(searchParams);
      setAggregations(response.data.aggregations || {});

    } catch (error) {
      message.error('加载聚合数据失败');
      console.error('Failed to load aggregation data:', error);
    } finally {
      setLoading(false);
    }
  };

  // 处理资产类型分布数据
  const getAssetTypeData = (): ChartData[] => {
    if (!aggregations.asset_types) return [];
    return aggregations.asset_types.map((item: any) => ({
      type: item.key,
      value: item.doc_count,
    }));
  };

  // 处理平台分布数据
  const getPlatformData = (): ChartData[] => {
    if (!aggregations.platforms) return [];
    return aggregations.platforms.map((item: any) => ({
      platform: item.key,
      count: item.doc_count,
    }));
  };

  // 处理时间线数据
  const getTimelineData = (): ChartData[] => {
    if (!aggregations.discovery_timeline) return [];
    return aggregations.discovery_timeline.map((item: any) => ({
      date: dayjs(item.key_as_string || item.key).format('YYYY-MM-DD'),
      count: item.doc_count,
    }));
  };

  // 处理置信度分布数据
  const getConfidenceData = (): ChartData[] => {
    if (!aggregations.confidence_levels) return [];
    return aggregations.confidence_levels.map((item: any) => ({
      level: item.key,
      value: item.doc_count,
      color: item.key === 'high' ? '#52c41a' : item.key === 'medium' ? '#faad14' : '#ff4d4f'
    }));
  };

  // 处理风险分析数据
  const getRiskAnalysisData = (): ChartData[] => {
    if (!aggregations.risk_analysis?.high_risk || !aggregations.risk_analysis?.medium_risk || !aggregations.risk_analysis?.low_risk) {
      return [];
    }
    
    return [
      { level: '高风险', count: aggregations.risk_analysis.high_risk.doc_count, color: '#ff4d4f' },
      { level: '中等风险', count: aggregations.risk_analysis.medium_risk.doc_count, color: '#faad14' },
      { level: '低风险', count: aggregations.risk_analysis.low_risk.doc_count, color: '#52c41a' },
    ];
  };

  // 处理技术栈分析数据
  const getTechnologyData = (): ChartData[] => {
    if (!aggregations.technology_analysis) return [];
    
    const data: ChartData[] = [];
    Object.entries(aggregations.technology_analysis).forEach(([tech, info]) => {
      if (typeof info === 'object' && info !== null && 'doc_count' in info) {
        data.push({
          technology: tech,
          count: (info as any).doc_count,
        });
      }
    });
    return data;
  };

  // 处理发现效率数据
  const getDiscoveryEfficiencyData = (): ChartData[] => {
    if (!aggregations.discovery_efficiency) return [];
    return aggregations.discovery_efficiency.map((item: any) => ({
      source: item.key,
      count: item.doc_count,
      efficiency: item.avg_discovery_time?.value || 0,
    }));
  };

  // 处理平台项目关系数据（桑基图）
  const getPlatformProjectSankeyData = () => {
    if (!aggregations.platform_project_analysis) return { nodes: [], links: [] };
    
    const nodes: any[] = [];
    const links: any[] = [];
    
    aggregations.platform_project_analysis.forEach((platform: any) => {
      nodes.push({ id: platform.key, name: platform.key });
      
      if (platform.projects?.buckets) {
        platform.projects.buckets.forEach((project: any) => {
          nodes.push({ id: project.key, name: project.key });
          links.push({
            source: platform.key,
            target: project.key,
            value: project.doc_count,
          });
        });
      }
    });
    
    return { nodes, links };
  };

  // 雷达图数据
  const getRadarData = (): ChartData[] => {
    const assetTypeData = getAssetTypeData();
    const maxValue = Math.max(...assetTypeData.map(item => item.value));
    
    return assetTypeData.map(item => ({
      item: item.type,
      score: (item.value / maxValue) * 100,
    }));
  };

  return (
    <div style={{ padding: 24 }}>
      <Card title="智能聚合分析" style={{ marginBottom: 24 }}>
        <Row gutter={16} align="middle">
          <Col span={4}>
            <Select
              placeholder="选择平台"
              style={{ width: '100%' }}
              value={selectedPlatform}
              onChange={setSelectedPlatform}
              allowClear
            >
              {platforms.map(platform => (
                <Option key={platform.id} value={platform.id}>
                  {platform.name}
                </Option>
              ))}
            </Select>
          </Col>
          <Col span={6}>
            <RangePicker
              placeholder={['开始时间', '结束时间']}
              style={{ width: '100%' }}
              value={timeRange}
              onChange={(dates) => setTimeRange(dates as [dayjs.Dayjs, dayjs.Dayjs] | null)}
            />
          </Col>
          <Col span={6}>
            <Space>
              <Switch 
                checked={autoRefresh} 
                onChange={setAutoRefresh}
                checkedChildren="自动刷新"
                unCheckedChildren="手动刷新"
              />
              {autoRefresh && (
                <Select
                  value={refreshInterval}
                  onChange={setRefreshInterval}
                  style={{ width: 100 }}
                >
                  <Option value={30}>30秒</Option>
                  <Option value={60}>1分钟</Option>
                  <Option value={300}>5分钟</Option>
                </Select>
              )}
            </Space>
          </Col>
          <Col span={4}>
            <Button 
              type="primary" 
              icon={<ReloadOutlined />} 
              onClick={loadAggregationData}
              loading={loading}
            >
              刷新数据
            </Button>
          </Col>
          <Col span={4}>
            <Button 
              icon={<ThunderboltOutlined />} 
              onClick={() => {
                setSelectedPlatform('');
                setSelectedProject('');
                setTimeRange(null);
                loadAggregationData();
              }}
            >
              重置过滤
            </Button>
          </Col>
        </Row>
      </Card>

      {loading && Object.keys(aggregations).length === 0 ? (
        <Card>
          <div style={{ textAlign: 'center', padding: 50 }}>
            <Spin size="large" />
            <div style={{ marginTop: 16 }}>加载聚合数据中...</div>
          </div>
        </Card>
      ) : (
        <Row gutter={[24, 24]}>
          {/* 第一行：基础统计图表 */}
          <Col span={8}>
            <Card title={<><PieChartOutlined /> 资产类型分布</>} size="small">
              {getAssetTypeData().length > 0 ? (
                <Pie
                  data={getAssetTypeData()}
                  angleField="value"
                  colorField="type"
                  radius={0.8}
                  label={{
                    type: 'outer',
                    content: '{name}: {percentage}'
                  }}
                  height={300}
                />
              ) : (
                <Empty description="暂无数据" />
              )}
            </Card>
          </Col>
          
          <Col span={8}>
            <Card title={<><BarChartOutlined /> 平台资产分布</>} size="small">
              {getPlatformData().length > 0 ? (
                <Column
                  data={getPlatformData()}
                  xField="platform"
                  yField="count"
                  colorField="platform"
                  height={300}
                  label={{
                    position: 'middle',
                    style: {
                      fill: '#FFFFFF',
                      opacity: 0.6,
                    },
                  }}
                />
              ) : (
                <Empty description="暂无数据" />
              )}
            </Card>
          </Col>

          <Col span={8}>
            <Card title={<><RadarChartOutlined /> 资产能力雷达</>} size="small">
              {getRadarData().length > 0 ? (
                <Radar
                  data={getRadarData()}
                  xField="item"
                  yField="score"
                  area={{
                    visible: true,
                    style: {
                      fillOpacity: 0.2,
                    },
                  }}
                  point={{
                    visible: true,
                  }}
                  height={300}
                />
              ) : (
                <Empty description="暂无数据" />
              )}
            </Card>
          </Col>

          {/* 第二行：时间线和风险分析 */}
          <Col span={12}>
            <Card title={<><LineChartOutlined /> 资产发现趋势</>} size="small">
              {getTimelineData().length > 0 ? (
                <Area
                  data={getTimelineData()}
                  xField="date"
                  yField="count"
                  height={300}
                />
              ) : (
                <Empty description="暂无数据" />
              )}
            </Card>
          </Col>

          <Col span={12}>
            <Card title={<><FunnelPlotOutlined /> 风险评估分布</>} size="small">
              {getRiskAnalysisData().length > 0 ? (
                <Funnel
                  data={getRiskAnalysisData()}
                  xField="level"
                  yField="count"
                  colorField="level"
                  height={300}
                  legend={{ position: 'bottom' }}
                />
              ) : (
                <Empty description="暂无数据" />
              )}
            </Card>
          </Col>

          {/* 第三行：技术栈和发现效率 */}
          <Col span={12}>
            <Card title="技术栈分析" size="small">
              {getTechnologyData().length > 0 ? (
                <Column
                  data={getTechnologyData()}
                  xField="technology"
                  yField="count"
                  colorField="technology"
                  height={300}
                  label={{
                    position: 'middle',
                    style: {
                      fill: '#FFFFFF',
                      opacity: 0.6,
                    },
                  }}
                />
              ) : (
                <Empty description="暂无数据" />
              )}
            </Card>
          </Col>

          <Col span={12}>
            <Card title="发现效率分析" size="small">
              {getDiscoveryEfficiencyData().length > 0 ? (
                <Column
                  data={getDiscoveryEfficiencyData()}
                  xField="source"
                  yField="count"
                  colorField="source"
                  height={300}
                  meta={{
                    count: { alias: '发现数量' },
                    source: { alias: '发现来源' },
                  }}
                />
              ) : (
                <Empty description="暂无数据" />
              )}
            </Card>
          </Col>

          {/* 第四行：置信度分析和统计表格 */}
          <Col span={8}>
            <Card title="置信度分布" size="small">
              {getConfidenceData().length > 0 ? (
                <Pie
                  data={getConfidenceData()}
                  angleField="value"
                  colorField="level"
                  color={getConfidenceData().map(item => item.color)}
                  radius={0.8}
                  label={{
                    type: 'inner',
                    content: '{percentage}',
                    style: {
                      fill: '#fff',
                      fontSize: 14,
                      textAlign: 'center',
                    },
                  }}
                  height={250}
                />
              ) : (
                <Empty description="暂无数据" />
              )}
            </Card>
          </Col>

          <Col span={16}>
            <Card title="聚合统计概览" size="small">
              <Row gutter={[16, 16]}>
                <Col span={6}>
                  <Statistic
                    title="平台数量"
                    value={aggregations.platforms?.length || 0}
                    prefix={<NodeIndexOutlined />}
                  />
                </Col>
                <Col span={6}>
                  <Statistic
                    title="资产类型"
                    value={aggregations.asset_types?.length || 0}
                    prefix={<BarChartOutlined />}
                  />
                </Col>
                <Col span={6}>
                  <Statistic
                    title="总发现量"
                    value={aggregations.asset_types?.reduce((sum: number, item: any) => sum + item.doc_count, 0) || 0}
                    prefix={<ThunderboltOutlined />}
                  />
                </Col>
                <Col span={6}>
                  <Statistic
                    title="时间跨度"
                    value={aggregations.discovery_timeline?.length || 0}
                    suffix="天"
                    prefix={<LineChartOutlined />}
                  />
                </Col>
              </Row>
              
              {/* 详细统计表格 */}
              <div style={{ marginTop: 16 }}>
                <Table
                  size="small"
                  pagination={false}
                  columns={[
                    { title: '指标', dataIndex: 'metric', key: 'metric' },
                    { title: '数值', dataIndex: 'value', key: 'value' },
                    { title: '占比', dataIndex: 'percentage', key: 'percentage', render: (val: number) => `${val.toFixed(1)}%` },
                  ]}
                  dataSource={[
                    ...(aggregations.asset_types?.map((item: any, index: number) => ({
                      key: index,
                      metric: item.key,
                      value: item.doc_count,
                      percentage: (item.doc_count / (aggregations.asset_types?.reduce((sum: number, i: any) => sum + i.doc_count, 0) || 1)) * 100
                    })) || [])
                  ]}
                  scroll={{ y: 200 }}
                />
              </div>
            </Card>
          </Col>
        </Row>
      )}
    </div>
  );
};

export default IntelligentAggregation;