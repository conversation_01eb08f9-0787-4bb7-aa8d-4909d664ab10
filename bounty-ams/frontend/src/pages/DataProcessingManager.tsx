import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Row, 
  Col, 
  Button, 
  Upload, 
  Table, 
  Progress, 
  Statistic, 
  Steps, 
  Result, 
  Alert, 
  Tabs, 
  Space,
  Tag,
  Descriptions,
  Timeline,
  List,
  message,
  Spin,
  Modal,
  Form,
  Input,
  Select,
  Switch,
  Collapse,
  Tree
} from 'antd';
import { 
  UploadOutlined, 
  SyncOutlined, 
  CheckCircleOutlined, 
  ExclamationCircleOutlined,
  DatabaseOutlined,
  NodeIndexOutlined,
  RadarChartOutlined,
  ClusterOutlined,
  FileTextOutlined,
  BarChartOutlined,
  SettingOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import type { UploadFile } from 'antd/es/upload/interface';
import { Sankey } from '@ant-design/plots';
import dayjs from 'dayjs';
import { enhancedSearchService } from '../services/api';

const { Step } = Steps;
const { TabPane } = Tabs;
const { Panel } = Collapse;
const { TextArea } = Input;

interface ProcessingJob {
  id: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  progress: number;
  originalCount: number;
  cleanedCount?: number;
  uniqueCount?: number;
  duplicatesRemoved?: number;
  indexedCount?: number;
  errorCount?: number;
  startTime: string;
  endTime?: string;
  correlations?: any;
}

interface AssetCorrelation {
  domain_groups: Record<string, any[]>;
  ip_clusters: Record<string, any[]>;
  technology_stacks: Record<string, any>;
  network_relationships: any[];
}

const DataProcessingManager: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [uploadFiles, setUploadFiles] = useState<UploadFile[]>([]);
  const [processingJobs, setProcessingJobs] = useState<ProcessingJob[]>([]);
  const [currentJob, setCurrentJob] = useState<ProcessingJob | null>(null);
  const [processingStats, setProcessingStats] = useState<any>(null);
  const [correlations, setCorrelations] = useState<AssetCorrelation | null>(null);
  const [activeTab, setActiveTab] = useState('upload');
  const [showAdvancedSettings, setShowAdvancedSettings] = useState(false);
  const [form] = Form.useForm();

  useEffect(() => {
    loadProcessingStats();
    loadRecentJobs();
  }, []);

  const loadProcessingStats = async () => {
    try {
      const response = await enhancedSearchService.getProcessingStatistics();
      setProcessingStats(response.data.statistics);
    } catch (error) {
      console.error('Failed to load processing stats:', error);
    }
  };

  const loadRecentJobs = () => {
    // 模拟加载最近的处理任务
    const mockJobs: ProcessingJob[] = [
      {
        id: '1',
        status: 'completed',
        progress: 100,
        originalCount: 1500,
        cleanedCount: 1420,
        uniqueCount: 1380,
        duplicatesRemoved: 40,
        indexedCount: 1380,
        errorCount: 0,
        startTime: dayjs().subtract(2, 'hour').toISOString(),
        endTime: dayjs().subtract(1, 'hour').toISOString(),
      },
      {
        id: '2',
        status: 'failed',
        progress: 45,
        originalCount: 800,
        cleanedCount: 750,
        errorCount: 50,
        startTime: dayjs().subtract(1, 'day').toISOString(),
      }
    ];
    setProcessingJobs(mockJobs);
  };

  // 处理文件上传
  const handleFileUpload = async (file: File) => {
    try {
      const text = await file.text();
      const data = JSON.parse(text);
      
      if (!Array.isArray(data)) {
        throw new Error('文件格式错误，需要JSON数组');
      }

      return data;
    } catch (error) {
      message.error('文件解析失败，请检查格式');
      throw error;
    }
  };

  // 开始数据处理
  const startDataProcessing = async (rawAssets: any[]) => {
    if (!rawAssets || rawAssets.length === 0) {
      message.error('没有可处理的数据');
      return;
    }

    setLoading(true);
    const jobId = Date.now().toString();
    
    try {
      // 创建新的处理任务
      const newJob: ProcessingJob = {
        id: jobId,
        status: 'running',
        progress: 0,
        originalCount: rawAssets.length,
        startTime: new Date().toISOString(),
      };
      
      setCurrentJob(newJob);
      setActiveTab('processing');

      // 获取处理参数
      const values = await form.validateFields();
      
      // 调用高级数据处理API
      const response = await enhancedSearchService.advancedDataProcessing(
        rawAssets,
        values.autoCorrelate !== false,
        values.autoIndex !== false
      );

      const result = response.data;

      // 更新任务状态
      const completedJob: ProcessingJob = {
        ...newJob,
        status: 'completed',
        progress: 100,
        cleanedCount: result.processing_summary.cleaned_count,
        uniqueCount: result.processing_summary.unique_count,
        duplicatesRemoved: result.processing_summary.duplicates_removed,
        indexedCount: result.processing_summary.indexed_count,
        errorCount: result.processing_summary.errors_count || 0,
        endTime: new Date().toISOString(),
        correlations: result.correlations
      };

      setCurrentJob(completedJob);
      setCorrelations(result.correlations);
      setProcessingJobs(prev => [completedJob, ...prev]);

      message.success(`数据处理完成！成功处理 ${result.processing_summary.unique_count} 个资产`);
      
      // 刷新统计数据
      loadProcessingStats();

    } catch (error) {
      const failedJob: ProcessingJob = {
        id: jobId,
        status: 'failed',
        progress: 0,
        originalCount: rawAssets.length,
        startTime: new Date().toISOString(),
        endTime: new Date().toISOString(),
        errorCount: 1
      };
      
      setCurrentJob(failedJob);
      setProcessingJobs(prev => [failedJob, ...prev]);
      
      message.error('数据处理失败');
      console.error('Processing failed:', error);
    } finally {
      setLoading(false);
    }
  };

  // 任务状态表格列
  const jobColumns: ColumnsType<ProcessingJob> = [
    {
      title: '任务ID',
      dataIndex: 'id',
      key: 'id',
      width: 100,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const statusConfig = {
          pending: { color: 'default', text: '等待中' },
          running: { color: 'processing', text: '处理中' },
          completed: { color: 'success', text: '已完成' },
          failed: { color: 'error', text: '失败' }
        };
        const config = statusConfig[status as keyof typeof statusConfig];
        return <Tag color={config.color}>{config.text}</Tag>;
      },
    },
    {
      title: '进度',
      dataIndex: 'progress',
      key: 'progress',
      render: (progress: number, record: ProcessingJob) => (
        <Progress 
          percent={progress} 
          size="small"
          status={record.status === 'failed' ? 'exception' : undefined}
        />
      ),
    },
    {
      title: '原始/清洗/去重',
      key: 'counts',
      render: (_, record: ProcessingJob) => (
        <div>
          <div>原始: {record.originalCount}</div>
          {record.cleanedCount !== undefined && <div>清洗: {record.cleanedCount}</div>}
          {record.uniqueCount !== undefined && <div>去重: {record.uniqueCount}</div>}
        </div>
      ),
    },
    {
      title: '索引/错误',
      key: 'results',
      render: (_, record: ProcessingJob) => (
        <div>
          {record.indexedCount !== undefined && <div>索引: {record.indexedCount}</div>}
          {record.errorCount !== undefined && <div style={{ color: '#ff4d4f' }}>错误: {record.errorCount}</div>}
        </div>
      ),
    },
    {
      title: '耗时',
      key: 'duration',
      render: (_, record: ProcessingJob) => {
        if (!record.endTime) return '-';
        const duration = dayjs(record.endTime).diff(dayjs(record.startTime), 'second');
        return `${duration}秒`;
      },
    },
    {
      title: '开始时间',
      dataIndex: 'startTime',
      key: 'startTime',
      render: (time: string) => dayjs(time).format('MM-DD HH:mm:ss'),
    },
  ];

  // 渲染关联分析结果
  const renderCorrelationAnalysis = () => {
    if (!correlations) {
      return <Alert message="暂无关联分析数据" type="info" />;
    }

    const { domain_groups, ip_clusters, technology_stacks, network_relationships } = correlations;

    return (
      <Row gutter={[16, 16]}>
        <Col span={12}>
          <Card title="域名组分析" size="small">
            <List
              size="small"
              dataSource={Object.entries(domain_groups).slice(0, 5)}
              renderItem={([domain, assets]) => (
                <List.Item>
                  <List.Item.Meta
                    title={domain}
                    description={`${(assets as any[]).length} 个相关资产`}
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>
        <Col span={12}>
          <Card title="IP集群分析" size="small">
            <List
              size="small"
              dataSource={Object.entries(ip_clusters).slice(0, 5)}
              renderItem={([network, assets]) => (
                <List.Item>
                  <List.Item.Meta
                    title={network}
                    description={`${(assets as any[]).length} 个IP地址`}
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>
        <Col span={24}>
          <Card title="技术栈关联" size="small">
            <Descriptions column={2} size="small">
              {Object.entries(technology_stacks).map(([category, data]) => (
                <Descriptions.Item key={category} label={category}>
                  {Array.isArray(data) ? data.length : JSON.stringify(data).length} 项
                </Descriptions.Item>
              ))}
            </Descriptions>
          </Card>
        </Col>
      </Row>
    );
  };

  // 渲染处理步骤
  const renderProcessingSteps = () => {
    if (!currentJob) return null;

    const steps = [
      { title: '数据上传', description: '解析和验证原始数据' },
      { title: '数据清洗', description: '标准化和验证资产数据' },
      { title: '去重处理', description: '识别和合并重复资产' },
      { title: '关联分析', description: '分析资产间的关系' },
      { title: '索引存储', description: '存储到Elasticsearch' }
    ];

    let currentStep = 0;
    if (currentJob.progress > 20) currentStep = 1;
    if (currentJob.progress > 40) currentStep = 2;
    if (currentJob.progress > 60) currentStep = 3;
    if (currentJob.progress > 80) currentStep = 4;

    return (
      <Steps current={currentStep} status={currentJob.status === 'failed' ? 'error' : 'process'}>
        {steps.map((step, index) => (
          <Step key={index} title={step.title} description={step.description} />
        ))}
      </Steps>
    );
  };

  return (
    <div style={{ padding: 24 }}>
      <Card title="数据处理管理中心" style={{ marginBottom: 24 }}>
        <Row gutter={16}>
          <Col span={6}>
            <Statistic 
              title="处理健康状态" 
              value={processingStats?.processing_health || 'Unknown'}
              prefix={<DatabaseOutlined />}
            />
          </Col>
          <Col span={6}>
            <Statistic 
              title="索引文档总数" 
              value={processingStats?.index_stats?.total_docs || 0}
              prefix={<NodeIndexOutlined />}
            />
          </Col>
          <Col span={6}>
            <Statistic 
              title="索引大小" 
              value={processingStats?.index_stats?.total_size ? 
                Math.round(processingStats.index_stats.total_size / 1024 / 1024) : 0}
              suffix="MB"
              prefix={<BarChartOutlined />}
            />
          </Col>
          <Col span={6}>
            <Statistic 
              title="活跃索引数" 
              value={processingStats?.index_stats?.indices_count || 0}
              prefix={<FileTextOutlined />}
            />
          </Col>
        </Row>
      </Card>

      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="数据上传" key="upload">
          <Card>
            <Row gutter={24}>
              <Col span={16}>
                <Form form={form} layout="vertical">
                  <Form.Item label="上传资产数据文件">
                    <Upload.Dragger
                      accept=".json,.txt,.csv"
                      multiple={false}
                      fileList={uploadFiles}
                      beforeUpload={(file) => {
                        setUploadFiles([file]);
                        return false; // 阻止自动上传
                      }}
                      onRemove={() => setUploadFiles([])}
                    >
                      <p className="ant-upload-drag-icon">
                        <UploadOutlined />
                      </p>
                      <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
                      <p className="ant-upload-hint">支持JSON、CSV格式的资产数据文件</p>
                    </Upload.Dragger>
                  </Form.Item>

                  <Collapse>
                    <Panel header="高级处理选项" key="advanced">
                      <Form.Item name="autoCorrelate" valuePropName="checked" initialValue={true}>
                        <Switch /> 启用关联分析
                      </Form.Item>
                      <Form.Item name="autoIndex" valuePropName="checked" initialValue={true}>
                        <Switch /> 自动索引到Elasticsearch
                      </Form.Item>
                      <Form.Item name="cleaningLevel" label="清洗级别" initialValue="standard">
                        <Select>
                          <Select.Option value="basic">基础清洗</Select.Option>
                          <Select.Option value="standard">标准清洗</Select.Option>
                          <Select.Option value="aggressive">深度清洗</Select.Option>
                        </Select>
                      </Form.Item>
                    </Panel>
                  </Collapse>

                  <Form.Item>
                    <Space>
                      <Button 
                        type="primary" 
                        icon={<SyncOutlined />}
                        loading={loading}
                        disabled={uploadFiles.length === 0}
                        onClick={async () => {
                          if (uploadFiles.length > 0) {
                            try {
                              const rawAssets = await handleFileUpload(uploadFiles[0] as any);
                              await startDataProcessing(rawAssets);
                            } catch (error) {
                              // 错误已在handleFileUpload中处理
                            }
                          }
                        }}
                      >
                        开始处理
                      </Button>
                      <Button onClick={() => setUploadFiles([])}>
                        清空文件
                      </Button>
                    </Space>
                  </Form.Item>
                </Form>
              </Col>
              <Col span={8}>
                <Alert
                  message="数据处理流程"
                  description={
                    <div>
                      <p>1. <strong>数据清洗</strong>：标准化格式、验证数据</p>
                      <p>2. <strong>智能去重</strong>：基于指纹算法去除重复</p>
                      <p>3. <strong>关联分析</strong>：识别资产间的关系</p>
                      <p>4. <strong>自动索引</strong>：存储到搜索引擎</p>
                    </div>
                  }
                  type="info"
                  showIcon
                />
              </Col>
            </Row>
          </Card>
        </TabPane>

        <TabPane tab="处理状态" key="processing">
          <Card>
            {currentJob ? (
              <div>
                {renderProcessingSteps()}
                <div style={{ marginTop: 24 }}>
                  <Row gutter={16}>
                    <Col span={4}>
                      <Statistic title="原始数据" value={currentJob.originalCount} />
                    </Col>
                    {currentJob.cleanedCount !== undefined && (
                      <Col span={4}>
                        <Statistic title="清洗后" value={currentJob.cleanedCount} />
                      </Col>
                    )}
                    {currentJob.uniqueCount !== undefined && (
                      <Col span={4}>
                        <Statistic title="去重后" value={currentJob.uniqueCount} />
                      </Col>
                    )}
                    {currentJob.duplicatesRemoved !== undefined && (
                      <Col span={4}>
                        <Statistic title="去除重复" value={currentJob.duplicatesRemoved} />
                      </Col>
                    )}
                    {currentJob.indexedCount !== undefined && (
                      <Col span={4}>
                        <Statistic title="已索引" value={currentJob.indexedCount} />
                      </Col>
                    )}
                    {currentJob.errorCount !== undefined && (
                      <Col span={4}>
                        <Statistic 
                          title="错误数" 
                          value={currentJob.errorCount} 
                          valueStyle={{ color: currentJob.errorCount > 0 ? '#ff4d4f' : undefined }}
                        />
                      </Col>
                    )}
                  </Row>
                </div>

                {currentJob.status === 'completed' && (
                  <Result
                    status="success"
                    title="数据处理完成！"
                    subTitle={`成功处理 ${currentJob.uniqueCount} 个唯一资产，去除 ${currentJob.duplicatesRemoved} 个重复项`}
                    extra={[
                      <Button key="correlations" onClick={() => setActiveTab('correlations')}>
                        查看关联分析
                      </Button>,
                      <Button key="new" type="primary" onClick={() => {
                        setActiveTab('upload');
                        setCurrentJob(null);
                        setUploadFiles([]);
                      }}>
                        处理新数据
                      </Button>,
                    ]}
                  />
                )}

                {currentJob.status === 'failed' && (
                  <Result
                    status="error"
                    title="数据处理失败"
                    subTitle="请检查数据格式并重新尝试"
                    extra={[
                      <Button key="retry" type="primary" onClick={() => {
                        setActiveTab('upload');
                        setCurrentJob(null);
                      }}>
                        重新尝试
                      </Button>,
                    ]}
                  />
                )}
              </div>
            ) : (
              <Alert message="暂无正在处理的任务" type="info" />
            )}
          </Card>
        </TabPane>

        <TabPane tab="历史任务" key="history">
          <Card>
            <Table
              columns={jobColumns}
              dataSource={processingJobs}
              rowKey="id"
              size="small"
              pagination={{ pageSize: 10 }}
            />
          </Card>
        </TabPane>

        <TabPane tab="关联分析" key="correlations">
          <Card title="资产关联分析结果">
            {renderCorrelationAnalysis()}
          </Card>
        </TabPane>
      </Tabs>
    </div>
  );
};

export default DataProcessingManager;