import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Form,
  Input,
  Select,
  Row,
  Col,
  Tag,
  message,
  Upload,
  Modal,
  Typography,
  Statistic,
  Alert,
  DatePicker,
  Drawer,
  Descriptions,
  Tabs,
  Progress,
  Badge,
  Tooltip,
  Divider,
  Spin
} from 'antd';
import {
  SearchOutlined,
  UploadOutlined,
  ReloadOutlined,
  BarChartOutlined,
  ClearOutlined,
  EyeOutlined,
  ExportOutlined,
  ApiOutlined,
  GlobalOutlined,
  SettingOutlined,
  DashboardOutlined,
  SecurityScanOutlined,
  LineChartOutlined,
  BugOutlined,
  ThunderboltOutlined,
  DatabaseOutlined,
  MonitorOutlined
} from '@ant-design/icons';
import { Line, Pie, Column } from '@ant-design/plots';
import dayjs from 'dayjs';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;
const { RangePicker } = DatePicker;
const { TabPane } = Tabs;

// 统一资产管理v2服务
const assetsV2UnifiedService = {
  // 基础API配置
  baseURL: '/api/assets-v2-unified',
  
  // 搜索资产
  searchAssets: async (params: any) => {
    const response = await fetch(`${assetsV2UnifiedService.baseURL}/search`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(params)
    });
    return response.json();
  },
  
  // 获取统计信息
  getStatistics: async () => {
    const response = await fetch(`${assetsV2UnifiedService.baseURL}/statistics`);
    return response.json();
  },
  
  // 趋势分析
  getTrendAnalysis: async (params: any) => {
    const queryParams = new URLSearchParams(params);
    const response = await fetch(`${assetsV2UnifiedService.baseURL}/analysis/trends?${queryParams}`);
    return response.json();
  },
  
  // 安全风险评估
  getSecurityRisk: async () => {
    const response = await fetch(`${assetsV2UnifiedService.baseURL}/analysis/security-risk`);
    return response.json();
  },
  
  // 数据质量评估
  getDataQuality: async () => {
    const response = await fetch(`${assetsV2UnifiedService.baseURL}/quality/assessment`);
    return response.json();
  },
  
  // 异常检测
  getAnomalies: async (params: any) => {
    const queryParams = new URLSearchParams(params);
    const response = await fetch(`${assetsV2UnifiedService.baseURL}/analysis/anomalies?${queryParams}`);
    return response.json();
  },
  
  // 去重分析
  getDuplicateAnalysis: async () => {
    const response = await fetch(`${assetsV2UnifiedService.baseURL}/analysis/duplicates`);
    return response.json();
  },
  
  // 索引健康状态
  getIndexHealth: async () => {
    const response = await fetch(`${assetsV2UnifiedService.baseURL}/admin/index/health`);
    return response.json();
  },
  
  // Kibana设置
  getKibanaSetup: async () => {
    const response = await fetch(`${assetsV2UnifiedService.baseURL}/kibana/setup`);
    return response.json();
  }
};

const AssetsV2Unified: React.FC = () => {
  // 基础状态
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');
  
  // 数据状态
  const [assets, setAssets] = useState<any[]>([]);
  const [statistics, setStatistics] = useState<any>(null);
  const [trends, setTrends] = useState<any>(null);
  const [securityRisk, setSecurityRisk] = useState<any>(null);
  const [dataQuality, setDataQuality] = useState<any>(null);
  const [anomalies, setAnomalies] = useState<any>(null);
  const [duplicates, setDuplicates] = useState<any>(null);
  const [indexHealth, setIndexHealth] = useState<any>(null);
  
  // 搜索状态
  const [searchForm] = Form.useForm();
  const [searchParams, setSearchParams] = useState({
    query: '*',
    page: 1,
    size: 20,
    include_aggregations: true
  });
  
  // 分页状态
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [total, setTotal] = useState(0);
  
  // UI状态
  const [selectedAsset, setSelectedAsset] = useState<any>(null);
  const [drawerVisible, setDrawerVisible] = useState(false);

  // 初始化加载
  useEffect(() => {
    loadInitialData();
  }, []);

  const loadInitialData = async () => {
    setLoading(true);
    try {
      await Promise.all([
        loadAssets(),
        loadStatistics(),
        loadTrendAnalysis(),
        loadSecurityRisk(),
        loadDataQuality(),
        loadIndexHealth()
      ]);
    } catch (error) {
      console.error('初始化加载失败:', error);
      message.error('数据加载失败');
    } finally {
      setLoading(false);
    }
  };

  const loadAssets = async () => {
    try {
      const response = await assetsV2UnifiedService.searchAssets({
        ...searchParams,
        page: currentPage,
        size: pageSize
      });
      
      if (response.success) {
        setAssets(response.data.hits || []);
        setTotal(response.data.total || 0);
      } else {
        message.error('加载资产失败');
      }
    } catch (error) {
      console.error('加载资产失败:', error);
      message.error('加载资产失败');
    }
  };

  const loadStatistics = async () => {
    try {
      const response = await assetsV2UnifiedService.getStatistics();
      if (response.success) {
        setStatistics(response.data);
      }
    } catch (error) {
      console.error('加载统计信息失败:', error);
    }
  };

  const loadTrendAnalysis = async () => {
    try {
      const response = await assetsV2UnifiedService.getTrendAnalysis({
        time_range: '30d',
        interval: '1d'
      });
      if (response.success) {
        setTrends(response.data);
      }
    } catch (error) {
      console.error('加载趋势分析失败:', error);
    }
  };

  const loadSecurityRisk = async () => {
    try {
      const response = await assetsV2UnifiedService.getSecurityRisk();
      if (response.success) {
        setSecurityRisk(response.data);
      }
    } catch (error) {
      console.error('加载安全风险失败:', error);
    }
  };

  const loadDataQuality = async () => {
    try {
      const response = await assetsV2UnifiedService.getDataQuality();
      if (response.success) {
        setDataQuality(response.data);
      }
    } catch (error) {
      console.error('加载数据质量失败:', error);
    }
  };

  const loadIndexHealth = async () => {
    try {
      const response = await assetsV2UnifiedService.getIndexHealth();
      if (response.success) {
        setIndexHealth(response.data);
      }
    } catch (error) {
      console.error('加载索引健康状态失败:', error);
    }
  };

  const handleSearch = async (values: any) => {
    const newParams = {
      ...searchParams,
      ...values,
      page: 1
    };
    setSearchParams(newParams);
    setCurrentPage(1);
    
    try {
      const response = await assetsV2UnifiedService.searchAssets(newParams);
      if (response.success) {
        setAssets(response.data.hits || []);
        setTotal(response.data.total || 0);
        message.success(`找到 ${response.data.total || 0} 个资产`);
      }
    } catch (error) {
      message.error('搜索失败');
    }
  };

  const handleViewDetails = (asset: any) => {
    setSelectedAsset(asset);
    setDrawerVisible(true);
  };

  // 获取风险等级颜色
  const getRiskLevelColor = (level: string) => {
    const colors = {
      critical: '#ff4d4f',
      high: '#ff7a45',
      medium: '#ffa940',
      low: '#52c41a',
      minimal: '#1890ff'
    };
    return colors[level as keyof typeof colors] || '#d9d9d9';
  };

  // 获取置信度颜色
  const getConfidenceColor = (confidence: string) => {
    const colors = {
      high: '#52c41a',
      medium: '#faad14',
      low: '#ff4d4f',
      unknown: '#d9d9d9'
    };
    return colors[confidence as keyof typeof colors] || '#d9d9d9';
  };

  // 表格列定义
  const columns = [
    {
      title: '资产类型',
      dataIndex: 'asset_type',
      key: 'asset_type',
      width: 100,
      render: (type: string) => (
        <Tag color="blue">{type}</Tag>
      ),
    },
    {
      title: '资产值',
      dataIndex: 'asset_value',
      key: 'asset_value',
      width: 300,
      ellipsis: true,
      render: (value: string) => (
        <Text code style={{ fontSize: '12px' }}>{value}</Text>
      ),
    },
    {
      title: '主机',
      dataIndex: 'asset_host',
      key: 'asset_host',
      width: 150,
      ellipsis: true,
    },
    {
      title: '置信度',
      dataIndex: 'confidence',
      key: 'confidence',
      width: 80,
      render: (confidence: string) => (
        <Tag color={getConfidenceColor(confidence)}>
          {confidence}
        </Tag>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status: string) => (
        <Tag color={status === 'active' ? 'green' : 'default'}>
          {status}
        </Tag>
      ),
    },
    {
      title: '来源',
      dataIndex: 'source',
      key: 'source',
      width: 120,
      render: (source: string) => (
        <Tag>{source}</Tag>
      ),
    },
    {
      title: '发现时间',
      dataIndex: 'discovered_at',
      key: 'discovered_at',
      width: 120,
      render: (date: string) => date ? (
        <Text type="secondary" style={{ fontSize: '11px' }}>
          {dayjs(date).format('MM-DD HH:mm')}
        </Text>
      ) : '-',
    },
    {
      title: '操作',
      key: 'action',
      width: 60,
      fixed: 'right' as const,
      render: (record: any) => (
        <Button
          type="text"
          size="small"
          icon={<EyeOutlined />}
          onClick={() => handleViewDetails(record)}
        />
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>
        <DatabaseOutlined /> 资产管理 V2.0 统一平台
      </Title>
      <Paragraph type="secondary">
        基于Elasticsearch的统一资产管理系统，支持大数据量、数据清洗、去重和Kibana集成
      </Paragraph>

      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        {/* 概览面板 */}
        <TabPane tab={<span><DashboardOutlined />概览</span>} key="overview">
          <Spin spinning={loading}>
            {/* 统计卡片 */}
            {statistics && (
              <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
                <Col span={6}>
                  <Card>
                    <Statistic
                      title="总资产数"
                      value={statistics.total_assets}
                      prefix={<DatabaseOutlined />}
                      valueStyle={{ color: '#1890ff' }}
                    />
                  </Card>
                </Col>
                <Col span={6}>
                  <Card>
                    <Statistic
                      title="唯一资产"
                      value={statistics.unique_assets}
                      prefix={<ThunderboltOutlined />}
                      valueStyle={{ color: '#52c41a' }}
                    />
                  </Card>
                </Col>
                <Col span={6}>
                  <Card>
                    <Statistic
                      title="关联平台"
                      value={statistics.platform_count}
                      prefix={<GlobalOutlined />}
                      valueStyle={{ color: '#722ed1' }}
                    />
                  </Card>
                </Col>
                <Col span={6}>
                  <Card>
                    <Statistic
                      title="数据质量"
                      value={statistics.avg_data_quality}
                      suffix="/ 1.0"
                      prefix={<MonitorOutlined />}
                      valueStyle={{ color: '#fa8c16' }}
                    />
                  </Card>
                </Col>
              </Row>
            )}

            {/* 风险和质量概览 */}
            <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
              {securityRisk && (
                <Col span={12}>
                  <Card title={<span><SecurityScanOutlined /> 安全风险评估</span>}>
                    <div style={{ textAlign: 'center', marginBottom: 16 }}>
                      <Progress
                        type="circle"
                        percent={(securityRisk.overall_risk.score / securityRisk.overall_risk.max_score) * 100}
                        format={() => (
                          <div>
                            <div style={{ fontSize: '24px', fontWeight: 'bold', color: getRiskLevelColor(securityRisk.overall_risk.level) }}>
                              {securityRisk.overall_risk.score}
                            </div>
                            <div style={{ fontSize: '12px', color: '#666' }}>
                              {securityRisk.overall_risk.level.toUpperCase()}
                            </div>
                          </div>
                        )}
                        strokeColor={getRiskLevelColor(securityRisk.overall_risk.level)}
                        size={120}
                      />
                    </div>
                    <div>
                      {securityRisk.risk_factors.slice(0, 3).map((factor: any, index: number) => (
                        <div key={index} style={{ marginBottom: 8 }}>
                          <Text strong>{factor.factor}:</Text>
                          <Text type="secondary" style={{ marginLeft: 8 }}>
                            {factor.description}
                          </Text>
                          <div style={{ marginTop: 4 }}>
                            <Progress
                              percent={(factor.score / 20) * 100}
                              size="small"
                              strokeColor={getRiskLevelColor(securityRisk.overall_risk.level)}
                              showInfo={false}
                            />
                          </div>
                        </div>
                      ))}
                    </div>
                  </Card>
                </Col>
              )}

              {indexHealth && (
                <Col span={12}>
                  <Card title={<span><MonitorOutlined /> 系统健康状态</span>}>
                    <Row gutter={16}>
                      <Col span={12}>
                        <Statistic
                          title="集群状态"
                          value={indexHealth.cluster_health.status}
                          valueStyle={{
                            color: indexHealth.cluster_health.status === 'green' ? '#52c41a' :
                                   indexHealth.cluster_health.status === 'yellow' ? '#faad14' : '#ff4d4f'
                          }}
                        />
                      </Col>
                      <Col span={12}>
                        <Statistic
                          title="活跃分片"
                          value={indexHealth.cluster_health.active_shards}
                          valueStyle={{ color: '#1890ff' }}
                        />
                      </Col>
                    </Row>
                    <Divider />
                    <Row gutter={16}>
                      <Col span={12}>
                        <Statistic
                          title="总文档数"
                          value={indexHealth.index_metrics.total_documents}
                          valueStyle={{ color: '#722ed1' }}
                        />
                      </Col>
                      <Col span={12}>
                        <Statistic
                          title="存储大小"
                          value={indexHealth.index_metrics.total_size_mb}
                          suffix="MB"
                          valueStyle={{ color: '#fa8c16' }}
                        />
                      </Col>
                    </Row>
                  </Card>
                </Col>
              )}
            </Row>

            {/* 趋势图表 */}
            {trends && trends.trends && (
              <Row gutter={[16, 16]}>
                {trends.trends.discovery_count && (
                  <Col span={12}>
                    <Card title={<span><LineChartOutlined /> 资产发现趋势</span>}>
                      <Line
                        data={trends.trends.discovery_count}
                        xField="date"
                        yField="count"
                        height={200}
                        smooth
                        point={{ size: 3 }}
                        color="#1890ff"
                      />
                    </Card>
                  </Col>
                )}

                {statistics && statistics.asset_types && (
                  <Col span={12}>
                    <Card title="资产类型分布">
                      <Pie
                        data={Object.entries(statistics.asset_types).map(([type, count]) => ({
                          type,
                          value: count as number
                        }))}
                        angleField="value"
                        colorField="type"
                        radius={0.8}
                        height={200}
                        label={{
                          type: 'outer',
                          content: '{name}: {percentage}'
                        }}
                      />
                    </Card>
                  </Col>
                )}
              </Row>
            )}
          </Spin>
        </TabPane>

        {/* 资产搜索面板 */}
        <TabPane tab={<span><SearchOutlined />资产搜索</span>} key="search">
          {/* 搜索表单 */}
          <Card style={{ marginBottom: 16 }}>
            <Form
              form={searchForm}
              onFinish={handleSearch}
              layout="vertical"
            >
              <Row gutter={[16, 16]}>
                <Col xs={24} sm={12} md={8}>
                  <Form.Item name="query" label="搜索关键词">
                    <Input
                      placeholder="搜索资产值、主机或目标..."
                      prefix={<SearchOutlined />}
                    />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12} md={8}>
                  <Form.Item name="asset_type" label="资产类型">
                    <Select placeholder="选择资产类型" allowClear>
                      <Option value="domain">域名</Option>
                      <Option value="subdomain">子域名</Option>
                      <Option value="ip">IP地址</Option>
                      <Option value="url">URL</Option>
                      <Option value="port">端口</Option>
                      <Option value="api_endpoint">API端点</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12} md={8}>
                  <Form.Item name="confidence" label="置信度">
                    <Select placeholder="选择置信度" allowClear>
                      <Option value="high">高</Option>
                      <Option value="medium">中</Option>
                      <Option value="low">低</Option>
                      <Option value="unknown">未知</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12} md={8}>
                  <Form.Item name="status" label="状态">
                    <Select placeholder="选择状态" allowClear>
                      <Option value="active">活跃</Option>
                      <Option value="inactive">非活跃</Option>
                      <Option value="pending">待验证</Option>
                      <Option value="verified">已验证</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12} md={8}>
                  <Form.Item name="source" label="发现来源">
                    <Select placeholder="选择发现来源" allowClear>
                      <Option value="agent_discovery">Agent发现</Option>
                      <Option value="dynamic_model">动态模型</Option>
                      <Option value="manual_import">手动导入</Option>
                      <Option value="api_sync">API同步</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12} md={8}>
                  <Form.Item name="dateRange" label="发现时间">
                    <RangePicker
                      style={{ width: '100%' }}
                      format="YYYY-MM-DD"
                    />
                  </Form.Item>
                </Col>
              </Row>
              <Row>
                <Col span={24}>
                  <Space>
                    <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
                      搜索
                    </Button>
                    <Button onClick={() => {
                      searchForm.resetFields();
                      setSearchParams({ query: '*', page: 1, size: 20, include_aggregations: true });
                      loadAssets();
                    }} icon={<ReloadOutlined />}>
                      重置
                    </Button>
                    <Button icon={<ExportOutlined />}>
                      导出
                    </Button>
                  </Space>
                </Col>
              </Row>
            </Form>
          </Card>

          {/* 资产列表 */}
          <Card>
            <Table
              columns={columns}
              dataSource={assets}
              loading={loading}
              rowKey="asset_id"
              pagination={{
                current: currentPage,
                pageSize: pageSize,
                total: total,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) =>
                  `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
                onChange: (page, size) => {
                  setCurrentPage(page);
                  setPageSize(size!);
                  loadAssets();
                },
              }}
              scroll={{ x: 1200, y: 600 }}
            />
          </Card>
        </TabPane>

        {/* 数据分析面板 */}
        <TabPane tab={<span><BarChartOutlined />数据分析</span>} key="analysis">
          <Row gutter={[16, 16]}>
            {/* 异常检测 */}
            <Col span={24}>
              <Card
                title={<span><BugOutlined /> 异常检测</span>}
                extra={
                  <Button
                    size="small"
                    onClick={async () => {
                      try {
                        const response = await assetsV2UnifiedService.getAnomalies({
                          field: 'data_quality_score',
                          threshold_std: 2.0
                        });
                        if (response.success) {
                          setAnomalies(response.data);
                          message.success(`检测到 ${response.data.anomaly_count} 个异常`);
                        }
                      } catch (error) {
                        message.error('异常检测失败');
                      }
                    }}
                  >
                    重新检测
                  </Button>
                }
              >
                {anomalies ? (
                  <div>
                    <Row gutter={16} style={{ marginBottom: 16 }}>
                      <Col span={6}>
                        <Statistic
                          title="异常数量"
                          value={anomalies.anomaly_count}
                          valueStyle={{ color: '#ff4d4f' }}
                        />
                      </Col>
                      <Col span={6}>
                        <Statistic
                          title="平均值"
                          value={anomalies.statistics.mean}
                          precision={3}
                        />
                      </Col>
                      <Col span={6}>
                        <Statistic
                          title="标准差"
                          value={anomalies.statistics.std_deviation}
                          precision={3}
                        />
                      </Col>
                      <Col span={6}>
                        <Statistic
                          title="样本数"
                          value={anomalies.statistics.count}
                        />
                      </Col>
                    </Row>

                    {anomalies.anomalies && anomalies.anomalies.length > 0 && (
                      <div>
                        <Title level={5}>异常资产</Title>
                        <div style={{ maxHeight: 300, overflow: 'auto' }}>
                          {anomalies.anomalies.slice(0, 10).map((anomaly: any, index: number) => (
                            <Alert
                              key={index}
                              message={
                                <div>
                                  <Text strong>{anomaly.asset_value}</Text>
                                  <Tag color={anomaly.severity === 'critical' ? 'red' : 'orange'} style={{ marginLeft: 8 }}>
                                    {anomaly.severity}
                                  </Tag>
                                  <Tag color={anomaly.anomaly_type === 'high' ? 'red' : 'blue'} style={{ marginLeft: 4 }}>
                                    {anomaly.anomaly_type}
                                  </Tag>
                                </div>
                              }
                              description={`${anomalies.field}: ${anomaly.field_value}, 偏差: ${anomaly.deviation.toFixed(2)}σ`}
                              type={anomaly.severity === 'critical' ? 'error' : 'warning'}
                              style={{ marginBottom: 8 }}
                              size="small"
                            />
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  <div style={{ textAlign: 'center', padding: '40px 0' }}>
                    <Text type="secondary">点击"重新检测"开始异常检测</Text>
                  </div>
                )}
              </Card>
            </Col>

            {/* 重复资产分析 */}
            <Col span={24}>
              <Card
                title={<span><ClearOutlined /> 重复资产分析</span>}
                extra={
                  <Button
                    size="small"
                    onClick={async () => {
                      try {
                        const response = await assetsV2UnifiedService.getDuplicateAnalysis();
                        if (response.success) {
                          setDuplicates(response.data);
                          message.success(`发现 ${response.data.total_duplicate_groups} 个重复组`);
                        }
                      } catch (error) {
                        message.error('重复分析失败');
                      }
                    }}
                  >
                    重新分析
                  </Button>
                }
              >
                {duplicates ? (
                  <div>
                    <Row gutter={16} style={{ marginBottom: 16 }}>
                      <Col span={8}>
                        <Statistic
                          title="重复组数"
                          value={duplicates.total_duplicate_groups}
                          valueStyle={{ color: '#fa8c16' }}
                        />
                      </Col>
                      <Col span={8}>
                        <Statistic
                          title="重复资产数"
                          value={duplicates.total_duplicate_assets}
                          valueStyle={{ color: '#ff4d4f' }}
                        />
                      </Col>
                      <Col span={8}>
                        <Statistic
                          title="相似词数"
                          value={duplicates.similar_terms?.length || 0}
                          valueStyle={{ color: '#1890ff' }}
                        />
                      </Col>
                    </Row>

                    {duplicates.duplicate_groups && duplicates.duplicate_groups.length > 0 && (
                      <div>
                        <Title level={5}>重复组详情</Title>
                        <div style={{ maxHeight: 300, overflow: 'auto' }}>
                          {duplicates.duplicate_groups.slice(0, 5).map((group: any, index: number) => (
                            <Card key={index} size="small" style={{ marginBottom: 8 }}>
                              <div>
                                <Text strong>指纹: </Text>
                                <Text code>{group.fingerprint.substring(0, 16)}...</Text>
                                <Badge count={group.count} style={{ marginLeft: 8 }} />
                              </div>
                              <div style={{ marginTop: 8 }}>
                                {group.assets.slice(0, 3).map((asset: any, assetIndex: number) => (
                                  <Tag key={assetIndex} style={{ marginBottom: 4 }}>
                                    {asset.asset_value}
                                  </Tag>
                                ))}
                                {group.assets.length > 3 && (
                                  <Text type="secondary">... 还有 {group.assets.length - 3} 个</Text>
                                )}
                              </div>
                            </Card>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  <div style={{ textAlign: 'center', padding: '40px 0' }}>
                    <Text type="secondary">点击"重新分析"开始重复检测</Text>
                  </div>
                )}
              </Card>
            </Col>
          </Row>
        </TabPane>

        {/* Kibana集成面板 */}
        <TabPane tab={<span><DashboardOutlined />Kibana集成</span>} key="kibana">
          <Row gutter={[16, 16]}>
            <Col span={24}>
              <Card title="Kibana仪表板集成">
                <Alert
                  message="Kibana深度集成"
                  description="通过Kibana获得强大的数据可视化和分析能力，包括实时仪表板、自定义图表和高级分析功能。"
                  type="info"
                  showIcon
                  style={{ marginBottom: 16 }}
                />

                <Row gutter={16}>
                  <Col span={8}>
                    <Card size="small" title="资产概览仪表板">
                      <Paragraph>
                        资产管理系统总体概览，包括资产数量、类型分布、发现趋势等核心指标。
                      </Paragraph>
                      <Button type="primary" block>
                        打开仪表板
                      </Button>
                    </Card>
                  </Col>
                  <Col span={8}>
                    <Card size="small" title="安全分析仪表板">
                      <Paragraph>
                        安全相关资产分析，包括漏洞热力图、端口分布、风险评估等。
                      </Paragraph>
                      <Button type="primary" block>
                        打开仪表板
                      </Button>
                    </Card>
                  </Col>
                  <Col span={8}>
                    <Card size="small" title="数据质量仪表板">
                      <Paragraph>
                        数据质量监控和分析，包括质量分数分布、缺失字段分析等。
                      </Paragraph>
                      <Button type="primary" block>
                        打开仪表板
                      </Button>
                    </Card>
                  </Col>
                </Row>

                <Divider />

                <Title level={5}>设置说明</Title>
                <ol>
                  <li>确保Kibana服务正在运行 (http://localhost:5601)</li>
                  <li>在Kibana中创建索引模式: unified-assets-*</li>
                  <li>设置时间字段: discovered_at</li>
                  <li>导入预配置的仪表板和可视化</li>
                </ol>

                <Space>
                  <Button
                    onClick={async () => {
                      try {
                        const response = await assetsV2UnifiedService.getKibanaSetup();
                        if (response.success) {
                          message.success('Kibana配置获取成功');
                          console.log('Kibana配置:', response.data);
                        }
                      } catch (error) {
                        message.error('获取Kibana配置失败');
                      }
                    }}
                  >
                    获取配置
                  </Button>
                  <Button type="primary" onClick={() => window.open('http://localhost:5601', '_blank')}>
                    打开Kibana
                  </Button>
                </Space>
              </Card>
            </Col>
          </Row>
        </TabPane>
      </Tabs>

      {/* 资产详情抽屉 */}
      <Drawer
        title="资产详情"
        width={700}
        onClose={() => setDrawerVisible(false)}
        open={drawerVisible}
      >
        {selectedAsset && (
          <div>
            <Descriptions title="基本信息" bordered size="small" column={2}>
              <Descriptions.Item label="资产ID" span={2}>
                <Text code>{selectedAsset.asset_id}</Text>
              </Descriptions.Item>
              <Descriptions.Item label="资产类型">
                <Tag color="blue">{selectedAsset.asset_type}</Tag>
              </Descriptions.Item>
              <Descriptions.Item label="资产值" span={2}>
                <Text code style={{ fontSize: '14px' }}>{selectedAsset.asset_value}</Text>
              </Descriptions.Item>
              <Descriptions.Item label="主机">
                {selectedAsset.asset_host || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="端口">
                {selectedAsset.asset_port || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="服务">
                {selectedAsset.asset_service || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="置信度">
                <Tag color={getConfidenceColor(selectedAsset.confidence)}>
                  {selectedAsset.confidence}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="状态">
                <Tag color={selectedAsset.status === 'active' ? 'green' : 'default'}>
                  {selectedAsset.status}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="来源">
                <Tag>{selectedAsset.source}</Tag>
              </Descriptions.Item>
              <Descriptions.Item label="发现时间" span={2}>
                {selectedAsset.discovered_at ?
                  dayjs(selectedAsset.discovered_at).format('YYYY-MM-DD HH:mm:ss') : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="数据质量分数">
                <Progress
                  percent={Math.round((selectedAsset.data_quality_score || 0) * 100)}
                  size="small"
                  style={{ width: 100 }}
                />
              </Descriptions.Item>
            </Descriptions>

            {selectedAsset.tags && selectedAsset.tags.length > 0 && (
              <div style={{ marginTop: 16 }}>
                <Title level={5}>标签</Title>
                <Space wrap>
                  {selectedAsset.tags.map((tag: string, index: number) => (
                    <Tag key={index} color="processing">{tag}</Tag>
                  ))}
                </Space>
              </div>
            )}

            {selectedAsset.metadata && Object.keys(selectedAsset.metadata).length > 0 && (
              <div style={{ marginTop: 16 }}>
                <Title level={5}>元数据</Title>
                <Card size="small" style={{ background: '#fafafa' }}>
                  <pre style={{
                    margin: 0,
                    fontSize: '12px',
                    lineHeight: '1.4',
                    maxHeight: '200px',
                    overflow: 'auto'
                  }}>
                    {JSON.stringify(selectedAsset.metadata, null, 2)}
                  </pre>
                </Card>
              </div>
            )}
          </div>
        )}
      </Drawer>
    </div>
  );
};

export default AssetsV2Unified;
