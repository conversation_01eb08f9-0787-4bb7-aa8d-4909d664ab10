/* 全局样式 */
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#root {
  min-height: 100vh;
  margin: 0;
  padding: 0;
  max-width: none;
  text-align: left;
}

.App {
  min-height: 100vh;
}

/* 代码样式 */
code {
  font-family: 'Fira Code', 'Monaco', 'Consolas', 'Ubuntu Mono', monospace;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 表格样式优化 */
.ant-table-tbody > tr > td {
  padding: 12px 16px;
}

.ant-table-thead > tr > th {
  background: #fafafa;
  font-weight: 600;
}

/* 卡片样式 */
.ant-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border-radius: 8px;
}

.ant-card-head {
  border-bottom: 1px solid #f0f0f0;
}

/* 按钮样式 */
.ant-btn {
  border-radius: 6px;
  font-weight: 500;
}

.ant-btn-primary {
  background: #1890ff;
  border-color: #1890ff;
}

.ant-btn-primary:hover {
  background: #40a9ff;
  border-color: #40a9ff;
}

/* 标签样式 */
.ant-tag {
  border-radius: 4px;
  font-size: 12px;
  padding: 2px 8px;
  margin-right: 4px;
}

/* 统计数字样式 */
.ant-statistic-content {
  font-size: 20px;
  font-weight: 600;
}

/* 抽屉样式 */
.ant-drawer-body {
  padding: 20px;
}

/* 描述列表样式 */
.ant-descriptions-item-label {
  font-weight: 600;
  color: #666;
}

/* 时间线样式 */
.ant-timeline-item-content {
  margin-left: 16px;
}

/* 进度条样式 */
.ant-progress-circle .ant-progress-text {
  font-size: 14px;
  font-weight: 600;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .ant-card {
    margin-bottom: 16px;
  }
  
  .ant-table-wrapper {
    overflow-x: auto;
  }
}

/* 自定义工具类 */
.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.mb-0 {
  margin-bottom: 0;
}

.mt-16 {
  margin-top: 16px;
}

.mb-16 {
  margin-bottom: 16px;
}

/* 图表容器样式 */
.chart-container {
  position: relative;
  height: 300px;
  width: 100%;
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #999;
}

/* 错误状态样式 */
.error-state {
  text-align: center;
  padding: 40px 20px;
  color: #ff4d4f;
}

/* 成功状态样式 */
.success-state {
  color: #52c41a;
}

/* 警告状态样式 */
.warning-state {
  color: #faad14;
}
