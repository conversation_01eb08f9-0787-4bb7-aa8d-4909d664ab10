import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { AuthProvider } from './context/AuthContext';
import ErrorBoundary from './components/ErrorBoundary';

// 基础组件
import Login from './pages/Login';
import Dashboard from './pages/Dashboard';
import MainLayout from './components/Layout/MainLayout';

// 已测试正常的组件
import Assets from './pages/Assets';
import Tasks from './pages/Tasks';
import PlatformProjectManager from './pages/PlatformProjectManager';

// 使用修复后的原始Agents组件
import Agents from './pages/Agents';

// 其他页面组件 - 逐个测试
import Search from './pages/Search';
import Models from './pages/Models';
import Workflows from './pages/Workflows';
import AdvancedSearch from './pages/AdvancedSearch';
import SecurityDashboard from './pages/SecurityDashboard';
import DataProcessingManager from './pages/DataProcessingManager';
import IntelligentAggregation from './pages/IntelligentAggregation';
import DataImport from './pages/DataImport';
import AssetsV2Test from './pages/AssetsV2Test';

const App: React.FC = () => {
  console.log('App rendering - 添加所有组件...');
  
  return (
    <ConfigProvider locale={zhCN}>
      <ErrorBoundary>
        <AuthProvider>
          <Router>
            <Routes>
              {/* 登录路由 */}
              <Route path="/login" element={<Login />} />
              
              {/* 主应用路由 - 使用MainLayout包装 */}
              <Route path="/" element={<MainLayout />}>
                <Route index element={<Dashboard />} />
                <Route path="platform-project" element={<PlatformProjectManager />} />
                <Route path="assets" element={<Assets />} />
                <Route path="assets-v2-test" element={<AssetsV2Test />} />
                <Route path="agents" element={<Agents />} />
                <Route path="tasks" element={<Tasks />} />
                <Route path="search" element={<Search />} />
                <Route path="models" element={<Models />} />
                <Route path="workflows" element={<Workflows />} />
                <Route path="advanced-search" element={<AdvancedSearch />} />
                <Route path="security-dashboard" element={<SecurityDashboard />} />
                <Route path="data-processing" element={<DataProcessingManager />} />
                <Route path="intelligent-aggregation" element={<IntelligentAggregation />} />
                <Route path="data-import" element={<DataImport />} />
              </Route>
            </Routes>
          </Router>
        </AuthProvider>
      </ErrorBoundary>
    </ConfigProvider>
  );
};

export default App;
