import React, { createContext, useState, useContext, useEffect } from 'react';
import { message } from 'antd';
import { api } from '../services/api';
import { showErrorMessage } from '../utils/errorHandler';

interface User {
  id: string;
  username: string;
  is_admin: boolean;
  email?: string;
  is_active?: boolean;
}

interface AuthContextType {
  user: User | null;
  token: string | null;
  login: (username: string, password: string) => Promise<boolean>;
  logout: () => void;
  loading: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Check for existing token in localStorage
    const storedToken = localStorage.getItem('token');
    const storedUser = localStorage.getItem('user');
    
    if (storedToken && storedUser) {
      setToken(storedToken);
      setUser(JSON.parse(storedUser));
      api.defaults.headers.common['Authorization'] = `Bearer ${storedToken}`;
    }
    setLoading(false);
  }, []);

  const login = async (username: string, password: string): Promise<boolean> => {
    try {
      console.log('开始登录请求...', { username });
      
      const response = await api.post('/auth/login', {
        username,
        password,
      });

      console.log('登录API响应:', response.status, response.data);

      const { access_token } = response.data;
      
      if (!access_token) {
        console.error('登录响应中没有access_token');
        message.error('登录响应格式错误');
        return false;
      }
      
      // 立即设置token和authorization header
      setToken(access_token);
      localStorage.setItem('token', access_token);
      api.defaults.headers.common['Authorization'] = `Bearer ${access_token}`;
      
      console.log('Token已设置，开始获取用户信息...');
      
      // Get user info after successful login
      try {
        const userResponse = await api.get('/auth/me');
        console.log('用户信息响应:', userResponse.status, userResponse.data);
        
        const userData = userResponse.data;
        
        setUser(userData);
        localStorage.setItem('user', JSON.stringify(userData));
        
        console.log('登录成功，用户信息已设置');
        message.success('登录成功');
        
        // 强制跳转到首页
        setTimeout(() => {
          window.location.href = '/';
        }, 100);
        
        return true;
      } catch (userError: any) {
        console.error('获取用户信息失败:', userError);
        
        // 即使获取用户信息失败，如果有token也算登录成功
        if (access_token) {
          // 创建一个默认用户对象
          const defaultUser = {
            id: 'unknown',
            username: username,
            is_admin: false
          };
          setUser(defaultUser);
          localStorage.setItem('user', JSON.stringify(defaultUser));
          
          message.warning('登录成功，但获取用户信息失败');
          
          // 强制跳转到首页
          setTimeout(() => {
            window.location.href = '/';
          }, 100);
          
          return true;
        }
        
        message.error('获取用户信息失败');
        return false;
      }
    } catch (error: any) {
      console.error('登录请求失败:', error);
      
      // 清理可能的部分状态
      setToken(null);
      setUser(null);
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      delete api.defaults.headers.common['Authorization'];
      
      showErrorMessage(error, '登录失败');
      return false;
    }
  };

  const logout = () => {
    setUser(null);
    setToken(null);
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    delete api.defaults.headers.common['Authorization'];
    message.success('已退出登录');
  };

  const value: AuthContextType = {
    user,
    token,
    login,
    logout,
    loading,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};