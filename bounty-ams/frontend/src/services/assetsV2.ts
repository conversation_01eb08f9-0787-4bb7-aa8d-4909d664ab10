/**
 * 资产管理服务 V2.0
 * 基于Elasticsearch的统一资产管理
 */

import { api } from './api';

export interface AssetSearchParams {
  q?: string;
  platform_id?: string;
  project_id?: string;
  asset_type?: string;
  confidence?: string;
  status?: string;
  tags?: string;
  sort_by?: string;
  sort_order?: string;
  page?: number;
  size?: number;
  include_aggs?: boolean;
}

export interface AssetSearchResult {
  total: number;
  hits: any[];
  page: number;
  size: number;
  aggregations?: any;
}

export interface AssetStats {
  total_assets: number;
  asset_types: Array<{key: string, count: number}>;
  platforms: Array<{key: string, count: number}>;
  projects: Array<{key: string, count: number}>;
  confidence_levels: Array<{key: string, count: number}>;
  discovery_timeline: Array<{date: string, count: number}>;
}

export interface ImportResult {
  status: string;
  message: string;
  details: {
    total_processed: number;
    success_count: number;
    duplicate_count: number;
    error_count: number;
    errors: string[];
  };
}

export const assetsV2Service = {
  /**
   * 搜索资产
   */
  searchAssets: (params: AssetSearchParams): Promise<{data: AssetSearchResult}> => {
    const searchParams = new URLSearchParams();
    
    // 确保参数正确传递
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        // 对布尔值特殊处理
        if (typeof value === 'boolean') {
          searchParams.append(key, value ? 'true' : 'false');
        } else {
          searchParams.append(key, String(value));
        }
      }
    });
    
    // 添加调试日志
    console.log('搜索URL:', `/assets-v2/search?${searchParams.toString()}`);
    
    return api.get(`/assets-v2/search?${searchParams.toString()}`);
  },

  /**
   * 获取资产统计
   */
  getAssetStats: (platform_id?: string, project_id?: string): Promise<{data: AssetStats}> => {
    const params = new URLSearchParams();
    if (platform_id) params.append('platform_id', platform_id);
    if (project_id) params.append('project_id', project_id);
    
    return api.get(`/assets-v2/stats?${params.toString()}`);
  },

  /**
   * 导入资产
   */
  importAssets: (
    file: File,
    mappings: any[],
    platform_id?: string,
    project_id?: string,
    auto_clean: boolean = true
  ): Promise<{data: ImportResult}> => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('mappings', JSON.stringify(mappings));
    formData.append('auto_clean', String(auto_clean));
    
    if (platform_id) {
      formData.append('platform_id', platform_id);
    }
    if (project_id) {
      formData.append('project_id', project_id);
    }
    
    return api.post('/assets-v2/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  /**
   * 获取平台资产
   */
  getPlatformAssets: (
    platform_id: string,
    page: number = 1,
    size: number = 50
  ): Promise<{data: AssetSearchResult}> => {
    return api.get(`/assets-v2/platforms/${platform_id}/assets`, {
      params: { page, size }
    });
  },

  /**
   * 获取项目资产
   */
  getProjectAssets: (
    project_id: string,
    page: number = 1,
    size: number = 50
  ): Promise<{data: AssetSearchResult}> => {
    return api.get(`/assets-v2/projects/${project_id}/assets`, {
      params: { page, size }
    });
  },

  /**
   * 批量更新资产
   */
  bulkUpdateAssets: (updates: any[]): Promise<{data: any}> => {
    return api.post('/assets-v2/bulk-update', updates);
  },

  /**
   * 清理重复资产
   */
  cleanupDuplicates: (dry_run: boolean = true): Promise<{data: any}> => {
    return api.delete('/assets-v2/cleanup', {
      params: { dry_run }
    });
  },

  /**
   * 高级搜索（兼容现有接口）
   */
  advancedSearch: (searchParams: any): Promise<{data: AssetSearchResult}> => {
    // 转换现有的高级搜索参数到V2格式
    const v2Params: AssetSearchParams = {
      q: searchParams.query || '*',
      platform_id: searchParams.platform_id,
      project_id: searchParams.project_id,
      asset_type: searchParams.asset_type,
      confidence: searchParams.confidence,
      status: searchParams.status,
      tags: Array.isArray(searchParams.tags) ? searchParams.tags.join(',') : searchParams.tags,
      page: searchParams.page || 1,
      size: searchParams.size || 20,
      include_aggs: true
    };
    
    return this.searchAssets(v2Params);
  }
};

/**
 * 数据转换工具
 */
export const assetDataTransformer = {
  /**
   * 将V2资产数据转换为前端兼容格式
   */
     transformAssetForDisplay: (asset: any) => {
    if (!asset) return null;
    
    return {
      _id: asset._id || asset.asset_id,
      _score: asset._score || 0,
      
      // 核心字段
      asset_type: asset.asset_type,
      asset_value: asset.asset_value,
      asset_host: asset.asset_host,
      
      // 关联信息
      platform_id: asset.platform_id,
      project_id: asset.project_id,
      platform_name: asset.platform_name,
      project_name: asset.project_name,
      
      // 状态信息
      status: asset.status,
      confidence: asset.confidence,
      tags: asset.tags || [],
      source: asset.source,
      source_task_type: asset.source_task_type,
      
      // 时间信息
      discovered_at: asset.discovered_at,
      last_seen_at: asset.last_seen_at,
      created_at: asset.created_at,
      updated_at: asset.updated_at,
      
      // 元数据
      metadata: asset.metadata || {},
      
      // 处理标记
      is_duplicate: asset.is_duplicate,
      duplicate_of: asset.duplicate_of,
      processing_status: asset.processing_status
    };
  },

  /**
   * 批量转换资产数据
   */
     transformAssetsForDisplay: (assets: any[]) => {
    return assets?.map(asset => assetDataTransformer.transformAssetForDisplay(asset)) || [];
  },

  /**
   * 转换聚合数据为图表友好格式
   */
  transformAggregationsForCharts: (aggregations: any) => {
    const charts: any = {};
    
    // 资产类型分布
    if (aggregations?.asset_types?.buckets) {
      charts.assetTypes = aggregations.asset_types.buckets.map((bucket: any) => ({
        name: bucket.key,
        value: bucket.doc_count
      }));
    }
    
    // 平台分布
    if (aggregations?.platforms?.buckets) {
      charts.platforms = aggregations.platforms.buckets.map((bucket: any) => ({
        name: bucket.key,
        value: bucket.doc_count
      }));
    }
    
    // 时间序列
    if (aggregations?.discovery_timeline?.buckets) {
      charts.timeline = aggregations.discovery_timeline.buckets.map((bucket: any) => ({
        date: bucket.key_as_string,
        count: bucket.doc_count
      }));
    }
    
    return charts;
  }
};

export default assetsV2Service; 
