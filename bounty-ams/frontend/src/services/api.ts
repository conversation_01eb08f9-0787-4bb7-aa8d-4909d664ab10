import axios from 'axios';

// 使用代理，所以直接使用 /api 而不是完整URL
const API_BASE_URL = '/api';

export const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 5000, // 减少到5秒
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    
    // 只在非登录请求时记录日志，避免日志污染
    if (!config.url?.includes('/auth/login')) {
      console.log('API Request:', config.method?.toUpperCase(), config.url, 'with auth:', !!token);
    }
    
    return config;
  },
  (error) => {
    console.error('Request interceptor error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor
api.interceptors.response.use(
  (response) => {
    // 只在非登录请求时记录日志
    if (!response.config.url?.includes('/auth/login')) {
      console.log('API Response:', response.status, response.config.url);
    }
    return response;
  },
  (error) => {
    console.error('API Error:', error.response?.status, error.response?.data, error.config?.url);
    
    if (error.response?.status === 401) {
      // Token expired or invalid - clear stored auth data
      console.log('Token expired or invalid, clearing auth data');
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      delete api.defaults.headers.common['Authorization'];
      
      // Only redirect to login if not already on login page
      if (window.location.pathname !== '/login') {
        window.location.href = '/login';
      }
    }
    return Promise.reject(error);
  }
);

// API Services
export const authService = {
  login: (username: string, password: string) =>
    api.post('/auth/login', { username, password }),
  
  getCurrentUser: () => api.get('/auth/me'),
};

export const assetService = {
  searchAssets: (params: any) => api.get('/discovered-assets/search', { params }),
  
  getAssetStats: () => api.get('/discovered-assets/stats'),
  
  getAssetTypes: () => api.get('/discovered-assets/types'),
  
  getAssetSources: () => api.get('/discovered-assets/sources'),
  
  getAssetTimeline: (days: number = 30) => 
    api.get(`/discovered-assets/timeline?days=${days}`),
  
  getAssetDetails: (assetId: string) => 
    api.get(`/discovered-assets/${assetId}`),
  
  processAssets: (limit: number = 100) => 
    api.post(`/discovered-assets/process-pending?limit=${limit}`),
  
  // 动态模型相关方法
  getDynamicModelTypes: () => api.get('/dynamic-models/types'),
  
  getDynamicEntities: (modelTypeId: string, params?: any) => 
    api.get(`/dynamic-models/entities`, { 
      params: { 
        model_type_id: modelTypeId,
        ...params 
      }
    }),
  
  getDynamicEntitiesCount: (modelTypeId: string, params?: any) => 
    api.get(`/dynamic-models/entities/count`, { 
      params: { 
        model_type_id: modelTypeId,
        ...params 
      }
    }),
  
  createDynamicEntity: (data: any) => api.post('/dynamic-models/entities', data),
  
  updateDynamicEntity: (entityId: string, data: any) => 
    api.put(`/dynamic-models/entities/${entityId}`, data),
  
  deleteDynamicEntity: (entityId: string) => 
    api.delete(`/dynamic-models/entities/${entityId}`),

  // 获取所有资产（聚合查询）
  getAllAssets: (params?: any) => 
    api.get('/dynamic-models/all-assets', { params }),

  // 获取所有资产数量
  getAllAssetsCount: (params?: any) => 
    api.get('/dynamic-models/all-assets/count', { params }),
};

export const workflowService = {
  getWorkflows: () => api.get('/workflows'),
  
  createWorkflow: (data: any) => api.post('/workflows', data),
  
  getWorkflowDetails: (workflowId: string) => 
    api.get(`/workflows/${workflowId}`),
  
  executeWorkflow: (workflowId: string, target: string) => 
    api.post(`/workflows/${workflowId}/execute`, { target }),
};

export const agentService = {
  getAgents: () => api.get('/agents/'),
  
  registerAgent: (data: any) => api.post('/agents/register', data),
  
  getAgentDetails: (agentId: string) => 
    api.get(`/agents/${agentId}`),
  
  updateAgentStatus: (agentId: string, status: string) => 
    api.post(`/agents/${agentId}/status`, { status }),
  
  deleteAgent: (agentId: string) => 
    api.delete(`/agents/${agentId}`),
  
  pingAgent: (agentId: string) =>
    api.post(`/agents/${agentId}/ping`),
    
  // Agent远程控制命令
  sendCommand: (agentId: string, action: string, parameters?: any) =>
    api.post(`/agents/${agentId}/command`, { action, parameters }),
  
  // 具体控制方法
  pauseAgent: (agentId: string) =>
    api.post(`/agents/${agentId}/command`, { action: 'pause' }),
    
  resumeAgent: (agentId: string) =>
    api.post(`/agents/${agentId}/command`, { action: 'resume' }),
    
  stopAgent: (agentId: string) =>
    api.post(`/agents/${agentId}/command`, { action: 'stop' }),
    
  restartAgent: (agentId: string) =>
    api.post(`/agents/${agentId}/command`, { action: 'restart' }),
    
  cancelAgentTasks: (agentId: string) =>
    api.post(`/agents/${agentId}/command`, { action: 'cancel_tasks' }),
};

export const taskService = {
  getTasks: (params: any) => api.get('/tasks', { params }),
  
  createTask: (data: any) => {
    // 如果是多个目标，使用批量创建API
    if (data.targets && Array.isArray(data.targets) && data.targets.length > 1) {
      return api.post('/tasks/batch', data);
    }
    // 单个目标使用原有API（保持向后兼容）
    return api.post('/tasks', data);
  },
  
  getTaskDetails: (taskId: string) => 
    api.get(`/tasks/${taskId}`),
  
  updateTaskStatus: (taskId: string, status: string) => 
    api.post(`/tasks/${taskId}/status`, { status }),
    
  cancelTask: (taskId: string) => 
    api.post(`/tasks/${taskId}/cancel`),
    
  retryTask: (taskId: string) => 
    api.post(`/tasks/${taskId}/retry`),
    
  deleteTask: (taskId: string) => 
    api.delete(`/tasks/${taskId}`),
  
  getTaskStats: () => api.get('/tasks/stats'),
  
  // 批量操作
  bulkCancelTasks: (taskIds: string[]) => 
    api.post('/tasks/bulk-cancel', { task_ids: taskIds }),
    
  bulkDeleteTasks: (taskIds: string[]) => 
    api.post('/tasks/bulk-delete', { task_ids: taskIds }),
};

export const dynamicModelService = {
  // Model Types
  getModelTypes: (params?: any) => api.get('/dynamic-models/types', { params }),
  
  createModelType: (data: any) => api.post('/dynamic-models/types', data),
  
  getModelType: (modelTypeId: string) => 
    api.get(`/dynamic-models/types/${modelTypeId}`),
  
  updateModelType: (modelTypeId: string, data: any) => 
    api.put(`/dynamic-models/types/${modelTypeId}`, data),
  
  deleteModelType: (modelTypeId: string) => 
    api.delete(`/dynamic-models/types/${modelTypeId}`),
  
  // Entities
  getEntities: (params?: any) => api.get('/dynamic-models/entities', { params }),
  
  createEntity: (data: any) => api.post('/dynamic-models/entities', data),
  
  getEntity: (entityId: string) => 
    api.get(`/dynamic-models/entities/${entityId}`),
  
  updateEntity: (entityId: string, data: any) => 
    api.put(`/dynamic-models/entities/${entityId}`, data),
  
  deleteEntity: (entityId: string) => 
    api.delete(`/dynamic-models/entities/${entityId}`),
  
  // Field Types
  getFieldTypes: () => api.get('/dynamic-models/field-types/'),
};

// ES深度集成服务
export const enhancedSearchService = {
  // 高级搜索
  advancedSearch: (searchParams: any) => 
    api.post('/enhanced-search/assets', searchParams),
  
  // 资产洞察分析
  getAssetInsights: (platformId?: string, projectId?: string) => 
    api.get('/enhanced-search/assets/insights', { 
      params: { platform_id: platformId, project_id: projectId }
    }),
  
  // 相似资产搜索
  getSimilarAssets: (assetId: string, size: number = 10) => 
    api.get(`/enhanced-search/assets/${assetId}/similar`, { 
      params: { size }
    }),
  
  // 搜索建议
  getSearchSuggestions: (query: string, field: string = 'asset_value') => 
    api.get('/enhanced-search/suggestions', { 
      params: { query, field }
    }),
  
  // 批量更新资产
  bulkUpdateAssets: (updates: any[]) => 
    api.post('/enhanced-search/assets/bulk-update', updates),
  
  // 处理原始资产数据
  processRawAssets: (rawAssets: any[]) => 
    api.post('/enhanced-search/assets/process', rawAssets),
  
  // 平台资产汇总
  getPlatformAssetSummary: (platformId: string) => 
    api.get(`/enhanced-search/platforms/${platformId}/assets/summary`),
  
  // 项目资产汇总
  getProjectAssetSummary: (projectId: string) => 
    api.get(`/enhanced-search/projects/${projectId}/assets/summary`),
  
  // 导出资产
  exportAssets: (searchParams: any, format: string = 'json') => 
    api.post('/enhanced-search/assets/export', searchParams, { 
      params: { export_format: format }
    }),
  
  // 重建索引
  reindexAssets: () => 
    api.post('/enhanced-search/maintenance/reindex'),
  
  // 健康检查
  healthCheck: () => 
    api.get('/enhanced-search/health'),
  
  // 安全分析
  securityAnalysis: (platformId?: string, riskLevel: string = 'high') => 
    api.post('/enhanced-search/security-analysis', {}, { 
      params: { platform_id: platformId, risk_level: riskLevel }
    }),
  
  // 自定义查询构建器
  buildCustomQuery: (queryConfig: any, execute: boolean = true) => 
    api.post('/enhanced-search/query-builder', queryConfig, { 
      params: { execute }
    }),
  
  // 高级数据处理管道
  advancedDataProcessing: (rawAssets: any[], autoCorrelate: boolean = true, autoIndex: boolean = true) => 
    api.post('/enhanced-search/data-pipeline/process', rawAssets, { 
      params: { auto_correlate: autoCorrelate, auto_index: autoIndex }
    }),
  
  // 获取资产关联情报
  getAssetCorrelations: (platformId?: string, projectId?: string, correlationType: string = 'all') => 
    api.get('/enhanced-search/intelligence/correlations', { 
      params: { 
        platform_id: platformId, 
        project_id: projectId, 
        correlation_type: correlationType 
      }
    }),
  
  // 综合风险评估
  comprehensiveRiskAssessment: (platformId?: string, projectId?: string, includeRemediation: boolean = true) => 
    api.get('/enhanced-search/intelligence/risk-assessment', { 
      params: { 
        platform_id: platformId, 
        project_id: projectId, 
        include_remediation: includeRemediation 
      }
    }),
  
  // 获取处理统计
  getProcessingStatistics: () => 
    api.get('/enhanced-search/processing/statistics'),
};

// Agent密钥管理服务
export const agentKeyService = {
  // 获取密钥列表
  getKeys: (params?: any) => api.get('/agent-keys/', { params }),
  
  // 获取密钥统计信息
  getStats: () => api.get('/agent-keys/stats'),
  
  // 创建密钥
  createKey: (data: any) => api.post('/agent-keys/', data),
  
  // 获取密钥详情
  getKey: (keyId: string) => api.get(`/agent-keys/${keyId}`),
  
  // 更新密钥
  updateKey: (keyId: string, data: any) => api.put(`/agent-keys/${keyId}`, data),
  
  // 撤销密钥
  revokeKey: (keyId: string) => api.post(`/agent-keys/${keyId}/revoke`),
  
  // 暂停密钥
  suspendKey: (keyId: string) => api.post(`/agent-keys/${keyId}/suspend`),
  
  // 激活密钥
  activateKey: (keyId: string) => api.post(`/agent-keys/${keyId}/activate`),
  
  // 删除密钥
  deleteKey: (keyId: string) => api.delete(`/agent-keys/${keyId}`),
  
  // 批量撤销密钥
  batchRevokeKeys: (keyIds: string[]) => api.post('/agent-keys/batch/revoke', keyIds),
  
  // 清理过期密钥
  cleanupExpiredKeys: () => api.post('/agent-keys/cleanup/expired'),
};

// 仪表板数据服务
export const dashboardService = {
  // 获取仪表板概览
  getOverview: () => api.get('/dashboard/overview'),
  
  // 获取组件数据
  getWidgetData: (widgetConfig: any) => 
    api.post('/dashboard/widget/data', widgetConfig),
  
  // 获取组件模板
  getWidgetTemplates: () => api.get('/dashboard/widget/templates'),
  
  // 获取风险分析
  getRiskAnalysis: () => api.get('/dashboard/risk-analysis'),
  
  // 执行自定义查询
  executeCustomQuery: (queryConfig: any) => 
    api.post('/dashboard/query/custom', queryConfig),
  
  // 获取可聚合字段
  getAggregatableFields: () => api.get('/dashboard/aggregations/fields'),
  
  // 健康检查
  healthCheck: () => api.get('/dashboard/health'),
};

// 数据导入服务
export const dataImportService = {
  // 分析上传文件
  analyzeFile: (formData: FormData) => {
    const apiWithFile = axios.create({
      baseURL: API_BASE_URL,
      timeout: 120000, // 分析大文件需要更长时间 (2分钟)
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    
    // 添加认证头
    const token = localStorage.getItem('token');
    if (token) {
      apiWithFile.defaults.headers['Authorization'] = `Bearer ${token}`;
    }
    
    return apiWithFile.post('/data-import/analyze', formData);
  },
  
  // 执行数据导入
  executeImport: (formData: FormData) => {
    const apiWithFile = axios.create({
      baseURL: API_BASE_URL,
      timeout: 600000, // 大文件导入可能需要很长时间 (10分钟)
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    
    // 添加认证头
    const token = localStorage.getItem('token');
    if (token) {
      apiWithFile.defaults.headers['Authorization'] = `Bearer ${token}`;
    }
    
    return apiWithFile.post('/data-import/execute', formData);
  },
  
  // 获取可用的资产类型
  getAssetTypes: () => api.get('/data-import/asset-types'),
  
  // 获取支持的字段类型
  getFieldTypes: () => api.get('/data-import/field-types'),
  
  // 获取平台项目列表
  getPlatforms: () => api.get('/data-import/platforms'),
  
  // 为资产类型添加字段
  addFieldsToAssetType: (assetTypeId: string, fields: any[]) => 
    api.post(`/data-import/asset-types/${assetTypeId}/fields`, { fields }),
};