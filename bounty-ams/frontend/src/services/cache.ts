interface CacheItem {
  data: any;
  timestamp: number;
  expirationTime: number;
}

interface CacheConfig {
  ttl: number; // 缓存时间（毫秒）
  maxSize: number; // 最大缓存项数
}

class ApiCache {
  private cache: Map<string, CacheItem> = new Map();
  private config: CacheConfig;
  private pendingRequests: Map<string, Promise<any>> = new Map();

  constructor(config: CacheConfig = { ttl: 5 * 60 * 1000, maxSize: 100 }) {
    this.config = config;
  }

  // 生成缓存key
  private generateKey(url: string, params?: any): string {
    const paramString = params ? JSON.stringify(params) : '';
    return `${url}:${paramString}`;
  }

  // 检查缓存是否有效
  private isValidCache(item: CacheItem): boolean {
    return Date.now() < item.expirationTime;
  }

  // 清理过期缓存
  private cleanupExpiredCache(): void {
    const now = Date.now();
    for (const [key, item] of this.cache.entries()) {
      if (now >= item.expirationTime) {
        this.cache.delete(key);
      }
    }
  }

  // 限制缓存大小
  private limitCacheSize(): void {
    if (this.cache.size > this.config.maxSize) {
      const entries = Array.from(this.cache.entries()).sort(
        (a, b) => a[1].timestamp - b[1].timestamp
      );
      // 删除最老的条目
      const toDelete = entries.slice(0, this.cache.size - this.config.maxSize);
      toDelete.forEach(([key]) => this.cache.delete(key));
    }
  }

  // 获取缓存数据
  get(url: string, params?: any): any | null {
    const key = this.generateKey(url, params);
    const item = this.cache.get(key);
    
    if (item && this.isValidCache(item)) {
      return item.data;
    }
    
    if (item) {
      this.cache.delete(key);
    }
    
    return null;
  }

  // 设置缓存数据
  set(url: string, data: any, params?: any, customTTL?: number): void {
    const key = this.generateKey(url, params);
    const ttl = customTTL || this.config.ttl;
    
    const item: CacheItem = {
      data,
      timestamp: Date.now(),
      expirationTime: Date.now() + ttl
    };
    
    this.cache.set(key, item);
    this.limitCacheSize();
    
    // 定期清理过期缓存
    setTimeout(() => this.cleanupExpiredCache(), ttl);
  }

  // 包装API调用，自动处理缓存和防止重复请求
  async wrapApiCall<T>(
    url: string,
    apiFunction: () => Promise<T>,
    params?: any,
    customTTL?: number
  ): Promise<T> {
    const key = this.generateKey(url, params);
    
    // 检查缓存
    const cached = this.get(url, params);
    if (cached) {
      return cached;
    }
    
    // 检查是否有正在进行的请求
    const pendingRequest = this.pendingRequests.get(key);
    if (pendingRequest) {
      return pendingRequest;
    }
    
    // 创建新请求
    const request = apiFunction().then(
      (result) => {
        this.set(url, result, params, customTTL);
        this.pendingRequests.delete(key);
        return result;
      },
      (error) => {
        this.pendingRequests.delete(key);
        throw error;
      }
    );
    
    this.pendingRequests.set(key, request);
    return request;
  }

  // 清除特定缓存
  clear(url: string, params?: any): void {
    const key = this.generateKey(url, params);
    this.cache.delete(key);
    this.pendingRequests.delete(key);
  }

  // 清除所有缓存
  clearAll(): void {
    this.cache.clear();
    this.pendingRequests.clear();
  }

  // 获取缓存统计信息
  getStats(): { cacheSize: number; pendingRequests: number; hitRate: number } {
    return {
      cacheSize: this.cache.size,
      pendingRequests: this.pendingRequests.size,
      hitRate: 0 // 可以通过跟踪命中/未命中来计算
    };
  }
}

// 创建全局缓存实例
export const apiCache = new ApiCache({
  ttl: 5 * 60 * 1000, // 5分钟
  maxSize: 150
});

// 专用缓存实例
export const longTermCache = new ApiCache({
  ttl: 30 * 60 * 1000, // 30分钟，用于不经常变化的数据
  maxSize: 50
});

export const shortTermCache = new ApiCache({
  ttl: 1 * 60 * 1000, // 1分钟，用于经常变化的数据
  maxSize: 100
});

// 缓存装饰器
export const withCache = (
  cache: ApiCache = apiCache,
  ttl?: number
) => {
  return (url: string, params?: any) => {
    return <T>(apiFunction: () => Promise<T>): Promise<T> => {
      return cache.wrapApiCall(url, apiFunction, params, ttl);
    };
  };
};

export default ApiCache; 