#!/usr/bin/env python3
"""
API功能完整性测试
验证新实现的API功能
"""

import asyncio
import aiohttp
import json
from datetime import datetime
import uuid

class APICompletenessTest:
    def __init__(self):
        self.base_url = "http://localhost:8000"
        self.token = None
        
    async def authenticate(self):
        """获取认证token"""
        try:
            async with aiohttp.ClientSession() as session:
                login_data = {
                    "username": "admin",
                    "password": "password"
                }
                
                async with session.post(
                    f"{self.base_url}/api/auth/login",
                    json=login_data,
                    headers={"Content-Type": "application/json"}
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        self.token = data.get("access_token")
                        print(f"✅ 认证成功")
                        return True
                    else:
                        print(f"❌ 认证失败: {response.status}")
                        return False
        except Exception as e:
            print(f"❌ 认证异常: {str(e)}")
            return False
    
    async def test_dynamic_models_uniqueness(self):
        """测试动态模型唯一性检查"""
        print("\n🔍 测试动态模型唯一性检查...")
        
        if not self.token:
            print("❌ 未认证，跳过测试")
            return False
            
        headers = {"Authorization": f"Bearer {self.token}"}
        
        try:
            async with aiohttp.ClientSession() as session:
                # 1. 创建一个测试模型类型
                model_type_data = {
                    "name": "test_unique_model",
                    "display_name": "测试唯一性模型",
                    "description": "用于测试唯一性检查的模型",
                    "fields": [
                        {
                            "field_name": "unique_field",
                            "field_type": "text",
                            "display_name": "唯一字段",
                            "is_required": True,
                            "is_unique": True
                        },
                        {
                            "field_name": "normal_field",
                            "field_type": "text",
                            "display_name": "普通字段",
                            "is_required": False,
                            "is_unique": False
                        }
                    ]
                }
                
                async with session.post(
                    f"{self.base_url}/api/dynamic-models/types",
                    json=model_type_data,
                    headers=headers
                ) as response:
                    if response.status == 201:
                        model_type = await response.json()
                        model_type_id = model_type["id"]
                        print(f"✅ 创建测试模型类型成功: {model_type_id}")
                    else:
                        print(f"❌ 创建测试模型类型失败: {response.status}")
                        return False
                
                # 2. 创建第一个实体
                entity_data = {
                    "model_type_id": model_type_id,
                    "entity_data": {
                        "unique_field": "test_unique_value",
                        "normal_field": "test_value"
                    }
                }
                
                async with session.post(
                    f"{self.base_url}/api/dynamic-models/entities",
                    json=entity_data,
                    headers=headers
                ) as response:
                    if response.status == 201:
                        entity = await response.json()
                        print(f"✅ 创建第一个实体成功: {entity['id']}")
                    else:
                        error_text = await response.text()
                        print(f"❌ 创建第一个实体失败: {response.status} - {error_text}")
                        return False
                
                # 3. 尝试创建具有相同唯一字段值的第二个实体（应该失败）
                duplicate_entity_data = {
                    "model_type_id": model_type_id,
                    "entity_data": {
                        "unique_field": "test_unique_value",  # 相同的唯一值
                        "normal_field": "different_value"
                    }
                }
                
                async with session.post(
                    f"{self.base_url}/api/dynamic-models/entities",
                    json=duplicate_entity_data,
                    headers=headers
                ) as response:
                    if response.status == 400:
                        error_data = await response.json()
                        print(f"✅ 唯一性检查生效: {error_data.get('detail', {}).get('message', '')}")
                        return True
                    else:
                        print(f"❌ 唯一性检查失败，应该返回400但返回了: {response.status}")
                        return False
                        
        except Exception as e:
            print(f"❌ 测试动态模型唯一性异常: {str(e)}")
            return False
            
    async def test_vulnerability_task_reference(self):
        """测试漏洞任务引用检查"""
        print("\n🔍 测试漏洞任务引用检查...")
        
        if not self.token:
            print("❌ 未认证，跳过测试")
            return False
            
        headers = {"Authorization": f"Bearer {self.token}"}
        
        try:
            async with aiohttp.ClientSession() as session:
                # 1. 创建一个测试漏洞
                vuln_data = {
                    "name": "测试漏洞",
                    "vuln_id": f"TEST-{uuid.uuid4().hex[:8]}",
                    "description": "用于测试引用检查的漏洞",
                    "severity": "Medium",
                    "affected_products": ["test_product"],
                    "poc_identifier": "test_poc"
                }
                
                async with session.post(
                    f"{self.base_url}/api/vulnerabilities/",
                    json=vuln_data,
                    headers=headers
                ) as response:
                    if response.status == 201:
                        vulnerability = await response.json()
                        vuln_id = vulnerability["id"]
                        print(f"✅ 创建测试漏洞成功: {vuln_id}")
                    else:
                        error_text = await response.text()
                        print(f"❌ 创建测试漏洞失败: {response.status} - {error_text}")
                        return False
                
                # 2. 创建一个引用该漏洞的任务
                task_data = {
                    "task_id": f"task-{uuid.uuid4().hex[:8]}",
                    "task_type": "vulnerability_scan",
                    "target": "test_target",
                    "parameters": {
                        "vulnerability_id": vuln_id,
                        "scan_type": "detailed"
                    },
                    "priority": "medium"
                }
                
                async with session.post(
                    f"{self.base_url}/api/tasks/",
                    json=task_data,
                    headers=headers
                ) as response:
                    if response.status == 201:
                        task = await response.json()
                        print(f"✅ 创建引用任务成功: {task['task_id']}")
                    else:
                        error_text = await response.text()
                        print(f"❌ 创建引用任务失败: {response.status} - {error_text}")
                        return False
                
                # 3. 尝试删除被引用的漏洞（应该失败）
                async with session.delete(
                    f"{self.base_url}/api/vulnerabilities/{vuln_id}",
                    headers=headers
                ) as response:
                    if response.status == 400:
                        error_data = await response.json()
                        print(f"✅ 引用检查生效: {error_data.get('detail', '')}")
                        return True
                    else:
                        print(f"❌ 引用检查失败，应该返回400但返回了: {response.status}")
                        return False
                        
        except Exception as e:
            print(f"❌ 测试漏洞任务引用异常: {str(e)}")
            return False
            
    async def test_asset_type_usage_check(self):
        """测试资产类型使用检查"""
        print("\n🔍 测试资产类型使用检查...")
        
        if not self.token:
            print("❌ 未认证，跳过测试")
            return False
            
        headers = {"Authorization": f"Bearer {self.token}"}
        
        try:
            async with aiohttp.ClientSession() as session:
                # 1. 创建一个测试资产类型
                asset_type_data = {
                    "name": "test_asset_type",
                    "display_name": "测试资产类型",
                    "description": "用于测试使用检查的资产类型",
                    "fields": [
                        {
                            "field_name": "hostname",
                            "field_type": "text",
                            "display_name": "主机名",
                            "is_required": True
                        }
                    ]
                }
                
                async with session.post(
                    f"{self.base_url}/api/asset-types/",
                    json=asset_type_data,
                    headers=headers
                ) as response:
                    if response.status == 201:
                        asset_type = await response.json()
                        asset_type_id = asset_type["id"]
                        print(f"✅ 创建测试资产类型成功: {asset_type_id}")
                    else:
                        error_text = await response.text()
                        print(f"❌ 创建测试资产类型失败: {response.status} - {error_text}")
                        return False
                
                # 2. 尝试删除资产类型（应该成功，因为没有资产使用它）
                async with session.delete(
                    f"{self.base_url}/api/asset-types/{asset_type_id}",
                    headers=headers
                ) as response:
                    if response.status == 204:
                        print(f"✅ 删除未使用的资产类型成功")
                        return True
                    else:
                        error_text = await response.text()
                        print(f"❌ 删除资产类型失败: {response.status} - {error_text}")
                        return False
                        
        except Exception as e:
            print(f"❌ 测试资产类型使用检查异常: {str(e)}")
            return False
            
    async def test_enhanced_api_endpoints(self):
        """测试增强的API端点"""
        print("\n🔍 测试增强的API端点...")
        
        if not self.token:
            print("❌ 未认证，跳过测试")
            return False
            
        headers = {"Authorization": f"Bearer {self.token}"}
        
        enhanced_endpoints = [
            ("/api/dynamic-models/field-types/", "GET", "获取支持的字段类型"),
            ("/api/asset-types/field-types/", "GET", "获取资产类型字段类型"),
            ("/api/vulnerabilities/stats/severity", "GET", "获取漏洞严重性统计"),
            ("/api/vulnerabilities/severities/", "GET", "获取可用严重性级别"),
            ("/api/dynamic-models/entities/count", "GET", "获取动态实体数量"),
        ]
        
        success_count = 0
        total_count = len(enhanced_endpoints)
        
        try:
            async with aiohttp.ClientSession() as session:
                for endpoint, method, description in enhanced_endpoints:
                    try:
                        url = f"{self.base_url}{endpoint}"
                        
                        if method == "GET":
                            async with session.get(url, headers=headers, timeout=5) as response:
                                if response.status == 200:
                                    data = await response.json()
                                    print(f"✅ {description}: 成功 (返回 {len(data) if isinstance(data, list) else 'object'})")
                                    success_count += 1
                                else:
                                    error_text = await response.text()
                                    print(f"❌ {description}: 失败 ({response.status}) - {error_text[:50]}...")
                                    
                    except Exception as e:
                        print(f"❌ {description}: 异常 - {str(e)}")
                        
        except Exception as e:
            print(f"❌ 测试增强API端点异常: {str(e)}")
            return False
            
        print(f"\n📊 增强API端点测试结果: {success_count}/{total_count} 成功")
        return success_count == total_count
        
    async def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始API功能完整性测试...")
        print("=" * 50)
        
        # 先认证
        if not await self.authenticate():
            print("❌ 认证失败，无法继续测试")
            return False
        
        test_results = {}
        
        # 运行所有测试
        test_results["uniqueness"] = await self.test_dynamic_models_uniqueness()
        test_results["vulnerability_reference"] = await self.test_vulnerability_task_reference()
        test_results["asset_type_usage"] = await self.test_asset_type_usage_check()
        test_results["enhanced_endpoints"] = await self.test_enhanced_api_endpoints()
        
        print("=" * 50)
        print("📊 API功能完整性测试总结:")
        print(f"动态模型唯一性检查: {'✅' if test_results['uniqueness'] else '❌'}")
        print(f"漏洞任务引用检查: {'✅' if test_results['vulnerability_reference'] else '❌'}")
        print(f"资产类型使用检查: {'✅' if test_results['asset_type_usage'] else '❌'}")
        print(f"增强API端点: {'✅' if test_results['enhanced_endpoints'] else '❌'}")
        
        success_count = sum(1 for result in test_results.values() if result)
        total_count = len(test_results)
        
        print(f"\n整体测试结果: {success_count}/{total_count} 通过")
        
        if success_count == total_count:
            print("🎉 所有API功能测试通过！后端API已完全实现。")
            return True
        else:
            print("⚠️  部分API功能测试失败。")
            return False

async def main():
    """主测试函数"""
    tester = APICompletenessTest()
    success = await tester.run_all_tests()
    
    if success:
        print("\n✅ 结论：所有API功能已完全实现，系统可以正常使用。")
    else:
        print("\n❌ 结论：部分API功能需要进一步完善。")
    
    return success

if __name__ == "__main__":
    asyncio.run(main()) 