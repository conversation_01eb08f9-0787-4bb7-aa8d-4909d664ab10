#!/usr/bin/env python3
"""
测试任务创建API
"""
import requests
import json

# 配置
BASE_URL = "http://localhost:8000"
USERNAME = "admin"
PASSWORD = "password"

def login():
    """登录获取token"""
    response = requests.post(
        f"{BASE_URL}/api/auth/login",
        data={
            "username": USERNAME,
            "password": PASSWORD
        }
    )
    if response.status_code == 200:
        data = response.json()
        return data.get("access_token")
    else:
        print(f"Login failed: {response.status_code} - {response.text}")
        return None

def test_batch_task_creation():
    """测试批量任务创建"""
    token = login()
    if not token:
        return
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # 准备测试数据
    task_data = {
        "task_type": "subdomain_discovery",
        "targets": ["example.com", "test.com", "demo.com"],
        "parameters": {
            "wordlist": "common.txt",
            "threads": 20,
            "timeout": 10,
            "recursive": False
        },
        "priority": "high",
        "timeout": 300,
        "retry_count": 1
    }
    
    print("Creating batch tasks...")
    print(f"Request data: {json.dumps(task_data, indent=2)}")
    
    response = requests.post(
        f"{BASE_URL}/api/tasks/batch",
        headers=headers,
        json=task_data
    )
    
    print(f"Response status: {response.status_code}")
    print(f"Response body: {json.dumps(response.json(), indent=2)}")
    
    return response.status_code == 200

def test_task_listing():
    """测试任务列表"""
    token = login()
    if not token:
        return
    
    headers = {
        "Authorization": f"Bearer {token}"
    }
    
    print("\nFetching task list...")
    response = requests.get(
        f"{BASE_URL}/api/tasks",
        headers=headers
    )
    
    print(f"Response status: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        tasks = data.get("tasks", [])
        print(f"Found {len(tasks)} tasks:")
        for task in tasks[:3]:  # 显示前3个任务
            print(f"  - {task['task_id']}: {task['task_type']} -> {task['target']} ({task['status']})")
    else:
        print(f"Error: {response.text}")

def test_task_operations():
    """测试任务操作"""
    token = login()
    if not token:
        return
    
    headers = {
        "Authorization": f"Bearer {token}"
    }
    
    # 获取一个pending任务
    response = requests.get(
        f"{BASE_URL}/api/tasks?status=pending&limit=1",
        headers=headers
    )
    
    if response.status_code == 200:
        tasks = response.json().get("tasks", [])
        if tasks:
            task_id = tasks[0]["task_id"]
            print(f"\nTesting operations on task: {task_id}")
            
            # 测试取消任务
            print("Testing task cancellation...")
            cancel_response = requests.post(
                f"{BASE_URL}/api/tasks/{task_id}/cancel",
                headers=headers
            )
            print(f"Cancel response: {cancel_response.status_code} - {cancel_response.json()}")
        else:
            print("No pending tasks found for testing operations")
    else:
        print(f"Failed to get tasks: {response.text}")

if __name__ == "__main__":
    print("=== 任务创建和管理API测试 ===")
    
    # 测试批量任务创建
    success = test_batch_task_creation()
    if success:
        print("✓ 批量任务创建成功")
    else:
        print("✗ 批量任务创建失败")
    
    # 测试任务列表
    test_task_listing()
    
    # 测试任务操作
    test_task_operations()
    
    print("\n=== 测试完成 ===")