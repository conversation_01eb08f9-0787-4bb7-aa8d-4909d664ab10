#!/usr/bin/env python3
"""
Final verification of frontend login and all functionality
"""
import requests
import json
import time

def final_system_check():
    """Perform final comprehensive system check"""
    print("🔬 FINAL BOUNTY AMS SYSTEM VERIFICATION")
    print("="*60)
    
    success_count = 0
    total_tests = 0
    
    # Test 1: Frontend accessibility
    total_tests += 1
    print("\n1️⃣ Frontend Accessibility Test")
    try:
        response = requests.get("http://localhost:5173", timeout=5)
        if response.status_code == 200 and 'vite' in response.text.lower():
            print("✅ Frontend is accessible and running")
            success_count += 1
        else:
            print("❌ Frontend has issues")
    except:
        print("❌ Frontend is not accessible")
    
    # Test 2: Backend health
    total_tests += 1
    print("\n2️⃣ Backend Health Test")
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code == 200:
            print("✅ Backend is healthy")
            success_count += 1
        else:
            print("❌ Backend has issues")
    except:
        print("❌ Backend is not accessible")
    
    # Test 3: Authentication flow
    total_tests += 1
    print("\n3️⃣ Authentication Flow Test")
    try:
        # Login
        login_response = requests.post(
            "http://localhost:8000/api/auth/login",
            headers={'Origin': 'http://localhost:5173', 'Content-Type': 'application/json'},
            json={"username": "admin", "password": "password"}
        )
        
        if login_response.status_code == 200:
            token = login_response.json()['access_token']
            
            # Get user info
            user_response = requests.get(
                "http://localhost:8000/api/auth/me",
                headers={'Authorization': f'Bearer {token}', 'Origin': 'http://localhost:5173'}
            )
            
            if user_response.status_code == 200:
                user_data = user_response.json()
                if 'username' in user_data and 'is_admin' in user_data:
                    print(f"✅ Authentication works: {user_data['username']} ({'Admin' if user_data['is_admin'] else 'User'})")
                    success_count += 1
                else:
                    print("❌ User data structure is incorrect")
            else:
                print("❌ User info retrieval failed")
        else:
            print("❌ Login failed")
    except Exception as e:
        print(f"❌ Authentication error: {e}")
    
    # Test 4: Dashboard APIs
    total_tests += 1
    print("\n4️⃣ Dashboard APIs Test")
    try:
        headers = {'Authorization': f'Bearer {token}', 'Origin': 'http://localhost:5173'}
        
        # Asset stats
        assets_resp = requests.get("http://localhost:8000/api/discovered-assets/stats", headers=headers)
        agents_resp = requests.get("http://localhost:8000/api/agents", headers=headers)
        tasks_resp = requests.get("http://localhost:8000/api/tasks/stats", headers=headers)
        
        if all(r.status_code == 200 for r in [assets_resp, agents_resp, tasks_resp]):
            asset_data = assets_resp.json()
            agent_data = agents_resp.json()
            task_data = tasks_resp.json()
            
            print(f"✅ Dashboard APIs work:")
            print(f"   • {asset_data.get('total_assets', 0)} assets")
            print(f"   • {len(agent_data.get('agents', []))} agents") 
            print(f"   • {task_data.get('total', 0)} tasks")
            success_count += 1
        else:
            print("❌ Some dashboard APIs failed")
    except:
        print("❌ Dashboard APIs test failed")
    
    # Test 5: CORS configuration
    total_tests += 1
    print("\n5️⃣ CORS Configuration Test")
    try:
        cors_response = requests.options(
            "http://localhost:8000/api/auth/login",
            headers={
                'Origin': 'http://localhost:5173',
                'Access-Control-Request-Method': 'POST',
                'Access-Control-Request-Headers': 'Content-Type,Authorization'
            }
        )
        
        if 'Access-Control-Allow-Origin' in cors_response.headers:
            allowed_origin = cors_response.headers['Access-Control-Allow-Origin']
            if 'localhost:5173' in allowed_origin:
                print("✅ CORS is properly configured for frontend")
                success_count += 1
            else:
                print(f"❌ CORS origin mismatch: {allowed_origin}")
        else:
            print("❌ CORS headers missing")
    except:
        print("❌ CORS test failed")
    
    # Test 6: Data consistency
    total_tests += 1
    print("\n6️⃣ Data Consistency Test")
    try:
        headers = {'Authorization': f'Bearer {token}', 'Origin': 'http://localhost:5173'}
        
        # Check asset search
        search_resp = requests.get("http://localhost:8000/api/discovered-assets/search", headers=headers)
        types_resp = requests.get("http://localhost:8000/api/discovered-assets/types", headers=headers)
        
        if search_resp.status_code == 200 and types_resp.status_code == 200:
            search_data = search_resp.json()
            types_data = types_resp.json()
            
            if 'assets' in search_data and 'types' in types_data:
                print("✅ Data consistency verified")
                success_count += 1
            else:
                print("❌ Data structure inconsistent")
        else:
            print("❌ Data consistency check failed")
    except:
        print("❌ Data consistency test failed")
    
    # Final Report
    print("\n" + "="*60)
    print("📊 FINAL VERIFICATION RESULTS")
    print("="*60)
    
    success_rate = (success_count / total_tests) * 100
    print(f"Tests Passed: {success_count}/{total_tests} ({success_rate:.1f}%)")
    
    if success_count == total_tests:
        print("\n🎉 ALL SYSTEMS FULLY OPERATIONAL!")
        print("\n🚀 Your Bounty AMS system is ready to use:")
        print(f"   📱 Frontend: http://localhost:5173")
        print(f"   🔧 Backend:  http://localhost:8000")
        print(f"   📚 API Docs: http://localhost:8000/docs")
        print("\n👤 Login Credentials:")
        print("   Username: admin")
        print("   Password: password")
        print("\n✨ Login issue has been completely resolved!")
        print("   The frontend can now successfully authenticate with the backend.")
        
    elif success_count >= total_tests * 0.8:
        print("\n⚠️ MOSTLY OPERATIONAL")
        print(f"   {total_tests - success_count} minor issues remain")
        
    else:
        print("\n❌ CRITICAL ISSUES DETECTED")
        print("   System needs attention before use")
    
    print("="*60)
    return success_count == total_tests

if __name__ == "__main__":
    final_system_check()