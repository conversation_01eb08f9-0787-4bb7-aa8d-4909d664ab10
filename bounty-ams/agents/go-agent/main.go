package main

import (
	"context"
	"flag"
	"fmt"
	"os"
	"os/signal"
	"sync"
	"syscall"
	"time"

	"bounty-agent/client"
	"bounty-agent/config"
	"bounty-agent/executor"

	"github.com/sirupsen/logrus"
)

// Agent 主Agent结构
type Agent struct {
	config         *config.AgentConfig
	client         *client.Client
	executorMgr    *executor.ExecutorManager
	logger         *logrus.Logger
	running        bool
	paused         bool
	mutex          sync.RWMutex
	currentTasks   int
	taskChan       chan *client.TaskDefinition
	resultChan     chan *client.TaskResult
	ctx            context.Context
	cancel         context.CancelFunc
}

// NewAgent 创建新的Agent
func NewAgent(configPath string) (*Agent, error) {
	// 加载配置
	cfg, err := config.LoadConfig(configPath)
	if err != nil {
		return nil, fmt.Errorf("failed to load config: %v", err)
	}

	// 初始化日志
	logger := logrus.New()
	level, err := logrus.ParseLevel(cfg.Log.Level)
	if err != nil {
		level = logrus.InfoLevel
	}
	logger.SetLevel(level)

	if cfg.Log.Format == "json" {
		logger.SetFormatter(&logrus.JSONFormatter{})
	} else {
		logger.SetFormatter(&logrus.TextFormatter{
			FullTimestamp: true,
		})
	}

	// 如果设置了日志文件
	if cfg.Log.File != "" {
		file, err := os.OpenFile(cfg.Log.File, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
		if err == nil {
			logger.SetOutput(file)
		}
	}

	// 如果Agent ID是默认值，则生成唯一ID
	if cfg.Agent.ID == "go-agent-code" || cfg.Agent.ID == "" {
		// 使用hostname + 随机字符串生成唯一ID
		hostname, _ := os.Hostname()
		randomStr := fmt.Sprintf("%d", time.Now().Unix())
		cfg.Agent.ID = fmt.Sprintf("go-agent-%s-%s", hostname, randomStr)
		logger.Infof("Generated unique Agent ID: %s", cfg.Agent.ID)
	}

	// 创建HTTP客户端
	timeout := time.Duration(cfg.Server.Timeout) * time.Second
	httpClient := client.NewClient(cfg.Server.BaseURL, timeout, logger)

	// 创建执行器管理器
	execMgr := executor.NewExecutorManager(logger)

	// 注册执行器
	if contains(cfg.Agent.Capabilities, "subdomain_discovery") {
		execMgr.RegisterExecutor(executor.NewSubdomainDiscoveryExecutor(logger))
	}
	if contains(cfg.Agent.Capabilities, "port_scanning") {
		execMgr.RegisterExecutor(executor.NewPortScanningExecutor(logger))
	}
	if contains(cfg.Agent.Capabilities, "service_detection") {
		execMgr.RegisterExecutor(executor.NewServiceDetectionExecutor(logger))
	}
	if contains(cfg.Agent.Capabilities, "dns_resolution") {
		execMgr.RegisterExecutor(executor.NewDNSResolutionExecutor(logger))
	}

	ctx, cancel := context.WithCancel(context.Background())

	agent := &Agent{
		config:      cfg,
		client:      httpClient,
		executorMgr: execMgr,
		logger:      logger,
		taskChan:    make(chan *client.TaskDefinition, 10),
		resultChan:  make(chan *client.TaskResult, 10),
		ctx:         ctx,
		cancel:      cancel,
	}

	// 设置执行器管理器的控制器引用
	execMgr.SetController(agent)

	return agent, nil
}

// Start 启动Agent
func (a *Agent) Start() error {
	a.mutex.Lock()
	if a.running {
		a.mutex.Unlock()
		return fmt.Errorf("agent is already running")
	}
	a.running = true
	a.mutex.Unlock()

	a.logger.Info("Starting Bounty Agent...")

	// 首先登录获取认证令牌
	if err := a.login(); err != nil {
		return fmt.Errorf("failed to login: %v", err)
	}

	// 注册Agent
	if err := a.register(); err != nil {
		return fmt.Errorf("failed to register: %v", err)
	}

	// 启动各个goroutine
	go a.heartbeatLoop()
	go a.taskPollingLoop()
	go a.taskExecutionLoop()
	go a.resultSubmissionLoop()

	a.logger.Info("Agent started successfully")
	return nil
}

// Stop 停止Agent
func (a *Agent) Stop() {
	a.mutex.Lock()
	defer a.mutex.Unlock()

	if !a.running {
		return
	}

	a.logger.Info("Stopping Agent...")
	a.running = false
	a.cancel()

	// 关闭通道
	close(a.taskChan)
	close(a.resultChan)

	// 如果是控制命令调用，强制退出
	go func() {
		time.Sleep(2 * time.Second)
		a.logger.Info("执行Agent停止...")
		os.Exit(0)
	}()
}

// login 使用API密钥进行认证
func (a *Agent) login() error {
	// 首先尝试从配置文件读取API密钥
	apiKey := a.config.Server.APIKey
	
	// 如果配置文件中没有，则从环境变量读取
	if apiKey == "" || apiKey == "your_api_key_here" {
		apiKey = os.Getenv("BOUNTY_API_KEY")
	}

	if apiKey == "" {
		return fmt.Errorf("API key not set in config file or BOUNTY_API_KEY environment variable")
	}

	// 安全地显示API密钥前缀
	keyPrefix := apiKey
	if len(apiKey) > 20 {
		keyPrefix = apiKey[:20]
	}
	a.logger.Infof("Authenticating with API key: %s...", keyPrefix)
	return a.client.SetAPIKey(apiKey)
}

// register 使用API密钥注册Agent
func (a *Agent) register() error {
	capabilities := a.executorMgr.GetCapabilities()
	
	metadata := map[string]interface{}{
		"os":       "linux",
		"arch":     "amd64",
		"tools":    []string{"subfinder", "naabu", "httpx"},
		"version":  a.config.Agent.Version,
		"hostname": a.config.Agent.Hostname,
	}

	req := &client.AgentRegisterWithKeyRequest{
		APIKey:             a.client.GetAPIKey(),
		AgentID:            a.config.Agent.ID,
		Name:               a.config.Agent.Name,
		Version:            a.config.Agent.Version,
		Capabilities:       capabilities,
		MaxConcurrentTasks: a.config.Agent.MaxConcurrentTasks,
		Hostname:           a.config.Agent.Hostname,
		IPAddress:          a.config.Agent.IPAddress,
		Metadata:           metadata,
	}

	agentInfo, err := a.client.RegisterAgentWithKey(req)
	if err != nil {
		return err
	}

	a.logger.Infof("Agent registered successfully: %s (Status: %s)", agentInfo.AgentID, agentInfo.Status)
	return nil
}

// heartbeatLoop 心跳循环
func (a *Agent) heartbeatLoop() {
	ticker := time.NewTicker(30 * time.Second) // 每30秒发送一次心跳
	defer ticker.Stop()

	for {
		select {
		case <-a.ctx.Done():
			return
		case <-ticker.C:
			a.sendHeartbeat()
		}
	}
}

// sendHeartbeat 使用API密钥发送心跳
func (a *Agent) sendHeartbeat() {
	a.mutex.RLock()
	currentTasks := a.currentTasks
	a.mutex.RUnlock()

	metadata := map[string]interface{}{
		"cpu_usage":    getCPUUsage(),
		"memory_usage": getMemoryUsage(),
		"uptime":       time.Now().Unix(),
	}

	req := &client.AgentHeartbeatWithKeyRequest{
		APIKey:       a.client.GetAPIKey(),
		AgentID:      a.config.Agent.ID,
		Status:       "online",
		CurrentTasks: currentTasks,
		SystemInfo:   metadata,
	}

	if err := a.client.SendHeartbeatWithKey(req); err != nil {
		a.logger.Warnf("Failed to send heartbeat: %v", err)
	} else {
		a.logger.Debug("Heartbeat sent successfully")
	}
}

// taskPollingLoop 任务轮询循环
func (a *Agent) taskPollingLoop() {
	ticker := time.NewTicker(5 * time.Second) // 每5秒轮询一次任务
	defer ticker.Stop()

	for {
		select {
		case <-a.ctx.Done():
			return
		case <-ticker.C:
			a.pollTasks()
		}
	}
}

// pollTasks 轮询任务
func (a *Agent) pollTasks() {
	a.mutex.RLock()
	currentTasks := a.currentTasks
	maxTasks := a.config.Agent.MaxConcurrentTasks
	paused := a.paused
	a.mutex.RUnlock()

	// 如果Agent暂停，不获取新任务
	if paused {
		return
	}

	// 如果已达到最大并发任务数，不再获取新任务
	if currentTasks >= maxTasks {
		return
	}

	capabilities := a.executorMgr.GetCapabilities()
	resp, err := a.client.PollTasksWithKey(a.config.Agent.ID, capabilities)
	if err != nil {
		a.logger.Warnf("Failed to poll tasks: %v", err)
		return
	}

	if resp.HasTask && resp.Task != nil {
		a.logger.Infof("Received new task: %s (Type: %s)", resp.Task.TaskID, resp.Task.TaskType)
		
		select {
		case a.taskChan <- resp.Task:
			a.mutex.Lock()
			a.currentTasks++
			a.mutex.Unlock()
		default:
			a.logger.Warn("Task channel is full, dropping task")
		}
	}
}

// taskExecutionLoop 任务执行循环
func (a *Agent) taskExecutionLoop() {
	for {
		select {
		case <-a.ctx.Done():
			return
		case task, ok := <-a.taskChan:
			if !ok {
				return
			}
			go a.executeTask(task)
		}
	}
}

// executeTask 执行任务
func (a *Agent) executeTask(task *client.TaskDefinition) {
	defer func() {
		a.mutex.Lock()
		a.currentTasks--
		a.mutex.Unlock()
	}()

	a.logger.Infof("Executing task: %s", task.TaskID)

	// 首先提交运行状态
	runningResult := &client.TaskResult{
		TaskID:  task.TaskID,
		AgentID: a.config.Agent.ID,
		Status:  "running",
		ResultData: map[string]interface{}{},
	}
	now := client.CustomTime{Time: time.Now()}
	runningResult.StartedAt = &now

	select {
	case a.resultChan <- runningResult:
	default:
		a.logger.Warn("Result channel is full when submitting running status")
	}

	// 创建任务上下文
	ctx, cancel := context.WithTimeout(a.ctx, time.Duration(task.Timeout)*time.Second)
	defer cancel()

	// 执行任务
	result, err := a.executorMgr.ExecuteTask(ctx, task)
	if result == nil {
		result = &client.TaskResult{}
	}

	result.TaskID = task.TaskID
	result.AgentID = a.config.Agent.ID

	if err != nil {
		result.Status = "failed"
		result.ErrorMessage = err.Error()
		a.logger.Errorf("Task %s failed: %v", task.TaskID, err)
	}

	// 提交结果
	select {
	case a.resultChan <- result:
	default:
		a.logger.Warn("Result channel is full when submitting final result")
	}
}

// resultSubmissionLoop 结果提交循环
func (a *Agent) resultSubmissionLoop() {
	for {
		select {
		case <-a.ctx.Done():
			return
		case result, ok := <-a.resultChan:
			if !ok {
				return
			}
			a.submitResult(result)
		}
	}
}

// submitResult 提交结果
func (a *Agent) submitResult(result *client.TaskResult) {
	if err := a.client.SubmitTaskResult(result); err != nil {
		a.logger.Errorf("Failed to submit result for task %s: %v", result.TaskID, err)
		// 可以考虑重试机制
	} else {
		a.logger.Infof("Result submitted for task %s (Status: %s)", result.TaskID, result.Status)
	}
}

// Pause 暂停Agent
func (a *Agent) Pause() error {
	a.mutex.Lock()
	defer a.mutex.Unlock()
	a.paused = true
	a.logger.Info("Agent已暂停")
	return nil
}

// Resume 恢复Agent
func (a *Agent) Resume() error {
	a.mutex.Lock()
	defer a.mutex.Unlock()
	a.paused = false
	a.logger.Info("Agent已恢复")
	return nil
}


// Restart 重启Agent  
func (a *Agent) Restart() error {
	a.logger.Info("Agent重启命令接收，2秒后重启...")
	go func() {
		time.Sleep(2 * time.Second)
		a.logger.Info("执行Agent重启...")
		// 这里可以实现重启逻辑，简化版直接退出让外部脚本重启
		os.Exit(1)
	}()
	return nil
}

// contains 检查切片是否包含元素
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// getCPUUsage 获取CPU使用率（简化版）
func getCPUUsage() float64 {
	// 这里应该实现真正的CPU使用率检测
	return 25.5
}

// getMemoryUsage 获取内存使用量（简化版）
func getMemoryUsage() int {
	// 这里应该实现真正的内存使用量检测
	return 512
}

func main() {
	var configPath = flag.String("config", "config.yaml", "Path to configuration file")
	var help = flag.Bool("help", false, "Show help message")
	flag.Parse()

	if *help {
		fmt.Println("Bounty Agent - Go实现的漏洞赏金资产管理Agent")
		fmt.Println("Usage:")
		fmt.Println("  bounty-agent [options]")
		fmt.Println("Options:")
		flag.PrintDefaults()
		os.Exit(0)
	}

	// 创建Agent
	agent, err := NewAgent(*configPath)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Failed to create agent: %v\n", err)
		os.Exit(1)
	}

	// 设置信号处理
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// 启动Agent
	if err := agent.Start(); err != nil {
		fmt.Fprintf(os.Stderr, "Failed to start agent: %v\n", err)
		os.Exit(1)
	}

	// 等待停止信号
	<-sigChan
	agent.logger.Info("Received stop signal")

	// 停止Agent
	agent.Stop()
}