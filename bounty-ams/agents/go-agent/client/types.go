package client

import (
	"encoding/json"
	"time"
)

// CustomTime handles multiple timestamp formats
type CustomTime struct {
	time.Time
}

// UnmarshalJSON implements custom JSON unmarshaling for timestamps
func (ct *CustomTime) UnmarshalJSON(data []byte) error {
	var str string
	if err := json.Unmarshal(data, &str); err != nil {
		return err
	}
	
	// Handle empty string
	if str == "" {
		ct.Time = time.Time{}
		return nil
	}
	
	// Try different timestamp formats
	formats := []string{
		time.RFC3339,
		time.RFC3339Nano,
		"2006-01-02T15:04:05.999999",      // Python datetime without timezone
		"2006-01-02T15:04:05",             // ISO format without timezone
		"2006-01-02 15:04:05.999999",      // Space separated format
		"2006-01-02 15:04:05",             // Space separated format without microseconds
	}
	
	for _, format := range formats {
		if t, err := time.Parse(format, str); err == nil {
			ct.Time = t
			return nil
		}
	}
	
	return &time.ParseError{
		Layout: "multiple formats",
		Value:  str,
	}
}

// AgentRegisterRequest Agent注册请求
type AgentRegisterRequest struct {
	AgentID            string            `json:"agent_id"`
	Name               string            `json:"name"`
	Version            string            `json:"version"`
	Capabilities       []string          `json:"capabilities"`
	MaxConcurrentTasks int               `json:"max_concurrent_tasks"`
	Hostname           string            `json:"hostname,omitempty"`
	IPAddress          string            `json:"ip_address,omitempty"`
	Metadata           map[string]interface{} `json:"metadata,omitempty"`
}

// AgentInfo Agent信息响应
type AgentInfo struct {
	ID                 string            `json:"id"`
	AgentID            string            `json:"agent_id"`
	Name               string            `json:"name"`
	Version            string            `json:"version"`
	Capabilities       []string          `json:"capabilities"`
	MaxConcurrentTasks int               `json:"max_concurrent_tasks"`
	CurrentTasks       int               `json:"current_tasks"`
	Status             string            `json:"status"`
	Hostname           string            `json:"hostname"`
	IPAddress          string            `json:"ip_address"`
	LastSeenAt         *CustomTime       `json:"last_seen_at"`
	Metadata           map[string]interface{} `json:"agent_metadata"`
	CreatedAt          CustomTime        `json:"created_at"`
	UpdatedAt          *CustomTime       `json:"updated_at"`
}

// AgentHeartbeatRequest Agent心跳请求
type AgentHeartbeatRequest struct {
	AgentID      string                 `json:"agent_id"`
	Status       string                 `json:"status"`
	CurrentTasks int                    `json:"current_tasks"`
	Metadata     map[string]interface{} `json:"metadata,omitempty"`
}

// TaskDefinition 任务定义
type TaskDefinition struct {
	TaskID       string                 `json:"task_id"`
	TaskType     string                 `json:"task_type"`
	Target       string                 `json:"target"`
	Parameters   map[string]interface{} `json:"parameters"`
	Priority     int                    `json:"priority"`
	Timeout      int                    `json:"timeout"`
	RetryCount   int                    `json:"retry_count"`
	Dependencies []string               `json:"dependencies,omitempty"`
	WorkflowID   string                 `json:"workflow_id,omitempty"`
}

// TaskPollResponse 任务轮询响应
type TaskPollResponse struct {
	HasTask bool            `json:"has_task"`
	Task    *TaskDefinition `json:"task,omitempty"`
	Message string          `json:"message,omitempty"`
}

type AgentHeartbeatWithKeyRequest struct {
	APIKey       string                 `json:"api_key"`
	AgentID      string                 `json:"agent_id"`
	Status       string                 `json:"status"`
	CurrentTasks int                    `json:"current_tasks"`
	SystemInfo   map[string]interface{} `json:"system_info,omitempty"`
}

// AgentRegisterWithKeyRequest Agent使用密钥注册请求
type AgentRegisterWithKeyRequest struct {
	APIKey             string                 `json:"api_key"`
	AgentID            string                 `json:"agent_id"`
	Name               string                 `json:"name"`
	Version            string                 `json:"version"`
	Capabilities       []string               `json:"capabilities"`
	MaxConcurrentTasks int                    `json:"max_concurrent_tasks"`
	Hostname           string                 `json:"hostname,omitempty"`
	IPAddress          string                 `json:"ip_address,omitempty"`
	Metadata           map[string]interface{} `json:"metadata,omitempty"`
}

// TaskResult 任务结果
type TaskResult struct {
	TaskID           string                   `json:"task_id"`
	AgentID          string                   `json:"agent_id"`
	Status           string                   `json:"status"`
	ResultData       map[string]interface{}   `json:"result_data"`
	ErrorMessage     string                   `json:"error_message,omitempty"`
	ExecutionTime    float64                  `json:"execution_time,omitempty"`
	AssetsDiscovered []map[string]interface{} `json:"assets_discovered,omitempty"`
	StartedAt        *CustomTime              `json:"started_at,omitempty"`
	CompletedAt      *CustomTime              `json:"completed_at,omitempty"`
}

// AssetDiscovery 资产发现结果
type AssetDiscovery struct {
	Type         string                 `json:"type"`
	Value        string                 `json:"value"`
	Source       string                 `json:"source"`
	Confidence   float64                `json:"confidence"`
	Metadata     map[string]interface{} `json:"metadata,omitempty"`
	ParentAsset  string                 `json:"parent_asset,omitempty"`
	DiscoveryTime CustomTime           `json:"discovery_time"`
}