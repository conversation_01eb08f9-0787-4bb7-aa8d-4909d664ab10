package client

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"time"

	"github.com/sirupsen/logrus"
)

// Client Agent HTTP客户端
type Client struct {
	baseURL    string
	httpClient *http.Client
	token      string
	apiKey     string
	logger     *logrus.Logger
}

// NewClient 创建新的Agent客户端
func NewClient(baseURL string, timeout time.Duration, logger *logrus.Logger) *Client {
	return &Client{
		baseURL: baseURL,
		httpClient: &http.Client{
			Timeout: timeout,
		},
		logger: logger,
	}
}

// SetAPIKey 设置API密钥
func (c *Client) SetAPIKey(apiKey string) error {
	if apiKey == "" {
		return fmt.Errorf("API key cannot be empty")
	}
	c.apiKey = apiKey
	return nil
}

// GetAPIKey 获取API密钥
func (c *Client) GetAPIKey() string {
	return c.apiKey
}

// SetToken 设置认证令牌
func (c *Client) SetToken(token string) {
	c.token = token
}

// makeRequest 发送HTTP请求
func (c *Client) makeRequest(method, endpoint string, data interface{}) ([]byte, error) {
	var body []byte
	var err error

	if data != nil {
		body, err = json.Marshal(data)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal request data: %v", err)
		}
	}

	url := c.baseURL + endpoint
	req, err := http.NewRequest(method, url, bytes.NewBuffer(body))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %v", err)
	}

	req.Header.Set("Content-Type", "application/json")
	if c.token != "" {
		req.Header.Set("Authorization", "Bearer "+c.token)
	}

	c.logger.Debugf("Making %s request to %s", method, url)

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to make request: %v", err)
	}
	defer resp.Body.Close()

	respBody, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %v", err)
	}

	if resp.StatusCode >= 400 {
		return nil, fmt.Errorf("HTTP %d: %s", resp.StatusCode, string(respBody))
	}

	return respBody, nil
}

// RegisterAgent 注册Agent
func (c *Client) RegisterAgent(req *AgentRegisterRequest) (*AgentInfo, error) {
	respBody, err := c.makeRequest("POST", "/api/agents/register", req)
	if err != nil {
		return nil, err
	}

	var agentInfo AgentInfo
	if err := json.Unmarshal(respBody, &agentInfo); err != nil {
		return nil, fmt.Errorf("failed to parse response: %v", err)
	}

	return &agentInfo, nil
}

// SendHeartbeat 发送心跳
func (c *Client) SendHeartbeat(req *AgentHeartbeatRequest) error {
	_, err := c.makeRequest("POST", "/api/agents/heartbeat", req)
	return err
}

// PollTasks 轮询任务
func (c *Client) PollTasks(agentID string, capabilities []string) (*TaskPollResponse, error) {
	endpoint := fmt.Sprintf("/api/agents/tasks/poll?agent_id=%s&capabilities=%s", 
		agentID, joinStrings(capabilities, ","))
	
	respBody, err := c.makeRequest("GET", endpoint, nil)
	if err != nil {
		return nil, err
	}

	var pollResp TaskPollResponse
	if err := json.Unmarshal(respBody, &pollResp); err != nil {
		return nil, fmt.Errorf("failed to parse response: %v", err)
	}

	return &pollResp, nil
}

// SubmitTaskResult 提交任务结果
func (c *Client) SubmitTaskResult(result *TaskResult) error {
	_, err := c.makeRequest("POST", "/api/agents/tasks/result", result)
	return err
}

// Login 用户登录获取token
func (c *Client) Login(username, password string) error {
	loginReq := map[string]string{
		"username": username,
		"password": password,
	}

	respBody, err := c.makeRequest("POST", "/api/auth/login", loginReq)
	if err != nil {
		return err
	}

	var loginResp struct {
		AccessToken string `json:"access_token"`
		TokenType   string `json:"token_type"`
	}

	if err := json.Unmarshal(respBody, &loginResp); err != nil {
		return fmt.Errorf("failed to parse login response: %v", err)
	}

	c.token = loginResp.AccessToken
	return nil
}

// joinStrings 连接字符串数组
func joinStrings(strs []string, separator string) string {
	if len(strs) == 0 {
		return ""
	}
	
	result := strs[0]
	for i := 1; i < len(strs); i++ {
		result += separator + strs[i]
	}
	return result
}

// ========== 基于密钥的新方法 ==========

// RegisterAgentWithKey 使用API密钥注册Agent
func (c *Client) RegisterAgentWithKey(req *AgentRegisterWithKeyRequest) (*AgentInfo, error) {
	respBody, err := c.makeRequest("POST", "/api/agents/register-with-key", req)
	if err != nil {
		return nil, err
	}

	var agentInfo AgentInfo
	if err := json.Unmarshal(respBody, &agentInfo); err != nil {
		return nil, fmt.Errorf("failed to parse agent info response: %v", err)
	}

	return &agentInfo, nil
}

// SendHeartbeatWithKey 使用API密钥发送心跳
func (c *Client) SendHeartbeatWithKey(req *AgentHeartbeatWithKeyRequest) error {
	_, err := c.makeRequest("POST", "/api/agents/heartbeat-with-key", req)
	return err
}

// PollTasksWithKey 使用API密钥轮询任务
func (c *Client) PollTasksWithKey(agentID string, capabilities []string) (*TaskPollResponse, error) {
	if c.apiKey == "" {
		return nil, fmt.Errorf("API key not set")
	}

	url := fmt.Sprintf("/api/agents/tasks/poll-with-key?api_key=%s&agent_id=%s&capabilities=%s", 
		c.apiKey, agentID, joinStrings(capabilities, ","))
	
	respBody, err := c.makeRequest("GET", url, nil)
	if err != nil {
		return nil, err
	}

	var pollResp TaskPollResponse
	if err := json.Unmarshal(respBody, &pollResp); err != nil {
		return nil, fmt.Errorf("failed to parse poll response: %v", err)
	}

	return &pollResp, nil
}