package executor

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"os/exec"
	"strings"
	"time"

	"bounty-agent/client"
	"github.com/sirupsen/logrus"
)

// ServiceDetectionExecutor 服务检测执行器
type ServiceDetectionExecutor struct {
	logger *logrus.Logger
}

// NewServiceDetectionExecutor 创建服务检测执行器
func NewServiceDetectionExecutor(logger *logrus.Logger) *ServiceDetectionExecutor {
	return &ServiceDetectionExecutor{
		logger: logger,
	}
}

// GetCapability 返回能力标识
func (e *ServiceDetectionExecutor) GetCapability() string {
	return "service_detection"
}

// Execute 执行服务检测任务
func (e *ServiceDetectionExecutor) Execute(ctx context.Context, task *client.TaskDefinition) (*client.TaskResult, error) {
	target := task.Target
	e.logger.Infof("Starting service detection for target: %s", target)

	// 从参数中获取配置
	timeout := 10
	if t, ok := task.Parameters["timeout"]; ok {
		if timeoutNum, ok := t.(float64); ok {
			timeout = int(timeoutNum)
		}
	}

	var services []map[string]interface{}
	var assetsDiscovered []map[string]interface{}

	// 如果目标包含端口信息，解析它们
	if strings.Contains(target, ":") {
		// 单个主机:端口
		services, assetsDiscovered = e.detectSingleService(ctx, target, timeout)
	} else if strings.Contains(target, ",") {
		// 多个目标
		targets := strings.Split(target, ",")
		for _, t := range targets {
			t = strings.TrimSpace(t)
			singleServices, singleAssets := e.detectSingleService(ctx, t, timeout)
			services = append(services, singleServices...)
			assetsDiscovered = append(assetsDiscovered, singleAssets...)
		}
	} else {
		// 单个主机，检测常用端口
		commonPorts := []string{"80", "443", "8080", "8443"}
		for _, port := range commonPorts {
			targetWithPort := fmt.Sprintf("%s:%s", target, port)
			singleServices, singleAssets := e.detectSingleService(ctx, targetWithPort, timeout)
			services = append(services, singleServices...)
			assetsDiscovered = append(assetsDiscovered, singleAssets...)
		}
	}

	resultData := map[string]interface{}{
		"target":         target,
		"services":       services,
		"total_services": len(services),
	}

	result := &client.TaskResult{
		Status:           "completed",
		ResultData:       resultData,
		AssetsDiscovered: assetsDiscovered,
	}

	e.logger.Infof("Service detection completed. Found %d services", len(services))
	return result, nil
}

// detectSingleService 检测单个服务
func (e *ServiceDetectionExecutor) detectSingleService(ctx context.Context, target string, timeout int) ([]map[string]interface{}, []map[string]interface{}) {
	var services []map[string]interface{}
	var assetsDiscovered []map[string]interface{}

	// 尝试使用httpx进行检测
	httpxResult, err := e.runHttpx(ctx, target, timeout)
	if err == nil && httpxResult != nil {
		services = append(services, httpxResult)
		
		// 创建资产发现记录
		asset := map[string]interface{}{
			"type":   "web_service",
			"value":  target,
			"source": "httpx",
			"metadata": map[string]interface{}{
				"target":         target,
				"service_info":   httpxResult,
				"discovery_time": time.Now().Format(time.RFC3339),
			},
			"confidence": 0.9,
		}
		assetsDiscovered = append(assetsDiscovered, asset)
	}

	// 如果httpx失败，尝试简单的HTTP检测
	if len(services) == 0 {
		if httpResult := e.detectHTTPService(ctx, target, timeout); httpResult != nil {
			services = append(services, httpResult)
			
			asset := map[string]interface{}{
				"type":   "web_service",
				"value":  target,
				"source": "http_detector",
				"metadata": map[string]interface{}{
					"target":         target,
					"service_info":   httpResult,
					"discovery_time": time.Now().Format(time.RFC3339),
				},
				"confidence": 0.7,
			}
			assetsDiscovered = append(assetsDiscovered, asset)
		}
	}

	return services, assetsDiscovered
}

// runHttpx 使用httpx进行服务检测
func (e *ServiceDetectionExecutor) runHttpx(ctx context.Context, target string, timeout int) (map[string]interface{}, error) {
	args := []string{
		"-u", target,
		"-silent",
		"-json",
		"-status-code",
		"-title",
		"-tech-detect",
		"-server",
		"-timeout", fmt.Sprintf("%d", timeout),
	}

	cmd := exec.CommandContext(ctx, "httpx", args...)
	output, err := cmd.Output()
	if err != nil {
		if strings.Contains(err.Error(), "executable file not found") {
			e.logger.Warn("httpx not found, falling back to basic HTTP detection")
			return nil, fmt.Errorf("httpx not available")
		}
		return nil, fmt.Errorf("httpx failed: %v", err)
	}

	// 解析httpx的JSON输出
	outputStr := strings.TrimSpace(string(output))
	if outputStr == "" {
		return nil, fmt.Errorf("no output from httpx")
	}

	var result map[string]interface{}
	if err := json.Unmarshal([]byte(outputStr), &result); err != nil {
		return nil, fmt.Errorf("failed to parse httpx output: %v", err)
	}

	return result, nil
}

// detectHTTPService 基本的HTTP服务检测
func (e *ServiceDetectionExecutor) detectHTTPService(ctx context.Context, target string, timeout int) map[string]interface{} {
	// 确保目标包含协议
	var urls []string
	if strings.Contains(target, ":") {
		// 包含端口，尝试HTTP和HTTPS
		if !strings.HasPrefix(target, "http") {
			urls = append(urls, "http://"+target)
			urls = append(urls, "https://"+target)
		} else {
			urls = append(urls, target)
		}
	} else {
		urls = append(urls, "http://"+target)
		urls = append(urls, "https://"+target)
	}

	client := &http.Client{
		Timeout: time.Duration(timeout) * time.Second,
	}

	for _, url := range urls {
		select {
		case <-ctx.Done():
			return nil
		default:
		}

		resp, err := client.Get(url)
		if err != nil {
			continue
		}
		resp.Body.Close()

		// 构造服务信息
		service := map[string]interface{}{
			"url":         url,
			"status_code": resp.StatusCode,
			"server":      resp.Header.Get("Server"),
			"title":       e.extractTitle(resp),
			"protocol":    strings.Split(url, "://")[0],
		}

		// 检测技术栈
		if technologies := e.detectTechnologies(resp); len(technologies) > 0 {
			service["technologies"] = technologies
		}

		return service
	}

	return nil
}

// extractTitle 从响应中提取标题（简化版）
func (e *ServiceDetectionExecutor) extractTitle(resp *http.Response) string {
	// 这里只是一个简化实现
	// 实际应该解析HTML内容提取<title>标签
	return "Unknown"
}

// detectTechnologies 检测技术栈（简化版）
func (e *ServiceDetectionExecutor) detectTechnologies(resp *http.Response) []string {
	var technologies []string

	// 通过响应头检测技术
	server := resp.Header.Get("Server")
	if server != "" {
		if strings.Contains(strings.ToLower(server), "nginx") {
			technologies = append(technologies, "Nginx")
		}
		if strings.Contains(strings.ToLower(server), "apache") {
			technologies = append(technologies, "Apache")
		}
		if strings.Contains(strings.ToLower(server), "iis") {
			technologies = append(technologies, "IIS")
		}
	}

	// 通过其他响应头检测
	xPoweredBy := resp.Header.Get("X-Powered-By")
	if xPoweredBy != "" {
		technologies = append(technologies, xPoweredBy)
	}

	return technologies
}