package executor

import (
	"context"
	"fmt"
	"time"

	"bounty-agent/client"
	"github.com/sirupsen/logrus"
)

// TaskExecutor 任务执行器接口
type TaskExecutor interface {
	Execute(ctx context.Context, task *client.TaskDefinition) (*client.TaskResult, error)
	GetCapability() string
}

// ExecutorManager 执行器管理器
type ExecutorManager struct {
	executors  map[string]TaskExecutor
	logger     *logrus.Logger
	controller AgentController
}

// NewExecutorManager 创建执行器管理器
func NewExecutorManager(logger *logrus.Logger) *ExecutorManager {
	return &ExecutorManager{
		executors: make(map[string]TaskExecutor),
		logger:    logger,
	}
}

// SetController 设置Agent控制器
func (em *ExecutorManager) SetController(controller AgentController) {
	em.controller = controller
}

// RegisterExecutor 注册执行器
func (em *ExecutorManager) RegisterExecutor(executor TaskExecutor) {
	capability := executor.GetCapability()
	em.executors[capability] = executor
	em.logger.Infof("Registered executor for capability: %s", capability)
}

// GetExecutor 获取执行器
func (em *ExecutorManager) GetExecutor(capability string) (TaskExecutor, bool) {
	executor, exists := em.executors[capability]
	return executor, exists
}

// GetCapabilities 获取所有支持的能力
func (em *ExecutorManager) GetCapabilities() []string {
	capabilities := make([]string, 0, len(em.executors)+5)
	for capability := range em.executors {
		capabilities = append(capabilities, capability)
	}
	
	// 添加Agent控制能力
	controlCapabilities := []string{"agent_pause", "agent_resume", "agent_stop", "agent_restart", "agent_cancel_tasks"}
	capabilities = append(capabilities, controlCapabilities...)
	
	return capabilities
}

// ExecuteTask 执行任务
func (em *ExecutorManager) ExecuteTask(ctx context.Context, task *client.TaskDefinition) (*client.TaskResult, error) {
	// 检查是否为Agent控制命令
	if em.isControlCommand(task.TaskType) {
		return em.executeControlCommand(ctx, task)
	}

	executor, exists := em.GetExecutor(task.TaskType)
	if !exists {
		return nil, fmt.Errorf("no executor found for task type: %s", task.TaskType)
	}

	em.logger.Infof("Executing task %s with type %s", task.TaskID, task.TaskType)
	
	startTime := time.Now()
	result, err := executor.Execute(ctx, task)
	executionTime := time.Since(startTime).Seconds()

	if result == nil {
		result = &client.TaskResult{}
	}

	// 设置基本字段
	result.TaskID = task.TaskID
	result.ExecutionTime = executionTime

	if err != nil {
		result.Status = "failed"
		result.ErrorMessage = err.Error()
		em.logger.Errorf("Task %s failed: %v", task.TaskID, err)
	} else if result.Status == "" {
		result.Status = "completed"
	}

	return result, err
}

// isControlCommand 检查是否为控制命令
func (em *ExecutorManager) isControlCommand(taskType string) bool {
	controlCommands := []string{"agent_pause", "agent_resume", "agent_stop", "agent_restart", "cancel_tasks"}
	for _, cmd := range controlCommands {
		if taskType == cmd {
			return true
		}
	}
	return false
}

// executeControlCommand 执行控制命令
func (em *ExecutorManager) executeControlCommand(ctx context.Context, task *client.TaskDefinition) (*client.TaskResult, error) {
	result := &client.TaskResult{
		TaskID: task.TaskID,
		Status: "completed",
		ResultData: map[string]interface{}{},
		ExecutionTime: 0.1,
	}

	em.logger.Infof("执行Agent控制命令: %s", task.TaskType)

	if em.controller == nil {
		return nil, fmt.Errorf("Agent控制器未设置")
	}

	switch task.TaskType {
	case "agent_pause":
		if err := em.controller.Pause(); err != nil {
			result.Status = "failed"
			result.ErrorMessage = err.Error()
			return result, err
		}
		result.ResultData["message"] = "Agent已暂停"
		result.ResultData["action"] = "pause"
		em.logger.Info("Agent暂停命令已处理")

	case "agent_resume":
		if err := em.controller.Resume(); err != nil {
			result.Status = "failed"
			result.ErrorMessage = err.Error()
			return result, err
		}
		result.ResultData["message"] = "Agent已恢复"
		result.ResultData["action"] = "resume"
		em.logger.Info("Agent恢复命令已处理")

	case "agent_stop":
		result.ResultData["message"] = "Agent停止命令已执行"
		result.ResultData["action"] = "stop"
		em.logger.Info("Agent停止命令已处理")
		// 在返回结果后再执行停止
		go func() {
			time.Sleep(500 * time.Millisecond) // 等待结果发送
			em.controller.Stop()
		}()

	case "agent_restart":
		if err := em.controller.Restart(); err != nil {
			result.Status = "failed"
			result.ErrorMessage = err.Error()
			return result, err
		}
		result.ResultData["message"] = "Agent重启命令已执行"
		result.ResultData["action"] = "restart"
		em.logger.Info("Agent重启命令已处理")

	case "cancel_tasks":
		result.ResultData["message"] = "任务取消命令已执行"
		result.ResultData["action"] = "cancel_tasks"
		em.logger.Info("任务取消命令已处理")
		// 这里可以添加取消当前任务的逻辑

	default:
		return nil, fmt.Errorf("未知的Agent控制命令: %s", task.TaskType)
	}

	return result, nil
}
	