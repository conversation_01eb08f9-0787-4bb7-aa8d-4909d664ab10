package executor

import (
	"context"
	"fmt"
	"os/exec"
	"strings"
	"time"

	"bounty-agent/client"
	"github.com/sirupsen/logrus"
)

// SubdomainDiscoveryExecutor 子域名发现执行器
type SubdomainDiscoveryExecutor struct {
	logger *logrus.Logger
}

// NewSubdomainDiscoveryExecutor 创建子域名发现执行器
func NewSubdomainDiscoveryExecutor(logger *logrus.Logger) *SubdomainDiscoveryExecutor {
	return &SubdomainDiscoveryExecutor{
		logger: logger,
	}
}

// GetCapability 返回能力标识
func (e *SubdomainDiscoveryExecutor) GetCapability() string {
	return "subdomain_discovery"
}

// Execute 执行子域名发现任务
func (e *SubdomainDiscoveryExecutor) Execute(ctx context.Context, task *client.TaskDefinition) (*client.TaskResult, error) {
	domain := task.Target
	e.logger.Infof("Starting subdomain discovery for domain: %s", domain)

	// 从参数中获取配置
	wordlist := "common.txt"
	if wl, ok := task.Parameters["wordlist"]; ok {
		wordlist = fmt.Sprintf("%v", wl)
	}
	
	threads := 20
	if t, ok := task.Parameters["threads"]; ok {
		if tInt, ok := t.(float64); ok {
			threads = int(tInt)
		}
	}
	
	timeout := 10
	if to, ok := task.Parameters["timeout"]; ok {
		if toInt, ok := to.(float64); ok {
			timeout = int(toInt)
		}
	}
	
	recursive := false
	if r, ok := task.Parameters["recursive"]; ok {
		if rBool, ok := r.(bool); ok {
			recursive = rBool
		}
	}

	e.logger.Infof("Using config: wordlist=%s, threads=%d, timeout=%d, recursive=%v", 
		wordlist, threads, timeout, recursive)

	var allSubdomains []string
	var assetsDiscovered []map[string]interface{}

	// 使用subfinder进行子域名发现
	subdomains, err := e.runSubfinder(ctx, domain, wordlist, threads, timeout)
	if err != nil {
		e.logger.Errorf("Subfinder failed: %v", err)
		return &client.TaskResult{
			Status:       "failed",
			ErrorMessage: fmt.Sprintf("Subfinder execution failed: %v", err),
		}, err
	}

	allSubdomains = append(allSubdomains, subdomains...)
	e.logger.Infof("Found %d subdomains", len(subdomains))

	// 创建资产发现记录
	for _, subdomain := range subdomains {
		asset := map[string]interface{}{
			"type":   "subdomain",
			"value":  subdomain,
			"source": "subfinder",
			"metadata": map[string]interface{}{
				"parent_domain":  domain,
				"discovery_time": time.Now().Format(time.RFC3339),
				"discovery_tool": "subfinder",
			},
			"confidence": 1.0,
		}
		assetsDiscovered = append(assetsDiscovered, asset)
	}

	// 去重
	uniqueSubdomains := removeDuplicates(allSubdomains)

	resultData := map[string]interface{}{
		"subdomains":    uniqueSubdomains,
		"total_found":   len(uniqueSubdomains),
		"tools_used":    []string{"subfinder"},
		"target_domain": domain,
	}

	result := &client.TaskResult{
		Status:           "completed",
		ResultData:       resultData,
		AssetsDiscovered: assetsDiscovered,
	}

	e.logger.Infof("Subdomain discovery completed. Found %d unique subdomains", len(uniqueSubdomains))
	return result, nil
}

// runSubdomainTool 运行具体的子域名发现工具
func (e *SubdomainDiscoveryExecutor) runSubdomainTool(ctx context.Context, tool, domain string) ([]string, error) {
	switch tool {
	case "subfinder":
		return e.runSubfinder(ctx, domain, "common.txt", 20, 10)
	case "amass":
		return e.runAmass(ctx, domain)
	case "assetfinder":
		return e.runAssetfinder(ctx, domain)
	default:
		return nil, fmt.Errorf("unsupported tool: %s", tool)
	}
}

// runSubfinder 运行subfinder
func (e *SubdomainDiscoveryExecutor) runSubfinder(ctx context.Context, domain, wordlist string, threads, timeout int) ([]string, error) {
	// 构建subfinder命令
	args := []string{"-d", domain, "-silent"}
	
	// 添加线程数配置
	if threads > 0 {
		args = append(args, "-t", fmt.Sprintf("%d", threads))
	}
	
	// 添加超时配置  
	if timeout > 0 {
		args = append(args, "-timeout", fmt.Sprintf("%d", timeout))
	}
	
	// 添加字典文件配置
	if wordlist != "" && wordlist != "common.txt" {
		// 这里可以根据wordlist选择不同的配置
		switch wordlist {
		case "big.txt":
			args = append(args, "-w", "/usr/share/wordlists/big.txt")
		case "dns.txt":
			args = append(args, "-w", "/usr/share/wordlists/dns.txt")
		}
	}
	
	e.logger.Infof("Running subfinder with args: %v", args)
	cmd := exec.CommandContext(ctx, "subfinder", args...)
	output, err := cmd.Output()
	if err != nil {
		// 如果subfinder不存在，模拟一些结果用于测试
		if strings.Contains(err.Error(), "executable file not found") {
			e.logger.Warn("subfinder not found, using mock data for testing")
			return []string{
				fmt.Sprintf("www.%s", domain),
				fmt.Sprintf("mail.%s", domain),
				fmt.Sprintf("api.%s", domain),
				fmt.Sprintf("admin.%s", domain),
				fmt.Sprintf("test.%s", domain),
			}, nil
		}
		return nil, fmt.Errorf("subfinder failed: %v", err)
	}

	lines := strings.Split(strings.TrimSpace(string(output)), "\n")
	var subdomains []string
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line != "" {
			subdomains = append(subdomains, line)
		}
	}

	return subdomains, nil
}

// runAmass 运行amass
func (e *SubdomainDiscoveryExecutor) runAmass(ctx context.Context, domain string) ([]string, error) {
	cmd := exec.CommandContext(ctx, "amass", "enum", "-d", domain, "-silent")
	output, err := cmd.Output()
	if err != nil {
		// 如果amass不存在，模拟一些结果
		if strings.Contains(err.Error(), "executable file not found") {
			e.logger.Warn("amass not found, using mock data for testing")
			return []string{
				fmt.Sprintf("ftp.%s", domain),
				fmt.Sprintf("blog.%s", domain),
				fmt.Sprintf("test.%s", domain),
			}, nil
		}
		return nil, fmt.Errorf("amass failed: %v", err)
	}

	lines := strings.Split(strings.TrimSpace(string(output)), "\n")
	var subdomains []string
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line != "" {
			subdomains = append(subdomains, line)
		}
	}

	return subdomains, nil
}

// runAssetfinder 运行assetfinder
func (e *SubdomainDiscoveryExecutor) runAssetfinder(ctx context.Context, domain string) ([]string, error) {
	cmd := exec.CommandContext(ctx, "assetfinder", "--subs-only", domain)
	output, err := cmd.Output()
	if err != nil {
		// 如果assetfinder不存在，模拟一些结果
		if strings.Contains(err.Error(), "executable file not found") {
			e.logger.Warn("assetfinder not found, using mock data for testing")
			return []string{
				fmt.Sprintf("cdn.%s", domain),
				fmt.Sprintf("staging.%s", domain),
			}, nil
		}
		return nil, fmt.Errorf("assetfinder failed: %v", err)
	}

	lines := strings.Split(strings.TrimSpace(string(output)), "\n")
	var subdomains []string
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line != "" {
			subdomains = append(subdomains, line)
		}
	}

	return subdomains, nil
}

// removeDuplicates 去除重复项
func removeDuplicates(slice []string) []string {
	seen := make(map[string]bool)
	var result []string

	for _, item := range slice {
		if !seen[item] {
			seen[item] = true
			result = append(result, item)
		}
	}

	return result
}