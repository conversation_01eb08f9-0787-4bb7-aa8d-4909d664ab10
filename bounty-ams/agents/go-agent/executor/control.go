package executor

import (
	"context"
	"fmt"
	"time"

	"bounty-agent/client"
	"github.com/sirupsen/logrus"
)

// AgentControlExecutor Agent控制命令执行器
type AgentControlExecutor struct {
	logger      *logrus.Logger
	agentCtrl   AgentController
}

// AgentController Agent控制器接口
type AgentController interface {
	Pause() error
	Resume() error
	Stop()
	Restart() error
}

// NewAgentControlExecutor 创建Agent控制执行器
func NewAgentControlExecutor(logger *logrus.Logger, agentCtrl AgentController) *AgentControlExecutor {
	return &AgentControlExecutor{
		logger:    logger,
		agentCtrl: agentCtrl,
	}
}

// Execute 执行控制命令
func (ace *AgentControlExecutor) Execute(ctx context.Context, task *client.TaskDefinition) (*client.TaskResult, error) {
	result := &client.TaskResult{
		TaskID: task.TaskID,
		Status: "completed",
		ResultData: map[string]interface{}{},
	}

	ace.logger.Infof("Executing agent control command: %s", task.TaskType)

	switch task.TaskType {
	case "agent_pause":
		if err := ace.agentCtrl.Pause(); err != nil {
			return nil, fmt.Errorf("failed to pause agent: %v", err)
		}
		result.ResultData["message"] = "Agent paused successfully"
		ace.logger.Info("Agent paused successfully")

	case "agent_resume":
		if err := ace.agentCtrl.Resume(); err != nil {
			return nil, fmt.Errorf("failed to resume agent: %v", err)
		}
		result.ResultData["message"] = "Agent resumed successfully"
		ace.logger.Info("Agent resumed successfully")

	case "agent_stop":
		result.ResultData["message"] = "Agent stop initiated"
		ace.logger.Info("Agent stop command received, shutting down...")
		// 延迟执行停止，允许结果先提交
		go func() {
			// 给一点时间提交结果
			time.Sleep(2 * time.Second)
			ace.agentCtrl.Stop()
		}()

	case "agent_restart":
		if err := ace.agentCtrl.Restart(); err != nil {
			return nil, fmt.Errorf("failed to restart agent: %v", err)
		}
		result.ResultData["message"] = "Agent restart initiated"
		ace.logger.Info("Agent restart successfully")

	case "cancel_tasks":
		// 取消当前任务的逻辑可以在这里实现
		result.ResultData["message"] = "Tasks cancellation initiated"
		ace.logger.Info("Task cancellation command received")

	default:
		return nil, fmt.Errorf("unknown agent control command: %s", task.TaskType)
	}

	return result, nil
}

// GetCapability 返回控制能力标识
func (ace *AgentControlExecutor) GetCapability() string {
	return "agent_control"
}