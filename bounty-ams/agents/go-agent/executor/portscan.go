package executor

import (
	"context"
	"encoding/json"
	"fmt"
	"net"
	"os/exec"
	"strconv"
	"strings"
	"sync"
	"time"

	"bounty-agent/client"
	"github.com/sirupsen/logrus"
)

// PortScanningExecutor 端口扫描执行器
type PortScanningExecutor struct {
	logger *logrus.Logger
}

// NewPortScanningExecutor 创建端口扫描执行器
func NewPortScanningExecutor(logger *logrus.Logger) *PortScanningExecutor {
	return &PortScanningExecutor{
		logger: logger,
	}
}

// GetCapability 返回能力标识
func (e *PortScanningExecutor) GetCapability() string {
	return "port_scanning"
}

// Execute 执行端口扫描任务
func (e *PortScanningExecutor) Execute(ctx context.Context, task *client.TaskDefinition) (*client.TaskResult, error) {
	target := task.Target
	e.logger.Infof("Starting port scanning for target: %s", target)

	// 从参数中获取配置
	portsParam := "top1000"
	if p, ok := task.Parameters["ports"]; ok {
		portsParam = fmt.Sprintf("%v", p)
	}

	scanType := "connect"
	if st, ok := task.Parameters["scan_type"]; ok {
		scanType = fmt.Sprintf("%v", st)
	}

	threads := 25
	if t, ok := task.Parameters["threads"]; ok {
		if threadNum, ok := t.(float64); ok {
			threads = int(threadNum)
		}
	}

	// 解析端口范围
	ports, err := e.parsePorts(portsParam)
	if err != nil {
		return nil, fmt.Errorf("failed to parse ports: %v", err)
	}

	var openPorts []map[string]interface{}
	var assetsDiscovered []map[string]interface{}

	// 执行端口扫描
	switch scanType {
	case "naabu":
		openPorts, err = e.runNaabu(ctx, target, portsParam)
	case "nmap":
		openPorts, err = e.runNmap(ctx, target, ports)
	default:
		openPorts, err = e.runConnectScan(ctx, target, ports, threads)
	}

	if err != nil {
		return nil, fmt.Errorf("port scanning failed: %v", err)
	}

	// 创建资产发现记录
	for _, portInfo := range openPorts {
		asset := map[string]interface{}{
			"type":   "open_port",
			"value":  fmt.Sprintf("%s:%v", target, portInfo["port"]),
			"source": "port_scanner",
			"metadata": map[string]interface{}{
				"target":         target,
				"port":           portInfo["port"],
				"protocol":       "tcp",
				"service":        portInfo["service"],
				"discovery_time": time.Now().Format(time.RFC3339),
				"scan_type":      scanType,
			},
			"confidence": 1.0,
		}
		assetsDiscovered = append(assetsDiscovered, asset)
	}

	resultData := map[string]interface{}{
		"target":      target,
		"open_ports":  openPorts,
		"total_open":  len(openPorts),
		"scan_type":   scanType,
		"ports_scanned": len(ports),
	}

	result := &client.TaskResult{
		Status:           "completed",
		ResultData:       resultData,
		AssetsDiscovered: assetsDiscovered,
	}

	e.logger.Infof("Port scanning completed. Found %d open ports", len(openPorts))
	return result, nil
}

// parsePorts 解析端口参数
func (e *PortScanningExecutor) parsePorts(portsParam string) ([]int, error) {
	switch portsParam {
	case "top100":
		return []int{
			21, 22, 23, 25, 53, 80, 110, 111, 135, 139,
			143, 443, 993, 995, 1723, 3306, 3389, 5900, 8080, 8443,
			// 添加更多常用端口...
		}, nil
	case "top1000":
		// 返回nmap的top1000端口
		return e.getNmapTop1000Ports(), nil
	case "all":
		// 返回所有65535个端口（谨慎使用）
		ports := make([]int, 65535)
		for i := 1; i <= 65535; i++ {
			ports[i-1] = i
		}
		return ports, nil
	default:
		// 解析自定义端口范围，如 "80,443,8080-8090"
		return e.parseCustomPorts(portsParam)
	}
}

// runNaabu 使用naabu进行端口扫描
func (e *PortScanningExecutor) runNaabu(ctx context.Context, target, ports string) ([]map[string]interface{}, error) {
	args := []string{"-host", target, "-p", ports, "-silent", "-json"}
	cmd := exec.CommandContext(ctx, "naabu", args...)
	
	output, err := cmd.Output()
	if err != nil {
		if strings.Contains(err.Error(), "executable file not found") {
			e.logger.Warn("naabu not found, falling back to connect scan")
			portList, _ := e.parsePorts(ports)
			return e.runConnectScan(ctx, target, portList, 25)
		}
		return nil, fmt.Errorf("naabu failed: %v", err)
	}

	// 解析naabu的JSON输出
	lines := strings.Split(strings.TrimSpace(string(output)), "\n")
	var openPorts []map[string]interface{}

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		var result map[string]interface{}
		if err := json.Unmarshal([]byte(line), &result); err != nil {
			continue
		}

		if host, hostOk := result["host"]; hostOk {
			if port, portOk := result["port"]; portOk {
				openPorts = append(openPorts, map[string]interface{}{
					"host":    host,
					"port":    port,
					"service": "unknown",
				})
			}
		}
	}

	return openPorts, nil
}

// runNmap 使用nmap进行端口扫描
func (e *PortScanningExecutor) runNmap(ctx context.Context, target string, ports []int) ([]map[string]interface{}, error) {
	portStr := e.formatPortsForNmap(ports)
	args := []string{"-p", portStr, "--open", "-T4", target}
	cmd := exec.CommandContext(ctx, "nmap", args...)
	
	output, err := cmd.Output()
	if err != nil {
		if strings.Contains(err.Error(), "executable file not found") {
			e.logger.Warn("nmap not found, falling back to connect scan")
			return e.runConnectScan(ctx, target, ports, 25)
		}
		return nil, fmt.Errorf("nmap failed: %v", err)
	}

	return e.parseNmapOutput(string(output)), nil
}

// runConnectScan 运行TCP Connect扫描
func (e *PortScanningExecutor) runConnectScan(ctx context.Context, target string, ports []int, threads int) ([]map[string]interface{}, error) {
	var openPorts []map[string]interface{}
	var mutex sync.Mutex
	
	// 创建工作池
	semaphore := make(chan struct{}, threads)
	var wg sync.WaitGroup

	for _, port := range ports {
		wg.Add(1)
		go func(p int) {
			defer wg.Done()
			semaphore <- struct{}{} // 获取信号量
			defer func() { <-semaphore }() // 释放信号量

			if e.isPortOpen(ctx, target, p) {
				mutex.Lock()
				openPorts = append(openPorts, map[string]interface{}{
					"host":    target,
					"port":    p,
					"service": e.guessService(p),
				})
				mutex.Unlock()
			}
		}(port)
	}

	wg.Wait()
	return openPorts, nil
}

// isPortOpen 检查端口是否开放
func (e *PortScanningExecutor) isPortOpen(ctx context.Context, host string, port int) bool {
	timeout := 3 * time.Second
	conn, err := net.DialTimeout("tcp", fmt.Sprintf("%s:%d", host, port), timeout)
	if err != nil {
		return false
	}
	conn.Close()
	return true
}

// guessService 根据端口号猜测服务
func (e *PortScanningExecutor) guessService(port int) string {
	services := map[int]string{
		21:   "ftp",
		22:   "ssh",
		23:   "telnet",
		25:   "smtp",
		53:   "dns",
		80:   "http",
		110:  "pop3",
		143:  "imap",
		443:  "https",
		993:  "imaps",
		995:  "pop3s",
		3306: "mysql",
		3389: "rdp",
		5432: "postgresql",
		5900: "vnc",
		8080: "http-alt",
		8443: "https-alt",
	}

	if service, ok := services[port]; ok {
		return service
	}
	return "unknown"
}

// getNmapTop1000Ports 获取nmap的top1000端口
func (e *PortScanningExecutor) getNmapTop1000Ports() []int {
	// 这里只是一个示例，实际应该包含nmap的完整top1000端口列表
	return []int{
		1, 3, 4, 6, 7, 9, 13, 17, 19, 20, 21, 22, 23, 24, 25, 26, 30, 32, 33, 37, 42, 43, 49, 53, 70, 79, 80, 81, 82, 83, 84, 85, 88, 89, 90, 99, 100, 106, 109, 110, 111, 113, 119, 125, 135, 139, 143, 144, 146, 161, 163, 179, 199, 211, 212, 222, 254, 255, 256, 259, 264, 280, 301, 306, 311, 340, 366, 389, 406, 407, 416, 417, 425, 427, 443, 444, 445, 458, 464, 465, 481, 497, 500, 512, 513, 514, 515, 524, 541, 543, 544, 545, 548, 554, 555, 563, 587, 593, 616, 617, 625, 631, 636, 646, 648, 666, 667, 668, 683, 687, 691, 700, 705, 711, 714, 720, 722, 726, 749, 765, 777, 783, 787, 800, 801, 808, 843, 873, 880, 888, 898, 900, 901, 902, 903, 911, 912, 981, 987, 990, 992, 993, 995, 999, 1000, 1001, 1002, 1007, 1009, 1010, 1011, 1021, 1022, 1023, 1024, 1025, 1026, 1027, 1028, 1029, 1030, 1031, 1032, 1033, 1034, 1035, 1036, 1037, 1038, 1039, 1040, 1041, 1042, 1043, 1044, 1045, 1046, 1047, 1048, 1049, 1050, 1051, 1052, 1053, 1054, 1055, 1056, 1057, 1058, 1059, 1060, 1061, 1062, 1063, 1064, 1065, 1066, 1067, 1068, 1069, 1070, 1071, 1072, 1073, 1074, 1075, 1076, 1077, 1078, 1079, 1080, 1081, 1082, 1083, 1084, 1085, 1086, 1087, 1088, 1089, 1090, 1091, 1092, 1093, 1094, 1095, 1096, 1097, 1098, 1099, 1100,
		// 添加更多端口...
	}
}

// parseCustomPorts 解析自定义端口格式
func (e *PortScanningExecutor) parseCustomPorts(portsParam string) ([]int, error) {
	var ports []int
	parts := strings.Split(portsParam, ",")
	
	for _, part := range parts {
		part = strings.TrimSpace(part)
		if strings.Contains(part, "-") {
			// 处理端口范围，如 "8080-8090"
			rangeParts := strings.Split(part, "-")
			if len(rangeParts) != 2 {
				return nil, fmt.Errorf("invalid port range: %s", part)
			}
			
			start, err := strconv.Atoi(strings.TrimSpace(rangeParts[0]))
			if err != nil {
				return nil, fmt.Errorf("invalid start port: %s", rangeParts[0])
			}
			
			end, err := strconv.Atoi(strings.TrimSpace(rangeParts[1]))
			if err != nil {
				return nil, fmt.Errorf("invalid end port: %s", rangeParts[1])
			}
			
			for i := start; i <= end; i++ {
				ports = append(ports, i)
			}
		} else {
			// 处理单个端口
			port, err := strconv.Atoi(part)
			if err != nil {
				return nil, fmt.Errorf("invalid port: %s", part)
			}
			ports = append(ports, port)
		}
	}
	
	return ports, nil
}

// formatPortsForNmap 格式化端口列表给nmap使用
func (e *PortScanningExecutor) formatPortsForNmap(ports []int) string {
	portStrs := make([]string, len(ports))
	for i, port := range ports {
		portStrs[i] = strconv.Itoa(port)
	}
	return strings.Join(portStrs, ",")
}

// parseNmapOutput 解析nmap输出
func (e *PortScanningExecutor) parseNmapOutput(output string) []map[string]interface{} {
	var openPorts []map[string]interface{}
	lines := strings.Split(output, "\n")
	
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if strings.Contains(line, "/tcp") && strings.Contains(line, "open") {
			parts := strings.Fields(line)
			if len(parts) >= 3 {
				portService := strings.Split(parts[0], "/")
				if len(portService) >= 1 {
					if port, err := strconv.Atoi(portService[0]); err == nil {
						service := "unknown"
						if len(parts) >= 3 {
							service = parts[2]
						}
						
						openPorts = append(openPorts, map[string]interface{}{
							"port":    port,
							"service": service,
						})
					}
				}
			}
		}
	}
	
	return openPorts
}