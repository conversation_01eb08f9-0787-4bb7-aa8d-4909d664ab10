package executor

import (
	"context"
	"fmt"
	"net"
	"strings"
	"time"

	"bounty-agent/client"
	"github.com/sirupsen/logrus"
)

// DNSResolutionExecutor DNS解析执行器
type DNSResolutionExecutor struct {
	logger *logrus.Logger
}

// NewDNSResolutionExecutor 创建DNS解析执行器
func NewDNSResolutionExecutor(logger *logrus.Logger) *DNSResolutionExecutor {
	return &DNSResolutionExecutor{
		logger: logger,
	}
}

// GetCapability 返回能力标识
func (e *DNSResolutionExecutor) GetCapability() string {
	return "dns_resolution"
}

// Execute 执行DNS解析任务
func (e *DNSResolutionExecutor) Execute(ctx context.Context, task *client.TaskDefinition) (*client.TaskResult, error) {
	target := task.Target
	e.logger.Infof("Starting DNS resolution for target: %s", target)

	// 从参数中获取配置
	recordTypes := []string{"A", "AAAA"}
	if rt, ok := task.Parameters["record_types"]; ok {
		if rtList, ok := rt.([]interface{}); ok {
			recordTypes = make([]string, len(rtList))
			for i, rt := range rtList {
				recordTypes[i] = fmt.Sprintf("%v", rt)
			}
		}
	}

	dnsServer := ""
	if ds, ok := task.Parameters["dns_server"]; ok {
		dnsServer = fmt.Sprintf("%v", ds)
	}

	timeout := 5
	if to, ok := task.Parameters["timeout"]; ok {
		if toInt, ok := to.(float64); ok {
			timeout = int(toInt)
		}
	}

	e.logger.Infof("Using config: record_types=%v, dns_server=%s, timeout=%d", 
		recordTypes, dnsServer, timeout)

	var assetsDiscovered []map[string]interface{}
	resultData := map[string]interface{}{
		"target":       target,
		"record_types": recordTypes,
		"records":      make(map[string][]string),
	}

	// 设置超时上下文
	ctx, cancel := context.WithTimeout(ctx, time.Duration(timeout)*time.Second)
	defer cancel()

	// 设置DNS解析器
	resolver := &net.Resolver{}
	if dnsServer != "" {
		resolver = &net.Resolver{
			PreferGo: true,
			Dial: func(ctx context.Context, network, address string) (net.Conn, error) {
				d := net.Dialer{
					Timeout: time.Duration(timeout) * time.Second,
				}
				return d.DialContext(ctx, network, dnsServer+":53")
			},
		}
	}

	// 执行不同类型的DNS查询
	for _, recordType := range recordTypes {
		records, err := e.queryDNSRecord(ctx, resolver, target, recordType)
		if err != nil {
			e.logger.Warnf("Failed to query %s record for %s: %v", recordType, target, err)
			continue
		}

		if len(records) > 0 {
			resultData["records"].(map[string][]string)[recordType] = records
			e.logger.Infof("Found %d %s records for %s", len(records), recordType, target)

			// 创建资产发现记录
			for _, record := range records {
				asset := map[string]interface{}{
					"type":        "dns_record",
					"value":       record,
					"record_type": recordType,
					"domain":      target,
					"source":      "dns_resolution",
				}
				assetsDiscovered = append(assetsDiscovered, asset)
			}
		}
	}

	result := &client.TaskResult{
		Status:           "completed",
		ResultData:       resultData,
		AssetsDiscovered: assetsDiscovered,
	}

	e.logger.Infof("DNS resolution completed. Found records: %v", resultData["records"])
	return result, nil
}

// queryDNSRecord 查询特定类型的DNS记录
func (e *DNSResolutionExecutor) queryDNSRecord(ctx context.Context, resolver *net.Resolver, domain, recordType string) ([]string, error) {
	var records []string

	switch strings.ToUpper(recordType) {
	case "A":
		ips, err := resolver.LookupIPAddr(ctx, domain)
		if err != nil {
			return nil, err
		}
		for _, ip := range ips {
			if ip.IP.To4() != nil { // IPv4
				records = append(records, ip.IP.String())
			}
		}

	case "AAAA":
		ips, err := resolver.LookupIPAddr(ctx, domain)
		if err != nil {
			return nil, err
		}
		for _, ip := range ips {
			if ip.IP.To4() == nil { // IPv6
				records = append(records, ip.IP.String())
			}
		}

	case "CNAME":
		cname, err := resolver.LookupCNAME(ctx, domain)
		if err != nil {
			return nil, err
		}
		if cname != "" {
			records = append(records, cname)
		}

	case "MX":
		mxRecords, err := resolver.LookupMX(ctx, domain)
		if err != nil {
			return nil, err
		}
		for _, mx := range mxRecords {
			records = append(records, fmt.Sprintf("%d %s", mx.Pref, mx.Host))
		}

	case "TXT":
		txtRecords, err := resolver.LookupTXT(ctx, domain)
		if err != nil {
			return nil, err
		}
		records = append(records, txtRecords...)

	case "NS":
		nsRecords, err := resolver.LookupNS(ctx, domain)
		if err != nil {
			return nil, err
		}
		for _, ns := range nsRecords {
			records = append(records, ns.Host)
		}

	default:
		return nil, fmt.Errorf("unsupported DNS record type: %s", recordType)
	}

	return records, nil
}