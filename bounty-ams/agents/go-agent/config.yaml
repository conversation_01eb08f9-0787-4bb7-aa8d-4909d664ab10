agent:
  id: go-agent-code
  name: Go Recon Agent
  version: 1.0.0
  max_concurrent_tasks: 3
  capabilities:
  - subdomain_discovery
  - port_scanning
  - service_detection
  - dns_resolution
  - vulnerability_testing
  hostname: code
  ip_address: ""
server:
  base_url: http://localhost:8000
  timeout: 30
  api_key: your_api_key_here
tools:
  subfinder:
    enabled: true
    config: ""
    sources:
    - all
  naabu:
    enabled: true
    rate: 1000
    threads: 25
  httpx:
    enabled: true
    threads: 50
    timeout: 10s
  nuclei:
    enabled: true
    templates: []
    templates_path: ""
    rate: 150
log:
  level: info
  format: text
  file: ""
