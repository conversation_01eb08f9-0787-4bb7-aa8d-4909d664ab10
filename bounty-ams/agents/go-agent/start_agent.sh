#!/bin/bash

# Go Agent 启动脚本 - 支持多实例
# 用法: ./start_agent.sh [实例名]

INSTANCE_NAME=${1:-$(hostname)-$(date +%s)}
CONFIG_DIR="./configs"
ORIGINAL_CONFIG="config.yaml"
INSTANCE_CONFIG="$CONFIG_DIR/config-$INSTANCE_NAME.yaml"

echo "启动Agent实例: $INSTANCE_NAME"

# 创建配置目录
mkdir -p $CONFIG_DIR

# 复制原始配置并修改Agent ID
if [ -f "$ORIGINAL_CONFIG" ]; then
    cp "$ORIGINAL_CONFIG" "$INSTANCE_CONFIG"
    
    # 使用sed替换Agent ID
    sed -i "s/id: go-agent-code/id: go-agent-$INSTANCE_NAME/" "$INSTANCE_CONFIG"
    
    echo "创建实例配置: $INSTANCE_CONFIG"
    echo "Agent ID: go-agent-$INSTANCE_NAME"
    
    # 启动Agent
    if [ -f "./bounty-agent" ]; then
        ./bounty-agent -config "$INSTANCE_CONFIG"
    else
        echo "错误: 找不到 bounty-agent 可执行文件"
        echo "请先编译: go build -o bounty-agent"
        exit 1
    fi
else
    echo "错误: 找不到原始配置文件 $ORIGINAL_CONFIG"
    exit 1
fi