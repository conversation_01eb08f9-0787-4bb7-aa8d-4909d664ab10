package config

import (
	"fmt"
	"gopkg.in/yaml.v2"
	"io/ioutil"
	"os"
)

// AgentConfig 代理配置结构
type AgentConfig struct {
	Agent struct {
		ID                 string   `yaml:"id"`
		Name               string   `yaml:"name"`
		Version            string   `yaml:"version"`
		MaxConcurrentTasks int      `yaml:"max_concurrent_tasks"`
		Capabilities       []string `yaml:"capabilities"`
		Hostname           string   `yaml:"hostname"`
		IPAddress          string   `yaml:"ip_address"`
	} `yaml:"agent"`

	Server struct {
		BaseURL string `yaml:"base_url"`
		Timeout int    `yaml:"timeout"`
		APIKey  string `yaml:"api_key"`
	} `yaml:"server"`

	Tools struct {
		Subfinder struct {
			Enabled bool     `yaml:"enabled"`
			Config  string   `yaml:"config"`
			Sources []string `yaml:"sources"`
		} `yaml:"subfinder"`

		Naabu struct {
			Enabled bool `yaml:"enabled"`
			Rate    int  `yaml:"rate"`
			Threads int  `yaml:"threads"`
		} `yaml:"naabu"`

		Httpx struct {
			Enabled bool   `yaml:"enabled"`
			Threads int    `yaml:"threads"`
			Timeout string `yaml:"timeout"`
		} `yaml:"httpx"`

		Nuclei struct {
			Enabled       bool     `yaml:"enabled"`
			Templates     []string `yaml:"templates"`
			TemplatesPath string   `yaml:"templates_path"`
			Rate          int      `yaml:"rate"`
		} `yaml:"nuclei"`
	} `yaml:"tools"`

	Log struct {
		Level  string `yaml:"level"`
		Format string `yaml:"format"`
		File   string `yaml:"file"`
	} `yaml:"log"`
}

// LoadConfig 加载配置文件
func LoadConfig(configPath string) (*AgentConfig, error) {
	// 如果配置文件不存在，创建默认配置
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		if err := createDefaultConfig(configPath); err != nil {
			return nil, fmt.Errorf("failed to create default config: %v", err)
		}
	}

	data, err := ioutil.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read config file: %v", err)
	}

	var config AgentConfig
	if err := yaml.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("failed to parse config: %v", err)
	}

	// 设置默认值
	if config.Agent.ID == "" {
		hostname, _ := os.Hostname()
		config.Agent.ID = fmt.Sprintf("go-agent-%s", hostname)
	}
	if config.Agent.Name == "" {
		config.Agent.Name = "Go Recon Agent"
	}
	if config.Agent.Version == "" {
		config.Agent.Version = "1.0.0"
	}
	if config.Agent.MaxConcurrentTasks == 0 {
		config.Agent.MaxConcurrentTasks = 3
	}

	return &config, nil
}

// createDefaultConfig 创建默认配置文件
func createDefaultConfig(configPath string) error {
	hostname, _ := os.Hostname()
	
	defaultConfig := &AgentConfig{}
	defaultConfig.Agent.ID = fmt.Sprintf("go-agent-%s", hostname)
	defaultConfig.Agent.Name = "Go Recon Agent"
	defaultConfig.Agent.Version = "1.0.0"
	defaultConfig.Agent.MaxConcurrentTasks = 3
	defaultConfig.Agent.Capabilities = []string{
		"subdomain_discovery",
		"port_scanning", 
		"service_detection",
		"dns_resolution",
		"vulnerability_testing",
	}
	defaultConfig.Agent.Hostname = hostname

	defaultConfig.Server.BaseURL = "http://localhost:8000"
	defaultConfig.Server.Timeout = 30

	defaultConfig.Tools.Subfinder.Enabled = true
	defaultConfig.Tools.Subfinder.Sources = []string{"all"}
	defaultConfig.Tools.Naabu.Enabled = true
	defaultConfig.Tools.Naabu.Rate = 1000
	defaultConfig.Tools.Naabu.Threads = 25
	defaultConfig.Tools.Httpx.Enabled = true
	defaultConfig.Tools.Httpx.Threads = 50
	defaultConfig.Tools.Httpx.Timeout = "10s"
	defaultConfig.Tools.Nuclei.Enabled = true
	defaultConfig.Tools.Nuclei.Rate = 150

	defaultConfig.Log.Level = "info"
	defaultConfig.Log.Format = "text"

	data, err := yaml.Marshal(defaultConfig)
	if err != nil {
		return err
	}

	return ioutil.WriteFile(configPath, data, 0644)
}