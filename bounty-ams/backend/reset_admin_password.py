#!/usr/bin/env python3
"""
Reset admin user password
"""
import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import get_db
from models_dynamic import User
from sqlalchemy import select
from auth import get_password_hash

async def reset_admin_password():
    """Reset admin user password"""
    print("Resetting admin password...")
    
    async for db in get_db():
        try:
            # Check if admin user exists
            result = await db.execute(
                select(User).where(User.username == "admin")
            )
            user = result.scalar_one_or_none()
            
            if user:
                # Update password using the same hash method as auth
                new_password = "password"
                hashed_password = get_password_hash(new_password)
                
                user.hashed_password = hashed_password
                await db.commit()
                
                print(f"✓ Admin password reset successfully")
                print(f"  Username: admin")
                print(f"  Password: password")
                
            else:
                print("✗ Admin user not found")
                
        except Exception as e:
            print(f"✗ Error: {e}")
            import traceback
            traceback.print_exc()
        finally:
            await db.close()

if __name__ == "__main__":
    asyncio.run(reset_admin_password())