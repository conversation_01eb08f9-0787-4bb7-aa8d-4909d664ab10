#!/usr/bin/env python3
"""
Generate API key for agent testing
"""
import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from agent_key_service import AgentKeyService
from schemas_agent_key import Agent<PERSON><PERSON><PERSON><PERSON>
from models_dynamic import User
from database import get_db
from sqlalchemy import select

async def generate_test_agent_key():
    """Generate API key for testing"""
    
    async for db in get_db():
        try:
            # 查找admin用户
            result = await db.execute(
                select(User).where(User.username == "admin")
            )
            admin_user = result.scalar_one_or_none()
            
            if not admin_user:
                print("❌ Admin user not found")
                return
            
            # 创建密钥
            key_data = AgentKeyCreate(
                name="Test Agent Key",
                description="API key for testing agent authentication",
                agent_id="go-agent-code"
            )
            
            agent_key = await AgentKeyService.create_agent_key(
                db=db,
                key_data=key_data,
                created_by_user_id=admin_user.id
            )
            
            print("✅ Agent API key generated successfully!")
            print("=" * 60)
            print(f"Key ID: {agent_key.key_id}")
            print(f"API Key: {agent_key.key_value}")
            print(f"Agent ID: {agent_key.agent_id}")
            print(f"Name: {agent_key.name}")
            print("=" * 60)
            print("⚠️  Please save this API key securely!")
            print("   This is the only time you'll see the full key value.")
            print()
            print("💡 To use this key with your agent:")
            print(f"   export BOUNTY_API_KEY='{agent_key.key_value}'")
            
            break
        except Exception as e:
            print(f"❌ Failed to generate API key: {e}")
            await db.rollback()
            raise

if __name__ == "__main__":
    asyncio.run(generate_test_agent_key())