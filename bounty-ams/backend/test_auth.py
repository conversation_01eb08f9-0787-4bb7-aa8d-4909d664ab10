#!/usr/bin/env python3
"""
Test authentication endpoints
"""
import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import requests
import json

async def test_auth():
    """Test authentication flow"""
    BASE_URL = "http://localhost:8000"
    
    print("Testing authentication...")
    
    # Test login
    login_data = {
        "username": "admin",
        "password": "password"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/auth/login", json=login_data)
        print(f"Login response status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✓ Login successful")
            print(f"Access token: {data['access_token'][:50]}...")
            
            # Test authenticated request
            headers = {"Authorization": f"Bearer {data['access_token']}"}
            
            # Test user info
            user_response = requests.get(f"{BASE_URL}/api/auth/me", headers=headers)
            print(f"User info response status: {user_response.status_code}")
            if user_response.status_code == 200:
                user_data = user_response.json()
                print(f"✓ User: {user_data['username']}, Admin: {user_data['is_admin']}")
            
            # Test asset stats
            stats_response = requests.get(f"{BASE_URL}/api/discovered-assets/stats", headers=headers)
            print(f"Asset stats response status: {stats_response.status_code}")
            if stats_response.status_code == 200:
                stats_data = stats_response.json()
                print(f"✓ Asset stats: {stats_data.get('total_assets', 0)} assets found")
            
        else:
            print(f"✗ Login failed: {response.text}")
            
    except Exception as e:
        print(f"✗ Error: {e}")

if __name__ == "__main__":
    asyncio.run(test_auth())