"""
Database Migration Script to Fix Agent Metadata Column

Rename metadata column to agent_metadata in agents table
"""
import asyncio
from sqlalchemy import text
from database import engine

async def fix_agent_metadata_column():
    """修复Agent表的metadata列名"""
    
    # SQL命令重命名列
    rename_column_sql = """
    ALTER TABLE agents RENAME COLUMN metadata TO agent_metadata;
    """
    
    try:
        async with engine.begin() as conn:
            print("🔄 修复Agent表metadata列名...")
            
            # 先检查列是否存在
            check_column_sql = """
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'agents' 
            AND column_name IN ('metadata', 'agent_metadata');
            """
            
            result = await conn.execute(text(check_column_sql))
            columns = [row[0] for row in result.fetchall()]
            print(f"现有列: {columns}")
            
            if 'metadata' in columns and 'agent_metadata' not in columns:
                print("重命名 metadata -> agent_metadata")
                await conn.execute(text(rename_column_sql))
                print("✅ 列重命名成功")
            elif 'agent_metadata' in columns:
                print("✅ agent_metadata列已存在")
            else:
                print("❌ 找不到metadata列")
                
    except Exception as e:
        print(f"❌ 列重命名失败: {e}")
        raise

async def main():
    """主函数"""
    print("🚀 开始修复Agent表metadata列...")
    await fix_agent_metadata_column()
    print("🎉 修复完成!")

if __name__ == "__main__":
    asyncio.run(main())