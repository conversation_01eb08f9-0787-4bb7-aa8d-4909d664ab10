/usr/local/lib/python3.10/dist-packages/pydantic/_internal/_fields.py:149: UserWarning: Field "model_type_id" has conflict with protected namespace "model_".

You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.
  warnings.warn(
/home/<USER>/code/code/bounty-ams/backend/main.py:26: DeprecationWarning: 
        on_event is deprecated, use lifespan event handlers instead.

        Read more about it in the
        [FastAPI docs for Lifespan Events](https://fastapi.tiangolo.com/advanced/events/).
        
  @app.on_event("startup")
/home/<USER>/code/code/bounty-ams/backend/main.py:35: DeprecationWarning: 
        on_event is deprecated, use lifespan event handlers instead.

        Read more about it in the
        [FastAPI docs for Lifespan Events](https://fastapi.tiangolo.com/advanced/events/).
        
  @app.on_event("shutdown")
INFO:     Started server process [1236389]
INFO:     Waiting for application startup.
INFO:elastic_transport.transport:GET http://localhost:9200/ [status:200 duration:0.055s]
INFO:elasticsearch_client:Connected to Elasticsearch: 8.11.0
INFO:elastic_transport.transport:HEAD http://localhost:9200/assets [status:200 duration:0.003s]
INFO:__main__:Elasticsearch connected successfully
INFO:     Application startup complete.
ERROR:    [Errno 98] error while attempting to bind on address ('0.0.0.0', 8000): address already in use
INFO:     Waiting for application shutdown.
INFO:__main__:Elasticsearch disconnected
INFO:     Application shutdown complete.
