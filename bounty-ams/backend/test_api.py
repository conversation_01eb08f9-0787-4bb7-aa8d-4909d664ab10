#!/usr/bin/env python3
"""
测试增强搜索API
"""

import asyncio
import json
import aiohttp
from datetime import datetime

async def test_enhanced_search_api():
    """测试增强搜索API"""
    base_url = "http://localhost:8000"
    
    # 模拟登录获取token（如果需要）
    # 这里假设有默认的测试token或者API不需要认证
    headers = {
        "Content-Type": "application/json",
        # "Authorization": "Bearer your_token_here"
    }
    
    async with aiohttp.ClientSession() as session:
        try:
            print("测试增强搜索API...")
            
            # 测试基本搜索
            search_data = {
                "query": "",
                "size": 10,
                "include_aggregations": True
            }
            
            async with session.post(
                f"{base_url}/api/enhanced-search/assets", 
                headers=headers, 
                json=search_data
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✓ 基本搜索成功: 找到 {data.get('total', 0)} 个资产")
                    print(f"  - 聚合数据: {list(data.get('aggregations', {}).keys())}")
                else:
                    print(f"✗ 基本搜索失败: {response.status}")
                    error_text = await response.text()
                    print(f"  错误信息: {error_text}")
            
            # 测试按项目搜索
            print("\n测试按项目搜索...")
            # 这里需要实际的项目ID，我们使用一个假的来测试错误处理
            project_search_data = {
                "project_id": "test-project-id",
                "size": 5,
                "include_aggregations": True
            }
            
            async with session.post(
                f"{base_url}/api/enhanced-search/assets", 
                headers=headers, 
                json=project_search_data
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✓ 项目搜索成功: 找到 {data.get('total', 0)} 个资产")
                else:
                    print(f"✗ 项目搜索失败: {response.status}")
                    error_text = await response.text()
                    print(f"  错误信息: {error_text}")
            
            # 测试健康检查
            print("\n测试健康检查...")
            async with session.get(f"{base_url}/api/enhanced-search/health") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✓ 健康检查成功: {data.get('status', 'unknown')}")
                    print(f"  - Elasticsearch状态: {data.get('elasticsearch', {}).get('cluster_status', 'unknown')}")
                else:
                    print(f"✗ 健康检查失败: {response.status}")
                    error_text = await response.text()
                    print(f"  错误信息: {error_text}")
            
        except Exception as e:
            print(f"测试时发生错误: {e}")

if __name__ == "__main__":
    asyncio.run(test_enhanced_search_api())