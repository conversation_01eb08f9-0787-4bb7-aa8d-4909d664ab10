#!/usr/bin/env python3
"""
Bounty AMS API Test Suite
Test vulnerability knowledge base management API
"""

import asyncio
import aiohttp
import json
from typing import Dict, Any, Optional
import sys

class APITester:
    def __init__(self, base_url: str = "http://localhost:8090"):
        self.base_url = base_url
        self.session = None
        self.access_token = None
        self.headers = {}
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def login(self, username: str = "admin", password: str = "admin123"):
        """Login and get access token"""
        login_data = {
            "username": username,
            "password": password
        }
        
        async with self.session.post(
            f"{self.base_url}/api/auth/login",
            json=login_data
        ) as response:
            if response.status == 200:
                data = await response.json()
                self.access_token = data["access_token"]
                self.headers = {"Authorization": f"Bearer {self.access_token}"}
                print(f"✓ Login successful")
                return True
            else:
                print(f"❌ Login failed: {response.status}")
                return False
    
    async def test_create_vulnerability(self):
        """Test creating a vulnerability"""
        print("\n🧪 Testing vulnerability creation...")
        
        vuln_data = {
            "name": "Apache Log4j Remote Code Execution",
            "vuln_id": "CVE-2021-44228",
            "description": "Apache Log4j2 JNDI features used in configuration, log messages, and parameters do not protect against attacker controlled LDAP and other JNDI related endpoints.",
            "severity": "Critical",
            "affected_products": ["Apache Log4j", "Various applications using Log4j"],
            "poc_identifier": "log4j-rce-poc"
        }
        
        async with self.session.post(
            f"{self.base_url}/api/vulnerabilities/",
            json=vuln_data,
            headers=self.headers
        ) as response:
            if response.status == 201:
                data = await response.json()
                print(f"✓ Vulnerability created: {data['name']} (ID: {data['id']})")
                return data
            else:
                error_text = await response.text()
                print(f"❌ Failed to create vulnerability: {response.status} - {error_text}")
                return None
    
    async def test_get_vulnerabilities(self):
        """Test getting vulnerability list"""
        print("\n🧪 Testing vulnerability listing...")
        
        async with self.session.get(
            f"{self.base_url}/api/vulnerabilities/",
            headers=self.headers
        ) as response:
            if response.status == 200:
                data = await response.json()
                print(f"✓ Retrieved {len(data)} vulnerabilities")
                return data
            else:
                print(f"❌ Failed to get vulnerabilities: {response.status}")
                return None
    
    async def test_search_vulnerabilities(self):
        """Test vulnerability search"""
        print("\n🧪 Testing vulnerability search...")
        
        # Search by name
        async with self.session.get(
            f"{self.base_url}/api/vulnerabilities/?search=Log4j",
            headers=self.headers
        ) as response:
            if response.status == 200:
                data = await response.json()
                print(f"✓ Search by name returned {len(data)} results")
            else:
                print(f"❌ Search by name failed: {response.status}")
        
        # Search by severity
        async with self.session.get(
            f"{self.base_url}/api/vulnerabilities/?severity=Critical",
            headers=self.headers
        ) as response:
            if response.status == 200:
                data = await response.json()
                print(f"✓ Search by severity returned {len(data)} results")
            else:
                print(f"❌ Search by severity failed: {response.status}")
    
    async def test_get_vulnerability_by_id(self, vuln_id: str):
        """Test getting specific vulnerability"""
        print(f"\n🧪 Testing get vulnerability by ID: {vuln_id}...")
        
        async with self.session.get(
            f"{self.base_url}/api/vulnerabilities/{vuln_id}",
            headers=self.headers
        ) as response:
            if response.status == 200:
                data = await response.json()
                print(f"✓ Retrieved vulnerability: {data['name']}")
                return data
            else:
                print(f"❌ Failed to get vulnerability: {response.status}")
                return None
    
    async def test_update_vulnerability(self, vuln_id: str):
        """Test updating a vulnerability"""
        print(f"\n🧪 Testing vulnerability update: {vuln_id}...")
        
        update_data = {
            "description": "Updated description: Apache Log4j2 JNDI features used in configuration, log messages, and parameters do not protect against attacker controlled LDAP and other JNDI related endpoints. This is a critical vulnerability.",
            "affected_products": ["Apache Log4j 2.x", "Various applications using Log4j 2.x", "Elasticsearch", "Kafka"]
        }
        
        async with self.session.put(
            f"{self.base_url}/api/vulnerabilities/{vuln_id}",
            json=update_data,
            headers=self.headers
        ) as response:
            if response.status == 200:
                data = await response.json()
                print(f"✓ Vulnerability updated: {data['name']}")
                return data
            else:
                error_text = await response.text()
                print(f"❌ Failed to update vulnerability: {response.status} - {error_text}")
                return None
    
    async def test_severity_stats(self):
        """Test severity statistics"""
        print("\n🧪 Testing severity statistics...")
        
        async with self.session.get(
            f"{self.base_url}/api/vulnerabilities/stats/severity",
            headers=self.headers
        ) as response:
            if response.status == 200:
                data = await response.json()
                print(f"✓ Severity stats retrieved:")
                for stat in data:
                    print(f"  - {stat['severity']}: {stat['count']}")
                return data
            else:
                print(f"❌ Failed to get severity stats: {response.status}")
                return None
    
    async def test_get_severities(self):
        """Test getting available severities"""
        print("\n🧪 Testing available severities...")
        
        async with self.session.get(
            f"{self.base_url}/api/vulnerabilities/severities/",
            headers=self.headers
        ) as response:
            if response.status == 200:
                data = await response.json()
                print(f"✓ Available severities: {', '.join(data)}")
                return data
            else:
                print(f"❌ Failed to get severities: {response.status}")
                return None
    
    async def test_delete_vulnerability(self, vuln_id: str):
        """Test deleting a vulnerability"""
        print(f"\n🧪 Testing vulnerability deletion: {vuln_id}...")
        
        async with self.session.delete(
            f"{self.base_url}/api/vulnerabilities/{vuln_id}",
            headers=self.headers
        ) as response:
            if response.status == 204:
                print(f"✓ Vulnerability deleted successfully")
                return True
            else:
                print(f"❌ Failed to delete vulnerability: {response.status}")
                return False
    
    async def run_all_tests(self):
        """Run all vulnerability API tests"""
        print("🚀 Starting Vulnerability Knowledge Base API Tests")
        print("=" * 60)
        
        # Login first
        if not await self.login():
            print("❌ Cannot proceed without login")
            return False
        
        # Test create vulnerability
        vulnerability = await self.test_create_vulnerability()
        if not vulnerability:
            print("❌ Cannot proceed without creating vulnerability")
            return False
        
        vuln_id = vulnerability["id"]
        
        # Test get vulnerabilities
        await self.test_get_vulnerabilities()
        
        # Test search
        await self.test_search_vulnerabilities()
        
        # Test get by ID
        await self.test_get_vulnerability_by_id(vuln_id)
        
        # Test update
        await self.test_update_vulnerability(vuln_id)
        
        # Test stats
        await self.test_severity_stats()
        
        # Test severities
        await self.test_get_severities()
        
        # Test delete (cleanup)
        await self.test_delete_vulnerability(vuln_id)
        
        print("\n" + "=" * 60)
        print("🎉 All vulnerability API tests completed!")
        
        return True

async def main():
    """Main test runner"""
    print("Bounty AMS - Vulnerability Knowledge Base API Test Suite")
    print("Make sure the API server is running on localhost:8090")
    print("And that the database is initialized with admin user")
    
    async with APITester() as tester:
        success = await tester.run_all_tests()
    
    return success

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)