from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from routes.auth import router as auth_router
# from routes.assets import router as assets_router  # Disabled - using dynamic assets
from routes.asset_types import router as asset_types_router  # Re-enabled for compatibility
# from routes.dynamic_assets import router as dynamic_assets_router  # Now using dynamic models
from routes.vulnerabilities import router as vulnerabilities_router  # Re-enabled for compatibility
from routes.dynamic_models import router as dynamic_models_router
from routes.agents import router as agents_router
from routes.tasks import router as tasks_router
from routes.workflows import router as workflows_router
from routes.discovered_assets import router as discovered_assets_router
from routes.enhanced_search import router as enhanced_search_router
from routes.dashboard import router as dashboard_router
from routes.agent_keys import router as agent_keys_router
from routes.data_import import router as data_import_router
from routes.assets_v2 import router as assets_v2_router
from config import settings
from elasticsearch_client import es_client
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="Bounty AMS API", version="1.0.0")

@app.on_event("startup")
async def startup_event():
    """Initialize connections on startup"""
    try:
        await es_client.connect()
        logger.info("Elasticsearch connected successfully")
    except Exception as e:
        logger.error(f"Failed to connect to Elasticsearch: {e}")

@app.on_event("shutdown")
async def shutdown_event():
    """Clean up connections on shutdown"""
    await es_client.disconnect()
    logger.info("Elasticsearch disconnected")

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(auth_router, prefix="/api/auth", tags=["auth"])
app.include_router(dynamic_models_router, prefix="/api", tags=["dynamic-models"])
app.include_router(asset_types_router, prefix="/api", tags=["asset-types"])
app.include_router(vulnerabilities_router, prefix="/api", tags=["vulnerabilities"])
app.include_router(agents_router, prefix="/api/agents", tags=["agents"])
app.include_router(tasks_router, prefix="/api/tasks", tags=["tasks"])
app.include_router(workflows_router, prefix="/api/workflows", tags=["workflows"])
app.include_router(discovered_assets_router, prefix="/api/discovered-assets", tags=["discovered-assets"])
app.include_router(enhanced_search_router, prefix="/api", tags=["enhanced-search"])
app.include_router(dashboard_router, prefix="/api", tags=["dashboard"])
app.include_router(agent_keys_router, prefix="/api", tags=["agent-keys"])
app.include_router(data_import_router, prefix="/api", tags=["data-import"])
app.include_router(assets_v2_router, prefix="/api", tags=["assets-v2"])

@app.get("/")
def root():
    return {"message": "Bounty AMS API is running"}

@app.get("/health")
def health_check():
    return {"status": "healthy"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)