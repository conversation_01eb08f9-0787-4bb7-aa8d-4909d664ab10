#!/usr/bin/env python3
"""
清理重复数据并创建完整的漏洞赏金平台和项目数据
"""

import asyncio
import json
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy import select, delete
from models_dynamic import ModelType, DynamicEntity
from config import settings

# 创建异步数据库引擎
engine = create_async_engine(settings.DATABASE_URL)
async_session = async_sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)

async def create_comprehensive_bounty_data():
    """创建完整的漏洞赏金平台和项目数据"""
    async with async_session() as session:
        try:
            # 获取模型类型
            platform_model_result = await session.execute(
                select(ModelType).where(ModelType.name == 'platform')
            )
            platform_model = platform_model_result.scalar_one_or_none()
            
            project_model_result = await session.execute(
                select(ModelType).where(ModelType.name == 'project')
            )
            project_model = project_model_result.scalar_one_or_none()
            
            if not platform_model or not project_model:
                print("错误：找不到平台或项目模型类型")
                return
            
            # 清理现有数据
            print("清理现有重复数据...")
            await session.execute(delete(DynamicEntity).where(DynamicEntity.model_type_id == platform_model.id))
            await session.execute(delete(DynamicEntity).where(DynamicEntity.model_type_id == project_model.id))
            await session.commit()
            
            # 创建完整的平台数据
            platforms_data = [
                # 主要漏洞赏金平台
                {
                    "name": "hackerone",
                    "display_name": "HackerOne",
                    "platform_type": "bug_bounty",
                    "website_url": "https://hackerone.com",
                    "api_base_url": "https://api.hackerone.com",
                    "description": "全球最大的漏洞赏金平台，拥有众多知名企业项目",
                    "status": "active",
                    "supported_asset_types": ["domain", "subdomain", "url", "api_endpoint", "mobile_app"],
                    "founded_year": 2012,
                    "headquarters": "San Francisco, CA"
                },
                {
                    "name": "bugcrowd",
                    "display_name": "Bugcrowd",
                    "platform_type": "bug_bounty",
                    "website_url": "https://bugcrowd.com",
                    "api_base_url": "https://api.bugcrowd.com",
                    "description": "知名的众包安全平台，提供漏洞赏金和渗透测试服务",
                    "status": "active",
                    "supported_asset_types": ["domain", "subdomain", "url", "api_endpoint", "mobile_app"],
                    "founded_year": 2012,
                    "headquarters": "San Francisco, CA"
                },
                {
                    "name": "intigriti",
                    "display_name": "Intigriti",
                    "platform_type": "bug_bounty",
                    "website_url": "https://intigriti.com",
                    "api_base_url": "https://api.intigriti.com",
                    "description": "欧洲领先的漏洞赏金平台，专注于企业安全",
                    "status": "active",
                    "supported_asset_types": ["domain", "subdomain", "url", "api_endpoint"],
                    "founded_year": 2016,
                    "headquarters": "Brussels, Belgium"
                },
                {
                    "name": "synack",
                    "display_name": "Synack",
                    "platform_type": "bug_bounty",
                    "website_url": "https://synack.com",
                    "description": "邀请制精英漏洞赏金平台，结合众包和AI技术",
                    "status": "active",
                    "supported_asset_types": ["domain", "subdomain", "url", "api_endpoint"],
                    "founded_year": 2013,
                    "headquarters": "Redwood City, CA"
                },
                {
                    "name": "yeswehack",
                    "display_name": "YesWeHack",
                    "platform_type": "bug_bounty",
                    "website_url": "https://yeswehack.com",
                    "description": "欧洲本土的漏洞赏金平台，专注于法国和欧洲市场",
                    "status": "active",
                    "supported_asset_types": ["domain", "subdomain", "url", "api_endpoint"],
                    "founded_year": 2013,
                    "headquarters": "Paris, France"
                },
                
                # 大公司自有VDP平台
                {
                    "name": "microsoft_msrc",
                    "display_name": "Microsoft MSRC",
                    "platform_type": "vendor_vdp",
                    "website_url": "https://msrc.microsoft.com",
                    "description": "微软安全响应中心，全球最大的软件厂商安全项目",
                    "status": "active",
                    "supported_asset_types": ["domain", "subdomain", "url", "api_endpoint", "software"],
                    "max_bounty": "$250,000",
                    "company": "Microsoft"
                },
                {
                    "name": "google_vrp",
                    "display_name": "Google VRP",
                    "platform_type": "vendor_vdp",
                    "website_url": "https://bughunters.google.com",
                    "description": "Google漏洞奖励项目，涵盖Google所有产品和服务",
                    "status": "active",
                    "supported_asset_types": ["domain", "subdomain", "url", "api_endpoint", "mobile_app"],
                    "max_bounty": "$100,000+",
                    "company": "Google"
                },
                {
                    "name": "meta_bounty",
                    "display_name": "Meta Bug Bounty",
                    "platform_type": "vendor_vdp",
                    "website_url": "https://bugbounty.meta.com",
                    "description": "Meta(Facebook)漏洞赏金项目，高额奖励知名互联网平台",
                    "status": "active",
                    "supported_asset_types": ["domain", "subdomain", "url", "api_endpoint", "mobile_app"],
                    "max_bounty": "$40,000",
                    "company": "Meta"
                },
                {
                    "name": "apple_security",
                    "display_name": "Apple Security Research",
                    "platform_type": "vendor_vdp",
                    "website_url": "https://security.apple.com",
                    "description": "苹果安全研究项目，专注于苹果生态系统安全",
                    "status": "active",
                    "supported_asset_types": ["domain", "subdomain", "url", "api_endpoint", "mobile_app", "software"],
                    "max_bounty": "$1,000,000",
                    "company": "Apple"
                },
                {
                    "name": "tesla_security",
                    "display_name": "Tesla Security",
                    "platform_type": "vendor_vdp",
                    "website_url": "https://bugcrowd.com/tesla",
                    "description": "特斯拉安全项目，专注于汽车和能源产品安全",
                    "status": "active",
                    "supported_asset_types": ["domain", "subdomain", "url", "api_endpoint", "iot"],
                    "max_bounty": "$15,000",
                    "company": "Tesla"
                },
                {
                    "name": "netflix_security",
                    "display_name": "Netflix Security",
                    "platform_type": "vendor_vdp",
                    "website_url": "https://bugcrowd.com/netflix",
                    "description": "Netflix安全项目，专注于流媒体平台安全",
                    "status": "active",
                    "supported_asset_types": ["domain", "subdomain", "url", "api_endpoint", "mobile_app"],
                    "max_bounty": "$15,000",
                    "company": "Netflix"
                },
                {
                    "name": "paypal_security",
                    "display_name": "PayPal Security",
                    "platform_type": "vendor_vdp",
                    "website_url": "https://bugcrowd.com/paypal",
                    "description": "PayPal安全项目，专注于金融科技平台安全",
                    "status": "active",
                    "supported_asset_types": ["domain", "subdomain", "url", "api_endpoint", "mobile_app"],
                    "max_bounty": "$30,000",
                    "company": "PayPal"
                }
            ]
            
            print("创建平台数据...")
            created_platforms = []
            for platform_data in platforms_data:
                platform = DynamicEntity(
                    model_type_id=platform_model.id,
                    entity_data=platform_data
                )
                session.add(platform)
                created_platforms.append(platform)
            
            await session.commit()
            
            # 刷新对象以获取ID
            for platform in created_platforms:
                await session.refresh(platform)
            
            print(f"创建了 {len(created_platforms)} 个平台")
            
            # 创建项目数据
            projects_data = []
            
            # HackerOne项目
            hackerone_platform = next(p for p in created_platforms if p.entity_data['name'] == 'hackerone')
            projects_data.extend([
                {
                    "name": "Shopify Bug Bounty",
                    "platform_id": str(hackerone_platform.id),
                    "external_id": "shopify",
                    "company_name": "Shopify",
                    "program_type": "public",
                    "status": "active",
                    "reward_range": "$500 - $25,000",
                    "description": "Shopify电商平台漏洞赏金项目",
                    "tags": ["e-commerce", "web", "api"],
                    "priority": "high",
                    "scope": {"in_scope": ["*.shopify.com", "shopify.com"], "out_of_scope": ["*.shopifycloud.com"]}
                },
                {
                    "name": "Uber Bug Bounty",
                    "platform_id": str(hackerone_platform.id),
                    "external_id": "uber",
                    "company_name": "Uber",
                    "program_type": "public",
                    "status": "active",
                    "reward_range": "$500 - $10,000",
                    "description": "Uber出行平台漏洞赏金项目",
                    "tags": ["transportation", "mobile", "api"],
                    "priority": "high",
                    "scope": {"in_scope": ["*.uber.com", "uber.com"], "out_of_scope": ["*.uberinternal.com"]}
                },
                {
                    "name": "GitLab Bug Bounty",
                    "platform_id": str(hackerone_platform.id),
                    "external_id": "gitlab",
                    "company_name": "GitLab",
                    "program_type": "public",
                    "status": "active",
                    "reward_range": "$100 - $12,000",
                    "description": "GitLab代码管理平台漏洞赏金项目",
                    "tags": ["devops", "git", "ci/cd"],
                    "priority": "high",
                    "scope": {"in_scope": ["*.gitlab.com", "gitlab.com"], "out_of_scope": ["*.gitlab.io"]}
                },
                {
                    "name": "Coinbase Bug Bounty",
                    "platform_id": str(hackerone_platform.id),
                    "external_id": "coinbase",
                    "company_name": "Coinbase",
                    "program_type": "public",
                    "status": "active",
                    "reward_range": "$200 - $50,000",
                    "description": "Coinbase加密货币交易平台漏洞赏金项目",
                    "tags": ["crypto", "fintech", "trading"],
                    "priority": "high",
                    "scope": {"in_scope": ["*.coinbase.com", "coinbase.com"], "out_of_scope": ["*.coinbasecloud.net"]}
                }
            ])
            
            # Bugcrowd项目
            bugcrowd_platform = next(p for p in created_platforms if p.entity_data['name'] == 'bugcrowd')
            projects_data.extend([
                {
                    "name": "Tesla Bug Bounty",
                    "platform_id": str(bugcrowd_platform.id),
                    "external_id": "tesla",
                    "company_name": "Tesla",
                    "program_type": "public",
                    "status": "active",
                    "reward_range": "$1,000 - $15,000",
                    "description": "特斯拉汽车和能源产品漏洞赏金项目",
                    "tags": ["automotive", "iot", "energy"],
                    "priority": "high",
                    "scope": {"in_scope": ["*.tesla.com", "tesla.com"], "out_of_scope": ["*.teslamotors.com"]}
                },
                {
                    "name": "Mozilla Bug Bounty",
                    "platform_id": str(bugcrowd_platform.id),
                    "external_id": "mozilla",
                    "company_name": "Mozilla",
                    "program_type": "public",
                    "status": "active",
                    "reward_range": "$500 - $10,000",
                    "description": "Mozilla浏览器和产品漏洞赏金项目",
                    "tags": ["browser", "security", "privacy"],
                    "priority": "high",
                    "scope": {"in_scope": ["*.mozilla.org", "mozilla.org"], "out_of_scope": ["*.mozilla.com"]}
                },
                {
                    "name": "Atlassian Bug Bounty",
                    "platform_id": str(bugcrowd_platform.id),
                    "external_id": "atlassian",
                    "company_name": "Atlassian",
                    "program_type": "public",
                    "status": "active",
                    "reward_range": "$300 - $7,500",
                    "description": "Atlassian协作工具平台漏洞赏金项目",
                    "tags": ["collaboration", "devtools", "enterprise"],
                    "priority": "medium",
                    "scope": {"in_scope": ["*.atlassian.com", "atlassian.com"], "out_of_scope": ["*.atlassian.net"]}
                }
            ])
            
            # Microsoft MSRC项目
            microsoft_platform = next(p for p in created_platforms if p.entity_data['name'] == 'microsoft_msrc')
            projects_data.extend([
                {
                    "name": "Microsoft Edge",
                    "platform_id": str(microsoft_platform.id),
                    "external_id": "edge",
                    "company_name": "Microsoft",
                    "program_type": "vdp",
                    "status": "active",
                    "reward_range": "$500 - $30,000",
                    "description": "Microsoft Edge浏览器漏洞奖励项目",
                    "tags": ["browser", "security", "web"],
                    "priority": "high",
                    "scope": {"in_scope": ["*.microsoftedge.com", "edge.microsoft.com"], "out_of_scope": ["*.edge-internal.com"]}
                },
                {
                    "name": "Microsoft Office 365",
                    "platform_id": str(microsoft_platform.id),
                    "external_id": "office365",
                    "company_name": "Microsoft",
                    "program_type": "vdp",
                    "status": "active",
                    "reward_range": "$500 - $25,000",
                    "description": "Microsoft Office 365云办公套件漏洞奖励项目",
                    "tags": ["cloud", "productivity", "enterprise"],
                    "priority": "high",
                    "scope": {"in_scope": ["*.office.com", "office.microsoft.com"], "out_of_scope": ["*.office365.com"]}
                },
                {
                    "name": "Azure Cloud Security",
                    "platform_id": str(microsoft_platform.id),
                    "external_id": "azure",
                    "company_name": "Microsoft",
                    "program_type": "vdp",
                    "status": "active",
                    "reward_range": "$1,000 - $40,000",
                    "description": "Microsoft Azure云平台漏洞奖励项目",
                    "tags": ["cloud", "infrastructure", "enterprise"],
                    "priority": "high",
                    "scope": {"in_scope": ["*.azure.com", "portal.azure.com"], "out_of_scope": ["*.azure.net"]}
                }
            ])
            
            # Google VRP项目
            google_platform = next(p for p in created_platforms if p.entity_data['name'] == 'google_vrp')
            projects_data.extend([
                {
                    "name": "Google Search",
                    "platform_id": str(google_platform.id),
                    "external_id": "google_search",
                    "company_name": "Google",
                    "program_type": "vdp",
                    "status": "active",
                    "reward_range": "$100 - $31,337",
                    "description": "Google搜索引擎漏洞奖励项目",
                    "tags": ["search", "ai", "web"],
                    "priority": "high",
                    "scope": {"in_scope": ["*.google.com", "google.com"], "out_of_scope": ["*.googleplex.com"]}
                },
                {
                    "name": "Google Chrome",
                    "platform_id": str(google_platform.id),
                    "external_id": "chrome",
                    "company_name": "Google",
                    "program_type": "vdp",
                    "status": "active",
                    "reward_range": "$500 - $30,000",
                    "description": "Google Chrome浏览器漏洞奖励项目",
                    "tags": ["browser", "security", "web"],
                    "priority": "high",
                    "scope": {"in_scope": ["Chrome Browser", "Chrome Extensions"], "out_of_scope": ["Third-party extensions"]}
                },
                {
                    "name": "Android Security",
                    "platform_id": str(google_platform.id),
                    "external_id": "android",
                    "company_name": "Google",
                    "program_type": "vdp",
                    "status": "active",
                    "reward_range": "$1,000 - $50,000",
                    "description": "Android操作系统漏洞奖励项目",
                    "tags": ["mobile", "os", "security"],
                    "priority": "high",
                    "scope": {"in_scope": ["Android OS", "Android Apps"], "out_of_scope": ["Third-party apps"]}
                }
            ])
            
            # Meta Bug Bounty项目
            meta_platform = next(p for p in created_platforms if p.entity_data['name'] == 'meta_bounty')
            projects_data.extend([
                {
                    "name": "Facebook Platform",
                    "platform_id": str(meta_platform.id),
                    "external_id": "facebook",
                    "company_name": "Meta",
                    "program_type": "vdp",
                    "status": "active",
                    "reward_range": "$500 - $40,000",
                    "description": "Facebook社交网络平台漏洞奖励项目",
                    "tags": ["social", "web", "mobile"],
                    "priority": "high",
                    "scope": {"in_scope": ["*.facebook.com", "facebook.com"], "out_of_scope": ["*.fbcdn.net"]}
                },
                {
                    "name": "Instagram Security",
                    "platform_id": str(meta_platform.id),
                    "external_id": "instagram",
                    "company_name": "Meta",
                    "program_type": "vdp",
                    "status": "active",
                    "reward_range": "$500 - $40,000",
                    "description": "Instagram图片社交平台漏洞奖励项目",
                    "tags": ["social", "mobile", "media"],
                    "priority": "high",
                    "scope": {"in_scope": ["*.instagram.com", "instagram.com"], "out_of_scope": ["*.cdninstagram.com"]}
                },
                {
                    "name": "WhatsApp Security",
                    "platform_id": str(meta_platform.id),
                    "external_id": "whatsapp",
                    "company_name": "Meta",
                    "program_type": "vdp",
                    "status": "active",
                    "reward_range": "$500 - $40,000",
                    "description": "WhatsApp即时通讯应用漏洞奖励项目",
                    "tags": ["messaging", "mobile", "encryption"],
                    "priority": "high",
                    "scope": {"in_scope": ["*.whatsapp.com", "WhatsApp App"], "out_of_scope": ["Third-party integrations"]}
                }
            ])
            
            # Apple Security项目
            apple_platform = next(p for p in created_platforms if p.entity_data['name'] == 'apple_security')
            projects_data.extend([
                {
                    "name": "iOS Security",
                    "platform_id": str(apple_platform.id),
                    "external_id": "ios",
                    "company_name": "Apple",
                    "program_type": "vdp",
                    "status": "active",
                    "reward_range": "$5,000 - $1,000,000",
                    "description": "iOS操作系统漏洞奖励项目",
                    "tags": ["mobile", "os", "security"],
                    "priority": "high",
                    "scope": {"in_scope": ["iOS System", "iOS Apps"], "out_of_scope": ["Third-party apps"]}
                },
                {
                    "name": "macOS Security",
                    "platform_id": str(apple_platform.id),
                    "external_id": "macos",
                    "company_name": "Apple",
                    "program_type": "vdp",
                    "status": "active",
                    "reward_range": "$5,000 - $100,000",
                    "description": "macOS操作系统漏洞奖励项目",
                    "tags": ["desktop", "os", "security"],
                    "priority": "high",
                    "scope": {"in_scope": ["macOS System", "macOS Apps"], "out_of_scope": ["Third-party apps"]}
                }
            ])
            
            print("创建项目数据...")
            created_projects = []
            for project_data in projects_data:
                project = DynamicEntity(
                    model_type_id=project_model.id,
                    entity_data=project_data
                )
                session.add(project)
                created_projects.append(project)
            
            await session.commit()
            print(f"创建了 {len(created_projects)} 个项目")
            
            # 显示创建的数据统计
            print("\n✅ 创建的平台统计：")
            platform_stats = {}
            for platform in created_platforms:
                platform_type = platform.entity_data.get('platform_type', 'unknown')
                if platform_type not in platform_stats:
                    platform_stats[platform_type] = 0
                platform_stats[platform_type] += 1
                print(f"  - {platform.entity_data['display_name']} ({platform.entity_data['name']})")
            
            print(f"\n平台类型分布：")
            for ptype, count in platform_stats.items():
                print(f"  - {ptype}: {count} 个")
            
            print(f"\n✅ 创建的项目统计：")
            project_stats = {}
            for project in created_projects:
                platform_id = project.entity_data.get('platform_id')
                platform = next((p for p in created_platforms if str(p.id) == platform_id), None)
                platform_name = platform.entity_data.get('display_name', 'Unknown') if platform else 'Unknown'
                
                if platform_name not in project_stats:
                    project_stats[platform_name] = 0
                project_stats[platform_name] += 1
                print(f"  - {project.entity_data['name']} ({platform_name})")
            
            print(f"\n项目按平台分布：")
            for platform_name, count in project_stats.items():
                print(f"  - {platform_name}: {count} 个项目")
            
            print(f"\n🎉 完成！创建了 {len(created_platforms)} 个平台和 {len(created_projects)} 个项目")
            
        except Exception as e:
            print(f"创建数据时发生错误: {e}")
            await session.rollback()

if __name__ == "__main__":
    asyncio.run(create_comprehensive_bounty_data())