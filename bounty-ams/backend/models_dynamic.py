from sqlalchemy import Column, Integer, String, DateTime, <PERSON><PERSON><PERSON>, Text, JSON, ForeignKey
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID
import uuid
from database import Base

# 核心用户模型 - 这个必须固定，因为它是整个系统的基础
class User(Base):
    __tablename__ = "users"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    username = Column(String(255), unique=True, index=True, nullable=False)
    email = Column(String(255), unique=True, index=True, nullable=False)
    hashed_password = Column(String(255), nullable=False)
    is_admin = Column(Boolean, default=False)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    created_model_types = relationship("ModelType", back_populates="created_by")
    created_dynamic_entities = relationship("DynamicEntity", back_populates="created_by")


# 通用动态模型类型定义
class ModelType(Base):
    __tablename__ = "model_types"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    name = Column(String(100), nullable=False, unique=True)  # vulnerability_kb, agent, task, etc.
    display_name = Column(String(255), nullable=False)
    description = Column(Text)
    icon = Column(String(100))
    color = Column(String(20))
    is_active = Column(Boolean, default=True)
    is_system = Column(Boolean, default=False)  # system types cannot be deleted
    created_by_user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    created_by = relationship("User", back_populates="created_model_types")
    fields = relationship("ModelField", back_populates="model_type", cascade="all, delete-orphan")
    entities = relationship("DynamicEntity", back_populates="model_type")


# 通用动态模型字段定义
class ModelField(Base):
    __tablename__ = "model_fields"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    model_type_id = Column(UUID(as_uuid=True), ForeignKey("model_types.id", ondelete="CASCADE"))
    field_name = Column(String(100), nullable=False)
    field_type = Column(String(50), nullable=False)  # text, number, ip, date, select, etc.
    display_name = Column(String(255), nullable=False)
    description = Column(Text)
    is_required = Column(Boolean, default=False)
    is_searchable = Column(Boolean, default=True)
    is_filterable = Column(Boolean, default=True)
    is_unique = Column(Boolean, default=False)
    default_value = Column(Text)
    validation_rules = Column(JSON)  # validation rules as JSON
    field_options = Column(JSON)  # options for select fields
    sort_order = Column(Integer, default=0)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    model_type = relationship("ModelType", back_populates="fields")


# 通用动态实体存储
class DynamicEntity(Base):
    __tablename__ = "dynamic_entities"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    model_type_id = Column(UUID(as_uuid=True), ForeignKey("model_types.id"))
    entity_data = Column(JSON, nullable=False)  # 存储实际的实体数据
    es_index = Column(String(100))  # 对应的Elasticsearch索引
    es_doc_id = Column(String(100))  # Elasticsearch文档ID
    created_by_user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    model_type = relationship("ModelType", back_populates="entities")
    created_by = relationship("User", back_populates="created_dynamic_entities")


# 保留原有的AssetType和AssetTypeField，因为它们已经是动态的
class AssetType(Base):
    __tablename__ = "asset_types"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    name = Column(String(100), nullable=False, unique=True)
    display_name = Column(String(255), nullable=False)
    description = Column(Text)
    icon = Column(String(100))
    color = Column(String(20))
    is_active = Column(Boolean, default=True)
    is_system = Column(Boolean, default=False)
    created_by_user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    created_by = relationship("User", back_populates="created_asset_types")
    fields = relationship("AssetTypeField", back_populates="asset_type", cascade="all, delete-orphan")


class AssetTypeField(Base):
    __tablename__ = "asset_type_fields"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    asset_type_id = Column(UUID(as_uuid=True), ForeignKey("asset_types.id", ondelete="CASCADE"))
    field_name = Column(String(100), nullable=False)
    field_type = Column(String(50), nullable=False)
    display_name = Column(String(255), nullable=False)
    description = Column(Text)
    is_required = Column(Boolean, default=False)
    is_searchable = Column(Boolean, default=True)
    is_filterable = Column(Boolean, default=True)
    default_value = Column(Text)
    validation_rules = Column(JSON)
    field_options = Column(JSON)
    sort_order = Column(Integer, default=0)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    asset_type = relationship("AssetType", back_populates="fields")


# Agent和Task模型 - 这些是系统核心功能，但仍然使用动态模型架构
class Agent(Base):
    __tablename__ = "agents"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    agent_id = Column(String(255), nullable=False, unique=True, index=True)
    name = Column(String(255), nullable=False)
    version = Column(String(50), nullable=False)
    capabilities = Column(JSON, nullable=False)  # List of capabilities
    max_concurrent_tasks = Column(Integer, default=3)
    current_tasks = Column(Integer, default=0)
    status = Column(String(20), default="offline")  # online, offline, busy, error, maintenance
    hostname = Column(String(255))
    ip_address = Column(String(45))
    last_seen_at = Column(DateTime(timezone=True))
    agent_metadata = Column(JSON)  # Changed from 'metadata' to avoid conflict
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    tasks = relationship("Task", back_populates="agent")
    agent_keys = relationship("AgentKey", back_populates="agent")


class AgentKey(Base):
    __tablename__ = "agent_keys"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    key_id = Column(String(100), nullable=False, unique=True, index=True)
    key_hash = Column(String(255), nullable=False)
    agent_id = Column(String(100), ForeignKey("agents.agent_id"), nullable=True, index=True)
    name = Column(String(200), nullable=False)
    description = Column(Text)
    status = Column(String(20), default="active")  # active, suspended, revoked, expired
    expires_at = Column(DateTime(timezone=True))
    created_by_user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    last_used_at = Column(DateTime(timezone=True))
    usage_count = Column(Integer, default=0)
    
    # Relationships
    agent = relationship("Agent", back_populates="agent_keys")
    created_by = relationship("User")


class Task(Base):
    __tablename__ = "tasks"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    task_id = Column(String(255), nullable=False, unique=True, index=True)
    task_type = Column(String(50), nullable=False)
    target = Column(String(500), nullable=False)
    parameters = Column(JSON, nullable=False)
    priority = Column(Integer, default=2)  # 1=low, 2=normal, 3=high, 4=urgent
    status = Column(String(20), default="pending")  # pending, assigned, running, completed, failed, cancelled, skipped
    agent_id = Column(String(255), ForeignKey("agents.agent_id"))
    preferred_agent_id = Column(String(255))  # 首选Agent ID
    workflow_id = Column(String(255))
    timeout = Column(Integer, default=300)
    retry_count = Column(Integer, default=3)
    current_retry = Column(Integer, default=0)
    dependencies = Column(JSON)  # List of task IDs
    result_data = Column(JSON)
    error_message = Column(Text)
    execution_time = Column(Integer)  # seconds
    assets_discovered = Column(JSON)  # List of discovered assets
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    assigned_at = Column(DateTime(timezone=True))
    started_at = Column(DateTime(timezone=True))
    completed_at = Column(DateTime(timezone=True))
    created_by_user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"))
    
    # Relationships
    agent = relationship("Agent", back_populates="tasks")
    created_by = relationship("User", back_populates="created_tasks")


class WorkflowExecution(Base):
    __tablename__ = "workflow_executions"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    execution_id = Column(String(255), nullable=False, unique=True, index=True)
    workflow_id = Column(String(255), nullable=False)
    target = Column(String(500), nullable=False)
    status = Column(String(20), default="pending")  # pending, running, completed, failed, cancelled
    priority = Column(Integer, default=2)
    variables = Column(JSON)
    total_tasks = Column(Integer, default=0)
    completed_tasks = Column(Integer, default=0)
    failed_tasks = Column(Integer, default=0)
    running_tasks = Column(Integer, default=0)
    progress = Column(Integer, default=0)  # 0-100
    started_at = Column(DateTime(timezone=True))
    completed_at = Column(DateTime(timezone=True))
    created_by_user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    created_by = relationship("User", back_populates="created_workflows")


# VulnerabilityKB - 漏洞知识库
class VulnerabilityKB(Base):
    __tablename__ = "vulnerability_kb"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    name = Column(String(255), nullable=False)
    vuln_id = Column(String(100), unique=True)  # CVE-2021-44228
    description = Column(Text)
    severity = Column(String(50))  # Critical, High, Medium, Low
    affected_products = Column(JSON)
    poc_identifier = Column(String(255))
    created_by_user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    created_by = relationship("User", back_populates="created_vulnerabilities")


# 更新User模型的关系
User.created_asset_types = relationship("AssetType", back_populates="created_by")
User.created_tasks = relationship("Task", back_populates="created_by")
User.created_workflows = relationship("WorkflowExecution", back_populates="created_by")
User.created_vulnerabilities = relationship("VulnerabilityKB", back_populates="created_by")