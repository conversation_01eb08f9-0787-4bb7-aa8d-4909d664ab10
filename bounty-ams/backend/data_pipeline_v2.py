"""
资产管理 V2.0 数据管道
统一数据管道：Agent发现 -> 动态模型 -> 手动导入 -> ES统一存储
"""

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, text
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime, timedelta
from enum import Enum
import asyncio
import logging
from dataclasses import dataclass

from models_dynamic import User, ModelType, DynamicEntity
from asset_management_v2_unified import (
    UnifiedAssetManager,
    DataSource,
    AssetStatus,
    ConfidenceLevel,
    AssetMetadata,
    ProcessingResult
)

logger = logging.getLogger(__name__)

class SyncMode(Enum):
    """同步模式"""
    FULL = "full"           # 全量同步
    INCREMENTAL = "incremental"  # 增量同步
    REAL_TIME = "real_time"      # 实时同步

@dataclass
class SyncConfig:
    """同步配置"""
    source_model_type: str
    target_field_mapping: Dict[str, str]
    sync_mode: SyncMode = SyncMode.INCREMENTAL
    batch_size: int = 1000
    sync_interval_minutes: int = 30
    enable_dedup: bool = True
    enable_cleaning: bool = True
    confidence_level: ConfidenceLevel = ConfidenceLevel.MEDIUM
    status: AssetStatus = AssetStatus.ACTIVE

class DataPipelineV2:
    """数据管道 V2.0"""
    
    def __init__(self, asset_manager: UnifiedAssetManager, db_session: AsyncSession):
        self.asset_manager = asset_manager
        self.db_session = db_session
        
        # 预定义的同步配置
        self.sync_configs = {
            "discovered_asset": SyncConfig(
                source_model_type="discovered_asset",
                target_field_mapping={
                    "asset_type": "asset_type",
                    "asset_value": "asset_value", 
                    "asset_host": "asset_host",
                    "port": "asset_port",
                    "service": "asset_service",
                    "tags": "tags",
                    "platform_id": "platform_id",
                    "project_id": "project_id",
                    "discovered_at": "discovered_at",
                    "confidence": "confidence",
                    "status": "status"
                },
                confidence_level=ConfidenceLevel.HIGH,
                status=AssetStatus.VERIFIED
            ),
            "vulnerability": SyncConfig(
                source_model_type="vulnerability",
                target_field_mapping={
                    "target": "asset_value",
                    "target_type": "asset_type",
                    "host": "asset_host",
                    "port": "asset_port",
                    "service": "asset_service",
                    "platform_id": "platform_id",
                    "project_id": "project_id",
                    "discovered_at": "discovered_at"
                },
                confidence_level=ConfidenceLevel.HIGH,
                status=AssetStatus.VERIFIED
            ),
            "subdomain": SyncConfig(
                source_model_type="subdomain",
                target_field_mapping={
                    "subdomain": "asset_value",
                    "domain": "asset_host",
                    "platform_id": "platform_id",
                    "project_id": "project_id",
                    "discovered_at": "discovered_at"
                },
                confidence_level=ConfidenceLevel.MEDIUM,
                status=AssetStatus.ACTIVE
            ),
            "port_scan": SyncConfig(
                source_model_type="port_scan",
                target_field_mapping={
                    "host": "asset_host",
                    "port": "asset_port",
                    "service": "asset_service",
                    "state": "status",
                    "platform_id": "platform_id",
                    "project_id": "project_id",
                    "scanned_at": "discovered_at"
                },
                confidence_level=ConfidenceLevel.HIGH,
                status=AssetStatus.VERIFIED
            )
        }
        
        # 运行状态
        self.is_running = False
        self.last_sync_times = {}
    
    async def start_pipeline(self):
        """启动数据管道"""
        if self.is_running:
            logger.warning("数据管道已在运行中")
            return
        
        self.is_running = True
        logger.info("🚀 启动数据管道 V2.0")
        
        # 启动各个同步任务
        tasks = []
        for model_type, config in self.sync_configs.items():
            if config.sync_mode == SyncMode.REAL_TIME:
                # 实时同步任务
                task = asyncio.create_task(self._real_time_sync_worker(model_type, config))
                tasks.append(task)
            else:
                # 定时同步任务
                task = asyncio.create_task(self._scheduled_sync_worker(model_type, config))
                tasks.append(task)
        
        # 等待所有任务
        try:
            await asyncio.gather(*tasks)
        except Exception as e:
            logger.error(f"数据管道运行异常: {e}")
        finally:
            self.is_running = False
    
    async def stop_pipeline(self):
        """停止数据管道"""
        self.is_running = False
        logger.info("⏹️ 停止数据管道")
    
    async def _scheduled_sync_worker(self, model_type: str, config: SyncConfig):
        """定时同步工作器"""
        while self.is_running:
            try:
                logger.info(f"开始同步 {model_type}")
                result = await self.sync_from_dynamic_model(model_type, config)
                logger.info(f"同步 {model_type} 完成: {result}")
                
                # 等待下次同步
                await asyncio.sleep(config.sync_interval_minutes * 60)
                
            except Exception as e:
                logger.error(f"同步 {model_type} 失败: {e}")
                await asyncio.sleep(60)  # 错误后等待1分钟重试
    
    async def _real_time_sync_worker(self, model_type: str, config: SyncConfig):
        """实时同步工作器（基于数据库变更监听）"""
        # TODO: 实现基于数据库变更通知的实时同步
        # 可以使用PostgreSQL的LISTEN/NOTIFY机制
        while self.is_running:
            await asyncio.sleep(10)  # 暂时使用轮询
    
    async def sync_from_dynamic_model(
        self, 
        model_type_name: str, 
        config: Optional[SyncConfig] = None,
        platform_id: Optional[str] = None,
        project_id: Optional[str] = None,
        since: Optional[datetime] = None
    ) -> ProcessingResult:
        """从动态模型同步数据"""
        
        if config is None:
            config = self.sync_configs.get(model_type_name)
            if not config:
                raise ValueError(f"未找到模型类型 {model_type_name} 的同步配置")
        
        try:
            # 1. 获取模型类型
            model_type = await self._get_model_type(model_type_name)
            if not model_type:
                raise ValueError(f"模型类型 {model_type_name} 不存在")
            
            # 2. 查询动态实体数据
            entities = await self._query_dynamic_entities(
                model_type.id, 
                platform_id, 
                project_id, 
                since or self.last_sync_times.get(model_type_name)
            )
            
            if not entities:
                logger.info(f"没有新的 {model_type_name} 数据需要同步")
                return ProcessingResult()
            
            # 3. 转换为资产格式
            raw_assets = []
            for entity in entities:
                asset_data = self._convert_entity_to_asset(entity, config)
                if asset_data:
                    raw_assets.append(asset_data)
            
            # 4. 构建元数据
            metadata = AssetMetadata(
                source=DataSource.DYNAMIC_MODEL,
                confidence=config.confidence_level,
                status=config.status,
                platform_id=platform_id,
                project_id=project_id,
                tags=[f"source:{model_type_name}"],
                discovered_at=datetime.utcnow()
            )
            
            # 5. 处理资产
            result = await self.asset_manager.process_assets_unified(
                raw_assets=raw_assets,
                source=DataSource.DYNAMIC_MODEL,
                metadata=metadata,
                enable_dedup=config.enable_dedup,
                enable_cleaning=config.enable_cleaning
            )
            
            # 6. 更新同步时间
            self.last_sync_times[model_type_name] = datetime.utcnow()
            
            logger.info(f"✅ 同步 {model_type_name} 完成: 处理 {result.total_processed}, 成功 {result.success_count}")
            return result
            
        except Exception as e:
            logger.error(f"❌ 同步 {model_type_name} 失败: {e}")
            raise
    
    async def _get_model_type(self, model_type_name: str) -> Optional[ModelType]:
        """获取模型类型"""
        query = select(ModelType).where(ModelType.name == model_type_name)
        result = await self.db_session.execute(query)
        return result.scalar_one_or_none()
    
    async def _query_dynamic_entities(
        self,
        model_type_id: str,
        platform_id: Optional[str] = None,
        project_id: Optional[str] = None,
        since: Optional[datetime] = None
    ) -> List[DynamicEntity]:
        """查询动态实体"""
        
        # 构建查询条件
        conditions = [DynamicEntity.model_type_id == model_type_id]
        
        if platform_id:
            conditions.append(
                DynamicEntity.entity_data.op('->>')('platform_id') == platform_id
            )
        
        if project_id:
            conditions.append(
                DynamicEntity.entity_data.op('->>')('project_id') == project_id
            )
        
        if since:
            conditions.append(DynamicEntity.updated_at > since)
        
        # 执行查询
        query = select(DynamicEntity).where(and_(*conditions)).limit(1000)
        result = await self.db_session.execute(query)
        return result.scalars().all()
    
    def _convert_entity_to_asset(
        self, 
        entity: DynamicEntity, 
        config: SyncConfig
    ) -> Optional[Dict[str, Any]]:
        """将动态实体转换为资产格式"""
        try:
            entity_data = entity.entity_data or {}
            asset_data = {}
            
            # 字段映射
            for source_field, target_field in config.target_field_mapping.items():
                value = entity_data.get(source_field)
                if value is not None:
                    asset_data[target_field] = value
            
            # 特殊处理
            if config.source_model_type == "subdomain":
                asset_data["asset_type"] = "subdomain"
            elif config.source_model_type == "port_scan":
                asset_data["asset_type"] = "port"
                asset_data["asset_value"] = f"{asset_data.get('asset_host', '')}:{asset_data.get('asset_port', '')}"
            elif config.source_model_type == "vulnerability":
                # 从漏洞数据中提取资产信息
                if not asset_data.get("asset_type"):
                    asset_data["asset_type"] = "host"  # 默认为主机类型
            
            # 添加元数据
            asset_data["metadata"] = {
                "source_entity_id": str(entity.id),
                "source_model_type": config.source_model_type,
                "original_data": entity_data,
                "sync_time": datetime.utcnow().isoformat()
            }
            
            # 验证必需字段
            if not asset_data.get("asset_type") or not asset_data.get("asset_value"):
                logger.warning(f"实体 {entity.id} 缺少必需字段，跳过同步")
                return None
            
            return asset_data
            
        except Exception as e:
            logger.error(f"转换实体 {entity.id} 失败: {e}")
            return None
    
    async def manual_sync_all(self) -> Dict[str, ProcessingResult]:
        """手动触发全量同步"""
        results = {}
        
        for model_type, config in self.sync_configs.items():
            try:
                logger.info(f"开始手动同步 {model_type}")
                result = await self.sync_from_dynamic_model(model_type, config)
                results[model_type] = result
                logger.info(f"手动同步 {model_type} 完成")
            except Exception as e:
                logger.error(f"手动同步 {model_type} 失败: {e}")
                results[model_type] = ProcessingResult(error_count=1, errors=[str(e)])
        
        return results
    
    async def get_sync_status(self) -> Dict[str, Any]:
        """获取同步状态"""
        return {
            "is_running": self.is_running,
            "last_sync_times": {
                model_type: time.isoformat() if time else None
                for model_type, time in self.last_sync_times.items()
            },
            "configured_models": list(self.sync_configs.keys()),
            "sync_configs": {
                model_type: {
                    "sync_mode": config.sync_mode.value,
                    "batch_size": config.batch_size,
                    "sync_interval_minutes": config.sync_interval_minutes,
                    "confidence_level": config.confidence_level.value,
                    "status": config.status.value
                }
                for model_type, config in self.sync_configs.items()
            }
        }

# 创建数据管道实例的工厂函数
async def create_data_pipeline(
    asset_manager: UnifiedAssetManager,
    db_session: AsyncSession
) -> DataPipelineV2:
    """创建数据管道实例"""
    return DataPipelineV2(asset_manager, db_session)
