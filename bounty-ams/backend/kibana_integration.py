"""
Kibana集成配置
提供深度数据分析和可视化能力，支持自定义图表和报告
"""

from elasticsearch import AsyncElasticsearch
from typing import Dict, List, Any, Optional
from datetime import datetime
import json
import logging

logger = logging.getLogger(__name__)

class KibanaIntegration:
    """Kibana集成管理器"""
    
    def __init__(self, es_client: AsyncElasticsearch, kibana_url: str = "http://localhost:5601"):
        self.es_client = es_client
        self.kibana_url = kibana_url
        self.index_pattern_id = "unified-assets-*"
        self.dashboard_configs = self._get_dashboard_configs()
    
    def _get_dashboard_configs(self) -> Dict[str, Any]:
        """获取仪表板配置"""
        return {
            "asset_overview": {
                "title": "资产概览仪表板",
                "description": "资产管理系统总体概览",
                "visualizations": [
                    "asset_count_metric",
                    "asset_type_pie",
                    "discovery_timeline",
                    "confidence_distribution",
                    "platform_distribution",
                    "data_quality_gauge"
                ]
            },
            "security_analysis": {
                "title": "安全分析仪表板", 
                "description": "安全相关资产分析",
                "visualizations": [
                    "vulnerability_heatmap",
                    "port_distribution",
                    "service_analysis",
                    "risk_assessment",
                    "threat_timeline"
                ]
            },
            "data_quality": {
                "title": "数据质量仪表板",
                "description": "数据质量监控和分析",
                "visualizations": [
                    "quality_score_histogram",
                    "missing_fields_analysis",
                    "duplicate_detection",
                    "source_quality_comparison",
                    "validation_errors"
                ]
            },
            "operational": {
                "title": "运营监控仪表板",
                "description": "系统运营和性能监控",
                "visualizations": [
                    "ingestion_rate",
                    "processing_performance",
                    "error_tracking",
                    "system_health",
                    "usage_statistics"
                ]
            }
        }
    
    async def create_index_pattern(self) -> Dict[str, Any]:
        """创建Kibana索引模式"""
        try:
            index_pattern = {
                "version": "8.0.0",
                "objects": [
                    {
                        "id": "unified-assets-pattern",
                        "type": "index-pattern",
                        "attributes": {
                            "title": "unified-assets-*",
                            "timeFieldName": "discovered_at",
                            "fields": json.dumps([
                                {
                                    "name": "asset_id",
                                    "type": "string",
                                    "searchable": True,
                                    "aggregatable": True
                                },
                                {
                                    "name": "asset_type",
                                    "type": "string", 
                                    "searchable": True,
                                    "aggregatable": True
                                },
                                {
                                    "name": "asset_value",
                                    "type": "string",
                                    "searchable": True,
                                    "aggregatable": False
                                },
                                {
                                    "name": "asset_host",
                                    "type": "string",
                                    "searchable": True,
                                    "aggregatable": True
                                },
                                {
                                    "name": "asset_port",
                                    "type": "number",
                                    "searchable": True,
                                    "aggregatable": True
                                },
                                {
                                    "name": "confidence",
                                    "type": "string",
                                    "searchable": True,
                                    "aggregatable": True
                                },
                                {
                                    "name": "status",
                                    "type": "string",
                                    "searchable": True,
                                    "aggregatable": True
                                },
                                {
                                    "name": "source",
                                    "type": "string",
                                    "searchable": True,
                                    "aggregatable": True
                                },
                                {
                                    "name": "platform_name",
                                    "type": "string",
                                    "searchable": True,
                                    "aggregatable": True
                                },
                                {
                                    "name": "project_name",
                                    "type": "string",
                                    "searchable": True,
                                    "aggregatable": True
                                },
                                {
                                    "name": "discovered_at",
                                    "type": "date",
                                    "searchable": True,
                                    "aggregatable": True
                                },
                                {
                                    "name": "data_quality_score",
                                    "type": "number",
                                    "searchable": True,
                                    "aggregatable": True
                                },
                                {
                                    "name": "tags",
                                    "type": "string",
                                    "searchable": True,
                                    "aggregatable": True
                                },
                                {
                                    "name": "geo_location",
                                    "type": "geo_point",
                                    "searchable": True,
                                    "aggregatable": True
                                }
                            ])
                        }
                    }
                ]
            }
            
            return {
                "success": True,
                "index_pattern": index_pattern,
                "message": "索引模式配置已生成"
            }
            
        except Exception as e:
            logger.error(f"创建索引模式失败: {e}")
            return {"success": False, "error": str(e)}
    
    def get_visualization_configs(self) -> Dict[str, Any]:
        """获取可视化配置"""
        return {
            "asset_count_metric": {
                "title": "总资产数量",
                "type": "metric",
                "query": {
                    "aggs": {
                        "total_assets": {
                            "value_count": {"field": "asset_id"}
                        }
                    }
                }
            },
            "asset_type_pie": {
                "title": "资产类型分布",
                "type": "pie",
                "query": {
                    "aggs": {
                        "asset_types": {
                            "terms": {
                                "field": "asset_type",
                                "size": 20
                            }
                        }
                    }
                }
            },
            "discovery_timeline": {
                "title": "资产发现时间线",
                "type": "line",
                "query": {
                    "aggs": {
                        "discovery_over_time": {
                            "date_histogram": {
                                "field": "discovered_at",
                                "calendar_interval": "day",
                                "min_doc_count": 1
                            }
                        }
                    }
                }
            },
            "confidence_distribution": {
                "title": "置信度分布",
                "type": "horizontal_bar",
                "query": {
                    "aggs": {
                        "confidence_levels": {
                            "terms": {
                                "field": "confidence",
                                "size": 10
                            }
                        }
                    }
                }
            },
            "platform_distribution": {
                "title": "平台分布",
                "type": "pie",
                "query": {
                    "aggs": {
                        "platforms": {
                            "terms": {
                                "field": "platform_name.keyword",
                                "size": 15
                            }
                        }
                    }
                }
            },
            "data_quality_gauge": {
                "title": "平均数据质量",
                "type": "gauge",
                "query": {
                    "aggs": {
                        "avg_quality": {
                            "avg": {"field": "data_quality_score"}
                        }
                    }
                }
            },
            "vulnerability_heatmap": {
                "title": "漏洞热力图",
                "type": "heatmap",
                "query": {
                    "aggs": {
                        "vulnerability_matrix": {
                            "composite": {
                                "sources": [
                                    {"asset_type": {"terms": {"field": "asset_type"}}},
                                    {"severity": {"terms": {"field": "vulnerabilities.severity"}}}
                                ]
                            }
                        }
                    }
                }
            },
            "port_distribution": {
                "title": "端口分布",
                "type": "histogram",
                "query": {
                    "aggs": {
                        "port_ranges": {
                            "histogram": {
                                "field": "asset_port",
                                "interval": 1000,
                                "min_doc_count": 1
                            }
                        }
                    }
                }
            },
            "quality_score_histogram": {
                "title": "数据质量分数分布",
                "type": "histogram",
                "query": {
                    "aggs": {
                        "quality_distribution": {
                            "histogram": {
                                "field": "data_quality_score",
                                "interval": 0.1,
                                "min_doc_count": 1
                            }
                        }
                    }
                }
            },
            "geographic_distribution": {
                "title": "地理分布",
                "type": "coordinate_map",
                "query": {
                    "aggs": {
                        "geo_grid": {
                            "geohash_grid": {
                                "field": "geo_location",
                                "precision": 3
                            }
                        }
                    }
                }
            }
        }
    
    def get_dashboard_export_config(self, dashboard_name: str) -> Dict[str, Any]:
        """获取仪表板导出配置"""
        if dashboard_name not in self.dashboard_configs:
            return {"error": f"仪表板 {dashboard_name} 不存在"}
        
        config = self.dashboard_configs[dashboard_name]
        visualizations = self.get_visualization_configs()
        
        dashboard_export = {
            "version": "8.0.0",
            "objects": []
        }
        
        # 添加可视化对象
        for viz_id in config["visualizations"]:
            if viz_id in visualizations:
                viz_config = visualizations[viz_id]
                dashboard_export["objects"].append({
                    "id": viz_id,
                    "type": "visualization",
                    "attributes": {
                        "title": viz_config["title"],
                        "visState": json.dumps({
                            "title": viz_config["title"],
                            "type": viz_config["type"],
                            "aggs": viz_config["query"]["aggs"]
                        }),
                        "uiStateJSON": "{}",
                        "description": "",
                        "version": 1,
                        "kibanaSavedObjectMeta": {
                            "searchSourceJSON": json.dumps({
                                "index": "unified-assets-pattern",
                                "query": {"match_all": {}},
                                "filter": []
                            })
                        }
                    }
                })
        
        # 添加仪表板对象
        dashboard_export["objects"].append({
            "id": f"dashboard-{dashboard_name}",
            "type": "dashboard",
            "attributes": {
                "title": config["title"],
                "description": config["description"],
                "panelsJSON": json.dumps([
                    {
                        "id": viz_id,
                        "type": "visualization",
                        "gridData": {
                            "x": (i % 3) * 20,
                            "y": (i // 3) * 15,
                            "w": 20,
                            "h": 15,
                            "i": str(i)
                        }
                    }
                    for i, viz_id in enumerate(config["visualizations"])
                ]),
                "version": 1,
                "timeRestore": False,
                "kibanaSavedObjectMeta": {
                    "searchSourceJSON": json.dumps({
                        "query": {"match_all": {}},
                        "filter": []
                    })
                }
            }
        })
        
        return dashboard_export
    
    async def get_kibana_setup_instructions(self) -> Dict[str, Any]:
        """获取Kibana设置说明"""
        return {
            "setup_steps": [
                {
                    "step": 1,
                    "title": "访问Kibana",
                    "description": f"在浏览器中打开 {self.kibana_url}",
                    "action": "navigate"
                },
                {
                    "step": 2,
                    "title": "创建索引模式",
                    "description": "在 Management > Stack Management > Index Patterns 中创建新的索引模式",
                    "details": {
                        "index_pattern": "unified-assets-*",
                        "time_field": "discovered_at"
                    },
                    "action": "create_index_pattern"
                },
                {
                    "step": 3,
                    "title": "导入仪表板",
                    "description": "在 Management > Stack Management > Saved Objects 中导入仪表板配置",
                    "action": "import_dashboards"
                },
                {
                    "step": 4,
                    "title": "配置数据视图",
                    "description": "确认数据视图正确显示资产数据",
                    "action": "verify_data"
                }
            ],
            "available_dashboards": list(self.dashboard_configs.keys()),
            "kibana_url": self.kibana_url,
            "index_pattern": self.index_pattern_id
        }

# 创建Kibana集成实例
async def create_kibana_integration(es_client: AsyncElasticsearch, kibana_url: str = None) -> KibanaIntegration:
    """创建Kibana集成实例"""
    if kibana_url is None:
        kibana_url = "http://localhost:5601"
    
    return KibanaIntegration(es_client, kibana_url)
