#!/usr/bin/env python3
"""
创建测试资产数据
"""

import asyncio
import json
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy import select
from models_dynamic import ModelType, DynamicEntity
from config import settings

# 创建异步数据库引擎
engine = create_async_engine(settings.DATABASE_URL)
async_session = async_sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)

async def create_test_assets():
    """创建测试资产数据"""
    async with async_session() as session:
        try:
            # 获取enhanced_asset模型类型
            enhanced_asset_model_result = await session.execute(
                select(ModelType).where(ModelType.name == 'enhanced_asset')
            )
            enhanced_asset_model = enhanced_asset_model_result.scalar_one_or_none()
            
            if not enhanced_asset_model:
                print("错误：找不到enhanced_asset模型类型")
                return
            
            # 获取现有的平台和项目
            platform_model_result = await session.execute(
                select(ModelType).where(ModelType.name == 'platform')
            )
            platform_model = platform_model_result.scalar_one_or_none()
            
            project_model_result = await session.execute(
                select(ModelType).where(ModelType.name == 'project')
            )
            project_model = project_model_result.scalar_one_or_none()
            
            if not platform_model or not project_model:
                print("错误：找不到平台或项目模型类型")
                return
            
            # 获取所有平台
            platforms_result = await session.execute(
                select(DynamicEntity).where(DynamicEntity.model_type_id == platform_model.id)
            )
            platforms = platforms_result.scalars().all()
            
            # 获取所有项目
            projects_result = await session.execute(
                select(DynamicEntity).where(DynamicEntity.model_type_id == project_model.id)
            )
            projects = projects_result.scalars().all()
            
            if not platforms or not projects:
                print("错误：找不到平台或项目数据，请先运行 create_sample_data.py")
                return
            
            print(f"找到 {len(platforms)} 个平台，{len(projects)} 个项目")
            
            # 创建测试资产数据
            test_assets = []
            
            # 为每个项目创建一些测试资产
            for project in projects:
                project_data = project.entity_data
                platform_id = project_data.get('platform_id')
                project_name = project_data.get('name', 'Unknown')
                
                # 根据项目类型创建不同的资产
                if 'Shopify' in project_name:
                    assets = [
                        {
                            "platform_id": platform_id,
                            "project_id": str(project.id),
                            "asset_type": "domain",
                            "asset_value": "shopify.com",
                            "asset_host": "shopify.com",
                            "confidence": "high",
                            "status": "active",
                            "discovered_at": datetime.now().isoformat(),
                            "source_task_type": "subdomain_enum",
                            "tags": ["main_domain", "in_scope"],
                            "metadata": {"dns_records": ["A", "AAAA", "MX"]}
                        },
                        {
                            "platform_id": platform_id,
                            "project_id": str(project.id),
                            "asset_type": "subdomain",
                            "asset_value": "api.shopify.com",
                            "asset_host": "api.shopify.com",
                            "confidence": "high",
                            "status": "active",
                            "discovered_at": datetime.now().isoformat(),
                            "source_task_type": "subdomain_enum",
                            "tags": ["api", "in_scope"],
                            "metadata": {"services": ["HTTPS", "API"]}
                        },
                        {
                            "platform_id": platform_id,
                            "project_id": str(project.id),
                            "asset_type": "url",
                            "asset_value": "https://shopify.com/admin",
                            "asset_host": "shopify.com",
                            "confidence": "medium",
                            "status": "active",
                            "discovered_at": datetime.now().isoformat(),
                            "source_task_type": "web_crawler",
                            "tags": ["admin", "sensitive"],
                            "metadata": {"status_code": 200}
                        }
                    ]
                elif 'Uber' in project_name:
                    assets = [
                        {
                            "platform_id": platform_id,
                            "project_id": str(project.id),
                            "asset_type": "domain",
                            "asset_value": "uber.com",
                            "asset_host": "uber.com",
                            "confidence": "high",
                            "status": "active",
                            "discovered_at": datetime.now().isoformat(),
                            "source_task_type": "subdomain_enum",
                            "tags": ["main_domain", "in_scope"],
                            "metadata": {"dns_records": ["A", "AAAA", "MX"]}
                        },
                        {
                            "platform_id": platform_id,
                            "project_id": str(project.id),
                            "asset_type": "subdomain",
                            "asset_value": "riders.uber.com",
                            "asset_host": "riders.uber.com",
                            "confidence": "high",
                            "status": "active",
                            "discovered_at": datetime.now().isoformat(),
                            "source_task_type": "subdomain_enum",
                            "tags": ["mobile", "in_scope"],
                            "metadata": {"services": ["HTTPS", "Mobile_API"]}
                        }
                    ]
                elif 'Microsoft Edge' in project_name:
                    assets = [
                        {
                            "platform_id": platform_id,
                            "project_id": str(project.id),
                            "asset_type": "domain",
                            "asset_value": "microsoftedge.com",
                            "asset_host": "microsoftedge.com",
                            "confidence": "high",
                            "status": "active",
                            "discovered_at": datetime.now().isoformat(),
                            "source_task_type": "subdomain_enum",
                            "tags": ["main_domain", "in_scope"],
                            "metadata": {"dns_records": ["A", "AAAA", "MX"]}
                        },
                        {
                            "platform_id": platform_id,
                            "project_id": str(project.id),
                            "asset_type": "subdomain",
                            "asset_value": "dev.microsoftedge.com",
                            "asset_host": "dev.microsoftedge.com",
                            "confidence": "medium",
                            "status": "active",
                            "discovered_at": datetime.now().isoformat(),
                            "source_task_type": "subdomain_enum",
                            "tags": ["dev", "in_scope"],
                            "metadata": {"services": ["HTTPS", "DEV"]}
                        }
                    ]
                elif 'Microsoft Office' in project_name:
                    assets = [
                        {
                            "platform_id": platform_id,
                            "project_id": str(project.id),
                            "asset_type": "domain",
                            "asset_value": "office.com",
                            "asset_host": "office.com",
                            "confidence": "high",
                            "status": "active",
                            "discovered_at": datetime.now().isoformat(),
                            "source_task_type": "subdomain_enum",
                            "tags": ["main_domain", "in_scope"],
                            "metadata": {"dns_records": ["A", "AAAA", "MX"]}
                        }
                    ]
                elif 'Tesla' in project_name:
                    assets = [
                        {
                            "platform_id": platform_id,
                            "project_id": str(project.id),
                            "asset_type": "domain",
                            "asset_value": "tesla.com",
                            "asset_host": "tesla.com",
                            "confidence": "high",
                            "status": "active",
                            "discovered_at": datetime.now().isoformat(),
                            "source_task_type": "subdomain_enum",
                            "tags": ["main_domain", "in_scope"],
                            "metadata": {"dns_records": ["A", "AAAA", "MX"]}
                        },
                        {
                            "platform_id": platform_id,
                            "project_id": str(project.id),
                            "asset_type": "subdomain",
                            "asset_value": "charging.tesla.com",
                            "asset_host": "charging.tesla.com",
                            "confidence": "high",
                            "status": "active",
                            "discovered_at": datetime.now().isoformat(),
                            "source_task_type": "subdomain_enum",
                            "tags": ["charging", "in_scope"],
                            "metadata": {"services": ["HTTPS", "API"]}
                        }
                    ]
                else:
                    # 默认资产
                    assets = [
                        {
                            "platform_id": platform_id,
                            "project_id": str(project.id),
                            "asset_type": "domain",
                            "asset_value": f"example-{project.id[:8]}.com",
                            "asset_host": f"example-{project.id[:8]}.com",
                            "confidence": "medium",
                            "status": "active",
                            "discovered_at": datetime.now().isoformat(),
                            "source_task_type": "subdomain_enum",
                            "tags": ["test", "in_scope"],
                            "metadata": {"dns_records": ["A"]}
                        }
                    ]
                
                # 创建资产实体
                for asset_data in assets:
                    asset = DynamicEntity(
                        model_type_id=enhanced_asset_model.id,
                        entity_data=asset_data
                    )
                    session.add(asset)
                    test_assets.append(asset)
            
            await session.commit()
            print(f"创建了 {len(test_assets)} 个测试资产")
            
            # 显示按项目分组的资产统计
            print("\n按项目分组的资产统计：")
            for project in projects:
                project_assets = [a for a in test_assets if a.entity_data.get('project_id') == str(project.id)]
                platform = next((p for p in platforms if str(p.id) == project.entity_data.get('platform_id')), None)
                platform_name = platform.entity_data.get('display_name', 'Unknown') if platform else 'Unknown'
                print(f"  - {project.entity_data.get('name')} ({platform_name}): {len(project_assets)} 个资产")
            
            print("\n测试资产数据创建完成！")
            
        except Exception as e:
            print(f"创建测试资产时发生错误: {e}")
            await session.rollback()

if __name__ == "__main__":
    asyncio.run(create_test_assets())