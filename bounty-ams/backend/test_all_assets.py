#!/usr/bin/env python3
"""
测试聚合查询所有资产的功能
"""
import asyncio
import aiohttp
import json

async def test_all_assets_query():
    base_url = "http://localhost:8000"
    
    async with aiohttp.ClientSession() as session:
        # 1. 登录
        print("🔐 登录...")
        login_data = {"username": "admin", "password": "password"}
        
        async with session.post(f"{base_url}/api/auth/login", json=login_data) as resp:
            if resp.status != 200:
                print(f"❌ 登录失败: {resp.status}")
                return False
            
            result = await resp.json()
            token = result["access_token"]
            print("✅ 登录成功")
        
        headers = {"Authorization": f"Bearer {token}"}
        
        # 2. 测试新的聚合查询API
        print("\n📊 测试聚合查询所有资产...")
        async with session.get(f"{base_url}/api/dynamic-models/all-assets", 
                              params={"limit": 10}, headers=headers) as resp:
            if resp.status != 200:
                print(f"❌ 查询失败: {resp.status}")
                error = await resp.text()
                print(f"错误: {error}")
                return False
            
            assets = await resp.json()
            print(f"✅ 查询成功，找到 {len(assets)} 个资产")
            
            # 显示前几个资产的信息
            for i, asset in enumerate(assets[:3]):
                entity_data = asset.get("entity_data", {})
                print(f"  资产 {i+1}: {entity_data.get('name', entity_data.get('asset_value', 'Unknown'))}")
                print(f"    平台ID: {entity_data.get('platform_id', 'None')}")
                print(f"    项目ID: {entity_data.get('project_id', 'None')}")
                print(f"    资产类型: {entity_data.get('asset_type', 'None')}")
        
        # 3. 测试数量查询
        print("\n📈 测试资产数量查询...")
        async with session.get(f"{base_url}/api/dynamic-models/all-assets/count", 
                              headers=headers) as resp:
            if resp.status != 200:
                print(f"❌ 数量查询失败: {resp.status}")
                return False
            
            count_result = await resp.json()
            total_count = count_result.get("count", 0)
            print(f"✅ 总资产数量: {total_count}")
        
        # 4. 测试带平台筛选的查询
        print("\n🔍 测试平台筛选查询...")
        
        # 先获取平台列表
        async with session.get(f"{base_url}/api/dynamic-models/types", headers=headers) as resp:
            model_types = await resp.json()
            platform_model = next((m for m in model_types if m["name"] == "platform"), None)
            
            if platform_model:
                # 获取第一个平台
                async with session.get(f"{base_url}/api/dynamic-models/entities", 
                                      params={"model_type_id": platform_model["id"], "limit": 1}, 
                                      headers=headers) as resp:
                    platforms = await resp.json()
                    if platforms:
                        test_platform_id = str(platforms[0]["id"])
                        print(f"  使用平台ID: {test_platform_id}")
                        
                        # 查询该平台的资产
                        async with session.get(f"{base_url}/api/dynamic-models/all-assets", 
                                              params={"platform_id": test_platform_id, "limit": 5}, 
                                              headers=headers) as resp:
                            if resp.status == 200:
                                platform_assets = await resp.json()
                                print(f"✅ 该平台关联的资产数量: {len(platform_assets)}")
                                
                                # 显示关联的资产
                                for asset in platform_assets:
                                    entity_data = asset.get("entity_data", {})
                                    asset_name = entity_data.get('name', entity_data.get('asset_value', 'Unknown'))
                                    print(f"    - {asset_name}")
                            else:
                                print(f"❌ 平台资产查询失败: {resp.status}")
        
        return True

async def main():
    print("🧪 聚合查询所有资产功能测试")
    print("=" * 50)
    
    try:
        success = await test_all_assets_query()
        if success:
            print("\n🎉 聚合查询功能测试通过！")
        else:
            print("\n❌ 聚合查询功能测试失败！")
        return success
    except Exception as e:
        print(f"\n💥 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)