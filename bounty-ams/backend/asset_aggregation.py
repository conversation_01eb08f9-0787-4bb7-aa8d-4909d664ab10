"""
Asset aggregation service for processing discovered assets from agent tasks
Stores assets in Elasticsearch for powerful search and analytics
"""
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from typing import List, Dict, Any, Optional
from datetime import datetime
import json
import logging
from elasticsearch import AsyncElasticsearch

from models_dynamic import Task
from elasticsearch_client import get_es_client

logger = logging.getLogger(__name__)

class AssetAggregationService:
    """Service for aggregating discovered assets from task results to Elasticsearch"""
    
    def __init__(self, db: AsyncSession, es_client: AsyncElasticsearch):
        self.db = db
        self.es_client = es_client
    
    async def process_task_assets(self, task_id: str) -> int:
        """
        Process assets discovered by a task and index them in Elasticsearch
        
        Args:
            task_id: ID of the completed task
            
        Returns:
            Number of assets processed
        """
        # Get task with assets
        result = await self.db.execute(
            select(Task).where(Task.task_id == task_id)
        )
        task = result.scalar_one_or_none()
        
        if not task or not task.assets_discovered:
            return 0
        
        logger.info(f"Processing assets from task {task_id} to Elasticsearch")
        
        processed_count = 0
        
        for asset_data in task.assets_discovered:
            if await self._index_asset_to_elasticsearch(asset_data, task):
                processed_count += 1
        
        logger.info(f"Indexed {processed_count} assets from task {task_id} to Elasticsearch")
        
        return processed_count
    
    async def _index_asset_to_elasticsearch(self, asset_data: Dict[str, Any], task: Task) -> bool:
        """
        Index a single discovered asset to Elasticsearch
        
        Args:
            asset_data: Asset data from task result
            task: Source task
            
        Returns:
            True if asset was indexed successfully
        """
        try:
            # Extract asset information
            asset_type = asset_data.get("type", "unknown")
            asset_value = asset_data.get("value", "")
            asset_host = asset_data.get("host", "")
            
            if not asset_value:
                return False
            
            # Create standardized asset document for Elasticsearch
            asset_doc = {
                # Asset identification
                "asset_type": asset_type,
                "asset_value": asset_value,
                "asset_host": asset_host,
                "asset_id": f"{asset_type}:{asset_value}:{asset_host}",
                
                # Discovery information
                "source": "agent_discovery",
                "source_task_id": task.task_id,
                "source_task_type": task.task_type,
                "source_agent_id": task.agent_id,
                "discovered_at": datetime.utcnow().isoformat(),
                "target": task.target,
                "workflow_id": task.workflow_id,
                
                # Asset metadata
                "confidence": asset_data.get("confidence", "medium"),
                "tags": self._extract_tags(asset_data, task),
                "metadata": {
                    "discovery_method": task.task_type,
                    "raw_data": asset_data
                },
                
                # Searchable fields
                "searchable_text": f"{asset_type} {asset_value} {asset_host} {task.target}",
                
                # Temporal information
                "timestamp": datetime.utcnow().isoformat(),
                "date": datetime.utcnow().strftime("%Y-%m-%d")
            }
            
            # Generate document ID to handle duplicates
            doc_id = self._generate_asset_doc_id(asset_type, asset_value, asset_host)
            
            # Index to Elasticsearch with upsert behavior
            index_name = f"assets-{datetime.utcnow().strftime('%Y-%m')}"
            
            # Check if asset already exists
            existing_doc = await self._get_existing_asset(index_name, doc_id)
            
            if existing_doc:
                # Update existing document
                await self._update_existing_asset_doc(index_name, doc_id, asset_doc, existing_doc)
                logger.debug(f"Updated existing asset in ES: {asset_type}={asset_value}")
            else:
                # Create new document
                await self._create_new_asset_doc(index_name, doc_id, asset_doc)
                logger.debug(f"Created new asset in ES: {asset_type}={asset_value}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error indexing asset {asset_data} to Elasticsearch: {e}")
            return False
    
    def _generate_asset_doc_id(self, asset_type: str, asset_value: str, asset_host: str) -> str:
        """Generate consistent document ID for asset"""
        import hashlib
        id_string = f"{asset_type}:{asset_value}:{asset_host}"
        return hashlib.md5(id_string.encode()).hexdigest()
    
    async def _get_existing_asset(self, index_name: str, doc_id: str) -> Optional[Dict[str, Any]]:
        """Get existing asset document from Elasticsearch"""
        try:
            response = await self.es_client.get(
                index=index_name,
                id=doc_id
            )
            return response['_source']
        except Exception:
            return None
    
    async def _update_existing_asset_doc(self, index_name: str, doc_id: str, new_doc: Dict[str, Any], existing_doc: Dict[str, Any]):
        """Update existing asset document in Elasticsearch"""
        # Merge discovery sources
        existing_sources = existing_doc.get("discovery_sources", [])
        existing_sources.append({
            "task_id": new_doc["source_task_id"],
            "task_type": new_doc["source_task_type"],
            "agent_id": new_doc["source_agent_id"],
            "discovered_at": new_doc["discovered_at"],
            "workflow_id": new_doc["workflow_id"]
        })
        
        # Update document
        update_doc = {
            "last_seen": new_doc["discovered_at"],
            "discovery_sources": existing_sources,
            "discovery_count": len(existing_sources),
            "timestamp": new_doc["timestamp"]
        }
        
        # Update confidence if higher
        existing_confidence = existing_doc.get("confidence", "low")
        new_confidence = new_doc.get("confidence", "medium")
        confidence_levels = {"low": 1, "medium": 2, "high": 3}
        
        if confidence_levels.get(new_confidence, 2) > confidence_levels.get(existing_confidence, 1):
            update_doc["confidence"] = new_confidence
        
        # Merge tags
        existing_tags = set(existing_doc.get("tags", []))
        new_tags = set(new_doc.get("tags", []))
        update_doc["tags"] = list(existing_tags.union(new_tags))
        
        await self.es_client.update(
            index=index_name,
            id=doc_id,
            body={"doc": update_doc}
        )
    
    async def _create_new_asset_doc(self, index_name: str, doc_id: str, asset_doc: Dict[str, Any]):
        """Create new asset document in Elasticsearch"""
        # Add discovery tracking
        asset_doc["first_seen"] = asset_doc["discovered_at"]
        asset_doc["last_seen"] = asset_doc["discovered_at"]
        asset_doc["discovery_sources"] = [{
            "task_id": asset_doc["source_task_id"],
            "task_type": asset_doc["source_task_type"],
            "agent_id": asset_doc["source_agent_id"],
            "discovered_at": asset_doc["discovered_at"],
            "workflow_id": asset_doc["workflow_id"]
        }]
        asset_doc["discovery_count"] = 1
        
        await self.es_client.index(
            index=index_name,
            id=doc_id,
            body=asset_doc
        )
    
    def _extract_tags(self, asset_data: Dict[str, Any], task: Task) -> List[str]:
        """Extract tags for the asset based on discovery context"""
        tags = []
        
        # Add task type as tag
        tags.append(f"discovered_by:{task.task_type}")
        
        # Add asset type as tag
        asset_type = asset_data.get("type", "unknown")
        tags.append(f"type:{asset_type}")
        
        # Add specific tags based on asset type
        if asset_type == "subdomain":
            tags.append("dns")
            tags.append("domain")
        elif asset_type == "port":
            tags.append("network")
            tags.append("service")
        elif asset_type == "url":
            tags.append("web")
            tags.append("endpoint")
        elif asset_type == "ip":
            tags.append("network")
            tags.append("host")
        
        # Add confidence as tag
        confidence = asset_data.get("confidence", "medium")
        tags.append(f"confidence:{confidence}")
        
        # Add target domain as tag
        if task.target:
            tags.append(f"target:{task.target}")
        
        return tags
    
    async def get_asset_statistics(self) -> Dict[str, Any]:
        """Get statistics about discovered assets from Elasticsearch"""
        try:
            # Search for all assets with updated field mappings
            search_body = {
                "size": 0,
                "aggs": {
                    "by_type": {
                        "terms": {
                            "field": "entity_data.asset_type.keyword",
                            "size": 20
                        }
                    },
                    "by_source": {
                        "terms": {
                            "field": "entity_data.source_task_type.keyword", 
                            "size": 20
                        }
                    },
                    "by_confidence": {
                        "terms": {
                            "field": "entity_data.confidence.keyword",
                            "size": 10
                        }
                    },
                    "by_date": {
                        "date_histogram": {
                            "field": "entity_data.discovered_at",
                            "calendar_interval": "day",
                            "format": "yyyy-MM-dd"
                        }
                    }
                }
            }
            
            response = await self.es_client.search(
                index="enhanced_asset-*",
                body=search_body
            )
            
            aggregations = response.get("aggregations", {})
            
            return {
                "total_assets": response["hits"]["total"]["value"],
                "by_type": {bucket["key"]: bucket["doc_count"] for bucket in aggregations.get("by_type", {}).get("buckets", [])},
                "by_source": {bucket["key"]: bucket["doc_count"] for bucket in aggregations.get("by_source", {}).get("buckets", [])},
                "by_confidence": {bucket["key"]: bucket["doc_count"] for bucket in aggregations.get("by_confidence", {}).get("buckets", [])},
                "by_date": {bucket["key_as_string"]: bucket["doc_count"] for bucket in aggregations.get("by_date", {}).get("buckets", [])}
            }
            
        except Exception as e:
            logger.error(f"Error getting asset statistics from Elasticsearch: {e}")
            return {
                "total_assets": 0,
                "by_type": {},
                "by_source": {},
                "by_confidence": {},
                "by_date": {}
            }

# Service function for task result processing
async def process_task_assets_on_completion(task_id: str, db: AsyncSession) -> int:
    """
    Process assets when a task completes
    
    Args:
        task_id: ID of the completed task
        db: Database session
        
    Returns:
        Number of assets processed
    """
    es_client = await get_es_client()
    service = AssetAggregationService(db, es_client)
    return await service.process_task_assets(task_id)

# Batch processing function
async def batch_process_unprocessed_tasks(db: AsyncSession, limit: int = 100) -> int:
    """
    Batch process tasks that have assets but haven't been processed
    
    Args:
        db: Database session
        limit: Maximum number of tasks to process
        
    Returns:
        Number of tasks processed
    """
    # Find completed tasks with assets
    result = await db.execute(
        select(Task).where(
            Task.status == "completed",
            Task.assets_discovered.is_not(None),
            Task.assets_discovered != "null"
        ).limit(limit)
    )
    
    tasks = result.scalars().all()
    es_client = await get_es_client()
    service = AssetAggregationService(db, es_client)
    
    total_assets = 0
    for task in tasks:
        try:
            assets_processed = await service.process_task_assets(task.task_id)
            total_assets += assets_processed
        except Exception as e:
            logger.error(f"Error processing task {task.task_id}: {e}")
    
    return total_assets