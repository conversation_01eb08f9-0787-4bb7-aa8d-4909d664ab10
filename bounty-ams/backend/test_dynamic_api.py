#!/usr/bin/env python3
"""
测试动态模型API
"""

import asyncio
import aiohttp
import json

async def test_dynamic_models_api():
    """测试动态模型API"""
    base_url = "http://localhost:8000"
    
    async with aiohttp.ClientSession() as session:
        try:
            print("🔍 测试动态模型API...")
            
            # 测试获取模型类型
            print("\n1. 测试获取模型类型...")
            async with session.get(f"{base_url}/api/dynamic-models/types") as response:
                print(f"状态码: {response.status}")
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ 成功获取 {len(data.get('data', []))} 个模型类型")
                    for model_type in data.get('data', []):
                        print(f"  - {model_type['name']}: {model_type['display_name']}")
                else:
                    error_text = await response.text()
                    print(f"❌ 获取模型类型失败: {error_text}")
                    return
            
            # 获取platform模型的ID
            platform_types = [t for t in data.get('data', []) if t['name'] == 'platform']
            if not platform_types:
                print("❌ 未找到platform模型类型")
                return
            
            platform_model_id = platform_types[0]['id']
            print(f"\n📋 Platform模型ID: {platform_model_id}")
            
            # 测试获取平台实体
            print("\n2. 测试获取平台实体...")
            async with session.get(f"{base_url}/api/dynamic-models/entities/{platform_model_id}") as response:
                print(f"状态码: {response.status}")
                if response.status == 200:
                    data = await response.json()
                    platforms = data.get('data', [])
                    print(f"✅ 成功获取 {len(platforms)} 个平台")
                    for platform in platforms[:3]:  # 只显示前3个
                        print(f"  - {platform['entity_data']['display_name']} ({platform['entity_data']['name']})")
                    if len(platforms) > 3:
                        print(f"  ... 还有 {len(platforms) - 3} 个平台")
                else:
                    error_text = await response.text()
                    print(f"❌ 获取平台实体失败: {error_text}")
                    return
            
            # 获取project模型的ID
            project_types = [t for t in data.get('data', []) if t['name'] == 'project']
            if project_types:
                project_model_id = project_types[0]['id']
                print(f"\n📋 Project模型ID: {project_model_id}")
                
                # 测试获取项目实体
                print("\n3. 测试获取项目实体...")
                async with session.get(f"{base_url}/api/dynamic-models/entities/{project_model_id}") as response:
                    print(f"状态码: {response.status}")
                    if response.status == 200:
                        data = await response.json()
                        projects = data.get('data', [])
                        print(f"✅ 成功获取 {len(projects)} 个项目")
                        for project in projects[:3]:  # 只显示前3个
                            print(f"  - {project['entity_data']['name']} (平台ID: {project['entity_data']['platform_id']})")
                        if len(projects) > 3:
                            print(f"  ... 还有 {len(projects) - 3} 个项目")
                    else:
                        error_text = await response.text()
                        print(f"❌ 获取项目实体失败: {error_text}")
            
            # 测试增强搜索API健康检查
            print("\n4. 测试增强搜索API健康检查...")
            async with session.get(f"{base_url}/api/enhanced-search/health") as response:
                print(f"状态码: {response.status}")
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ 增强搜索API健康状态: {data.get('status', 'unknown')}")
                else:
                    error_text = await response.text()
                    print(f"❌ 增强搜索API健康检查失败: {error_text}")
            
            print("\n🎉 API测试完成！")
                    
        except Exception as e:
            print(f"❌ API测试时发生错误: {e}")

if __name__ == "__main__":
    asyncio.run(test_dynamic_models_api())