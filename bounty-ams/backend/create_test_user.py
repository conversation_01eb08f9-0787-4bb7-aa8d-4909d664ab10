#!/usr/bin/env python3
"""
创建测试用户
"""

import asyncio
import hashlib
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy import select
from models_dynamic import User
from config import settings

# 创建异步数据库引擎
engine = create_async_engine(settings.DATABASE_URL)
async_session = async_sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)

def hash_password(password: str) -> str:
    """简单的密码哈希函数"""
    return hashlib.sha256(password.encode()).hexdigest()

async def create_test_user():
    """创建测试用户"""
    async with async_session() as session:
        try:
            # 检查是否已存在test用户
            existing_user_result = await session.execute(
                select(User).where(User.username == 'test')
            )
            existing_user = existing_user_result.scalar_one_or_none()
            
            if existing_user:
                print("✅ 测试用户已存在")
                print(f"用户名: {existing_user.username}")
                print(f"邮箱: {existing_user.email}")
                print(f"管理员: {existing_user.is_admin}")
                return
            
            # 创建测试用户
            test_user = User(
                username='test',
                email='<EMAIL>',
                hashed_password=hash_password('test123'),
                is_admin=True,
                is_active=True
            )
            
            session.add(test_user)
            await session.commit()
            await session.refresh(test_user)
            
            print("✅ 成功创建测试用户")
            print(f"用户名: {test_user.username}")
            print(f"密码: test123")
            print(f"邮箱: {test_user.email}")
            print(f"管理员: {test_user.is_admin}")
            print(f"用户ID: {test_user.id}")
            
        except Exception as e:
            print(f"❌ 创建测试用户失败: {e}")
            await session.rollback()

if __name__ == "__main__":
    asyncio.run(create_test_user())