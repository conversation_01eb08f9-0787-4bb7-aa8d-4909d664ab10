#!/usr/bin/env python3
"""
测试跳转筛选功能
"""

import asyncio
import aiohttp

async def test_navigation_filtering():
    base_url = "http://localhost:8000"
    
    async with aiohttp.ClientSession() as session:
        # 1. 登录
        login_data = {"username": "admin", "password": "password"}
        async with session.post(f"{base_url}/api/auth/login", json=login_data) as response:
            auth_data = await response.json()
            token = auth_data.get('access_token')
            
        headers = {"Authorization": f"Bearer {token}"}
        
        print("🧪 测试平台→项目筛选功能:")
        
        # 2. 获取一个平台
        async with session.get(f"{base_url}/api/dynamic-models/entities?model_type_id=461767c9-921a-4dc8-982f-70cab4dc08e4&limit=1", headers=headers) as response:
            platforms = await response.json()
            if platforms:
                platform = platforms[0]
                platform_id = platform['id']
                platform_name = platform['entity_data']['display_name']
                print(f"  测试平台: {platform_name} (ID: {platform_id})")
                
                # 3. 测试筛选该平台的项目
                async with session.get(f"{base_url}/api/dynamic-models/entities?model_type_id=e489cf55-09c3-48fe-a14b-93f3e63b3c8a&platform_id={platform_id}", headers=headers) as response:
                    projects = await response.json()
                    print(f"  该平台的项目数: {len(projects)}")
                    
                    if projects:
                        project = projects[0]
                        project_id = project['id']
                        project_name = project['entity_data']['name']
                        print(f"  测试项目: {project_name} (ID: {project_id})")
                        
                        # 4. 测试筛选该项目的资产
                        async with session.get(f"{base_url}/api/dynamic-models/entities?model_type_id=6a75f1f3-2f9f-48ea-8ace-084d5b7fc999&project_id={project_id}", headers=headers) as response:
                            assets = await response.json()
                            print(f"  该项目的资产数: {len(assets)}")
                            
                        # 5. 测试筛选该平台的所有资产
                        async with session.get(f"{base_url}/api/dynamic-models/entities?model_type_id=6a75f1f3-2f9f-48ea-8ace-084d5b7fc999&platform_id={platform_id}", headers=headers) as response:
                            platform_assets = await response.json()
                            print(f"  该平台的所有资产数: {len(platform_assets)}")
                
        print("\n✅ 后端筛选API工作正常！")
        print("前端跳转应该能正确显示筛选结果")

if __name__ == "__main__":
    asyncio.run(test_navigation_filtering())