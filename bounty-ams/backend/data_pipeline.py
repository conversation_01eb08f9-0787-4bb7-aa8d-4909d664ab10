"""
数据清洗和处理管道
提供资产数据的标准化、去重、关联和增强功能
"""

from typing import Dict, List, Optional, Any, Tuple, Set
from datetime import datetime, timedelta
import asyncio
import hashlib
import json
import re
import ipaddress
from urllib.parse import urlparse
from dataclasses import dataclass, asdict
import logging

logger = logging.getLogger(__name__)

@dataclass
class AssetFingerprint:
    """资产指纹"""
    primary_hash: str  # 主要标识哈希
    secondary_hash: str  # 次要标识哈希
    asset_type: str
    normalized_value: str
    confidence_score: float

class AssetDataProcessor:
    """资产数据处理器"""
    
    def __init__(self, search_service=None):
        self.search_service = search_service
        self.domain_patterns = self._load_domain_patterns()
        self.normalization_rules = self._load_normalization_rules()
        
    def _load_domain_patterns(self) -> Dict[str, re.Pattern]:
        """加载域名模式"""
        return {
            'subdomain': re.compile(r'^([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}$'),
            'ip': re.compile(r'^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$'),
            'port': re.compile(r'^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}:\d+$'),
            'url': re.compile(r'^https?://.+'),
            'email': re.compile(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
        }
    
    def _load_normalization_rules(self) -> Dict[str, Any]:
        """加载标准化规则"""
        return {
            'domain': {
                'lowercase': True,
                'remove_www': True,
                'remove_trailing_dot': True
            },
            'ip': {
                'validate_range': True,
                'remove_port': False
            },
            'url': {
                'lowercase_scheme': True,
                'remove_fragment': True,
                'normalize_path': True
            }
        }
    
    async def clean_and_normalize_assets(self, raw_assets: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        清洗和标准化资产数据
        """
        cleaned_assets = []
        
        for asset in raw_assets:
            try:
                cleaned_asset = await self._clean_single_asset(asset)
                if cleaned_asset:
                    cleaned_assets.append(cleaned_asset)
            except Exception as e:
                logger.warning(f"Failed to clean asset {asset}: {e}")
                continue
        
        logger.info(f"Cleaned {len(cleaned_assets)} assets from {len(raw_assets)} raw assets")
        return cleaned_assets
    
    async def _clean_single_asset(self, asset: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """清洗单个资产"""
        # 提取关键字段
        asset_value = asset.get('asset_value', '').strip()
        asset_type = asset.get('asset_type', '').lower()
        
        if not asset_value:
            return None
        
        # 自动检测资产类型
        if not asset_type or asset_type == 'unknown':
            asset_type = self._detect_asset_type(asset_value)
        
        # 标准化资产值
        normalized_value = self._normalize_asset_value(asset_value, asset_type)
        if not normalized_value:
            return None
        
        # 构建清洗后的资产
        cleaned_asset = {
            'asset_type': asset_type,
            'asset_value': normalized_value,
            'asset_host': self._extract_host(normalized_value, asset_type),
            'original_value': asset_value,
            'confidence': self._calculate_confidence(asset_value, asset_type),
            'status': asset.get('status', 'active'),
            'discovered_at': self._normalize_timestamp(asset.get('discovered_at')),
            'source_task_type': asset.get('source_task_type', 'manual'),
            'tags': self._normalize_tags(asset.get('tags', [])),
            'metadata': self._extract_metadata(asset_value, asset_type, asset.get('metadata', {})),
            'platform_id': asset.get('platform_id'),
            'project_id': asset.get('project_id')
        }
        
        # 添加enrichment数据
        enriched_data = await self._enrich_asset_data(cleaned_asset)
        cleaned_asset.update(enriched_data)
        
        return cleaned_asset
    
    def _detect_asset_type(self, value: str) -> str:
        """自动检测资产类型"""
        value = value.lower().strip()
        
        # IP地址检测
        if self.domain_patterns['ip'].match(value):
            return 'ip'
        
        # 端口检测
        if self.domain_patterns['port'].match(value):
            return 'port'
        
        # URL检测
        if self.domain_patterns['url'].match(value):
            return 'url'
        
        # 邮箱检测
        if self.domain_patterns['email'].match(value):
            return 'email'
        
        # 域名检测
        if self.domain_patterns['subdomain'].match(value):
            # 判断是主域名还是子域名
            parts = value.split('.')
            if len(parts) == 2:
                return 'domain'
            else:
                return 'subdomain'
        
        return 'unknown'
    
    def _normalize_asset_value(self, value: str, asset_type: str) -> Optional[str]:
        """标准化资产值"""
        if not value:
            return None
        
        try:
            if asset_type == 'domain' or asset_type == 'subdomain':
                return self._normalize_domain(value)
            elif asset_type == 'ip':
                return self._normalize_ip(value)
            elif asset_type == 'url':
                return self._normalize_url(value)
            elif asset_type == 'email':
                return self._normalize_email(value)
            else:
                return value.strip().lower()
        except Exception:
            return None
    
    def _normalize_domain(self, domain: str) -> Optional[str]:
        """标准化域名"""
        domain = domain.lower().strip()
        
        # 移除协议
        if domain.startswith(('http://', 'https://')):
            domain = urlparse(domain).netloc
        
        # 移除www前缀
        if domain.startswith('www.'):
            domain = domain[4:]
        
        # 移除末尾的点
        if domain.endswith('.'):
            domain = domain[:-1]
        
        # 验证域名格式
        if not self.domain_patterns['subdomain'].match(domain):
            return None
        
        return domain
    
    def _normalize_ip(self, ip: str) -> Optional[str]:
        """标准化IP地址"""
        try:
            # 解析IP地址
            ip_obj = ipaddress.ip_address(ip.split(':')[0])
            
            # 过滤私有IP
            if ip_obj.is_private:
                return None
            
            return str(ip_obj)
        except ValueError:
            return None
    
    def _normalize_url(self, url: str) -> Optional[str]:
        """标准化URL"""
        try:
            parsed = urlparse(url)
            
            # 标准化协议
            scheme = parsed.scheme.lower() if parsed.scheme else 'http'
            
            # 标准化主机名
            netloc = parsed.netloc.lower()
            if netloc.startswith('www.'):
                netloc = netloc[4:]
            
            # 标准化路径
            path = parsed.path if parsed.path else '/'
            if path != '/' and path.endswith('/'):
                path = path[:-1]
            
            # 重建URL（忽略fragment）
            normalized = f"{scheme}://{netloc}{path}"
            if parsed.query:
                normalized += f"?{parsed.query}"
            
            return normalized
        except Exception:
            return None
    
    def _normalize_email(self, email: str) -> Optional[str]:
        """标准化邮箱"""
        email = email.lower().strip()
        if self.domain_patterns['email'].match(email):
            return email
        return None
    
    def _extract_host(self, value: str, asset_type: str) -> Optional[str]:
        """提取主机名"""
        if asset_type in ['domain', 'subdomain']:
            return value
        elif asset_type == 'url':
            try:
                return urlparse(value).netloc
            except:
                return None
        elif asset_type == 'ip':
            return value.split(':')[0]
        return None
    
    def _calculate_confidence(self, value: str, asset_type: str) -> str:
        """计算置信度"""
        confidence_score = 0.5
        
        # 基于资产类型的基础置信度
        type_confidence = {
            'domain': 0.9,
            'subdomain': 0.8,
            'ip': 0.7,
            'url': 0.8,
            'email': 0.6,
            'port': 0.7
        }
        confidence_score = type_confidence.get(asset_type, 0.5)
        
        # 基于格式正确性的调整
        if asset_type in ['domain', 'subdomain']:
            if '.' in value and len(value.split('.')) >= 2:
                confidence_score += 0.1
        
        # 基于长度的调整
        if len(value) < 5 or len(value) > 100:
            confidence_score -= 0.2
        
        # 转换为分类
        if confidence_score >= 0.8:
            return 'high'
        elif confidence_score >= 0.6:
            return 'medium'
        else:
            return 'low'
    
    def _normalize_timestamp(self, timestamp: Any) -> str:
        """标准化时间戳"""
        if isinstance(timestamp, datetime):
            return timestamp.isoformat()
        elif isinstance(timestamp, str):
            try:
                dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                return dt.isoformat()
            except:
                pass
        
        # 默认使用当前时间
        return datetime.now().isoformat()
    
    def _normalize_tags(self, tags: Any) -> List[str]:
        """标准化标签"""
        if isinstance(tags, str):
            tags = [tags]
        elif not isinstance(tags, list):
            return []
        
        normalized_tags = []
        for tag in tags:
            if isinstance(tag, str) and tag.strip():
                normalized_tags.append(tag.strip().lower())
        
        return list(set(normalized_tags))  # 去重
    
    def _extract_metadata(self, value: str, asset_type: str, existing_metadata: Dict[str, Any]) -> Dict[str, Any]:
        """提取元数据"""
        metadata = existing_metadata.copy()
        
        if asset_type == 'url':
            try:
                parsed = urlparse(value)
                metadata.update({
                    'scheme': parsed.scheme,
                    'port': parsed.port,
                    'path': parsed.path,
                    'query': parsed.query
                })
            except:
                pass
        elif asset_type in ['domain', 'subdomain']:
            parts = value.split('.')
            metadata.update({
                'domain_parts': len(parts),
                'tld': parts[-1] if parts else None,
                'sld': parts[-2] if len(parts) >= 2 else None
            })
        
        return metadata
    
    async def _enrich_asset_data(self, asset: Dict[str, Any]) -> Dict[str, Any]:
        """增强资产数据"""
        enrichment = {}
        
        # 基于资产类型的增强
        asset_type = asset.get('asset_type')
        asset_value = asset.get('asset_value')
        
        if asset_type in ['domain', 'subdomain'] and asset_value:
            # 域名相关增强
            enrichment.update(await self._enrich_domain(asset_value))
        elif asset_type == 'ip' and asset_value:
            # IP相关增强
            enrichment.update(await self._enrich_ip(asset_value))
        
        return enrichment
    
    async def _enrich_domain(self, domain: str) -> Dict[str, Any]:
        """增强域名数据"""
        enrichment = {}
        
        try:
            # 提取根域名
            parts = domain.split('.')
            if len(parts) >= 2:
                root_domain = '.'.join(parts[-2:])
                enrichment['root_domain'] = root_domain
                enrichment['subdomain_level'] = len(parts) - 2
        except:
            pass
        
        return enrichment
    
    async def _enrich_ip(self, ip: str) -> Dict[str, Any]:
        """增强IP数据"""
        enrichment = {}
        
        try:
            ip_obj = ipaddress.ip_address(ip)
            enrichment.update({
                'ip_version': ip_obj.version,
                'is_private': ip_obj.is_private,
                'is_multicast': ip_obj.is_multicast,
                'is_loopback': ip_obj.is_loopback
            })
        except:
            pass
        
        return enrichment
    
    async def deduplicate_assets(self, assets: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        资产去重
        基于多层次指纹算法进行智能去重
        """
        fingerprints = {}
        unique_assets = []
        duplicate_groups = []
        
        for asset in assets:
            # 生成资产指纹
            fingerprint = self._generate_asset_fingerprint(asset)
            
            # 检查是否重复
            if fingerprint.primary_hash in fingerprints:
                # 找到潜在重复
                existing_asset = fingerprints[fingerprint.primary_hash]
                if self._are_assets_duplicate(asset, existing_asset['asset'], fingerprint, existing_asset['fingerprint']):
                    # 合并重复资产
                    merged_asset = self._merge_duplicate_assets(existing_asset['asset'], asset)
                    fingerprints[fingerprint.primary_hash]['asset'] = merged_asset
                    duplicate_groups.append({
                        'master': merged_asset,
                        'duplicates': [asset]
                    })
                    continue
            
            # 添加唯一资产
            fingerprints[fingerprint.primary_hash] = {
                'asset': asset,
                'fingerprint': fingerprint
            }
            unique_assets.append(asset)
        
        logger.info(f"Deduplicated {len(assets)} assets to {len(unique_assets)} unique assets")
        logger.info(f"Found {len(duplicate_groups)} duplicate groups")
        
        return unique_assets
    
    def _generate_asset_fingerprint(self, asset: Dict[str, Any]) -> AssetFingerprint:
        """生成资产指纹"""
        asset_type = asset.get('asset_type', '')
        asset_value = asset.get('asset_value', '')
        asset_host = asset.get('asset_host', '')
        
        # 主要指纹：基于标准化的资产值
        primary_data = f"{asset_type}:{asset_value}".lower()
        primary_hash = hashlib.md5(primary_data.encode()).hexdigest()
        
        # 次要指纹：基于主机和类型
        secondary_data = f"{asset_type}:{asset_host}".lower()
        secondary_hash = hashlib.md5(secondary_data.encode()).hexdigest()
        
        # 计算置信度分数
        confidence_score = self._calculate_fingerprint_confidence(asset)
        
        return AssetFingerprint(
            primary_hash=primary_hash,
            secondary_hash=secondary_hash,
            asset_type=asset_type,
            normalized_value=asset_value,
            confidence_score=confidence_score
        )
    
    def _calculate_fingerprint_confidence(self, asset: Dict[str, Any]) -> float:
        """计算指纹置信度"""
        score = 0.5
        
        # 基于字段完整性
        required_fields = ['asset_type', 'asset_value', 'asset_host']
        present_fields = sum(1 for field in required_fields if asset.get(field))
        score += (present_fields / len(required_fields)) * 0.3
        
        # 基于数据质量
        if asset.get('confidence') == 'high':
            score += 0.2
        elif asset.get('confidence') == 'medium':
            score += 0.1
        
        return min(score, 1.0)
    
    def _are_assets_duplicate(self, asset1: Dict[str, Any], asset2: Dict[str, Any], 
                             fp1: AssetFingerprint, fp2: AssetFingerprint) -> bool:
        """判断两个资产是否重复"""
        # 主要指纹相同
        if fp1.primary_hash == fp2.primary_hash:
            return True
        
        # 次要指纹相同且资产类型相同
        if fp1.secondary_hash == fp2.secondary_hash and fp1.asset_type == fp2.asset_type:
            # 进一步检查相似度
            similarity = self._calculate_asset_similarity(asset1, asset2)
            return similarity > 0.8
        
        return False
    
    def _calculate_asset_similarity(self, asset1: Dict[str, Any], asset2: Dict[str, Any]) -> float:
        """计算资产相似度"""
        similarities = []
        
        # 值相似度
        val1 = asset1.get('asset_value', '').lower()
        val2 = asset2.get('asset_value', '').lower()
        if val1 and val2:
            similarities.append(self._string_similarity(val1, val2))
        
        # 主机相似度
        host1 = asset1.get('asset_host', '').lower()
        host2 = asset2.get('asset_host', '').lower()
        if host1 and host2:
            similarities.append(self._string_similarity(host1, host2))
        
        # 类型相同性
        if asset1.get('asset_type') == asset2.get('asset_type'):
            similarities.append(1.0)
        else:
            similarities.append(0.0)
        
        return sum(similarities) / len(similarities) if similarities else 0.0
    
    def _string_similarity(self, s1: str, s2: str) -> float:
        """计算字符串相似度（简化版编辑距离）"""
        if s1 == s2:
            return 1.0
        
        if not s1 or not s2:
            return 0.0
        
        # 简化的Levenshtein距离
        m, n = len(s1), len(s2)
        if m > n:
            s1, s2 = s2, s1
            m, n = n, m
        
        current_row = list(range(n + 1))
        for i in range(1, m + 1):
            previous_row, current_row = current_row, [i] + [0] * n
            for j in range(1, n + 1):
                add, delete, change = previous_row[j] + 1, current_row[j - 1] + 1, previous_row[j - 1]
                if s1[i - 1] != s2[j - 1]:
                    change += 1
                current_row[j] = min(add, delete, change)
        
        distance = current_row[n]
        return 1.0 - (distance / max(m, n))
    
    def _merge_duplicate_assets(self, master: Dict[str, Any], duplicate: Dict[str, Any]) -> Dict[str, Any]:
        """合并重复资产"""
        merged = master.copy()
        
        # 合并标签
        master_tags = set(master.get('tags', []))
        duplicate_tags = set(duplicate.get('tags', []))
        merged['tags'] = list(master_tags.union(duplicate_tags))
        
        # 使用更高的置信度
        master_conf = self._confidence_to_score(master.get('confidence', 'low'))
        duplicate_conf = self._confidence_to_score(duplicate.get('confidence', 'low'))
        if duplicate_conf > master_conf:
            merged['confidence'] = duplicate.get('confidence')
        
        # 使用更早的发现时间
        master_time = master.get('discovered_at', '')
        duplicate_time = duplicate.get('discovered_at', '')
        if duplicate_time < master_time:
            merged['discovered_at'] = duplicate_time
        
        # 合并元数据
        master_metadata = master.get('metadata', {})
        duplicate_metadata = duplicate.get('metadata', {})
        merged_metadata = {**master_metadata, **duplicate_metadata}
        merged['metadata'] = merged_metadata
        
        # 记录合并信息
        merged['merged_from'] = merged.get('merged_from', []) + [duplicate.get('asset_value', '')]
        
        return merged
    
    def _confidence_to_score(self, confidence: str) -> float:
        """置信度转换为数值"""
        confidence_map = {'high': 0.9, 'medium': 0.6, 'low': 0.3}
        return confidence_map.get(confidence, 0.3)
    
    async def correlate_assets(self, assets: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        资产关联分析
        识别资产间的关系和依赖
        """
        correlations = {
            'domain_groups': {},
            'ip_clusters': {},
            'technology_stacks': {},
            'network_relationships': []
        }
        
        # 按域名分组
        domain_assets = [a for a in assets if a.get('asset_type') in ['domain', 'subdomain']]
        correlations['domain_groups'] = self._group_by_root_domain(domain_assets)
        
        # 按IP聚类
        ip_assets = [a for a in assets if a.get('asset_type') == 'ip']
        correlations['ip_clusters'] = self._cluster_by_ip_range(ip_assets)
        
        # 技术栈分析
        correlations['technology_stacks'] = await self._analyze_technology_stacks(assets)
        
        # 网络关系分析
        correlations['network_relationships'] = await self._analyze_network_relationships(assets)
        
        return correlations
    
    def _group_by_root_domain(self, domain_assets: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """按根域名分组"""
        groups = {}
        
        for asset in domain_assets:
            domain = asset.get('asset_value', '')
            if not domain:
                continue
            
            # 提取根域名
            parts = domain.split('.')
            if len(parts) >= 2:
                root_domain = '.'.join(parts[-2:])
                if root_domain not in groups:
                    groups[root_domain] = []
                groups[root_domain].append(asset)
        
        return groups
    
    def _cluster_by_ip_range(self, ip_assets: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """按IP范围聚类"""
        clusters = {}
        
        for asset in ip_assets:
            ip = asset.get('asset_value', '')
            if not ip:
                continue
            
            try:
                ip_obj = ipaddress.ip_address(ip)
                # 按/24网段聚类
                network = ipaddress.ip_network(f"{ip}/24", strict=False)
                network_str = str(network)
                
                if network_str not in clusters:
                    clusters[network_str] = []
                clusters[network_str].append(asset)
            except:
                continue
        
        return clusters
    
    async def _analyze_technology_stacks(self, assets: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析技术栈"""
        tech_patterns = {
            'web_servers': ['nginx', 'apache', 'iis'],
            'cms': ['wordpress', 'drupal', 'joomla'],
            'frameworks': ['django', 'rails', 'laravel', 'express'],
            'databases': ['mysql', 'postgres', 'mongodb', 'redis']
        }
        
        technology_analysis = {}
        
        for category, patterns in tech_patterns.items():
            matches = []
            for asset in assets:
                asset_value = asset.get('asset_value', '').lower()
                metadata = asset.get('metadata', {})
                
                for pattern in patterns:
                    if pattern in asset_value or any(pattern in str(v).lower() for v in metadata.values()):
                        matches.append({
                            'asset': asset,
                            'technology': pattern,
                            'confidence': 'medium'
                        })
            
            technology_analysis[category] = matches
        
        return technology_analysis
    
    async def _analyze_network_relationships(self, assets: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """分析网络关系"""
        relationships = []
        
        # 分析域名-IP关系
        domain_assets = {a.get('asset_value'): a for a in assets if a.get('asset_type') in ['domain', 'subdomain']}
        ip_assets = {a.get('asset_value'): a for a in assets if a.get('asset_type') == 'ip'}
        
        for domain, domain_asset in domain_assets.items():
            # 寻找可能的IP关联（通过元数据等）
            metadata = domain_asset.get('metadata', {})
            if 'ip' in metadata and metadata['ip'] in ip_assets:
                relationships.append({
                    'type': 'domain_ip_resolution',
                    'source': domain_asset,
                    'target': ip_assets[metadata['ip']],
                    'confidence': 'high'
                })
        
        return relationships