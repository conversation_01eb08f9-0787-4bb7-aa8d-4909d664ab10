#!/usr/bin/env python3
"""
检查现有平台和项目数据
"""

import asyncio
import json
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy import select
from models_dynamic import ModelType, DynamicEntity
from config import settings

# 创建异步数据库引擎
engine = create_async_engine(settings.DATABASE_URL)
async_session = async_sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)

async def check_existing_data():
    """检查现有数据"""
    async with async_session() as session:
        try:
            # 检查模型类型
            model_types_result = await session.execute(select(ModelType))
            model_types = model_types_result.scalars().all()
            
            print("现有模型类型：")
            for model_type in model_types:
                print(f"  - {model_type.name}: {model_type.display_name}")
            
            # 检查平台数据
            platform_model = next((m for m in model_types if m.name == 'platform'), None)
            if platform_model:
                platforms_result = await session.execute(
                    select(DynamicEntity).where(DynamicEntity.model_type_id == platform_model.id)
                )
                platforms = platforms_result.scalars().all()
                
                print(f"\n现有平台数据 ({len(platforms)} 个)：")
                for platform in platforms:
                    print(f"  - {platform.entity_data.get('display_name', 'N/A')} ({platform.entity_data.get('name', 'N/A')})")
            else:
                print("\n❌ 未找到平台模型类型")
            
            # 检查项目数据
            project_model = next((m for m in model_types if m.name == 'project'), None)
            if project_model:
                projects_result = await session.execute(
                    select(DynamicEntity).where(DynamicEntity.model_type_id == project_model.id)
                )
                projects = projects_result.scalars().all()
                
                print(f"\n现有项目数据 ({len(projects)} 个)：")
                for project in projects:
                    print(f"  - {project.entity_data.get('name', 'N/A')} (平台ID: {project.entity_data.get('platform_id', 'N/A')})")
            else:
                print("\n❌ 未找到项目模型类型")
            
            # 检查资产数据
            asset_model = next((m for m in model_types if m.name == 'enhanced_asset'), None)
            if asset_model:
                assets_result = await session.execute(
                    select(DynamicEntity).where(DynamicEntity.model_type_id == asset_model.id)
                )
                assets = assets_result.scalars().all()
                
                print(f"\n现有资产数据 ({len(assets)} 个)：")
                for asset in assets[:5]:  # 只显示前5个
                    print(f"  - {asset.entity_data.get('asset_value', 'N/A')} ({asset.entity_data.get('asset_type', 'N/A')})")
                if len(assets) > 5:
                    print(f"  ... 还有 {len(assets) - 5} 个资产")
            else:
                print("\n❌ 未找到增强资产模型类型")
            
        except Exception as e:
            print(f"检查数据时发生错误: {e}")

if __name__ == "__main__":
    asyncio.run(check_existing_data())