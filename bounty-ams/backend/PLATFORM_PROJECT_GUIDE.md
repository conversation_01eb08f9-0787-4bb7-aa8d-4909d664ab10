# Platform & Project 动态模型使用指南

## 🎯 概述

本指南介绍如何使用新添加的 Platform（平台）和 Project（项目）动态模型来重构您的资产管理系统，实现按平台和项目维度管理资产的核心需求。

## 📋 实施步骤

### 第一步：初始化Platform和Project模型

```bash
cd backend
python init_platform_project_models.py
```

这将创建以下动态模型：
- **Platform（漏洞平台）**: 管理HackerOne、MSRC、Bugcrowd等平台
- **Project（漏洞项目）**: 管理每个平台下的具体项目
- **Enhanced Asset（增强资产）**: 带有平台和项目关联的资产模型

### 第二步：通过前端创建平台数据

1. **访问Models页面** (http://localhost:5173/models)
2. **创建Platform实例**：
   - 选择 "漏洞平台" 模型
   - 创建以下示例平台：

```json
// HackerOne平台示例
{
  "name": "hackerone",
  "display_name": "HackerOne",
  "platform_type": "bug_bounty",
  "website_url": "https://hackerone.com",
  "api_base_url": "https://api.hackerone.com",
  "api_config": {
    "api_key": "your_api_key",
    "username": "your_username"
  },
  "description": "全球领先的漏洞赏金平台",
  "status": "active",
  "supported_asset_types": ["domain", "subdomain", "url", "api_endpoint"]
}

// MSRC平台示例
{
  "name": "msrc",
  "display_name": "Microsoft MSRC",
  "platform_type": "vendor_vdp",
  "website_url": "https://msrc.microsoft.com",
  "description": "微软安全响应中心",
  "status": "active",
  "supported_asset_types": ["domain", "subdomain", "url", "api_endpoint"]
}
```

### 第三步：创建Project实例

为每个平台创建对应的项目：

```json
// HackerOne项目示例
{
  "name": "Shopify Bug Bounty",
  "platform_id": "hackerone_platform_id",
  "external_id": "shopify",
  "company_name": "Shopify",
  "program_type": "public",
  "status": "active",
  "scope": {
    "in_scope": ["*.shopify.com", "shopify.com"],
    "out_of_scope": ["internal.shopify.com"]
  },
  "reward_range": "$100 - $25,000",
  "description": "Shopify公开漏洞赏金项目",
  "tags": ["web", "api", "e-commerce"],
  "priority": "high"
}

// MSRC项目示例
{
  "name": "Microsoft Edge",
  "platform_id": "msrc_platform_id",
  "external_id": "edge",
  "company_name": "Microsoft",
  "program_type": "vdp",
  "status": "active",
  "scope": {
    "in_scope": ["*.microsoftedge.com", "edge.microsoft.com"],
    "out_of_scope": ["internal.microsoft.com"]
  },
  "description": "Microsoft Edge浏览器漏洞披露项目",
  "tags": ["browser", "web", "security"],
  "priority": "high"
}
```

## 🔍 使用增强搜索功能

### 高级搜索API

```bash
# 搜索特定平台的资产
POST /api/enhanced-search/assets
{
  "platform_id": "hackerone_platform_id",
  "query": "shopify.com",
  "asset_types": ["domain", "subdomain"],
  "confidence": "high",
  "include_aggregations": true
}

# 搜索特定项目的资产
POST /api/enhanced-search/assets
{
  "project_id": "shopify_project_id",
  "query": "api",
  "size": 50,
  "sort_by": "discovered_at",
  "sort_order": "desc"
}
```

### 资产洞察分析

```bash
# 获取平台资产洞察
GET /api/enhanced-search/assets/insights?platform_id=hackerone_platform_id

# 获取项目资产洞察
GET /api/enhanced-search/assets/insights?project_id=shopify_project_id
```

### 数据导出

```bash
# 导出平台资产
POST /api/enhanced-search/assets/export
{
  "platform_id": "hackerone_platform_id",
  "export_format": "json",
  "filters": {
    "confidence": "high",
    "status": "active"
  }
}
```

## 🛠️ 数据迁移

### 从现有资产迁移到Enhanced Asset

1. **数据处理和清洗**：
```bash
POST /api/enhanced-search/assets/process
{
  "raw_assets": [
    {
      "domain": "example.com",
      "confidence": "high",
      "discovered_at": "2025-01-01T00:00:00Z"
    }
  ]
}
```

2. **批量创建Enhanced Asset**：
```bash
POST /api/dynamic-models/entities
{
  "model_type_id": "enhanced_asset_model_id",
  "entity_data": {
    "platform_id": "hackerone_platform_id",
    "project_id": "shopify_project_id",
    "asset_type": "domain",
    "asset_value": "example.com",
    "confidence": "high",
    "status": "active",
    "discovered_at": "2025-01-01T00:00:00Z",
    "tags": ["in_scope", "verified"]
  }
}
```

## 🎨 前端集成

### 更新前端资产页面

1. **添加平台/项目筛选器**：
```typescript
// 在Assets.tsx中添加平台和项目选择
const [selectedPlatform, setSelectedPlatform] = useState<string>('');
const [selectedProject, setSelectedProject] = useState<string>('');

// 加载平台列表
const loadPlatforms = async () => {
  const response = await api.get('/api/dynamic-models/entities?model_type_id=platform_model_id');
  setPlatforms(response.data);
};

// 加载项目列表
const loadProjects = async (platformId: string) => {
  const response = await api.get(`/api/dynamic-models/entities?model_type_id=project_model_id&platform_id=${platformId}`);
  setProjects(response.data);
};
```

2. **使用增强搜索API**：
```typescript
// 替换原有的搜索逻辑
const searchAssets = async (searchParams: any) => {
  const response = await api.post('/api/enhanced-search/assets', {
    ...searchParams,
    platform_id: selectedPlatform,
    project_id: selectedProject,
    include_aggregations: true
  });
  
  setAssets(response.data.hits);
  setAggregations(response.data.aggregations);
};
```

### 创建平台/项目管理页面

1. **创建PlatformManager.tsx**：
```typescript
// 平台管理组件
const PlatformManager: React.FC = () => {
  const [platforms, setPlatforms] = useState([]);
  
  const loadPlatforms = async () => {
    const response = await api.get('/api/dynamic-models/entities?model_type_id=platform_model_id');
    setPlatforms(response.data);
  };
  
  // 渲染平台列表和管理功能
  return (
    <div>
      <h2>平台管理</h2>
      {/* 平台列表、创建、编辑、删除功能 */}
    </div>
  );
};
```

2. **创建ProjectManager.tsx**：
```typescript
// 项目管理组件
const ProjectManager: React.FC = () => {
  const [projects, setProjects] = useState([]);
  
  const loadProjects = async () => {
    const response = await api.get('/api/dynamic-models/entities?model_type_id=project_model_id');
    setProjects(response.data);
  };
  
  // 渲染项目列表和管理功能
  return (
    <div>
      <h2>项目管理</h2>
      {/* 项目列表、创建、编辑、删除功能 */}
    </div>
  );
};
```

## 📊 数据分析和可视化

### 平台维度分析

```typescript
// 获取平台资产分析
const getPlatformAnalysis = async (platformId: string) => {
  const response = await api.get(`/api/enhanced-search/platforms/${platformId}/assets/summary`);
  return response.data;
};

// 渲染平台分析图表
const renderPlatformCharts = (data: any) => {
  // 使用ECharts渲染平台资产分布、趋势等图表
};
```

### 项目维度分析

```typescript
// 获取项目资产分析
const getProjectAnalysis = async (projectId: string) => {
  const response = await api.get(`/api/enhanced-search/projects/${projectId}/assets/summary`);
  return response.data;
};

// 渲染项目分析图表
const renderProjectCharts = (data: any) => {
  // 使用ECharts渲染项目资产分布、风险评估等图表
};
```

## 🔧 Agent集成

### 更新Agent来支持平台/项目概念

1. **Agent配置更新**：
```yaml
# config.yaml
agent:
  id: "go-agent-001"
  name: "HackerOne Agent"
  default_platform_id: "hackerone_platform_id"
  default_project_id: "shopify_project_id"
```

2. **资产上报时包含平台/项目信息**：
```go
// 在Agent的资产上报中包含平台和项目信息
type AssetReport struct {
    PlatformID   string `json:"platform_id"`
    ProjectID    string `json:"project_id"`
    AssetType    string `json:"asset_type"`
    AssetValue   string `json:"asset_value"`
    Confidence   string `json:"confidence"`
    // ... 其他字段
}
```

## 🎯 下一步计划

1. **完善前端界面**：
   - 在Assets页面添加平台/项目筛选
   - 创建专门的平台/项目管理页面
   - 更新仪表盘显示平台/项目维度数据

2. **Agent功能增强**：
   - 支持平台API集成（HackerOne、MSRC等）
   - 自动资产范围检查
   - 项目范围验证

3. **数据分析增强**：
   - 平台/项目维度的风险评估
   - 资产覆盖率分析
   - 漏洞分布统计

4. **自动化工作流**：
   - 基于平台/项目的自动化任务分发
   - 资产范围自动验证
   - 结果自动归类

## 🚀 立即开始

1. **运行初始化脚本**：
```bash
python init_platform_project_models.py
```

2. **通过前端创建平台和项目**：
   - 访问 Models 页面
   - 创建您的第一个平台
   - 为平台创建项目

3. **开始使用增强搜索**：
   - 测试新的搜索API
   - 体验数据洞察功能
   - 导出和分析数据

通过这个重构，您的资产管理系统将真正成为一个**按平台和项目维度管理资产**的专业系统，完全符合漏洞赏金资产管理的实际需求！