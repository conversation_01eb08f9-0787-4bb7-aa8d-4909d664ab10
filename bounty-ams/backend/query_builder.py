"""
高级查询DSL构建器
支持复杂的Elasticsearch查询构建
"""

from typing import Dict, List, Optional, Any, Union
from datetime import datetime
import re
import json

class QueryBuilder:
    """Elasticsearch查询DSL构建器"""
    
    def __init__(self):
        self.query = {"bool": {"must": [], "filter": [], "should": [], "must_not": []}}
        self._aggregations = {}
        self._sort = []
        self._highlight = {}
        
    def reset(self):
        """重置查询构建器"""
        self.__init__()
        return self
    
    def text_search(self, query: str, fields: List[str] = None, fuzziness: str = "AUTO") -> 'QueryBuilder':
        """
        添加全文搜索
        支持多字段搜索、模糊匹配、短语匹配
        """
        if not query:
            return self
            
        if not fields:
            fields = [
                "entity_data.asset_value^3",
                "entity_data.asset_host^2", 
                "entity_data.tags^1.5",
                "entity_data.metadata.*"
            ]
        
        # 检测查询类型
        if '"' in query:
            # 短语查询
            phrase_query = query.strip('"')
            self.query["bool"]["must"].append({
                "multi_match": {
                    "query": phrase_query,
                    "fields": fields,
                    "type": "phrase",
                    "boost": 2.0
                }
            })
        elif '*' in query or '?' in query:
            # 通配符查询
            for field in fields:
                field_name = field.split('^')[0]  # 移除boost
                self.query["bool"]["should"].append({
                    "wildcard": {
                        field_name: {
                            "value": query.lower(),
                            "boost": 1.5
                        }
                    }
                })
        else:
            # 标准多字段搜索
            self.query["bool"]["must"].append({
                "multi_match": {
                    "query": query,
                    "fields": fields,
                    "type": "best_fields",
                    "fuzziness": fuzziness,
                    "operator": "and",
                    "boost": 1.0
                }
            })
            
            # 添加模糊匹配作为should条件
            self.query["bool"]["should"].append({
                "multi_match": {
                    "query": query,
                    "fields": fields,
                    "type": "phrase_prefix",
                    "boost": 0.5
                }
            })
        
        return self
    
    def exact_match(self, field: str, value: Union[str, List[str]]) -> 'QueryBuilder':
        """精确匹配"""
        if isinstance(value, list):
            if value:  # 只有非空列表才添加条件
                self.query["bool"]["filter"].append({
                    "terms": {field: value}
                })
        else:
            self.query["bool"]["filter"].append({
                "term": {field: value}
            })
        return self
    
    def range_filter(self, field: str, gte=None, lte=None, gt=None, lt=None) -> 'QueryBuilder':
        """范围过滤"""
        range_query = {}
        if gte is not None:
            range_query["gte"] = gte
        if lte is not None:
            range_query["lte"] = lte
        if gt is not None:
            range_query["gt"] = gt
        if lt is not None:
            range_query["lt"] = lt
            
        if range_query:
            self.query["bool"]["filter"].append({
                "range": {field: range_query}
            })
        return self
    
    def date_range(self, field: str, start: datetime = None, end: datetime = None) -> 'QueryBuilder':
        """日期范围过滤"""
        range_query = {}
        if start:
            range_query["gte"] = start.isoformat()
        if end:
            range_query["lte"] = end.isoformat()
            
        if range_query:
            self.query["bool"]["filter"].append({
                "range": {field: range_query}
            })
        return self
    
    def exists(self, field: str) -> 'QueryBuilder':
        """字段存在性过滤"""
        self.query["bool"]["filter"].append({
            "exists": {"field": field}
        })
        return self
    
    def not_exists(self, field: str) -> 'QueryBuilder':
        """字段不存在过滤"""
        self.query["bool"]["must_not"].append({
            "exists": {"field": field}
        })
        return self
    
    def nested_query(self, path: str, query: Dict[str, Any]) -> 'QueryBuilder':
        """嵌套查询"""
        self.query["bool"]["must"].append({
            "nested": {
                "path": path,
                "query": query
            }
        })
        return self
    
    def geo_distance(self, field: str, lat: float, lon: float, distance: str) -> 'QueryBuilder':
        """地理距离查询"""
        self.query["bool"]["filter"].append({
            "geo_distance": {
                "distance": distance,
                field: {"lat": lat, "lon": lon}
            }
        })
        return self
    
    def regex_match(self, field: str, pattern: str) -> 'QueryBuilder':
        """正则表达式匹配"""
        self.query["bool"]["must"].append({
            "regexp": {
                field: {
                    "value": pattern,
                    "flags": "ALL"
                }
            }
        })
        return self
    
    def script_query(self, script: str, params: Dict[str, Any] = None) -> 'QueryBuilder':
        """脚本查询"""
        script_query = {
            "script": {
                "source": script
            }
        }
        if params:
            script_query["script"]["params"] = params
            
        self.query["bool"]["must"].append(script_query)
        return self
    
    def boosting_query(self, positive: Dict[str, Any], negative: Dict[str, Any], negative_boost: float = 0.2) -> 'QueryBuilder':
        """提升查询（正负权重）"""
        boosting = {
            "boosting": {
                "positive": positive,
                "negative": negative,
                "negative_boost": negative_boost
            }
        }
        self.query = boosting
        return self
    
    def function_score(self, functions: List[Dict[str, Any]], score_mode: str = "sum", boost_mode: str = "multiply") -> 'QueryBuilder':
        """函数评分查询"""
        function_score = {
            "function_score": {
                "query": self.query,
                "functions": functions,
                "score_mode": score_mode,
                "boost_mode": boost_mode
            }
        }
        self.query = function_score
        return self
    
    def add_aggregation(self, name: str, agg_query: Dict[str, Any]) -> 'QueryBuilder':
        """添加聚合"""
        self._aggregations[name] = agg_query
        return self
    
    def terms_aggregation(self, name: str, field: str, size: int = 10, min_doc_count: int = 1) -> 'QueryBuilder':
        """词条聚合"""
        self._aggregations[name] = {
            "terms": {
                "field": field,
                "size": size,
                "min_doc_count": min_doc_count
            }
        }
        return self
    
    def date_histogram(self, name: str, field: str, interval: str = "1d", format: str = "yyyy-MM-dd") -> 'QueryBuilder':
        """日期直方图聚合"""
        self._aggregations[name] = {
            "date_histogram": {
                "field": field,
                "calendar_interval": interval,
                "format": format,
                "min_doc_count": 0
            }
        }
        return self
    
    def stats_aggregation(self, name: str, field: str) -> 'QueryBuilder':
        """统计聚合"""
        self._aggregations[name] = {
            "stats": {"field": field}
        }
        return self
    
    def nested_aggregation(self, name: str, path: str, aggs: Dict[str, Any]) -> 'QueryBuilder':
        """嵌套聚合"""
        self._aggregations[name] = {
            "nested": {"path": path},
            "aggs": aggs
        }
        return self
    
    def pipeline_aggregation(self, name: str, pipeline_type: str, config: Dict[str, Any]) -> 'QueryBuilder':
        """管道聚合"""
        self._aggregations[name] = {
            pipeline_type: config
        }
        return self
    
    def add_sort(self, field: str, order: str = "desc", mode: str = None) -> 'QueryBuilder':
        """添加排序"""
        sort_config = {field: {"order": order}}
        if mode:
            sort_config[field]["mode"] = mode
        self._sort.append(sort_config)
        return self
    
    def add_script_sort(self, script: str, order: str = "desc", type: str = "number") -> 'QueryBuilder':
        """脚本排序"""
        self._sort.append({
            "_script": {
                "type": type,
                "script": {"source": script},
                "order": order
            }
        })
        return self
    
    def add_highlight(self, fields: Dict[str, Any], pre_tags: List[str] = None, post_tags: List[str] = None) -> 'QueryBuilder':
        """添加高亮"""
        self._highlight = {"fields": fields}
        if pre_tags:
            self._highlight["pre_tags"] = pre_tags
        if post_tags:
            self._highlight["post_tags"] = post_tags
        return self
    
    def build(self, size: int = 20, from_: int = 0) -> Dict[str, Any]:
        """构建最终查询"""
        query_body = {
            "query": self.query,
            "size": size,
            "from": from_
        }
        
        if self._aggregations:
            query_body["aggs"] = self._aggregations
            
        if self._sort:
            query_body["sort"] = self._sort
        else:
            # 默认按相关性和时间排序
            query_body["sort"] = [
                {"_score": {"order": "desc"}},
                {"entity_data.discovered_at": {"order": "desc", "missing": "_last"}}
            ]
            
        if self._highlight:
            query_body["highlight"] = self._highlight
            
        return query_body

class AdvancedQueryTemplates:
    """预定义的高级查询模板"""
    
    @staticmethod
    def asset_security_analysis(platform_id: str = None, risk_level: str = "high") -> QueryBuilder:
        """
        资产安全分析查询
        识别高风险资产和安全问题
        """
        builder = QueryBuilder()
        
        # 基础过滤
        if platform_id:
            builder.exact_match("entity_data.platform_id", platform_id)
        
        # 高风险条件
        if risk_level == "high":
            builder.query["bool"]["should"] = [
                {"term": {"entity_data.confidence": "low"}},
                {"range": {"entity_data.discovered_at": {"lte": "now-30d"}}},
                {"terms": {"entity_data.asset_type": ["api_endpoint", "admin_panel"]}},
                {"wildcard": {"entity_data.asset_value": "*admin*"}},
                {"wildcard": {"entity_data.asset_value": "*test*"}},
                {"wildcard": {"entity_data.asset_value": "*dev*"}}
            ]
            builder.query["bool"]["minimum_should_match"] = 1
        
        # 聚合分析
        builder.terms_aggregation("risk_by_type", "entity_data.asset_type")
        builder.terms_aggregation("confidence_levels", "entity_data.confidence")
        builder.date_histogram("discovery_timeline", "entity_data.discovered_at", "1d")
        
        return builder
    
    @staticmethod
    def asset_inventory_report(platform_id: str = None, project_id: str = None) -> QueryBuilder:
        """
        资产清单报告查询
        全面的资产统计和分类
        """
        builder = QueryBuilder()
        
        # 过滤条件
        if platform_id:
            builder.exact_match("entity_data.platform_id", platform_id)
        if project_id:
            builder.exact_match("entity_data.project_id", project_id)
        
        # 综合聚合
        builder.terms_aggregation("assets_by_type", "entity_data.asset_type", size=20)
        builder.terms_aggregation("assets_by_confidence", "entity_data.confidence")
        builder.terms_aggregation("assets_by_status", "entity_data.status")
        builder.terms_aggregation("top_sources", "entity_data.source_task_type", size=10)
        
        # 嵌套聚合 - 按标签分组
        builder.add_aggregation("assets_by_tags", {
            "terms": {
                "field": "entity_data.tags",
                "size": 20
            },
            "aggs": {
                "types": {
                    "terms": {
                        "field": "entity_data.asset_type",
                        "size": 5
                    }
                }
            }
        })
        
        # 时间趋势
        builder.date_histogram("discovery_trend", "entity_data.discovered_at", "1w")
        
        return builder
    
    @staticmethod
    def domain_analysis(domain_pattern: str = None) -> QueryBuilder:
        """
        域名资产分析
        专门针对域名和子域名的深度分析
        """
        builder = QueryBuilder()
        
        # 域名过滤
        builder.exact_match("entity_data.asset_type", ["domain", "subdomain"])
        
        if domain_pattern:
            builder.query["bool"]["should"] = [
                {"wildcard": {"entity_data.asset_value": f"*{domain_pattern}*"}},
                {"wildcard": {"entity_data.asset_host": f"*{domain_pattern}*"}}
            ]
            builder.query["bool"]["minimum_should_match"] = 1
        
        # 域名特定聚合
        builder.add_aggregation("domain_hierarchy", {
            "terms": {
                "script": {
                    "source": """
                    String value = doc['entity_data.asset_value'].value;
                    if (value != null) {
                        String[] parts = value.split('\\\\.');
                        if (parts.length >= 2) {
                            return parts[parts.length-2] + '.' + parts[parts.length-1];
                        }
                    }
                    return value;
                    """
                },
                "size": 50
            }
        })
        
        builder.add_aggregation("subdomain_stats", {
            "filter": {"term": {"entity_data.asset_type": "subdomain"}},
            "aggs": {
                "count": {"value_count": {"field": "entity_data.asset_value"}},
                "unique_hosts": {"cardinality": {"field": "entity_data.asset_host"}}
            }
        })
        
        return builder

class QueryValidator:
    """查询验证器"""
    
    @staticmethod
    def validate_query(query_body: Dict[str, Any]) -> tuple[bool, str]:
        """验证查询语法和安全性"""
        try:
            # 基础结构检查
            if not isinstance(query_body, dict):
                return False, "Query must be a dictionary"
            
            # 大小限制检查
            if query_body.get("size", 0) > 10000:
                return False, "Query size too large (max 10000)"
            
            # 脚本安全检查
            if QueryValidator._has_unsafe_script(query_body):
                return False, "Unsafe script detected"
            
            # 复杂度检查
            if QueryValidator._is_too_complex(query_body):
                return False, "Query too complex"
            
            return True, "Valid"
            
        except Exception as e:
            return False, f"Validation error: {str(e)}"
    
    @staticmethod
    def _has_unsafe_script(obj: Any) -> bool:
        """检查不安全的脚本"""
        if isinstance(obj, dict):
            for key, value in obj.items():
                if key == "script" and isinstance(value, dict):
                    source = value.get("source", "")
                    # 检查危险关键词
                    dangerous_keywords = [
                        "System", "Runtime", "Process", "File", 
                        "Network", "Socket", "Class", "Method"
                    ]
                    if any(keyword in source for keyword in dangerous_keywords):
                        return True
                elif QueryValidator._has_unsafe_script(value):
                    return True
        elif isinstance(obj, list):
            return any(QueryValidator._has_unsafe_script(item) for item in obj)
        return False
    
    @staticmethod
    def _is_too_complex(query_body: Dict[str, Any], max_depth: int = 10) -> bool:
        """检查查询复杂度"""
        def count_depth(obj, current_depth=0):
            if current_depth > max_depth:
                return True
            if isinstance(obj, dict):
                return any(count_depth(v, current_depth + 1) for v in obj.values())
            elif isinstance(obj, list):
                return any(count_depth(item, current_depth + 1) for item in obj)
            return False
        
        return count_depth(query_body)