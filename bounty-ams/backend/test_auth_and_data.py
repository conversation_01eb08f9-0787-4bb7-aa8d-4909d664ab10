#!/usr/bin/env python3
"""
测试认证API
"""

import asyncio
import aiohttp
import json

async def test_auth_and_data():
    """测试认证和数据获取"""
    base_url = "http://localhost:8000"
    
    async with aiohttp.ClientSession() as session:
        try:
            print("🔐 测试认证系统...")
            
            # 1. 登录获取token
            login_data = {
                "username": "admin",
                "password": "password"
            }
            
            async with session.post(f"{base_url}/api/auth/login", json=login_data) as response:
                print(f"登录状态码: {response.status}")
                if response.status == 200:
                    auth_data = await response.json()
                    token = auth_data.get('access_token')
                    print(f"✅ 登录成功，获得token: {token[:20]}...")
                else:
                    error_text = await response.text()
                    print(f"❌ 登录失败: {error_text}")
                    return
            
            # 2. 使用token获取模型类型
            headers = {"Authorization": f"Bearer {token}"}
            
            print("\n📋 测试获取模型类型...")
            async with session.get(f"{base_url}/api/dynamic-models/types", headers=headers) as response:
                print(f"状态码: {response.status}")
                if response.status == 200:
                    data = await response.json()
                    if isinstance(data, list):
                        model_types = data
                    else:
                        model_types = data.get('data', [])
                    print(f"✅ 成功获取 {len(model_types)} 个模型类型")
                    
                    # 找到platform和project模型
                    platform_model = next((m for m in model_types if m['name'] == 'platform'), None)
                    project_model = next((m for m in model_types if m['name'] == 'project'), None)
                    
                    if platform_model:
                        print(f"  📍 Platform模型ID: {platform_model['id']}")
                        
                        # 3. 获取平台数据
                        print("\n🏢 测试获取平台数据...")
                        async with session.get(f"{base_url}/api/dynamic-models/entities/{platform_model['id']}", headers=headers) as response:
                            print(f"状态码: {response.status}")
                            if response.status == 200:
                                data = await response.json()
                                if isinstance(data, list):
                                    platforms = data
                                else:
                                    platforms = data.get('data', [])
                                print(f"✅ 成功获取 {len(platforms)} 个平台")
                                for platform in platforms[:3]:
                                    print(f"  - {platform['entity_data']['display_name']}")
                            else:
                                error_text = await response.text()
                                print(f"❌ 获取平台数据失败: {error_text}")
                    
                    if project_model:
                        print(f"  📍 Project模型ID: {project_model['id']}")
                        
                        # 4. 获取项目数据
                        print("\n🎯 测试获取项目数据...")
                        async with session.get(f"{base_url}/api/dynamic-models/entities/{project_model['id']}", headers=headers) as response:
                            print(f"状态码: {response.status}")
                            if response.status == 200:
                                data = await response.json()
                                if isinstance(data, list):
                                    projects = data
                                else:
                                    projects = data.get('data', [])
                                print(f"✅ 成功获取 {len(projects)} 个项目")
                                for project in projects[:3]:
                                    print(f"  - {project['entity_data']['name']}")
                            else:
                                error_text = await response.text()
                                print(f"❌ 获取项目数据失败: {error_text}")
                else:
                    error_text = await response.text()
                    print(f"❌ 获取模型类型失败: {error_text}")
            
            print("\n🎉 认证和数据API测试完成！")
                    
        except Exception as e:
            print(f"❌ 测试时发生错误: {e}")

if __name__ == "__main__":
    asyncio.run(test_auth_and_data())