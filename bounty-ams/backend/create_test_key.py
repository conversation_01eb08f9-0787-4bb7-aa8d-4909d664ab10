#!/usr/bin/env python3
"""
Create a new API key for testing
"""
import asyncio
import sys
import os
import uuid
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import datetime
from database import get_db
from models_dynamic import User
from agent_key_service import AgentKeyService
from schemas_agent_key import <PERSON><PERSON><PERSON><PERSON><PERSON>
from sqlalchemy import select

async def create_test_api_key():
    """创建测试用API密钥"""
    
    async for db in get_db():
        try:
            # 获取admin用户
            result = await db.execute(select(User).where(User.username == "admin"))
            admin_user = result.scalar_one_or_none()
            
            if not admin_user:
                print("❌ Admin user not found")
                return
            
            # 创建新的API密钥
            key_data = AgentKeyCreate(
                name="Debug Test Key",
                description="API key for debugging agent heartbeat issues",
                expires_at=None
            )
            
            api_key_response = await AgentKeyService.create_agent_key(
                db=db,
                key_data=key_data,
                created_by_user_id=admin_user.id
            )
            
            print(f"✅ Created new API key:")
            print(f"   Key: {api_key_response.key_value}")
            print(f"   Key ID: {api_key_response.key_id}")
            print(f"   Status: {api_key_response.status}")
            
            break
        except Exception as e:
            print(f"❌ Error: {e}")
            await db.rollback()
            raise

if __name__ == "__main__":
    asyncio.run(create_test_api_key())