"""
资产管理 V2.0 统一架构
基于Elasticsearch的统一资产管理系统，支持大数据量、数据清洗、去重和Kibana集成

核心设计理念：
1. Elasticsearch为主数据存储，PostgreSQL为元数据存储
2. 统一数据管道：Agent发现 -> 动态模型 -> 手动导入 -> ES统一存储
3. 智能去重和数据清洗
4. 深度Kibana集成
5. 高性能搜索和分析
"""

from elasticsearch import AsyncElasticsearch
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_
from typing import Dict, List, Any, Optional, Union, Tuple
from datetime import datetime, timedelta
from enum import Enum
import json
import hashlib
import asyncio
from dataclasses import dataclass, asdict
import logging

from models_dynamic import User, ModelType, DynamicEntity
from elasticsearch_client import get_es_client
from elasticsearch_index_manager import ElasticsearchIndexManager, create_index_manager

logger = logging.getLogger(__name__)

class DataSource(Enum):
    """数据源类型"""
    AGENT_DISCOVERY = "agent_discovery"      # Agent自动发现
    DYNAMIC_MODEL = "dynamic_model"          # 动态模型数据
    MANUAL_IMPORT = "manual_import"          # 手动导入
    API_SYNC = "api_sync"                    # API同步
    WORKFLOW_RESULT = "workflow_result"      # 工作流结果

class AssetStatus(Enum):
    """资产状态"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    PENDING = "pending"
    VERIFIED = "verified"
    DEPRECATED = "deprecated"

class ConfidenceLevel(Enum):
    """置信度级别"""
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    UNKNOWN = "unknown"

@dataclass
class AssetFingerprint:
    """资产指纹，用于去重"""
    asset_type: str
    asset_value: str
    asset_host: Optional[str] = None
    port: Optional[int] = None
    service: Optional[str] = None
    
    def generate_hash(self) -> str:
        """生成指纹哈希"""
        content = f"{self.asset_type}:{self.asset_value}:{self.asset_host or ''}:{self.port or ''}:{self.service or ''}"
        return hashlib.sha256(content.encode()).hexdigest()

@dataclass
class AssetMetadata:
    """资产元数据"""
    source: DataSource
    confidence: ConfidenceLevel
    status: AssetStatus
    tags: List[str]
    platform_id: Optional[str] = None
    project_id: Optional[str] = None
    workflow_id: Optional[str] = None
    task_id: Optional[str] = None
    agent_id: Optional[str] = None
    discovered_at: Optional[datetime] = None
    verified_at: Optional[datetime] = None
    last_seen: Optional[datetime] = None
    raw_data: Optional[Dict[str, Any]] = None

@dataclass
class ProcessingResult:
    """处理结果"""
    total_processed: int = 0
    success_count: int = 0
    duplicate_count: int = 0
    error_count: int = 0
    cleaned_count: int = 0
    errors: List[str] = None
    
    def __post_init__(self):
        if self.errors is None:
            self.errors = []

class UnifiedAssetManager:
    """统一资产管理器 V2.0"""
    
    def __init__(self, es_client: AsyncElasticsearch, db_session: AsyncSession, index_manager: ElasticsearchIndexManager = None):
        self.es_client = es_client
        self.db_session = db_session
        self.index_manager = index_manager

        # 索引配置
        self.asset_index_pattern = "unified-assets-{date}"  # unified-assets-2025-07
        self.asset_template_name = "unified-assets-template"
        self.asset_alias = "unified-assets"
        
        # 去重配置
        self.dedup_similarity_threshold = 0.85
        self.dedup_time_window_hours = 24
        
        # 数据清洗配置
        self.required_fields = ["asset_type", "asset_value"]
        self.max_tag_length = 100
        self.max_tags_per_asset = 50
        
    async def initialize(self):
        """初始化ES索引模板和别名"""
        if self.index_manager:
            # 使用优化的索引管理器
            await self.index_manager.create_optimized_index_template()
            await self.index_manager.create_monthly_index()
        else:
            # 使用原有的初始化方法
            await self._create_unified_index_template()
            await self._create_index_alias()
        
    async def _create_unified_index_template(self):
        """创建统一资产索引模板"""
        template = {
            "index_patterns": ["unified-assets-*"],
            "template": {
                "settings": {
                    "number_of_shards": 3,
                    "number_of_replicas": 1,
                    "index.max_result_window": 50000,
                    "analysis": {
                        "analyzer": {
                            "asset_analyzer": {
                                "type": "custom",
                                "tokenizer": "keyword",
                                "filter": ["lowercase", "trim"]
                            },
                            "text_analyzer": {
                                "type": "custom", 
                                "tokenizer": "standard",
                                "filter": ["lowercase", "stop", "snowball"]
                            }
                        }
                    }
                },
                "mappings": {
                    "properties": {
                        # 核心资产字段
                        "asset_id": {"type": "keyword"},
                        "asset_type": {"type": "keyword"},
                        "asset_value": {
                            "type": "text",
                            "analyzer": "asset_analyzer",
                            "fields": {
                                "keyword": {"type": "keyword"},
                                "raw": {"type": "keyword", "index": False}
                            }
                        },
                        "asset_host": {"type": "keyword"},
                        "asset_port": {"type": "integer"},
                        "asset_service": {"type": "keyword"},
                        
                        # 指纹和去重
                        "fingerprint_hash": {"type": "keyword"},
                        "is_duplicate": {"type": "boolean"},
                        "duplicate_of": {"type": "keyword"},
                        "duplicate_score": {"type": "float"},
                        
                        # 元数据
                        "source": {"type": "keyword"},
                        "confidence": {"type": "keyword"},
                        "status": {"type": "keyword"},
                        "tags": {"type": "keyword"},
                        
                        # 关联信息
                        "platform_id": {"type": "keyword"},
                        "project_id": {"type": "keyword"},
                        "platform_name": {"type": "keyword"},
                        "project_name": {"type": "keyword"},
                        "workflow_id": {"type": "keyword"},
                        "task_id": {"type": "keyword"},
                        "agent_id": {"type": "keyword"},
                        
                        # 时间字段
                        "discovered_at": {"type": "date"},
                        "verified_at": {"type": "date"},
                        "last_seen": {"type": "date"},
                        "created_at": {"type": "date"},
                        "updated_at": {"type": "date"},
                        
                        # 技术栈和服务信息
                        "technology_stack": {
                            "type": "nested",
                            "properties": {
                                "name": {"type": "keyword"},
                                "version": {"type": "keyword"},
                                "category": {"type": "keyword"}
                            }
                        },
                        
                        # 端口和服务信息
                        "ports": {
                            "type": "nested",
                            "properties": {
                                "port": {"type": "integer"},
                                "protocol": {"type": "keyword"},
                                "service": {"type": "keyword"},
                                "version": {"type": "keyword"},
                                "state": {"type": "keyword"}
                            }
                        },
                        
                        # 漏洞关联
                        "vulnerabilities": {
                            "type": "nested",
                            "properties": {
                                "cve_id": {"type": "keyword"},
                                "severity": {"type": "keyword"},
                                "status": {"type": "keyword"},
                                "verified": {"type": "boolean"},
                                "verified_at": {"type": "date"}
                            }
                        },
                        
                        # 地理位置信息
                        "geo_location": {
                            "type": "geo_point"
                        },
                        "country": {"type": "keyword"},
                        "city": {"type": "keyword"},
                        "organization": {"type": "keyword"},
                        
                        # 原始数据和扩展字段
                        "raw_data": {"type": "object", "enabled": False},
                        "metadata": {"type": "object"},
                        "custom_fields": {"type": "object"},
                        
                        # 搜索优化字段
                        "searchable_text": {
                            "type": "text",
                            "analyzer": "text_analyzer"
                        },
                        
                        # 数据质量字段
                        "data_quality_score": {"type": "float"},
                        "validation_errors": {"type": "keyword"},
                        "processing_notes": {"type": "text"}
                    }
                }
            }
        }
        
        try:
            await self.es_client.indices.put_index_template(
                name=self.asset_template_name,
                body=template
            )
            logger.info(f"✅ 创建统一资产索引模板: {self.asset_template_name}")
        except Exception as e:
            logger.error(f"❌ 创建索引模板失败: {e}")
            raise
    
    async def _create_index_alias(self):
        """创建索引别名"""
        try:
            # 获取当前月份的索引名
            current_index = self.asset_index_pattern.format(
                date=datetime.utcnow().strftime("%Y-%m")
            )
            
            # 检查索引是否存在，不存在则创建
            if not await self.es_client.indices.exists(index=current_index):
                await self.es_client.indices.create(index=current_index)
                logger.info(f"✅ 创建索引: {current_index}")
            
            # 创建或更新别名
            await self.es_client.indices.put_alias(
                index=current_index,
                name=self.asset_alias
            )
            logger.info(f"✅ 创建索引别名: {self.asset_alias} -> {current_index}")
            
        except Exception as e:
            logger.error(f"❌ 创建索引别名失败: {e}")
            raise

    async def process_assets_unified(
        self,
        raw_assets: List[Dict[str, Any]],
        source: DataSource,
        metadata: Optional[AssetMetadata] = None,
        enable_dedup: bool = True,
        enable_cleaning: bool = True
    ) -> ProcessingResult:
        """统一资产处理入口"""
        result = ProcessingResult()
        result.total_processed = len(raw_assets)

        try:
            # 1. 数据清洗和标准化
            if enable_cleaning:
                cleaned_assets = await self._clean_and_normalize_assets(raw_assets)
                result.cleaned_count = len(cleaned_assets)
            else:
                cleaned_assets = raw_assets

            # 2. 转换为统一格式
            unified_assets = []
            for asset_data in cleaned_assets:
                try:
                    unified_asset = await self._convert_to_unified_format(
                        asset_data, source, metadata
                    )
                    if unified_asset:
                        unified_assets.append(unified_asset)
                        result.success_count += 1
                    else:
                        result.error_count += 1
                        result.errors.append(f"转换失败: {asset_data}")
                except Exception as e:
                    result.error_count += 1
                    result.errors.append(f"转换异常: {str(e)}")

            # 3. 智能去重
            if enable_dedup:
                deduplicated_assets, dup_count = await self._intelligent_deduplication(unified_assets)
                result.duplicate_count = dup_count
            else:
                deduplicated_assets = unified_assets

            # 4. 批量索引到ES
            if deduplicated_assets:
                await self._bulk_index_unified_assets(deduplicated_assets)

            logger.info(f"资产处理完成: {result}")
            return result

        except Exception as e:
            logger.error(f"资产处理失败: {e}")
            result.error_count = result.total_processed
            result.errors.append(f"处理异常: {str(e)}")
            return result

    async def _clean_and_normalize_assets(self, raw_assets: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """数据清洗和标准化"""
        cleaned_assets = []

        for asset in raw_assets:
            try:
                # 基础字段验证
                if not all(field in asset for field in self.required_fields):
                    continue

                # 数据清洗
                cleaned_asset = {
                    "asset_type": str(asset.get("asset_type", "")).lower().strip(),
                    "asset_value": str(asset.get("asset_value", "")).strip(),
                    "asset_host": str(asset.get("asset_host", "")).strip() or None,
                    "asset_port": self._normalize_port(asset.get("asset_port")),
                    "asset_service": str(asset.get("asset_service", "")).strip() or None,
                }

                # 清洗标签
                tags = asset.get("tags", [])
                if isinstance(tags, str):
                    tags = [tag.strip() for tag in tags.split(",")]
                cleaned_asset["tags"] = [
                    tag[:self.max_tag_length] for tag in tags[:self.max_tags_per_asset]
                    if tag and isinstance(tag, str)
                ]

                # 保留其他字段
                for key, value in asset.items():
                    if key not in cleaned_asset and value is not None:
                        cleaned_asset[key] = value

                cleaned_assets.append(cleaned_asset)

            except Exception as e:
                logger.warning(f"清洗资产数据失败: {e}, 数据: {asset}")
                continue

        return cleaned_assets

    def _normalize_port(self, port: Any) -> Optional[int]:
        """标准化端口号"""
        if port is None:
            return None
        try:
            port_int = int(port)
            return port_int if 1 <= port_int <= 65535 else None
        except (ValueError, TypeError):
            return None

    async def _convert_to_unified_format(
        self,
        asset_data: Dict[str, Any],
        source: DataSource,
        metadata: Optional[AssetMetadata] = None
    ) -> Optional[Dict[str, Any]]:
        """转换为统一格式"""
        try:
            # 创建资产指纹
            fingerprint = AssetFingerprint(
                asset_type=asset_data["asset_type"],
                asset_value=asset_data["asset_value"],
                asset_host=asset_data.get("asset_host"),
                port=asset_data.get("asset_port"),
                service=asset_data.get("asset_service")
            )

            # 生成资产ID
            asset_id = fingerprint.generate_hash()

            # 构建统一格式
            unified_asset = {
                "asset_id": asset_id,
                "fingerprint_hash": fingerprint.generate_hash(),
                "asset_type": asset_data["asset_type"],
                "asset_value": asset_data["asset_value"],
                "asset_host": asset_data.get("asset_host"),
                "asset_port": asset_data.get("asset_port"),
                "asset_service": asset_data.get("asset_service"),

                # 元数据
                "source": source.value,
                "confidence": metadata.confidence.value if metadata else ConfidenceLevel.MEDIUM.value,
                "status": metadata.status.value if metadata else AssetStatus.ACTIVE.value,
                "tags": asset_data.get("tags", []),

                # 关联信息
                "platform_id": metadata.platform_id if metadata else asset_data.get("platform_id"),
                "project_id": metadata.project_id if metadata else asset_data.get("project_id"),
                "workflow_id": metadata.workflow_id if metadata else asset_data.get("workflow_id"),
                "task_id": metadata.task_id if metadata else asset_data.get("task_id"),
                "agent_id": metadata.agent_id if metadata else asset_data.get("agent_id"),

                # 时间字段
                "discovered_at": metadata.discovered_at.isoformat() if metadata and metadata.discovered_at else datetime.utcnow().isoformat(),
                "last_seen": metadata.last_seen.isoformat() if metadata and metadata.last_seen else datetime.utcnow().isoformat(),
                "created_at": datetime.utcnow().isoformat(),
                "updated_at": datetime.utcnow().isoformat(),

                # 原始数据
                "raw_data": metadata.raw_data if metadata else asset_data,
                "metadata": asset_data.get("metadata", {}),

                # 搜索优化
                "searchable_text": self._build_searchable_text(asset_data),

                # 初始化去重字段
                "is_duplicate": False,
                "duplicate_of": None,
                "duplicate_score": 0.0,

                # 数据质量
                "data_quality_score": self._calculate_data_quality_score(asset_data),
                "validation_errors": [],
                "processing_notes": f"Processed by {source.value} at {datetime.utcnow().isoformat()}"
            }

            # 添加平台和项目名称
            if unified_asset["platform_id"]:
                unified_asset["platform_name"] = await self._get_platform_name(unified_asset["platform_id"])
            if unified_asset["project_id"]:
                unified_asset["project_name"] = await self._get_project_name(unified_asset["project_id"])

            return unified_asset

        except Exception as e:
            logger.error(f"转换统一格式失败: {e}")
            return None

    async def _intelligent_deduplication(
        self,
        assets: List[Dict[str, Any]]
    ) -> Tuple[List[Dict[str, Any]], int]:
        """智能去重算法"""
        deduplicated_assets = []
        duplicate_count = 0

        # 按指纹分组
        fingerprint_groups = {}
        for asset in assets:
            fingerprint = asset["fingerprint_hash"]
            if fingerprint not in fingerprint_groups:
                fingerprint_groups[fingerprint] = []
            fingerprint_groups[fingerprint].append(asset)

        for fingerprint, group_assets in fingerprint_groups.items():
            if len(group_assets) == 1:
                # 单个资产，检查ES中是否已存在
                asset = group_assets[0]
                existing_asset = await self._find_existing_asset(fingerprint)

                if existing_asset:
                    # 合并信息并更新
                    merged_asset = await self._merge_asset_data(existing_asset, asset)
                    deduplicated_assets.append(merged_asset)
                    duplicate_count += 1
                else:
                    # 新资产
                    deduplicated_assets.append(asset)
            else:
                # 多个相同指纹的资产，合并处理
                merged_asset = await self._merge_multiple_assets(group_assets)
                deduplicated_assets.append(merged_asset)
                duplicate_count += len(group_assets) - 1

        return deduplicated_assets, duplicate_count

    async def _find_existing_asset(self, fingerprint_hash: str) -> Optional[Dict[str, Any]]:
        """查找已存在的资产"""
        try:
            query = {
                "query": {
                    "term": {"fingerprint_hash": fingerprint_hash}
                },
                "size": 1
            }

            response = await self.es_client.search(
                index=self.asset_alias,
                body=query
            )

            hits = response.get("hits", {}).get("hits", [])
            return hits[0]["_source"] if hits else None

        except Exception as e:
            logger.error(f"查找已存在资产失败: {e}")
            return None

    async def _merge_asset_data(
        self,
        existing_asset: Dict[str, Any],
        new_asset: Dict[str, Any]
    ) -> Dict[str, Any]:
        """合并资产数据"""
        merged = existing_asset.copy()

        # 更新时间字段
        merged["last_seen"] = max(
            existing_asset.get("last_seen", ""),
            new_asset.get("last_seen", "")
        )
        merged["updated_at"] = datetime.utcnow().isoformat()

        # 合并标签
        existing_tags = set(existing_asset.get("tags", []))
        new_tags = set(new_asset.get("tags", []))
        merged["tags"] = list(existing_tags.union(new_tags))

        # 更新置信度（取最高）
        confidence_order = {"high": 3, "medium": 2, "low": 1, "unknown": 0}
        existing_conf = confidence_order.get(existing_asset.get("confidence", "unknown"), 0)
        new_conf = confidence_order.get(new_asset.get("confidence", "unknown"), 0)
        if new_conf > existing_conf:
            merged["confidence"] = new_asset["confidence"]

        # 合并元数据
        existing_metadata = existing_asset.get("metadata", {})
        new_metadata = new_asset.get("metadata", {})
        merged["metadata"] = {**existing_metadata, **new_metadata}

        # 记录处理信息
        merged["processing_notes"] = f"{existing_asset.get('processing_notes', '')}; Merged with {new_asset['source']} at {datetime.utcnow().isoformat()}"

        return merged

    async def _merge_multiple_assets(self, assets: List[Dict[str, Any]]) -> Dict[str, Any]:
        """合并多个相同指纹的资产"""
        if not assets:
            return {}

        # 以第一个为基础
        merged = assets[0].copy()

        # 合并所有资产的信息
        all_tags = set()
        all_sources = set()
        latest_time = ""
        highest_confidence = "unknown"

        confidence_order = {"high": 3, "medium": 2, "low": 1, "unknown": 0}

        for asset in assets:
            all_tags.update(asset.get("tags", []))
            all_sources.add(asset.get("source", ""))

            asset_time = asset.get("last_seen", "")
            if asset_time > latest_time:
                latest_time = asset_time

            asset_conf = asset.get("confidence", "unknown")
            if confidence_order.get(asset_conf, 0) > confidence_order.get(highest_confidence, 0):
                highest_confidence = asset_conf

        merged["tags"] = list(all_tags)
        merged["confidence"] = highest_confidence
        merged["last_seen"] = latest_time
        merged["updated_at"] = datetime.utcnow().isoformat()
        merged["processing_notes"] = f"Merged from sources: {', '.join(all_sources)} at {datetime.utcnow().isoformat()}"

        return merged

    def _build_searchable_text(self, asset_data: Dict[str, Any]) -> str:
        """构建可搜索文本"""
        searchable_parts = [
            asset_data.get("asset_value", ""),
            asset_data.get("asset_host", ""),
            asset_data.get("asset_service", ""),
            " ".join(asset_data.get("tags", [])),
        ]

        # 添加元数据中的文本
        metadata = asset_data.get("metadata", {})
        for key, value in metadata.items():
            if isinstance(value, str):
                searchable_parts.append(value)

        return " ".join(filter(None, searchable_parts))

    def _calculate_data_quality_score(self, asset_data: Dict[str, Any]) -> float:
        """计算数据质量分数"""
        score = 0.0
        max_score = 10.0

        # 必需字段完整性 (40%)
        required_score = 0.0
        if asset_data.get("asset_type"):
            required_score += 2.0
        if asset_data.get("asset_value"):
            required_score += 2.0
        score += required_score

        # 可选字段完整性 (30%)
        optional_fields = ["asset_host", "asset_port", "asset_service", "tags"]
        optional_score = sum(1.0 for field in optional_fields if asset_data.get(field)) * 0.75
        score += optional_score

        # 数据格式正确性 (20%)
        format_score = 2.0
        if asset_data.get("asset_port"):
            try:
                port = int(asset_data["asset_port"])
                if not (1 <= port <= 65535):
                    format_score -= 0.5
            except (ValueError, TypeError):
                format_score -= 0.5
        score += format_score

        # 元数据丰富度 (10%)
        metadata_score = min(1.0, len(asset_data.get("metadata", {})) * 0.1)
        score += metadata_score

        return min(max_score, score) / max_score  # 归一化到0-1

    async def _get_platform_name(self, platform_id: str) -> Optional[str]:
        """获取平台名称"""
        try:
            query = select(DynamicEntity).where(DynamicEntity.id == platform_id)
            result = await self.db_session.execute(query)
            entity = result.scalar_one_or_none()

            if entity and entity.entity_data:
                return entity.entity_data.get("display_name") or entity.entity_data.get("name")
            return None
        except Exception as e:
            logger.error(f"获取平台名称失败: {e}")
            return None

    async def _get_project_name(self, project_id: str) -> Optional[str]:
        """获取项目名称"""
        try:
            query = select(DynamicEntity).where(DynamicEntity.id == project_id)
            result = await self.db_session.execute(query)
            entity = result.scalar_one_or_none()

            if entity and entity.entity_data:
                return entity.entity_data.get("name")
            return None
        except Exception as e:
            logger.error(f"获取项目名称失败: {e}")
            return None

    async def _bulk_index_unified_assets(self, assets: List[Dict[str, Any]]):
        """批量索引资产到ES"""
        if not assets:
            return

        # 构建批量操作
        bulk_operations = []
        current_index = self.asset_index_pattern.format(
            date=datetime.utcnow().strftime("%Y-%m")
        )

        for asset in assets:
            # 使用asset_id作为文档ID，支持幂等操作
            doc_id = asset["asset_id"]

            bulk_operations.extend([
                {
                    "index": {
                        "_index": current_index,
                        "_id": doc_id
                    }
                },
                asset
            ])

        try:
            response = await self.es_client.bulk(
                body=bulk_operations,
                refresh=True
            )

            # 检查错误
            if response.get("errors"):
                error_count = sum(1 for item in response["items"] if "error" in item.get("index", {}))
                logger.warning(f"批量索引部分失败: {error_count} 个错误")
            else:
                logger.info(f"✅ 成功批量索引 {len(assets)} 个资产")

        except Exception as e:
            logger.error(f"❌ 批量索引失败: {e}")
            raise

    async def advanced_search(
        self,
        query: str = "*",
        filters: Optional[Dict[str, Any]] = None,
        aggregations: Optional[Dict[str, Any]] = None,
        page: int = 1,
        size: int = 20,
        sort: Optional[List[Dict[str, Any]]] = None
    ) -> Dict[str, Any]:
        """高级搜索功能"""
        if filters is None:
            filters = {}

        # 构建查询
        search_body = {
            "query": self._build_advanced_query(query, filters),
            "from": (page - 1) * size,
            "size": size,
            "track_total_hits": True
        }

        # 添加排序
        if sort:
            search_body["sort"] = sort
        else:
            search_body["sort"] = [
                {"updated_at": {"order": "desc"}},
                {"_score": {"order": "desc"}}
            ]

        # 添加聚合
        if aggregations:
            search_body["aggs"] = aggregations
        else:
            # 默认聚合
            search_body["aggs"] = {
                "asset_types": {
                    "terms": {"field": "asset_type", "size": 50}
                },
                "confidence_levels": {
                    "terms": {"field": "confidence", "size": 10}
                },
                "sources": {
                    "terms": {"field": "source", "size": 20}
                },
                "platforms": {
                    "terms": {"field": "platform_name.keyword", "size": 50}
                },
                "projects": {
                    "terms": {"field": "project_name.keyword", "size": 100}
                },
                "status_distribution": {
                    "terms": {"field": "status", "size": 10}
                },
                "discovery_timeline": {
                    "date_histogram": {
                        "field": "discovered_at",
                        "calendar_interval": "day",
                        "min_doc_count": 1
                    }
                },
                "data_quality": {
                    "histogram": {
                        "field": "data_quality_score",
                        "interval": 0.1,
                        "min_doc_count": 1
                    }
                }
            }

        try:
            response = await self.es_client.search(
                index=self.asset_alias,
                body=search_body
            )

            return {
                "hits": [hit["_source"] for hit in response["hits"]["hits"]],
                "total": response["hits"]["total"]["value"],
                "aggregations": response.get("aggregations", {}),
                "took": response.get("took", 0)
            }

        except Exception as e:
            logger.error(f"高级搜索失败: {e}")
            raise

    def _build_advanced_query(self, query: str, filters: Dict[str, Any]) -> Dict[str, Any]:
        """构建高级查询"""
        must_clauses = []
        filter_clauses = []

        # 主查询
        if query and query != "*":
            must_clauses.append({
                "multi_match": {
                    "query": query,
                    "fields": [
                        "asset_value^3",
                        "asset_host^2",
                        "searchable_text^1.5",
                        "tags^2",
                        "platform_name^2",
                        "project_name^2",
                        "metadata.*"
                    ],
                    "type": "best_fields",
                    "fuzziness": "AUTO"
                }
            })
        else:
            must_clauses.append({"match_all": {}})

        # 过滤条件
        for field, value in filters.items():
            if value is not None and value != "":
                if isinstance(value, list):
                    filter_clauses.append({"terms": {field: value}})
                elif field.endswith("_range") and isinstance(value, dict):
                    # 范围查询
                    field_name = field.replace("_range", "")
                    range_query = {}
                    if "gte" in value:
                        range_query["gte"] = value["gte"]
                    if "lte" in value:
                        range_query["lte"] = value["lte"]
                    if range_query:
                        filter_clauses.append({"range": {field_name: range_query}})
                else:
                    filter_clauses.append({"term": {field: value}})

        # 排除重复项（默认）
        filter_clauses.append({
            "bool": {
                "should": [
                    {"term": {"is_duplicate": False}},
                    {"bool": {"must_not": {"exists": {"field": "is_duplicate"}}}}
                ]
            }
        })

        return {
            "bool": {
                "must": must_clauses,
                "filter": filter_clauses
            }
        }

    async def get_asset_statistics(self) -> Dict[str, Any]:
        """获取资产统计信息"""
        try:
            # 基础统计查询
            stats_query = {
                "size": 0,
                "aggs": {
                    "total_assets": {
                        "value_count": {"field": "asset_id"}
                    },
                    "unique_assets": {
                        "cardinality": {"field": "fingerprint_hash"}
                    },
                    "asset_types": {
                        "terms": {"field": "asset_type", "size": 100}
                    },
                    "confidence_distribution": {
                        "terms": {"field": "confidence", "size": 10}
                    },
                    "source_distribution": {
                        "terms": {"field": "source", "size": 20}
                    },
                    "platform_count": {
                        "cardinality": {"field": "platform_id"}
                    },
                    "project_count": {
                        "cardinality": {"field": "project_id"}
                    },
                    "discovery_trend": {
                        "date_histogram": {
                            "field": "discovered_at",
                            "calendar_interval": "day",
                            "min_doc_count": 1
                        }
                    },
                    "avg_data_quality": {
                        "avg": {"field": "data_quality_score"}
                    },
                    "duplicate_count": {
                        "filter": {"term": {"is_duplicate": True}},
                        "aggs": {
                            "count": {"value_count": {"field": "asset_id"}}
                        }
                    }
                }
            }

            response = await self.es_client.search(
                index=self.asset_alias,
                body=stats_query
            )

            aggs = response.get("aggregations", {})

            return {
                "total_assets": aggs.get("total_assets", {}).get("value", 0),
                "unique_assets": aggs.get("unique_assets", {}).get("value", 0),
                "duplicate_count": aggs.get("duplicate_count", {}).get("count", {}).get("value", 0),
                "platform_count": aggs.get("platform_count", {}).get("value", 0),
                "project_count": aggs.get("project_count", {}).get("value", 0),
                "avg_data_quality": round(aggs.get("avg_data_quality", {}).get("value", 0), 2),
                "asset_types": {
                    bucket["key"]: bucket["doc_count"]
                    for bucket in aggs.get("asset_types", {}).get("buckets", [])
                },
                "confidence_distribution": {
                    bucket["key"]: bucket["doc_count"]
                    for bucket in aggs.get("confidence_distribution", {}).get("buckets", [])
                },
                "source_distribution": {
                    bucket["key"]: bucket["doc_count"]
                    for bucket in aggs.get("source_distribution", {}).get("buckets", [])
                },
                "discovery_trend": [
                    {
                        "date": bucket["key_as_string"],
                        "count": bucket["doc_count"]
                    }
                    for bucket in aggs.get("discovery_trend", {}).get("buckets", [])
                ]
            }

        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return {}

    async def advanced_deduplication_analysis(
        self,
        similarity_threshold: float = 0.85,
        time_window_hours: int = 24
    ) -> Dict[str, Any]:
        """高级去重分析"""
        try:
            # 查找潜在重复项
            query = {
                "size": 0,
                "aggs": {
                    "potential_duplicates": {
                        "terms": {
                            "field": "fingerprint_hash",
                            "min_doc_count": 2,
                            "size": 1000
                        },
                        "aggs": {
                            "assets": {
                                "top_hits": {
                                    "size": 10,
                                    "_source": [
                                        "asset_id", "asset_value", "asset_type",
                                        "discovered_at", "source", "confidence"
                                    ]
                                }
                            }
                        }
                    },
                    "similar_assets": {
                        "significant_text": {
                            "field": "searchable_text",
                            "size": 100
                        }
                    }
                }
            }

            response = await self.es_client.search(
                index=self.asset_alias,
                body=query
            )

            aggs = response.get("aggregations", {})

            # 分析重复项
            duplicate_groups = []
            for bucket in aggs.get("potential_duplicates", {}).get("buckets", []):
                fingerprint = bucket["key"]
                assets = bucket["assets"]["hits"]["hits"]

                if len(assets) > 1:
                    duplicate_groups.append({
                        "fingerprint": fingerprint,
                        "count": bucket["doc_count"],
                        "assets": [hit["_source"] for hit in assets]
                    })

            # 相似度分析
            similar_terms = []
            for bucket in aggs.get("similar_assets", {}).get("buckets", []):
                similar_terms.append({
                    "term": bucket["key"],
                    "score": bucket["score"],
                    "doc_count": bucket["doc_count"]
                })

            return {
                "duplicate_groups": duplicate_groups,
                "total_duplicate_groups": len(duplicate_groups),
                "total_duplicate_assets": sum(group["count"] for group in duplicate_groups),
                "similar_terms": similar_terms[:20],  # 前20个相似词
                "analysis_time": datetime.utcnow().isoformat()
            }

        except Exception as e:
            logger.error(f"去重分析失败: {e}")
            return {}

    async def cleanup_duplicates(
        self,
        dry_run: bool = True,
        merge_strategy: str = "keep_latest"
    ) -> Dict[str, Any]:
        """清理重复资产"""
        try:
            # 获取重复分析
            analysis = await self.advanced_deduplication_analysis()
            duplicate_groups = analysis.get("duplicate_groups", [])

            if not duplicate_groups:
                return {
                    "message": "没有发现重复资产",
                    "processed": 0,
                    "merged": 0,
                    "deleted": 0
                }

            processed = 0
            merged = 0
            deleted = 0
            operations = []

            for group in duplicate_groups:
                assets = group["assets"]
                if len(assets) < 2:
                    continue

                # 选择保留的资产
                if merge_strategy == "keep_latest":
                    # 保留最新的
                    assets.sort(key=lambda x: x.get("discovered_at", ""), reverse=True)
                elif merge_strategy == "keep_highest_confidence":
                    # 保留置信度最高的
                    confidence_order = {"high": 3, "medium": 2, "low": 1, "unknown": 0}
                    assets.sort(key=lambda x: confidence_order.get(x.get("confidence", "unknown"), 0), reverse=True)

                primary_asset = assets[0]
                duplicate_assets = assets[1:]

                # 合并信息到主资产
                merged_asset = await self._merge_multiple_assets(assets)

                if not dry_run:
                    # 更新主资产
                    operations.append({
                        "index": {
                            "_index": self.asset_alias,
                            "_id": primary_asset["asset_id"]
                        }
                    })
                    operations.append(merged_asset)

                    # 标记重复资产为删除
                    for dup_asset in duplicate_assets:
                        operations.append({
                            "update": {
                                "_index": self.asset_alias,
                                "_id": dup_asset["asset_id"]
                            }
                        })
                        operations.append({
                            "doc": {
                                "is_duplicate": True,
                                "duplicate_of": primary_asset["asset_id"],
                                "updated_at": datetime.utcnow().isoformat()
                            }
                        })

                processed += 1
                merged += 1
                deleted += len(duplicate_assets)

            # 执行批量操作
            if not dry_run and operations:
                await self.es_client.bulk(body=operations, refresh=True)

            return {
                "message": f"{'模拟' if dry_run else '实际'}清理完成",
                "processed": processed,
                "merged": merged,
                "deleted": deleted,
                "dry_run": dry_run
            }

        except Exception as e:
            logger.error(f"清理重复资产失败: {e}")
            raise

    async def data_quality_assessment(self) -> Dict[str, Any]:
        """数据质量评估"""
        try:
            query = {
                "size": 0,
                "aggs": {
                    "quality_distribution": {
                        "histogram": {
                            "field": "data_quality_score",
                            "interval": 0.1,
                            "min_doc_count": 1
                        }
                    },
                    "missing_fields": {
                        "filters": {
                            "filters": {
                                "missing_host": {"bool": {"must_not": {"exists": {"field": "asset_host"}}}},
                                "missing_port": {"bool": {"must_not": {"exists": {"field": "asset_port"}}}},
                                "missing_service": {"bool": {"must_not": {"exists": {"field": "asset_service"}}}},
                                "missing_tags": {"bool": {"must_not": {"exists": {"field": "tags"}}}},
                                "missing_platform": {"bool": {"must_not": {"exists": {"field": "platform_id"}}}},
                                "missing_project": {"bool": {"must_not": {"exists": {"field": "project_id"}}}}
                            }
                        }
                    },
                    "validation_errors": {
                        "terms": {
                            "field": "validation_errors",
                            "size": 20
                        }
                    },
                    "avg_quality_by_source": {
                        "terms": {
                            "field": "source",
                            "size": 20
                        },
                        "aggs": {
                            "avg_quality": {
                                "avg": {"field": "data_quality_score"}
                            }
                        }
                    },
                    "low_quality_assets": {
                        "filter": {
                            "range": {"data_quality_score": {"lt": 0.5}}
                        },
                        "aggs": {
                            "by_type": {
                                "terms": {"field": "asset_type", "size": 10}
                            }
                        }
                    }
                }
            }

            response = await self.es_client.search(
                index=self.asset_alias,
                body=query
            )

            aggs = response.get("aggregations", {})

            # 处理结果
            quality_distribution = [
                {
                    "score_range": f"{bucket['key']:.1f}-{bucket['key']+0.1:.1f}",
                    "count": bucket["doc_count"]
                }
                for bucket in aggs.get("quality_distribution", {}).get("buckets", [])
            ]

            missing_fields = {
                field: bucket["doc_count"]
                for field, bucket in aggs.get("missing_fields", {}).get("buckets", {}).items()
            }

            validation_errors = [
                {
                    "error": bucket["key"],
                    "count": bucket["doc_count"]
                }
                for bucket in aggs.get("validation_errors", {}).get("buckets", [])
            ]

            quality_by_source = [
                {
                    "source": bucket["key"],
                    "count": bucket["doc_count"],
                    "avg_quality": round(bucket["avg_quality"]["value"], 3)
                }
                for bucket in aggs.get("avg_quality_by_source", {}).get("buckets", [])
            ]

            low_quality_by_type = [
                {
                    "asset_type": bucket["key"],
                    "count": bucket["doc_count"]
                }
                for bucket in aggs.get("low_quality_assets", {}).get("by_type", {}).get("buckets", [])
            ]

            return {
                "quality_distribution": quality_distribution,
                "missing_fields": missing_fields,
                "validation_errors": validation_errors,
                "quality_by_source": quality_by_source,
                "low_quality_assets": {
                    "total": aggs.get("low_quality_assets", {}).get("doc_count", 0),
                    "by_type": low_quality_by_type
                },
                "assessment_time": datetime.utcnow().isoformat()
            }

        except Exception as e:
            logger.error(f"数据质量评估失败: {e}")
            return {}

    async def enhance_data_quality(
        self,
        fix_missing_fields: bool = True,
        normalize_values: bool = True,
        validate_formats: bool = True
    ) -> Dict[str, Any]:
        """提升数据质量"""
        try:
            # 查找需要改进的资产
            query = {
                "query": {
                    "range": {"data_quality_score": {"lt": 0.8}}
                },
                "size": 1000
            }

            response = await self.es_client.search(
                index=self.asset_alias,
                body=query
            )

            assets = [hit["_source"] for hit in response["hits"]["hits"]]

            if not assets:
                return {"message": "没有需要改进的资产", "processed": 0}

            enhanced_count = 0
            operations = []

            for asset in assets:
                enhanced_asset = asset.copy()
                improved = False

                # 修复缺失字段
                if fix_missing_fields:
                    if not enhanced_asset.get("asset_host") and enhanced_asset.get("asset_value"):
                        # 尝试从asset_value中提取host
                        value = enhanced_asset["asset_value"]
                        if "://" in value:
                            try:
                                from urllib.parse import urlparse
                                parsed = urlparse(value)
                                enhanced_asset["asset_host"] = parsed.hostname
                                improved = True
                            except:
                                pass

                # 标准化值
                if normalize_values:
                    if enhanced_asset.get("asset_value"):
                        original_value = enhanced_asset["asset_value"]
                        normalized_value = original_value.lower().strip()
                        if normalized_value != original_value:
                            enhanced_asset["asset_value"] = normalized_value
                            improved = True

                # 格式验证
                if validate_formats:
                    validation_errors = []

                    # 验证端口
                    if enhanced_asset.get("asset_port"):
                        try:
                            port = int(enhanced_asset["asset_port"])
                            if not (1 <= port <= 65535):
                                validation_errors.append("invalid_port_range")
                        except (ValueError, TypeError):
                            validation_errors.append("invalid_port_format")

                    # 验证IP地址
                    if enhanced_asset.get("asset_type") == "ip":
                        import ipaddress
                        try:
                            ipaddress.ip_address(enhanced_asset["asset_value"])
                        except ValueError:
                            validation_errors.append("invalid_ip_format")

                    enhanced_asset["validation_errors"] = validation_errors
                    improved = True

                if improved:
                    # 重新计算质量分数
                    enhanced_asset["data_quality_score"] = self._calculate_data_quality_score(enhanced_asset)
                    enhanced_asset["updated_at"] = datetime.utcnow().isoformat()
                    enhanced_asset["processing_notes"] = f"{enhanced_asset.get('processing_notes', '')}; Quality enhanced at {datetime.utcnow().isoformat()}"

                    operations.extend([
                        {"index": {"_index": self.asset_alias, "_id": enhanced_asset["asset_id"]}},
                        enhanced_asset
                    ])
                    enhanced_count += 1

            # 执行批量更新
            if operations:
                await self.es_client.bulk(body=operations, refresh=True)

            return {
                "message": "数据质量提升完成",
                "processed": len(assets),
                "enhanced": enhanced_count,
                "improvement_rate": f"{enhanced_count/len(assets)*100:.1f}%" if assets else "0%"
            }

        except Exception as e:
            logger.error(f"数据质量提升失败: {e}")
            raise

    async def trend_analysis(
        self,
        time_range: str = "30d",
        interval: str = "1d",
        metrics: List[str] = None
    ) -> Dict[str, Any]:
        """趋势分析"""
        if metrics is None:
            metrics = ["discovery_count", "asset_types", "confidence_levels", "data_quality"]

        try:
            # 构建时间范围查询
            query = {
                "size": 0,
                "query": {
                    "range": {
                        "discovered_at": {
                            "gte": f"now-{time_range}"
                        }
                    }
                },
                "aggs": {}
            }

            # 添加趋势聚合
            if "discovery_count" in metrics:
                query["aggs"]["discovery_trend"] = {
                    "date_histogram": {
                        "field": "discovered_at",
                        "calendar_interval": interval,
                        "min_doc_count": 0,
                        "extended_bounds": {
                            "min": f"now-{time_range}",
                            "max": "now"
                        }
                    }
                }

            if "asset_types" in metrics:
                query["aggs"]["asset_type_trend"] = {
                    "date_histogram": {
                        "field": "discovered_at",
                        "calendar_interval": interval,
                        "min_doc_count": 0
                    },
                    "aggs": {
                        "types": {
                            "terms": {"field": "asset_type", "size": 10}
                        }
                    }
                }

            if "confidence_levels" in metrics:
                query["aggs"]["confidence_trend"] = {
                    "date_histogram": {
                        "field": "discovered_at",
                        "calendar_interval": interval,
                        "min_doc_count": 0
                    },
                    "aggs": {
                        "confidence": {
                            "terms": {"field": "confidence", "size": 5}
                        }
                    }
                }

            if "data_quality" in metrics:
                query["aggs"]["quality_trend"] = {
                    "date_histogram": {
                        "field": "discovered_at",
                        "calendar_interval": interval,
                        "min_doc_count": 0
                    },
                    "aggs": {
                        "avg_quality": {
                            "avg": {"field": "data_quality_score"}
                        }
                    }
                }

            response = await self.es_client.search(
                index=self.asset_alias,
                body=query
            )

            aggs = response.get("aggregations", {})

            # 处理结果
            trends = {}

            if "discovery_trend" in aggs:
                trends["discovery_count"] = [
                    {
                        "date": bucket["key_as_string"],
                        "count": bucket["doc_count"]
                    }
                    for bucket in aggs["discovery_trend"]["buckets"]
                ]

            if "asset_type_trend" in aggs:
                trends["asset_types"] = [
                    {
                        "date": bucket["key_as_string"],
                        "types": {
                            type_bucket["key"]: type_bucket["doc_count"]
                            for type_bucket in bucket["types"]["buckets"]
                        }
                    }
                    for bucket in aggs["asset_type_trend"]["buckets"]
                ]

            if "confidence_trend" in aggs:
                trends["confidence_levels"] = [
                    {
                        "date": bucket["key_as_string"],
                        "confidence": {
                            conf_bucket["key"]: conf_bucket["doc_count"]
                            for conf_bucket in bucket["confidence"]["buckets"]
                        }
                    }
                    for bucket in aggs["confidence_trend"]["buckets"]
                ]

            if "quality_trend" in aggs:
                trends["data_quality"] = [
                    {
                        "date": bucket["key_as_string"],
                        "avg_quality": round(bucket["avg_quality"]["value"] or 0, 3)
                    }
                    for bucket in aggs["quality_trend"]["buckets"]
                ]

            return {
                "time_range": time_range,
                "interval": interval,
                "trends": trends,
                "analysis_time": datetime.utcnow().isoformat()
            }

        except Exception as e:
            logger.error(f"趋势分析失败: {e}")
            return {}

    async def correlation_analysis(
        self,
        field1: str,
        field2: str,
        sample_size: int = 1000
    ) -> Dict[str, Any]:
        """相关性分析"""
        try:
            # 获取样本数据
            query = {
                "size": sample_size,
                "_source": [field1, field2],
                "query": {
                    "bool": {
                        "must": [
                            {"exists": {"field": field1}},
                            {"exists": {"field": field2}}
                        ]
                    }
                }
            }

            response = await self.es_client.search(
                index=self.asset_alias,
                body=query
            )

            # 提取数据
            data_points = []
            for hit in response["hits"]["hits"]:
                source = hit["_source"]
                value1 = source.get(field1)
                value2 = source.get(field2)

                if value1 is not None and value2 is not None:
                    data_points.append((value1, value2))

            if len(data_points) < 2:
                return {
                    "error": "数据点不足，无法进行相关性分析",
                    "sample_size": len(data_points)
                }

            # 计算相关性（简单实现）
            # 这里可以集成更复杂的统计分析库
            correlation_matrix = self._calculate_correlation(data_points)

            return {
                "field1": field1,
                "field2": field2,
                "sample_size": len(data_points),
                "correlation": correlation_matrix,
                "analysis_time": datetime.utcnow().isoformat()
            }

        except Exception as e:
            logger.error(f"相关性分析失败: {e}")
            return {}

    def _calculate_correlation(self, data_points: List[Tuple]) -> Dict[str, Any]:
        """计算相关性（简化版本）"""
        try:
            # 这里实现简单的相关性计算
            # 在实际应用中，建议使用numpy/scipy等库

            n = len(data_points)
            if n < 2:
                return {"correlation": 0, "strength": "无法计算"}

            # 简单的共现分析
            unique_pairs = len(set(data_points))
            diversity = unique_pairs / n

            # 简化的相关性指标
            if diversity > 0.8:
                strength = "弱相关"
                correlation = 0.3
            elif diversity > 0.5:
                strength = "中等相关"
                correlation = 0.6
            else:
                strength = "强相关"
                correlation = 0.9

            return {
                "correlation": correlation,
                "strength": strength,
                "diversity": diversity,
                "unique_combinations": unique_pairs
            }

        except Exception as e:
            logger.error(f"计算相关性失败: {e}")
            return {"correlation": 0, "strength": "计算失败"}

    async def anomaly_detection(
        self,
        field: str = "data_quality_score",
        threshold_std: float = 2.0
    ) -> Dict[str, Any]:
        """异常检测"""
        try:
            # 获取统计信息
            stats_query = {
                "size": 0,
                "aggs": {
                    "field_stats": {
                        "stats": {"field": field}
                    },
                    "percentiles": {
                        "percentiles": {
                            "field": field,
                            "percents": [1, 5, 25, 50, 75, 95, 99]
                        }
                    }
                }
            }

            stats_response = await self.es_client.search(
                index=self.asset_alias,
                body=stats_query
            )

            stats = stats_response["aggregations"]["field_stats"]
            percentiles = stats_response["aggregations"]["percentiles"]["values"]

            # 计算异常阈值
            mean = stats["avg"]
            std_dev = stats["std_deviation"]

            lower_threshold = mean - (threshold_std * std_dev)
            upper_threshold = mean + (threshold_std * std_dev)

            # 查找异常值
            anomaly_query = {
                "size": 100,
                "query": {
                    "bool": {
                        "should": [
                            {"range": {field: {"lt": lower_threshold}}},
                            {"range": {field: {"gt": upper_threshold}}}
                        ]
                    }
                },
                "_source": ["asset_id", "asset_type", "asset_value", field, "discovered_at"]
            }

            anomaly_response = await self.es_client.search(
                index=self.asset_alias,
                body=anomaly_query
            )

            anomalies = []
            for hit in anomaly_response["hits"]["hits"]:
                source = hit["_source"]
                value = source.get(field)

                if value is not None:
                    anomaly_type = "low" if value < lower_threshold else "high"
                    severity = "critical" if (value < percentiles["1.0"] or value > percentiles["99.0"]) else "warning"

                    anomalies.append({
                        "asset_id": source.get("asset_id"),
                        "asset_type": source.get("asset_type"),
                        "asset_value": source.get("asset_value"),
                        "field_value": value,
                        "anomaly_type": anomaly_type,
                        "severity": severity,
                        "deviation": abs(value - mean) / std_dev if std_dev > 0 else 0,
                        "discovered_at": source.get("discovered_at")
                    })

            return {
                "field": field,
                "statistics": {
                    "mean": round(mean, 3),
                    "std_deviation": round(std_dev, 3),
                    "min": stats["min"],
                    "max": stats["max"],
                    "count": stats["count"]
                },
                "thresholds": {
                    "lower": round(lower_threshold, 3),
                    "upper": round(upper_threshold, 3),
                    "std_multiplier": threshold_std
                },
                "percentiles": {
                    f"p{int(k)}": round(v, 3)
                    for k, v in percentiles.items()
                },
                "anomalies": anomalies,
                "anomaly_count": len(anomalies),
                "analysis_time": datetime.utcnow().isoformat()
            }

        except Exception as e:
            logger.error(f"异常检测失败: {e}")
            return {}

    async def security_risk_assessment(self) -> Dict[str, Any]:
        """安全风险评估"""
        try:
            query = {
                "size": 0,
                "aggs": {
                    # 高风险端口分析
                    "high_risk_ports": {
                        "terms": {
                            "field": "asset_port",
                            "include": [21, 22, 23, 25, 53, 80, 110, 143, 443, 993, 995, 3389, 5432, 3306],
                            "size": 20
                        }
                    },

                    # 服务暴露分析
                    "exposed_services": {
                        "terms": {
                            "field": "asset_service",
                            "size": 20
                        }
                    },

                    # 未验证资产
                    "unverified_assets": {
                        "filter": {
                            "terms": {"confidence": ["low", "unknown"]}
                        },
                        "aggs": {
                            "by_type": {
                                "terms": {"field": "asset_type", "size": 10}
                            }
                        }
                    },

                    # 漏洞关联分析
                    "vulnerability_exposure": {
                        "nested": {
                            "path": "vulnerabilities"
                        },
                        "aggs": {
                            "severity_distribution": {
                                "terms": {"field": "vulnerabilities.severity", "size": 10}
                            },
                            "unverified_vulns": {
                                "filter": {"term": {"vulnerabilities.verified": False}},
                                "aggs": {
                                    "count": {"value_count": {"field": "vulnerabilities.cve_id"}}
                                }
                            }
                        }
                    },

                    # 地理风险分析
                    "geographic_risk": {
                        "terms": {
                            "field": "country",
                            "size": 20
                        }
                    }
                }
            }

            response = await self.es_client.search(
                index=self.asset_alias,
                body=query
            )

            aggs = response.get("aggregations", {})

            # 计算风险评分
            risk_factors = []
            total_risk_score = 0

            # 高风险端口评分
            high_risk_ports = aggs.get("high_risk_ports", {}).get("buckets", [])
            if high_risk_ports:
                port_risk = sum(bucket["doc_count"] for bucket in high_risk_ports)
                risk_factors.append({
                    "factor": "high_risk_ports",
                    "score": min(port_risk * 0.1, 10),
                    "description": f"发现 {port_risk} 个高风险端口暴露"
                })
                total_risk_score += min(port_risk * 0.1, 10)

            # 未验证资产评分
            unverified_count = aggs.get("unverified_assets", {}).get("doc_count", 0)
            if unverified_count > 0:
                unverified_risk = min(unverified_count * 0.05, 8)
                risk_factors.append({
                    "factor": "unverified_assets",
                    "score": unverified_risk,
                    "description": f"{unverified_count} 个资产未经验证"
                })
                total_risk_score += unverified_risk

            # 漏洞暴露评分
            vuln_aggs = aggs.get("vulnerability_exposure", {})
            if vuln_aggs:
                critical_vulns = sum(
                    bucket["doc_count"] for bucket in vuln_aggs.get("severity_distribution", {}).get("buckets", [])
                    if bucket["key"] in ["critical", "high"]
                )
                if critical_vulns > 0:
                    vuln_risk = min(critical_vulns * 0.2, 15)
                    risk_factors.append({
                        "factor": "critical_vulnerabilities",
                        "score": vuln_risk,
                        "description": f"{critical_vulns} 个高危漏洞"
                    })
                    total_risk_score += vuln_risk

            # 风险等级
            if total_risk_score >= 20:
                risk_level = "critical"
            elif total_risk_score >= 15:
                risk_level = "high"
            elif total_risk_score >= 10:
                risk_level = "medium"
            elif total_risk_score >= 5:
                risk_level = "low"
            else:
                risk_level = "minimal"

            return {
                "overall_risk": {
                    "score": round(total_risk_score, 2),
                    "level": risk_level,
                    "max_score": 50
                },
                "risk_factors": risk_factors,
                "detailed_analysis": {
                    "high_risk_ports": [
                        {"port": bucket["key"], "count": bucket["doc_count"]}
                        for bucket in high_risk_ports
                    ],
                    "exposed_services": [
                        {"service": bucket["key"], "count": bucket["doc_count"]}
                        for bucket in aggs.get("exposed_services", {}).get("buckets", [])
                    ],
                    "unverified_by_type": [
                        {"type": bucket["key"], "count": bucket["doc_count"]}
                        for bucket in aggs.get("unverified_assets", {}).get("by_type", {}).get("buckets", [])
                    ],
                    "geographic_distribution": [
                        {"country": bucket["key"], "count": bucket["doc_count"]}
                        for bucket in aggs.get("geographic_risk", {}).get("buckets", [])
                    ]
                },
                "recommendations": self._generate_security_recommendations(risk_factors),
                "assessment_time": datetime.utcnow().isoformat()
            }

        except Exception as e:
            logger.error(f"安全风险评估失败: {e}")
            return {}

    def _generate_security_recommendations(self, risk_factors: List[Dict]) -> List[str]:
        """生成安全建议"""
        recommendations = []

        for factor in risk_factors:
            if factor["factor"] == "high_risk_ports":
                recommendations.append("建议审查高风险端口暴露，关闭不必要的服务")
            elif factor["factor"] == "unverified_assets":
                recommendations.append("建议验证未确认的资产，提高数据可信度")
            elif factor["factor"] == "critical_vulnerabilities":
                recommendations.append("优先修复高危漏洞，建立漏洞管理流程")

        # 通用建议
        recommendations.extend([
            "定期进行安全扫描和风险评估",
            "建立资产清单管理制度",
            "实施网络分段和访问控制",
            "建立安全监控和告警机制"
        ])

        return recommendations

# 创建管理器实例的工厂函数
async def create_unified_asset_manager(db_session: AsyncSession = None) -> UnifiedAssetManager:
    """创建统一资产管理器实例"""
    es_client = await get_es_client()
    if db_session is None:
        raise ValueError("db_session is required")

    # 创建优化的索引管理器
    index_manager = await create_index_manager(es_client)

    # 创建资产管理器
    manager = UnifiedAssetManager(es_client, db_session, index_manager)
    await manager.initialize()

    return manager
