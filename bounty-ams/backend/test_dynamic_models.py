#!/usr/bin/env python3
"""
Dynamic Model System Test Suite
测试完全动态的模型管理系统
"""

import asyncio
import aiohttp
import json
from typing import Dict, Any, Optional, List
import sys
from datetime import datetime

class DynamicModelTester:
    def __init__(self, base_url: str = "http://localhost:8090"):
        self.base_url = base_url
        self.session = None
        self.access_token = None
        self.headers = {}
        self.created_model_types = []
        self.created_entities = []
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def login(self, username: str = "admin", password: str = "admin123"):
        """登录获取访问令牌"""
        login_data = {
            "username": username,
            "password": password
        }
        
        async with self.session.post(
            f"{self.base_url}/api/auth/login",
            json=login_data
        ) as response:
            if response.status == 200:
                data = await response.json()
                self.access_token = data["access_token"]
                self.headers = {"Authorization": f"Bearer {self.access_token}"}
                print(f"✓ 登录成功")
                return True
            else:
                print(f"❌ 登录失败: {response.status}")
                return False
    
    async def test_get_field_types(self):
        """测试获取支持的字段类型"""
        print("\n🧪 测试获取字段类型...")
        
        async with self.session.get(
            f"{self.base_url}/api/dynamic-models/field-types/",
            headers=self.headers
        ) as response:
            if response.status == 200:
                data = await response.json()
                print(f"✓ 获取到 {len(data)} 种字段类型")
                for field_type in data[:3]:  # 显示前3种
                    print(f"  - {field_type['display_name']}: {field_type['description']}")
                return data
            else:
                print(f"❌ 获取字段类型失败: {response.status}")
                return None
    
    async def test_create_custom_model_type(self):
        """测试创建自定义模型类型"""
        print("\n🧪 测试创建自定义模型类型...")
        
        # 创建一个"客户信息"模型
        model_data = {
            "name": "customer_info",
            "display_name": "客户信息",
            "description": "客户基本信息管理",
            "icon": "👤",
            "color": "#3B82F6",
            "is_active": True,
            "fields": [
                {
                    "field_name": "company_name",
                    "field_type": "text",
                    "display_name": "公司名称",
                    "description": "客户公司的名称",
                    "is_required": True,
                    "is_searchable": True,
                    "is_filterable": True,
                    "sort_order": 0
                },
                {
                    "field_name": "contact_email",
                    "field_type": "email",
                    "display_name": "联系邮箱",
                    "description": "主要联系人邮箱",
                    "is_required": True,
                    "is_searchable": True,
                    "is_filterable": True,
                    "is_unique": True,
                    "sort_order": 1
                },
                {
                    "field_name": "industry",
                    "field_type": "select",
                    "display_name": "行业",
                    "description": "客户所属行业",
                    "is_required": False,
                    "is_searchable": False,
                    "is_filterable": True,
                    "field_options": {
                        "options": [
                            {"value": "tech", "label": "科技"},
                            {"value": "finance", "label": "金融"},
                            {"value": "healthcare", "label": "医疗"},
                            {"value": "education", "label": "教育"},
                            {"value": "other", "label": "其他"}
                        ]
                    },
                    "sort_order": 2
                },
                {
                    "field_name": "annual_revenue",
                    "field_type": "number",
                    "display_name": "年收入",
                    "description": "年度收入（万元）",
                    "is_required": False,
                    "is_searchable": False,
                    "is_filterable": True,
                    "validation_rules": {"min": 0, "max": 100000},
                    "sort_order": 3
                },
                {
                    "field_name": "contract_date",
                    "field_type": "date",
                    "display_name": "签约日期",
                    "description": "与客户签约的日期",
                    "is_required": False,
                    "is_searchable": False,
                    "is_filterable": True,
                    "sort_order": 4
                },
                {
                    "field_name": "is_active",
                    "field_type": "boolean",
                    "display_name": "是否活跃",
                    "description": "客户是否为活跃状态",
                    "is_required": False,
                    "is_searchable": False,
                    "is_filterable": True,
                    "default_value": "true",
                    "sort_order": 5
                },
                {
                    "field_name": "notes",
                    "field_type": "textarea",
                    "display_name": "备注",
                    "description": "关于客户的额外信息",
                    "is_required": False,
                    "is_searchable": True,
                    "is_filterable": False,
                    "sort_order": 6
                }
            ]
        }
        
        async with self.session.post(
            f"{self.base_url}/api/dynamic-models/types",
            json=model_data,
            headers=self.headers
        ) as response:
            if response.status == 201:
                data = await response.json()
                self.created_model_types.append(data["id"])
                print(f"✓ 创建模型类型成功: {data['display_name']} (ID: {data['id']})")
                print(f"  - 包含 {len(data['fields'])} 个字段")
                return data
            else:
                error_text = await response.text()
                print(f"❌ 创建模型类型失败: {response.status} - {error_text}")
                return None
    
    async def test_get_model_types(self):
        """测试获取模型类型列表"""
        print("\n🧪 测试获取模型类型列表...")
        
        async with self.session.get(
            f"{self.base_url}/api/dynamic-models/types",
            headers=self.headers
        ) as response:
            if response.status == 200:
                data = await response.json()
                print(f"✓ 获取到 {len(data)} 个模型类型")
                for model_type in data:
                    print(f"  - {model_type['display_name']} ({model_type['name']}) - {len(model_type['fields'])} 字段")
                return data
            else:
                print(f"❌ 获取模型类型列表失败: {response.status}")
                return None
    
    async def test_create_dynamic_entities(self, model_type_id: str):
        """测试创建动态实体"""
        print(f"\n🧪 测试创建动态实体...")
        
        # 创建几个客户信息实体
        entities_data = [
            {
                "model_type_id": model_type_id,
                "entity_data": {
                    "company_name": "科技有限公司",
                    "contact_email": "<EMAIL>",
                    "industry": "tech",
                    "annual_revenue": 5000,
                    "contract_date": "2024-01-15",
                    "is_active": True,
                    "notes": "重要的科技客户，主要从事软件开发业务"
                }
            },
            {
                "model_type_id": model_type_id,
                "entity_data": {
                    "company_name": "金融投资集团",
                    "contact_email": "<EMAIL>",
                    "industry": "finance",
                    "annual_revenue": 20000,
                    "contract_date": "2024-02-20",
                    "is_active": True,
                    "notes": "大型金融客户，有多个子公司"
                }
            },
            {
                "model_type_id": model_type_id,
                "entity_data": {
                    "company_name": "教育科技公司",
                    "contact_email": "<EMAIL>",
                    "industry": "education",
                    "annual_revenue": 1200,
                    "contract_date": "2024-03-10",
                    "is_active": False,
                    "notes": "中小型教育客户，合同已暂停"
                }
            }
        ]
        
        created_count = 0
        for i, entity_data in enumerate(entities_data):
            async with self.session.post(
                f"{self.base_url}/api/dynamic-models/entities",
                json=entity_data,
                headers=self.headers
            ) as response:
                if response.status == 201:
                    data = await response.json()
                    self.created_entities.append(data["id"])
                    created_count += 1
                    print(f"✓ 创建实体 {i+1}: {data['entity_data']['company_name']}")
                else:
                    error_text = await response.text()
                    print(f"❌ 创建实体 {i+1} 失败: {response.status} - {error_text}")
        
        print(f"✓ 成功创建 {created_count} 个动态实体")
        return created_count > 0
    
    async def test_get_dynamic_entities(self, model_type_id: str):
        """测试获取动态实体列表"""
        print(f"\n🧪 测试获取动态实体列表...")
        
        async with self.session.get(
            f"{self.base_url}/api/dynamic-models/entities?model_type_id={model_type_id}",
            headers=self.headers
        ) as response:
            if response.status == 200:
                data = await response.json()
                print(f"✓ 获取到 {len(data)} 个实体")
                for entity in data:
                    company_name = entity.get('entity_data', {}).get('company_name', 'Unknown')
                    print(f"  - {company_name} (ID: {entity['id']})")
                return data
            else:
                print(f"❌ 获取实体列表失败: {response.status}")
                return None
    
    async def test_update_dynamic_entity(self, entity_id: str):
        """测试更新动态实体"""
        print(f"\n🧪 测试更新动态实体: {entity_id}...")
        
        update_data = {
            "entity_data": {
                "company_name": "科技有限公司（已更新）",
                "contact_email": "<EMAIL>",
                "industry": "tech",
                "annual_revenue": 6000,
                "contract_date": "2024-01-15",
                "is_active": True,
                "notes": "重要的科技客户，已更新联系方式和收入信息"
            }
        }
        
        async with self.session.put(
            f"{self.base_url}/api/dynamic-models/entities/{entity_id}",
            json=update_data,
            headers=self.headers
        ) as response:
            if response.status == 200:
                data = await response.json()
                print(f"✓ 实体更新成功: {data['entity_data']['company_name']}")
                return data
            else:
                error_text = await response.text()
                print(f"❌ 更新实体失败: {response.status} - {error_text}")
                return None
    
    async def test_search_entities(self, model_type_id: str):
        """测试搜索实体"""
        print(f"\n🧪 测试搜索实体...")
        
        # 搜索包含"科技"的实体
        async with self.session.get(
            f"{self.base_url}/api/dynamic-models/entities?model_type_id={model_type_id}&search=科技",
            headers=self.headers
        ) as response:
            if response.status == 200:
                data = await response.json()
                print(f"✓ 搜索 '科技' 返回 {len(data)} 个结果")
                return len(data) > 0
            else:
                print(f"❌ 搜索实体失败: {response.status}")
                return False
    
    async def test_update_model_type(self, model_type_id: str):
        """测试更新模型类型"""
        print(f"\n🧪 测试更新模型类型: {model_type_id}...")
        
        # 添加一个新字段
        update_data = {
            "description": "客户基本信息管理（已更新）",
            "fields": [
                {
                    "field_name": "company_name",
                    "field_type": "text",
                    "display_name": "公司名称",
                    "description": "客户公司的名称",
                    "is_required": True,
                    "is_searchable": True,
                    "is_filterable": True,
                    "sort_order": 0
                },
                {
                    "field_name": "contact_email",
                    "field_type": "email",
                    "display_name": "联系邮箱",
                    "description": "主要联系人邮箱",
                    "is_required": True,
                    "is_searchable": True,
                    "is_filterable": True,
                    "is_unique": True,
                    "sort_order": 1
                },
                {
                    "field_name": "industry",
                    "field_type": "select",
                    "display_name": "行业",
                    "description": "客户所属行业",
                    "is_required": False,
                    "is_searchable": False,
                    "is_filterable": True,
                    "field_options": {
                        "options": [
                            {"value": "tech", "label": "科技"},
                            {"value": "finance", "label": "金融"},
                            {"value": "healthcare", "label": "医疗"},
                            {"value": "education", "label": "教育"},
                            {"value": "manufacturing", "label": "制造业"},  # 新增
                            {"value": "other", "label": "其他"}
                        ]
                    },
                    "sort_order": 2
                },
                {
                    "field_name": "contact_phone",  # 新增字段
                    "field_type": "text",
                    "display_name": "联系电话",
                    "description": "客户联系电话",
                    "is_required": False,
                    "is_searchable": True,
                    "is_filterable": False,
                    "validation_rules": {"pattern": "^[0-9-+()\\s]+$"},
                    "sort_order": 3
                },
                {
                    "field_name": "annual_revenue",
                    "field_type": "number",
                    "display_name": "年收入",
                    "description": "年度收入（万元）",
                    "is_required": False,
                    "is_searchable": False,
                    "is_filterable": True,
                    "validation_rules": {"min": 0, "max": 100000},
                    "sort_order": 4
                },
                {
                    "field_name": "contract_date",
                    "field_type": "date",
                    "display_name": "签约日期",
                    "description": "与客户签约的日期",
                    "is_required": False,
                    "is_searchable": False,
                    "is_filterable": True,
                    "sort_order": 5
                },
                {
                    "field_name": "is_active",
                    "field_type": "boolean",
                    "display_name": "是否活跃",
                    "description": "客户是否为活跃状态",
                    "is_required": False,
                    "is_searchable": False,
                    "is_filterable": True,
                    "default_value": "true",
                    "sort_order": 6
                },
                {
                    "field_name": "notes",
                    "field_type": "textarea",
                    "display_name": "备注",
                    "description": "关于客户的额外信息",
                    "is_required": False,
                    "is_searchable": True,
                    "is_filterable": False,
                    "sort_order": 7
                }
            ]
        }
        
        async with self.session.put(
            f"{self.base_url}/api/dynamic-models/types/{model_type_id}",
            json=update_data,
            headers=self.headers
        ) as response:
            if response.status == 200:
                data = await response.json()
                print(f"✓ 模型类型更新成功: {data['display_name']}")
                print(f"  - 现在包含 {len(data['fields'])} 个字段")
                return data
            else:
                error_text = await response.text()
                print(f"❌ 更新模型类型失败: {response.status} - {error_text}")
                return None
    
    async def cleanup_test_data(self):
        """清理测试数据"""
        print("\n🧹 清理测试数据...")
        
        # 删除实体
        deleted_entities = 0
        for entity_id in self.created_entities:
            async with self.session.delete(
                f"{self.base_url}/api/dynamic-models/entities/{entity_id}",
                headers=self.headers
            ) as response:
                if response.status == 204:
                    deleted_entities += 1
        
        print(f"✓ 删除了 {deleted_entities} 个实体")
        
        # 删除模型类型（软删除）
        deleted_types = 0
        for model_type_id in self.created_model_types:
            async with self.session.delete(
                f"{self.base_url}/api/dynamic-models/types/{model_type_id}",
                headers=self.headers
            ) as response:
                if response.status == 204:
                    deleted_types += 1
        
        print(f"✓ 删除了 {deleted_types} 个模型类型")
        return True
    
    async def run_all_tests(self):
        """运行所有动态模型测试"""
        print("🚀 开始动态模型系统测试")
        print("=" * 80)
        
        # 登录
        if not await self.login():
            return False
        
        # 获取字段类型
        field_types = await self.test_get_field_types()
        if not field_types:
            return False
        
        # 创建自定义模型类型
        model_type = await self.test_create_custom_model_type()
        if not model_type:
            return False
        
        model_type_id = model_type["id"]
        
        # 获取模型类型列表
        await self.test_get_model_types()
        
        # 创建动态实体
        if not await self.test_create_dynamic_entities(model_type_id):
            return False
        
        # 等待ES索引
        await asyncio.sleep(2)
        
        # 获取实体列表
        entities = await self.test_get_dynamic_entities(model_type_id)
        if not entities:
            return False
        
        # 更新实体
        if entities and len(entities) > 0:
            await self.test_update_dynamic_entity(entities[0]["id"])
        
        # 搜索实体
        await self.test_search_entities(model_type_id)
        
        # 更新模型类型
        await self.test_update_model_type(model_type_id)
        
        # 清理测试数据
        await self.cleanup_test_data()
        
        print("\n" + "=" * 80)
        print("🎉 动态模型系统测试完成！")
        
        return True

async def main():
    """主测试运行器"""
    print("Bounty AMS - 动态模型系统测试套件")
    print("确保API服务器在localhost:8090运行")
    print("并且数据库已初始化了管理员用户")
    
    async with DynamicModelTester() as tester:
        success = await tester.run_all_tests()
    
    return success

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)