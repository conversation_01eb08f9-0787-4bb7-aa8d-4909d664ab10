#!/usr/bin/env python3
"""
Check user data in database
"""
import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import get_db
from models_dynamic import User
from sqlalchemy import select
import bcrypt

async def check_user():
    """Check user data"""
    print("Checking user data...")
    
    async for db in get_db():
        try:
            # Check if admin user exists
            result = await db.execute(
                select(User).where(User.username == "admin")
            )
            user = result.scalar_one_or_none()
            
            if user:
                print(f"✓ User found: {user.username}")
                print(f"  Email: {user.email}")
                print(f"  Is admin: {user.is_admin}")
                print(f"  Is active: {user.is_active}")
                print(f"  Hashed password: {user.hashed_password[:50]}...")
                
                # Test password verification
                test_password = "password"
                is_valid = bcrypt.checkpw(test_password.encode('utf-8'), user.hashed_password.encode('utf-8'))
                print(f"  Password verification: {'✓' if is_valid else '✗'}")
                
            else:
                print("✗ Admin user not found")
                
        except Exception as e:
            print(f"✗ Error: {e}")
            import traceback
            traceback.print_exc()
        finally:
            await db.close()

if __name__ == "__main__":
    asyncio.run(check_user())