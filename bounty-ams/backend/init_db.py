#!/usr/bin/env python3

import sys
import os
import asyncio
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from models import User, AssetType, AssetTypeField, Base
from auth import get_password_hash

# Use sync version for initialization
SYNC_DATABASE_URL = "postgresql://postgres:postgres@localhost:5432/bounty_ams"

def create_default_asset_types(db, admin_user):
    """Create default asset types for the system"""
    
    # Check if default types already exist
    existing_types = db.query(AssetType).filter(AssetType.is_system == True).count()
    if existing_types > 0:
        print("Default asset types already exist")
        return
    
    # Domain Asset Type
    domain_type = AssetType(
        name="domain",
        display_name="Domain",
        description="Internet domain names",
        icon="🌐",
        color="#3B82F6",
        is_active=True,
        is_system=True,
        created_by_user_id=admin_user.id
    )
    db.add(domain_type)
    db.flush()
    
    # Domain fields
    domain_fields = [
        AssetTypeField(
            asset_type_id=domain_type.id,
            field_name="domain_name",
            field_type="text",
            display_name="Domain Name",
            description="The domain name (e.g., example.com)",
            is_required=True,
            is_searchable=True,
            is_filterable=True,
            sort_order=0
        ),
        AssetTypeField(
            asset_type_id=domain_type.id,
            field_name="registrar",
            field_type="text",
            display_name="Registrar",
            description="Domain registrar",
            is_required=False,
            is_searchable=True,
            is_filterable=True,
            sort_order=1
        ),
        AssetTypeField(
            asset_type_id=domain_type.id,
            field_name="expiry_date",
            field_type="date",
            display_name="Expiry Date",
            description="Domain expiration date",
            is_required=False,
            is_searchable=False,
            is_filterable=True,
            sort_order=2
        )
    ]
    
    for field in domain_fields:
        db.add(field)
    
    # IP Address Asset Type
    ip_type = AssetType(
        name="ip_address",
        display_name="IP Address",
        description="IPv4 and IPv6 addresses",
        icon="🔢",
        color="#10B981",
        is_active=True,
        is_system=True,
        created_by_user_id=admin_user.id
    )
    db.add(ip_type)
    db.flush()
    
    # IP fields
    ip_fields = [
        AssetTypeField(
            asset_type_id=ip_type.id,
            field_name="ip_address",
            field_type="ip",
            display_name="IP Address",
            description="IPv4 or IPv6 address",
            is_required=True,
            is_searchable=True,
            is_filterable=True,
            sort_order=0
        ),
        AssetTypeField(
            asset_type_id=ip_type.id,
            field_name="location",
            field_type="text",
            display_name="Location",
            description="Geographic location",
            is_required=False,
            is_searchable=True,
            is_filterable=True,
            sort_order=1
        ),
        AssetTypeField(
            asset_type_id=ip_type.id,
            field_name="organization",
            field_type="text",
            display_name="Organization",
            description="Organization owning the IP",
            is_required=False,
            is_searchable=True,
            is_filterable=True,
            sort_order=2
        )
    ]
    
    for field in ip_fields:
        db.add(field)
    
    # Certificate Asset Type
    cert_type = AssetType(
        name="certificate",
        display_name="SSL Certificate",
        description="SSL/TLS certificates",
        icon="🔒",
        color="#F59E0B",
        is_active=True,
        is_system=True,
        created_by_user_id=admin_user.id
    )
    db.add(cert_type)
    db.flush()
    
    # Certificate fields
    cert_fields = [
        AssetTypeField(
            asset_type_id=cert_type.id,
            field_name="common_name",
            field_type="text",
            display_name="Common Name",
            description="Certificate common name",
            is_required=True,
            is_searchable=True,
            is_filterable=True,
            sort_order=0
        ),
        AssetTypeField(
            asset_type_id=cert_type.id,
            field_name="issuer",
            field_type="text",
            display_name="Issuer",
            description="Certificate authority",
            is_required=False,
            is_searchable=True,
            is_filterable=True,
            sort_order=1
        ),
        AssetTypeField(
            asset_type_id=cert_type.id,
            field_name="expiry_date",
            field_type="date",
            display_name="Expiry Date",
            description="Certificate expiration date",
            is_required=False,
            is_searchable=False,
            is_filterable=True,
            sort_order=2
        ),
        AssetTypeField(
            asset_type_id=cert_type.id,
            field_name="san_domains",
            field_type="textarea",
            display_name="SAN Domains",
            description="Subject Alternative Names (one per line)",
            is_required=False,
            is_searchable=True,
            is_filterable=False,
            sort_order=3
        )
    ]
    
    for field in cert_fields:
        db.add(field)
    
    # API Endpoint Asset Type
    api_type = AssetType(
        name="api_endpoint",
        display_name="API Endpoint",
        description="REST API endpoints",
        icon="🔌",
        color="#8B5CF6",
        is_active=True,
        is_system=True,
        created_by_user_id=admin_user.id
    )
    db.add(api_type)
    db.flush()
    
    # API fields
    api_fields = [
        AssetTypeField(
            asset_type_id=api_type.id,
            field_name="endpoint_url",
            field_type="url",
            display_name="Endpoint URL",
            description="API endpoint URL",
            is_required=True,
            is_searchable=True,
            is_filterable=True,
            sort_order=0
        ),
        AssetTypeField(
            asset_type_id=api_type.id,
            field_name="http_method",
            field_type="select",
            display_name="HTTP Method",
            description="HTTP method used",
            is_required=False,
            is_searchable=False,
            is_filterable=True,
            field_options={
                "options": [
                    {"value": "GET", "label": "GET"},
                    {"value": "POST", "label": "POST"},
                    {"value": "PUT", "label": "PUT"},
                    {"value": "DELETE", "label": "DELETE"},
                    {"value": "PATCH", "label": "PATCH"}
                ]
            },
            sort_order=1
        ),
        AssetTypeField(
            asset_type_id=api_type.id,
            field_name="authentication",
            field_type="select",
            display_name="Authentication",
            description="Authentication method",
            is_required=False,
            is_searchable=False,
            is_filterable=True,
            field_options={
                "options": [
                    {"value": "none", "label": "None"},
                    {"value": "api_key", "label": "API Key"},
                    {"value": "bearer_token", "label": "Bearer Token"},
                    {"value": "basic_auth", "label": "Basic Auth"},
                    {"value": "oauth", "label": "OAuth"}
                ]
            },
            sort_order=2
        ),
        AssetTypeField(
            asset_type_id=api_type.id,
            field_name="documentation_url",
            field_type="url",
            display_name="Documentation URL",
            description="API documentation URL",
            is_required=False,
            is_searchable=False,
            is_filterable=False,
            sort_order=3
        )
    ]
    
    for field in api_fields:
        db.add(field)
    
    db.commit()
    print("Default asset types created successfully:")
    print("- Domain")
    print("- IP Address")
    print("- SSL Certificate")
    print("- API Endpoint")

def create_admin_user():
    engine = create_engine(SYNC_DATABASE_URL)
    Base.metadata.create_all(bind=engine)
    
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        # Check if admin user already exists
        admin_user = db.query(User).filter(User.username == "admin").first()
        if admin_user:
            print("Admin user already exists")
        else:
            # Create admin user
            hashed_password = get_password_hash("admin123")
            admin_user = User(
                username="admin",
                email="<EMAIL>",
                hashed_password=hashed_password,
                is_admin=True
            )
            db.add(admin_user)
            db.commit()
            db.refresh(admin_user)
            print("Admin user created successfully")
            print("Username: admin")
            print("Password: admin123")
        
        # Create default asset types
        create_default_asset_types(db, admin_user)
        
    except Exception as e:
        print(f"Error creating admin user: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    create_admin_user()