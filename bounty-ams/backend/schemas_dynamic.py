from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
from uuid import UUID

# 用户相关schemas保持不变
class UserBase(BaseModel):
    username: str
    email: str
    is_admin: bool = False

class UserCreate(UserBase):
    password: str

class UserUpdate(BaseModel):
    username: Optional[str] = None
    email: Optional[str] = None
    is_admin: Optional[bool] = None
    is_active: Optional[bool] = None

class User(UserBase):
    id: UUID
    is_active: bool
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

# 动态模型字段schemas
class ModelFieldBase(BaseModel):
    field_name: str
    field_type: str
    display_name: str
    description: Optional[str] = None
    is_required: bool = False
    is_searchable: bool = True
    is_filterable: bool = True
    is_unique: bool = False
    default_value: Optional[str] = None
    validation_rules: Optional[Dict[str, Any]] = None
    field_options: Optional[Dict[str, Any]] = None
    sort_order: int = 0

class ModelFieldCreate(ModelFieldBase):
    pass

class ModelFieldUpdate(BaseModel):
    field_name: Optional[str] = None
    field_type: Optional[str] = None
    display_name: Optional[str] = None
    description: Optional[str] = None
    is_required: Optional[bool] = None
    is_searchable: Optional[bool] = None
    is_filterable: Optional[bool] = None
    is_unique: Optional[bool] = None
    default_value: Optional[str] = None
    validation_rules: Optional[Dict[str, Any]] = None
    field_options: Optional[Dict[str, Any]] = None
    sort_order: Optional[int] = None

class ModelField(ModelFieldBase):
    id: UUID
    model_type_id: UUID
    created_at: datetime
    
    class Config:
        from_attributes = True

# 动态模型类型schemas
class ModelTypeBase(BaseModel):
    name: str
    display_name: str
    description: Optional[str] = None
    icon: Optional[str] = None
    color: Optional[str] = None
    is_active: bool = True

class ModelTypeCreate(ModelTypeBase):
    fields: List[ModelFieldCreate] = []

class ModelTypeUpdate(BaseModel):
    name: Optional[str] = None
    display_name: Optional[str] = None
    description: Optional[str] = None
    icon: Optional[str] = None
    color: Optional[str] = None
    is_active: Optional[bool] = None
    fields: Optional[List[ModelFieldCreate]] = None

class ModelType(ModelTypeBase):
    id: UUID
    is_system: bool
    created_by_user_id: Optional[UUID] = None
    created_at: datetime
    updated_at: Optional[datetime] = None
    fields: List[ModelField] = []
    
    class Config:
        from_attributes = True

# 动态实体schemas
class DynamicEntityBase(BaseModel):
    model_type_id: UUID
    entity_data: Dict[str, Any]

class DynamicEntityCreate(DynamicEntityBase):
    pass

class DynamicEntityUpdate(BaseModel):
    entity_data: Optional[Dict[str, Any]] = None

class DynamicEntity(DynamicEntityBase):
    id: UUID
    es_index: Optional[str] = None
    es_doc_id: Optional[str] = None
    created_by_user_id: Optional[UUID] = None
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True

# 保留原有的AssetType相关schemas
class AssetTypeFieldBase(BaseModel):
    field_name: str
    field_type: str
    display_name: str
    description: Optional[str] = None
    is_required: bool = False
    is_searchable: bool = True
    is_filterable: bool = True
    default_value: Optional[str] = None
    validation_rules: Optional[Dict[str, Any]] = None
    field_options: Optional[Dict[str, Any]] = None
    sort_order: int = 0

class AssetTypeFieldCreate(AssetTypeFieldBase):
    pass

class AssetTypeFieldUpdate(BaseModel):
    field_name: Optional[str] = None
    field_type: Optional[str] = None
    display_name: Optional[str] = None
    description: Optional[str] = None
    is_required: Optional[bool] = None
    is_searchable: Optional[bool] = None
    is_filterable: Optional[bool] = None
    default_value: Optional[str] = None
    validation_rules: Optional[Dict[str, Any]] = None
    field_options: Optional[Dict[str, Any]] = None
    sort_order: Optional[int] = None

class AssetTypeField(AssetTypeFieldBase):
    id: UUID
    asset_type_id: UUID
    created_at: datetime
    
    class Config:
        from_attributes = True

class AssetTypeBase(BaseModel):
    name: str
    display_name: str
    description: Optional[str] = None
    icon: Optional[str] = None
    color: Optional[str] = None
    is_active: bool = True

class AssetTypeCreate(AssetTypeBase):
    fields: List[AssetTypeFieldCreate] = []

class AssetTypeUpdate(BaseModel):
    name: Optional[str] = None
    display_name: Optional[str] = None
    description: Optional[str] = None
    icon: Optional[str] = None
    color: Optional[str] = None
    is_active: Optional[bool] = None
    fields: Optional[List[AssetTypeFieldCreate]] = None

class AssetType(AssetTypeBase):
    id: UUID
    is_system: bool
    created_by_user_id: Optional[UUID] = None
    created_at: datetime
    updated_at: Optional[datetime] = None
    fields: List[AssetTypeField] = []
    
    class Config:
        from_attributes = True

# 动态资产schemas
class DynamicAssetBase(BaseModel):
    asset_type_id: UUID
    data: Dict[str, Any]
    tags: Optional[List[str]] = None
    source: str = "manual"

class DynamicAssetCreate(DynamicAssetBase):
    pass

class DynamicAssetUpdate(BaseModel):
    data: Optional[Dict[str, Any]] = None
    tags: Optional[List[str]] = None
    source: Optional[str] = None

class DynamicAsset(DynamicAssetBase):
    id: str  # Elasticsearch _id
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True

# 认证相关schemas
class Token(BaseModel):
    access_token: str
    token_type: str

class TokenData(BaseModel):
    username: Optional[str] = None

class UserLogin(BaseModel):
    username: str
    password: str