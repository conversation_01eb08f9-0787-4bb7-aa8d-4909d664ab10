#!/usr/bin/env python3
"""
Debug agent online status calculation
"""
import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import datetime, timedelta
from sqlalchemy import select
from database import get_db
from models_dynamic import Agent

async def debug_agent_status():
    """Debug agent online status calculation"""
    
    # 定义在线判断标准：最后心跳时间在5分钟内
    online_threshold = datetime.utcnow() - timedelta(minutes=5)
    
    print(f"Current UTC time: {datetime.utcnow()}")
    print(f"Online threshold (5 minutes ago): {online_threshold}")
    print("="*60)
    
    async for db in get_db():
        try:
            result = await db.execute(
                select(Agent).order_by(Agent.created_at.desc())
            )
            agents = result.scalars().all()
            
            print(f"Found {len(agents)} agents:")
            print()
            
            for agent in agents:
                last_seen = agent.last_seen_at
                if last_seen:
                    # Convert to timezone-aware if needed
                    if last_seen.tzinfo is None:
                        last_seen_utc = last_seen
                    else:
                        last_seen_utc = last_seen.astimezone().utctimetuple()
                        last_seen_utc = datetime(*last_seen_utc[:6])
                    
                    is_online = last_seen_utc > online_threshold if last_seen else False
                    time_diff = datetime.utcnow() - last_seen_utc if last_seen else None
                    
                    print(f"Agent: {agent.name} ({agent.agent_id})")
                    print(f"  Database status: {agent.status}")
                    print(f"  Last seen: {last_seen}")
                    print(f"  Last seen UTC: {last_seen_utc}")
                    print(f"  Time difference: {time_diff}")
                    print(f"  Should be online: {is_online}")
                    print()
                else:
                    print(f"Agent: {agent.name} ({agent.agent_id})")
                    print(f"  Database status: {agent.status}")
                    print(f"  Last seen: Never")
                    print(f"  Should be online: False")
                    print()
            
            break
        except Exception as e:
            print(f"❌ Error: {e}")
            await db.rollback()
            raise

if __name__ == "__main__":
    asyncio.run(debug_agent_status())