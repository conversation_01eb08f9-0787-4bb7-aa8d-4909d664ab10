#!/usr/bin/env python3
"""
Test script for discovered assets API endpoints
"""
import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from elasticsearch_client import get_es_client
import json

async def test_asset_search():
    """Test asset search functionality"""
    print("Testing asset search in Elasticsearch...")
    
    try:
        es_client = await get_es_client()
        
        # Search for all assets
        search_body = {
            "query": {"match_all": {}},
            "size": 10,
            "sort": [{"discovered_at": {"order": "desc"}}]
        }
        
        response = await es_client.search(
            index="assets-*",
            body=search_body
        )
        
        total_assets = response.get("hits", {}).get("total", {}).get("value", 0)
        print(f"Found {total_assets} assets in Elasticsearch")
        
        assets = response.get("hits", {}).get("hits", [])
        if assets:
            print("Sample discovered assets:")
            for i, hit in enumerate(assets[:3]):
                asset = hit["_source"]
                print(f"  {i+1}. {asset.get('asset_type', 'unknown')}:{asset.get('asset_value', 'unknown')} (confidence: {asset.get('confidence', 'unknown')})")
        
        # Test aggregation query
        agg_search_body = {
            "size": 0,
            "aggs": {
                "by_type": {
                    "terms": {
                        "field": "asset_type.keyword",
                        "size": 10
                    }
                }
            }
        }
        
        agg_response = await es_client.search(
            index="assets-*",
            body=agg_search_body
        )
        
        print("\nAsset types distribution:")
        for bucket in agg_response.get("aggregations", {}).get("by_type", {}).get("buckets", []):
            print(f"  {bucket['key']}: {bucket['doc_count']}")
        
        print("✓ Asset search test completed successfully")
        
    except Exception as e:
        print(f"✗ Error during asset search test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_asset_search())