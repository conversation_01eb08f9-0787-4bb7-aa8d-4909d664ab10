#!/usr/bin/env python3
"""
创建ES测试数据脚本
向Elasticsearch中添加测试资产数据，用于前端展示
"""

import asyncio
import json
from datetime import datetime, timedelta
import random
from elasticsearch import AsyncElasticsearch
from config import settings

async def create_es_test_data():
    """创建ES测试数据"""
    
    # 连接Elasticsearch
    es = AsyncElasticsearch(
        hosts=[settings.ELASTICSEARCH_URL],
        timeout=30,
        max_retries=3,
        retry_on_timeout=True
    )
    
    try:
        # 测试连接
        info = await es.info()
        print(f"Connected to Elasticsearch: {info['version']['number']}")
        
        # 准备测试数据
        test_assets = []
        
        # 域名资产
        domains = [
            "hackerone.com", "bugcrowd.com", "microsoft.com", "apple.com", "google.com",
            "facebook.com", "twitter.com", "github.com", "gitlab.com", "stackoverflow.com"
        ]
        
        # 子域名
        subdomains = [
            "api", "admin", "dev", "test", "staging", "www", "mail", "ftp", "blog", "shop",
            "support", "docs", "cdn", "static", "assets", "images", "secure", "internal"
        ]
        
        # IP地址
        ips = [
            "*******", "*******", "**************", "***********", "*********",
            "*************", "*********", "************", "************", "***********"
        ]
        
        # 平台和项目ID
        platforms = ["hackerone", "bugcrowd", "microsoft", "apple", "google"]
        projects = {
            "hackerone": ["h1-project-1", "h1-project-2", "h1-project-3"],
            "bugcrowd": ["bc-project-1", "bc-project-2"],
            "microsoft": ["msrc-project-1", "msrc-project-2"],
            "apple": ["apple-project-1"],
            "google": ["google-vrp-1", "google-vrp-2"]
        }
        
        # 生成域名资产
        for domain in domains:
            platform_id = random.choice(platforms)
            project_id = random.choice(projects[platform_id])
            
            # 主域名
            test_assets.append({
                "model_type_id": "asset",
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat(),
                "entity_data": {
                    "platform_id": platform_id,
                    "project_id": project_id,
                    "asset_type": "domain",
                    "asset_value": domain,
                    "asset_host": domain,
                    "confidence": random.choice(["high", "medium", "low"]),
                    "status": random.choice(["active", "inactive", "verified"]),
                    "discovered_at": (datetime.now() - timedelta(days=random.randint(1, 30))).isoformat(),
                    "source_task_type": random.choice(["subfinder", "amass", "manual"]),
                    "source_task_id": f"task_{random.randint(1000, 9999)}",
                    "tags": random.sample(["verified", "in_scope", "critical", "production", "testing"], k=random.randint(1, 3)),
                    "metadata": {
                        "domain_parts": len(domain.split('.')),
                        "tld": domain.split('.')[-1],
                        "sld": domain.split('.')[-2] if len(domain.split('.')) >= 2 else None,
                        "registrar": random.choice(["GoDaddy", "Namecheap", "CloudFlare", "Network Solutions"]),
                        "nameservers": [f"ns{i}.{domain}" for i in range(1, 3)]
                    }
                }
            })
            
            # 子域名
            for _ in range(random.randint(2, 8)):
                subdomain_name = random.choice(subdomains)
                subdomain = f"{subdomain_name}.{domain}"
                
                test_assets.append({
                    "model_type_id": "asset",
                    "created_at": datetime.now().isoformat(),
                    "updated_at": datetime.now().isoformat(),
                    "entity_data": {
                        "platform_id": platform_id,
                        "project_id": project_id,
                        "asset_type": "subdomain",
                        "asset_value": subdomain,
                        "asset_host": subdomain,
                        "confidence": random.choice(["high", "medium", "low"]),
                        "status": random.choice(["active", "inactive", "verified"]),
                        "discovered_at": (datetime.now() - timedelta(days=random.randint(1, 30))).isoformat(),
                        "source_task_type": random.choice(["subfinder", "amass", "httpx", "manual"]),
                        "source_task_id": f"task_{random.randint(1000, 9999)}",
                        "tags": random.sample(["verified", "in_scope", "admin", "api", "development", "exposed"], k=random.randint(1, 4)),
                        "metadata": {
                            "subdomain_level": len(subdomain.split('.')) - 2,
                            "root_domain": domain,
                            "http_status": random.choice([200, 301, 302, 403, 404, 500]),
                            "title": f"{subdomain_name.title()} - {domain}",
                            "technology": random.choice(["nginx", "apache", "cloudflare", "aws"]),
                            "ssl_cert": random.choice([True, False])
                        }
                    }
                })
        
        # 生成IP资产
        for ip in ips:
            platform_id = random.choice(platforms)
            project_id = random.choice(projects[platform_id])
            
            test_assets.append({
                "model_type_id": "asset",
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat(),
                "entity_data": {
                    "platform_id": platform_id,
                    "project_id": project_id,
                    "asset_type": "ip",
                    "asset_value": ip,
                    "asset_host": ip,
                    "confidence": random.choice(["high", "medium", "low"]),
                    "status": random.choice(["active", "inactive", "scanning"]),
                    "discovered_at": (datetime.now() - timedelta(days=random.randint(1, 30))).isoformat(),
                    "source_task_type": random.choice(["nmap", "masscan", "naabu", "manual"]),
                    "source_task_id": f"task_{random.randint(1000, 9999)}",
                    "tags": random.sample(["public", "cloud", "server", "firewall", "load_balancer"], k=random.randint(1, 3)),
                    "metadata": {
                        "ip_version": 4,
                        "is_private": False,
                        "is_multicast": False,
                        "is_loopback": False,
                        "geolocation": {
                            "country": random.choice(["US", "EU", "AS", "CA"]),
                            "city": random.choice(["San Francisco", "New York", "London", "Tokyo"])
                        },
                        "asn": random.randint(1000, 99999),
                        "org": random.choice(["Amazon", "Google", "Cloudflare", "Microsoft"])
                    }
                }
            })
            
            # 为每个IP生成一些端口
            for port in random.sample([22, 80, 443, 8080, 8443, 3306, 5432, 6379, 9200], k=random.randint(1, 4)):
                test_assets.append({
                    "model_type_id": "asset",
                    "created_at": datetime.now().isoformat(),
                    "updated_at": datetime.now().isoformat(),
                    "entity_data": {
                        "platform_id": platform_id,
                        "project_id": project_id,
                        "asset_type": "port",
                        "asset_value": f"{ip}:{port}",
                        "asset_host": ip,
                        "confidence": random.choice(["high", "medium", "low"]),
                        "status": random.choice(["open", "closed", "filtered"]),
                        "discovered_at": (datetime.now() - timedelta(days=random.randint(1, 30))).isoformat(),
                        "source_task_type": random.choice(["nmap", "masscan", "naabu"]),
                        "source_task_id": f"task_{random.randint(1000, 9999)}",
                        "tags": random.sample(["open", "service", "vulnerable", "secured"], k=random.randint(1, 2)),
                        "metadata": {
                            "port": port,
                            "protocol": "tcp",
                            "service": {
                                22: "ssh",
                                80: "http",
                                443: "https",
                                8080: "http-alt",
                                8443: "https-alt",
                                3306: "mysql",
                                5432: "postgresql",
                                6379: "redis",
                                9200: "elasticsearch"
                            }.get(port, "unknown"),
                            "banner": f"Service running on port {port}",
                            "version": random.choice(["1.0", "2.1", "3.2", "latest"])
                        }
                    }
                })
        
        # 生成URL资产
        url_paths = ["/admin", "/api/v1", "/login", "/dashboard", "/config", "/backup", "/test", "/dev"]
        for domain in domains[:5]:  # 只为前5个域名生成URL
            platform_id = random.choice(platforms)
            project_id = random.choice(projects[platform_id])
            
            for path in random.sample(url_paths, k=random.randint(2, 5)):
                url = f"https://{domain}{path}"
                test_assets.append({
                    "model_type_id": "asset",
                    "created_at": datetime.now().isoformat(),
                    "updated_at": datetime.now().isoformat(),
                    "entity_data": {
                        "platform_id": platform_id,
                        "project_id": project_id,
                        "asset_type": "url",
                        "asset_value": url,
                        "asset_host": domain,
                        "confidence": random.choice(["high", "medium", "low"]),
                        "status": random.choice(["accessible", "forbidden", "not_found"]),
                        "discovered_at": (datetime.now() - timedelta(days=random.randint(1, 30))).isoformat(),
                        "source_task_type": random.choice(["katana", "httpx", "dirsearch", "manual"]),
                        "source_task_id": f"task_{random.randint(1000, 9999)}",
                        "tags": random.sample(["admin", "api", "sensitive", "public", "authenticated"], k=random.randint(1, 3)),
                        "metadata": {
                            "scheme": "https",
                            "port": 443,
                            "path": path,
                            "query": None,
                            "status_code": random.choice([200, 301, 302, 401, 403, 404]),
                            "content_length": random.randint(100, 50000),
                            "content_type": random.choice(["text/html", "application/json", "text/plain"])
                        }
                    }
                })
        
        print(f"Generated {len(test_assets)} test assets")
        
        # 批量插入到Elasticsearch
        index_name = f"enhanced_asset-{datetime.now().strftime('%Y-%m')}"
        
        # 创建索引模板
        template = {
            "index_patterns": ["enhanced_asset-*"],
            "settings": {
                "number_of_shards": 1,
                "number_of_replicas": 0,
                "analysis": {
                    "analyzer": {
                        "asset_analyzer": {
                            "type": "custom",
                            "tokenizer": "standard",
                            "filter": ["lowercase", "stop"]
                        }
                    }
                }
            },
            "mappings": {
                "properties": {
                    "model_type_id": {"type": "keyword"},
                    "created_at": {"type": "date"},
                    "updated_at": {"type": "date"},
                    "entity_data": {
                        "properties": {
                            "platform_id": {"type": "keyword"},
                            "project_id": {"type": "keyword"},
                            "asset_type": {"type": "keyword"},
                            "asset_value": {
                                "type": "text",
                                "analyzer": "asset_analyzer",
                                "fields": {
                                    "keyword": {"type": "keyword"},
                                    "suggest": {
                                        "type": "completion",
                                        "analyzer": "simple"
                                    }
                                }
                            },
                            "asset_host": {
                                "type": "text",
                                "analyzer": "asset_analyzer",
                                "fields": {
                                    "keyword": {"type": "keyword"}
                                }
                            },
                            "confidence": {"type": "keyword"},
                            "status": {"type": "keyword"},
                            "discovered_at": {"type": "date"},
                            "source_task_type": {"type": "keyword"},
                            "source_task_id": {"type": "keyword"},
                            "tags": {"type": "keyword"},
                            "metadata": {"type": "object", "dynamic": True}
                        }
                    }
                }
            }
        }
        
        # 创建索引模板
        await es.indices.put_template(name="enhanced_asset_template", body=template)
        print("Created index template")
        
        # 批量插入数据
        bulk_data = []
        for asset in test_assets:
            bulk_data.append({"index": {"_index": index_name}})
            bulk_data.append(asset)
        
        # 执行批量插入
        response = await es.bulk(body=bulk_data, refresh=True)
        
        # 统计结果
        indexed_count = 0
        error_count = 0
        
        for item in response["items"]:
            if "index" in item:
                if item["index"].get("status") in [200, 201]:
                    indexed_count += 1
                else:
                    error_count += 1
        
        print(f"Indexing completed:")
        print(f"  - Indexed: {indexed_count}")
        print(f"  - Errors: {error_count}")
        print(f"  - Index: {index_name}")
        
        # 验证数据
        await asyncio.sleep(1)  # 等待索引刷新
        count_response = await es.count(index=index_name)
        print(f"Total documents in index: {count_response['count']}")
        
        # 测试聚合查询
        agg_query = {
            "size": 0,
            "aggs": {
                "platforms": {
                    "terms": {"field": "entity_data.platform_id", "size": 10}
                },
                "asset_types": {
                    "terms": {"field": "entity_data.asset_type", "size": 10}
                },
                "confidence_levels": {
                    "terms": {"field": "entity_data.confidence", "size": 5}
                }
            }
        }
        
        agg_response = await es.search(index=index_name, body=agg_query)
        print("\\nAggregation results:")
        
        for agg_name, agg_data in agg_response["aggregations"].items():
            print(f"  {agg_name}:")
            for bucket in agg_data["buckets"]:
                print(f"    {bucket['key']}: {bucket['doc_count']}")
        
    except Exception as e:
        print(f"Error: {e}")
        raise
    
    finally:
        await es.close()

if __name__ == "__main__":
    asyncio.run(create_es_test_data())