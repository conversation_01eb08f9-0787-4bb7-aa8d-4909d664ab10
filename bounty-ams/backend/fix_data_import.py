#!/usr/bin/env python3
"""
修复数据导入过程中的平台项目关联问题
"""

import os
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from typing import Dict, Any, List, Optional
import logging
from datetime import datetime
from sqlalchemy import select, and_
from sqlalchemy.ext.asyncio import AsyncSession

from models.model_type import ModelType
from models.dynamic_entity import DynamicEntity
from core.config import settings
from core.elasticsearch import get_es_client
from core.utils import convert_field_value, generate_unique_asset_id
from models.user import User

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def fix_data_import_route():
    """修复数据导入路由中的问题"""
    
    # 读取原始文件
    import_file_path = "routes/data_import.py"
    
    try:
        with open(import_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 修复导入逻辑中的关键问题
        fixes = [
            {
                'description': '确保平台项目信息正确传递到entity_data',
                'old_code': '''# 添加平台项目信息（如果提供）
                if platform_id:
                    import_metadata["platform_id"] = platform_id
                if project_id:
                    import_metadata["project_id"] = project_id
                
                converted_data.update(import_metadata)''',
                'new_code': '''# 添加平台项目信息（如果提供）
                if platform_id:
                    import_metadata["platform_id"] = platform_id
                    converted_data["platform_id"] = platform_id  # 确保在entity_data中
                if project_id:
                    import_metadata["project_id"] = project_id
                    converted_data["project_id"] = project_id  # 确保在entity_data中
                
                converted_data.update(import_metadata)'''
            },
            {
                'description': '确保Elasticsearch文档结构正确',
                'old_code': '''# 构建ES文档 - 使用与动态模型系统一致的结构
                es_doc = {
                    "model_type_id": str(asset_type.id),
                    "entity_data": converted_data,
                    "created_at": datetime.utcnow().isoformat(),
                    "updated_at": datetime.utcnow().isoformat(),
                    "created_by_user_id": str(current_user.id),
                    # 为了高级搜索兼容性，也添加传统字段
                    "asset_type": asset_type.name,
                    "platform_id": platform_id or "imported",
                    "project_id": project_id or "imported",
                    "discovered_at": datetime.utcnow().isoformat(),
                    "source_task_type": "data_import",
                    "confidence": "high"
                }''',
                'new_code': '''# 构建ES文档 - 使用与动态模型系统一致的结构
                es_doc = {
                    "model_type_id": str(asset_type.id),
                    "entity_data": converted_data,
                    "created_at": datetime.utcnow().isoformat(),
                    "updated_at": datetime.utcnow().isoformat(),
                    "created_by_user_id": str(current_user.id)
                }
                
                # 确保平台项目信息在根级别也存在（兼容性）
                if platform_id:
                    es_doc["platform_id"] = platform_id
                if project_id:
                    es_doc["project_id"] = project_id
                
                # 添加其他兼容性字段
                es_doc.update({
                    "asset_type": asset_type.name,
                    "discovered_at": datetime.utcnow().isoformat(),
                    "source_task_type": "data_import",
                    "confidence": "high"
                })'''
            }
        ]
        
        # 应用修复
        for fix in fixes:
            if fix['old_code'] in content:
                content = content.replace(fix['old_code'], fix['new_code'])
                logger.info(f"应用修复: {fix['description']}")
            else:
                logger.warning(f"未找到要修复的代码: {fix['description']}")
        
        # 写回文件
        with open(import_file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        logger.info("数据导入路由修复完成")
        
    except Exception as e:
        logger.error(f"修复数据导入路由失败: {e}")

def create_enhanced_import_function():
    """创建增强的导入函数"""
    
    enhanced_import_code = '''
async def enhanced_data_import(
    db: AsyncSession,
    raw_data: List[Dict[str, Any]],
    field_mappings: List[Dict[str, Any]],
    asset_type: AssetType,
    current_user: User,
    platform_id: Optional[str] = None,
    project_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    增强的数据导入函数，确保平台项目关联正确
    """
    results = {
        "total_records": len(raw_data),
        "successful_imports": 0,
        "failed_imports": 0,
        "errors": []
    }
    
    # 验证平台项目存在性
    if platform_id:
        platform_exists = await verify_platform_exists(db, platform_id)
        if not platform_exists:
            results["errors"].append(f"平台ID {platform_id} 不存在")
            return results
    
    if project_id:
        project_exists = await verify_project_exists(db, project_id)
        if not project_exists:
            results["errors"].append(f"项目ID {project_id} 不存在")
            return results
    
    # 获取ES客户端
    es_client = await get_es_client()
    
    # 确定ES索引名 - 使用增强资产索引
    index_name = "enhanced_asset-" + datetime.utcnow().strftime("%Y-%m")
    
    # 批量处理
    batch_size = 1000
    total_records = len(raw_data)
    
    for batch_start in range(0, total_records, batch_size):
        batch_end = min(batch_start + batch_size, total_records)
        batch_data = raw_data[batch_start:batch_end]
        
        bulk_operations = []
        
        for i, raw_record in enumerate(batch_data):
            try:
                # 数据映射转换
                converted_data = {}
                for mapping in field_mappings:
                    if mapping.get("target_field") and mapping.get("source_field"):
                        source_value = raw_record.get(mapping["source_field"])
                        if source_value is not None:
                            converted_value = convert_field_value(
                                source_value, 
                                mapping.get("target_type", "text")
                            )
                            converted_data[mapping["target_field"]] = converted_value
                
                # 关键：确保平台项目信息正确设置
                if platform_id:
                    converted_data["platform_id"] = platform_id
                if project_id:
                    converted_data["project_id"] = project_id
                
                # 添加必要的元数据
                converted_data.update({
                    "imported_at": datetime.utcnow().isoformat(),
                    "import_source": "data_import",
                    "original_record_index": batch_start + i,
                    "asset_type": asset_type.name,
                    "imported_by": current_user.username,
                    "confidence": "high",
                    "status": "active"
                })
                
                # 构建ES文档
                es_doc = {
                    "model_type_id": str(asset_type.id),
                    "entity_data": converted_data,
                    "created_at": datetime.utcnow().isoformat(),
                    "updated_at": datetime.utcnow().isoformat(),
                    "created_by_user_id": str(current_user.id)
                }
                
                # 生成唯一的文档ID
                doc_id = generate_unique_asset_id(converted_data)
                
                bulk_operations.append({
                    "index": {
                        "_index": index_name,
                        "_id": doc_id
                    }
                })
                bulk_operations.append(es_doc)
                
            except Exception as e:
                results["failed_imports"] += 1
                results["errors"].append({
                    "record_index": batch_start + i,
                    "error": str(e),
                    "record_data": raw_record
                })
        
        # 批量索引到ES
        if bulk_operations:
            try:
                response = await es_client.bulk(body=bulk_operations)
                
                # 处理响应
                for item in response["items"]:
                    if "index" in item:
                        if item["index"].get("status") in [200, 201]:
                            results["successful_imports"] += 1
                        else:
                            results["failed_imports"] += 1
                            if "error" in item["index"]:
                                results["errors"].append(item["index"]["error"])
                
            except Exception as e:
                results["failed_imports"] += len(bulk_operations) // 2
                results["errors"].append(f"批量索引失败: {str(e)}")
        
        # 同步到PostgreSQL
        await sync_batch_to_postgresql(db, bulk_operations, asset_type, current_user)
    
    return results

async def verify_platform_exists(db: AsyncSession, platform_id: str) -> bool:
    """验证平台是否存在"""
    try:
        # 获取平台模型类型
        platform_model_result = await db.execute(
            select(ModelType).where(ModelType.name == 'platform')
        )
        platform_model = platform_model_result.scalar_one_or_none()
        
        if not platform_model:
            return False
        
        # 检查平台实体是否存在
        platform_result = await db.execute(
            select(DynamicEntity).where(
                and_(
                    DynamicEntity.model_type_id == platform_model.id,
                    DynamicEntity.id == platform_id
                )
            )
        )
        
        return platform_result.scalar_one_or_none() is not None
        
    except Exception:
        return False

async def verify_project_exists(db: AsyncSession, project_id: str) -> bool:
    """验证项目是否存在"""
    try:
        # 获取项目模型类型
        project_model_result = await db.execute(
            select(ModelType).where(ModelType.name == 'project')
        )
        project_model = project_model_result.scalar_one_or_none()
        
        if not project_model:
            return False
        
        # 检查项目实体是否存在
        project_result = await db.execute(
            select(DynamicEntity).where(
                and_(
                    DynamicEntity.model_type_id == project_model.id,
                    DynamicEntity.id == project_id
                )
            )
        )
        
        return project_result.scalar_one_or_none() is not None
        
    except Exception:
        return False

async def sync_batch_to_postgresql(
    db: AsyncSession,
    bulk_operations: List[Dict[str, Any]],
    asset_type: ModelType, # Changed from AssetType to ModelType
    current_user: User
):
    """同步批量数据到PostgreSQL"""
    try:
        # 获取或创建对应的ModelType
        model_type_result = await db.execute(
            select(ModelType).where(ModelType.name == asset_type.name)
        )
        model_type = model_type_result.scalar_one_or_none()
        
        if not model_type:
            # 创建新的ModelType
            model_type = ModelType(
                name=asset_type.name,
                display_name=asset_type.display_name,
                description=f"从资产类型 {asset_type.display_name} 创建",
                is_system=False,
                created_by_user_id=current_user.id
            )
            db.add(model_type)
            await db.flush()
        
        # 创建DynamicEntity记录
        entities = []
        for i in range(0, len(bulk_operations), 2):
            index_op = bulk_operations[i]
            doc_data = bulk_operations[i + 1]
            
            entity = DynamicEntity(
                model_type_id=model_type.id,
                entity_data=doc_data["entity_data"],
                es_index=index_op["index"]["_index"],
                es_doc_id=index_op["index"]["_id"],
                created_by_user_id=current_user.id
            )
            entities.append(entity)
        
        db.add_all(entities)
        await db.commit()
        
    except Exception as e:
        logger.warning(f"同步到PostgreSQL失败: {e}")
        # 不抛出异常，因为ES导入成功就算成功

def generate_unique_asset_id(entity_data: Dict[str, Any]) -> str:
    """生成唯一的资产ID"""
    import hashlib
    
    # 使用关键字段生成唯一ID
    key_fields = [
        entity_data.get("asset_type", "unknown"),
        entity_data.get("asset_value", ""),
        entity_data.get("asset_host", ""),
        entity_data.get("platform_id", ""),
        entity_data.get("project_id", "")
    ]
    
    id_string = "|".join(str(field) for field in key_fields)
    return hashlib.md5(id_string.encode()).hexdigest()

def convert_field_value(value: Any, field_type: str) -> Any:
    """转换字段值类型"""
    if value is None:
        return None
    
    try:
        if field_type == "integer":
            return int(value)
        elif field_type == "float":
            return float(value)
        elif field_type == "boolean":
            return str(value).lower() in ['true', '1', 'yes', 'on']
        else:
            return str(value)
    except:
        return str(value)
'''
    
    # 将增强的导入函数写入文件
    with open("enhanced_import_functions.py", 'w', encoding='utf-8') as f:
        f.write(enhanced_import_code)
    
    logger.info("增强的导入函数已创建")

def main():
    """主函数"""
    logger.info("开始修复数据导入过程...")
    
    # 修复现有的导入路由
    fix_data_import_route()
    
    # 创建增强的导入函数
    create_enhanced_import_function()
    
    logger.info("数据导入修复完成！")
    
    print("\n修复说明:")
    print("1. 修复了现有的数据导入路由")
    print("2. 创建了增强的导入函数")
    print("3. 确保平台项目信息正确传递")
    print("4. 同时支持Elasticsearch和PostgreSQL存储")

if __name__ == "__main__":
    main() 