from sqlalchemy import Column, Inte<PERSON>, String, DateTime, <PERSON><PERSON><PERSON>, <PERSON>, JSO<PERSON>, ForeignKey
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID
import uuid
from database import Base

class User(Base):
    __tablename__ = "users"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    username = Column(String(255), unique=True, index=True, nullable=False)
    email = Column(String(255), unique=True, index=True, nullable=False)
    hashed_password = Column(String(255), nullable=False)
    is_admin = Column(Boolean, default=False)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    created_asset_types = relationship("AssetType", back_populates="created_by")
    created_tasks = relationship("Task", back_populates="created_by")
    created_vulnerabilities = relationship("VulnerabilityKB", back_populates="created_by")

class AssetType(Base):
    __tablename__ = "asset_types"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    name = Column(String(100), nullable=False, unique=True)  # domain, ip, certificate, etc.
    display_name = Column(String(255), nullable=False)
    description = Column(Text)
    icon = Column(String(100))
    color = Column(String(20))
    is_active = Column(Boolean, default=True)
    is_system = Column(Boolean, default=False)  # system types cannot be deleted
    created_by_user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    created_by = relationship("User", back_populates="created_asset_types")
    fields = relationship("AssetTypeField", back_populates="asset_type", cascade="all, delete-orphan")


class AssetTypeField(Base):
    __tablename__ = "asset_type_fields"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    asset_type_id = Column(UUID(as_uuid=True), ForeignKey("asset_types.id", ondelete="CASCADE"))
    field_name = Column(String(100), nullable=False)
    field_type = Column(String(50), nullable=False)  # text, number, ip, date, select, etc.
    display_name = Column(String(255), nullable=False)
    description = Column(Text)
    is_required = Column(Boolean, default=False)
    is_searchable = Column(Boolean, default=True)
    is_filterable = Column(Boolean, default=True)
    default_value = Column(Text)
    validation_rules = Column(JSON)  # validation rules as JSON
    field_options = Column(JSON)  # options for select fields
    sort_order = Column(Integer, default=0)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    asset_type = relationship("AssetType", back_populates="fields")


class VulnerabilityKB(Base):
    __tablename__ = "vulnerability_kb"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    name = Column(String(255), nullable=False)
    vuln_id = Column(String(100), unique=True)  # CVE-2021-44228
    description = Column(Text)
    severity = Column(String(50))  # Critical, High, Medium, Low
    affected_products = Column(JSON)
    poc_identifier = Column(String(255))
    created_by_user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    created_by = relationship("User", back_populates="created_vulnerabilities")
    tasks = relationship("Task", back_populates="vulnerability")


class Agent(Base):
    __tablename__ = "agents"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    name = Column(String(255))
    hostname = Column(String(255))
    status = Column(String(50), nullable=False)  # online, offline, busy
    version = Column(String(50))
    last_seen_at = Column(DateTime(timezone=True))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    tasks = relationship("Task", back_populates="agent")


class Task(Base):
    __tablename__ = "tasks"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    asset_identifier = Column(String(255), nullable=False)  # ES asset _id
    vulnerability_kb_id = Column(UUID(as_uuid=True), ForeignKey("vulnerability_kb.id"))
    agent_id = Column(UUID(as_uuid=True), ForeignKey("agents.id"))
    status = Column(String(50), nullable=False)  # pending, running, completed, failed
    result = Column(JSON)
    created_by_user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    completed_at = Column(DateTime(timezone=True))
    
    # Relationships
    created_by = relationship("User", back_populates="created_tasks")
    vulnerability = relationship("VulnerabilityKB", back_populates="tasks")
    agent = relationship("Agent", back_populates="tasks")