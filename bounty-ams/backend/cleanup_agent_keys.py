#!/usr/bin/env python3
"""
Clean up duplicate agent keys
"""
import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import select, update, delete
from database import get_db
from models_dynamic import <PERSON><PERSON><PERSON>

async def cleanup_duplicate_keys():
    """清理重复的agent密钥，每个agent只保留一个最新的活跃密钥"""
    
    async for db in get_db():
        try:
            # 获取所有密钥，按agent_id分组
            result = await db.execute(
                select(Agent<PERSON>ey).order_by(AgentKey.agent_id, AgentKey.created_at.desc())
            )
            all_keys = result.scalars().all()
            
            # 按agent_id分组
            agent_keys = {}
            for key in all_keys:
                if key.agent_id:  # 只处理已绑定agent的密钥
                    if key.agent_id not in agent_keys:
                        agent_keys[key.agent_id] = []
                    agent_keys[key.agent_id].append(key)
            
            print(f"Found keys for {len(agent_keys)} agents:")
            
            keys_to_revoke = []
            for agent_id, keys in agent_keys.items():
                active_keys = [k for k in keys if k.status == 'active']
                print(f"\nAgent {agent_id}: {len(keys)} total keys, {len(active_keys)} active")
                
                if len(active_keys) > 1:
                    # 保留最新的一个活跃密钥，撤销其他的
                    keys_to_keep = active_keys[:1]  # 保留第一个（最新的）
                    keys_to_revoke_for_agent = active_keys[1:]  # 撤销其他的
                    
                    print(f"  Keeping: {keys_to_keep[0].key_id} (created: {keys_to_keep[0].created_at})")
                    for key in keys_to_revoke_for_agent:
                        print(f"  Revoking: {key.key_id} (created: {key.created_at})")
                        keys_to_revoke.append(key.key_id)
                elif len(active_keys) == 1:
                    print(f"  OK: Only one active key: {active_keys[0].key_id}")
                else:
                    print(f"  No active keys for this agent")
            
            # 执行撤销操作
            if keys_to_revoke:
                print(f"\n撤销 {len(keys_to_revoke)} 个重复密钥...")
                await db.execute(
                    update(AgentKey)
                    .where(AgentKey.key_id.in_(keys_to_revoke))
                    .values(status="revoked")
                )
                await db.commit()
                print("✅ 清理完成")
            else:
                print("\n✅ 没有需要清理的重复密钥")
            
            # 删除未绑定agent的密钥
            unbound_result = await db.execute(
                select(AgentKey).where(AgentKey.agent_id.is_(None))
            )
            unbound_keys = unbound_result.scalars().all()
            
            if unbound_keys:
                print(f"\n删除 {len(unbound_keys)} 个未绑定的密钥...")
                await db.execute(
                    delete(AgentKey).where(AgentKey.agent_id.is_(None))
                )
                await db.commit()
                print("✅ 未绑定密钥删除完成")
            
            break
        except Exception as e:
            print(f"❌ Error: {e}")
            await db.rollback()
            raise

if __name__ == "__main__":
    asyncio.run(cleanup_duplicate_keys())