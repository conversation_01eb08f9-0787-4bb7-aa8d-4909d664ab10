#!/usr/bin/env python3
"""
Enhanced Asset Search and Statistics API Test Suite
"""

import asyncio
import aiohttp
import json
from typing import Dict, Any, Optional, List
import sys
from datetime import datetime, timedelta

class AssetAPITester:
    def __init__(self, base_url: str = "http://localhost:8090"):
        self.base_url = base_url
        self.session = None
        self.access_token = None
        self.headers = {}
        self.domain_type_id = None
        self.created_assets = []
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def login(self, username: str = "admin", password: str = "admin123"):
        """Login and get access token"""
        login_data = {
            "username": username,
            "password": password
        }
        
        async with self.session.post(
            f"{self.base_url}/api/auth/login",
            json=login_data
        ) as response:
            if response.status == 200:
                data = await response.json()
                self.access_token = data["access_token"]
                self.headers = {"Authorization": f"Bearer {self.access_token}"}
                print(f"✓ Login successful")
                return True
            else:
                print(f"❌ Login failed: {response.status}")
                return False
    
    async def get_domain_asset_type_id(self):
        """Get domain asset type ID"""
        async with self.session.get(
            f"{self.base_url}/api/asset-types/",
            headers=self.headers
        ) as response:
            if response.status == 200:
                data = await response.json()
                for asset_type in data:
                    if asset_type["name"] == "domain":
                        self.domain_type_id = asset_type["id"]
                        print(f"✓ Found domain asset type: {self.domain_type_id}")
                        return True
                print("❌ Domain asset type not found")
                return False
            else:
                print(f"❌ Failed to get asset types: {response.status}")
                return False
    
    async def create_test_assets(self):
        """Create test assets for testing"""
        print("\n🧪 Creating test assets...")
        
        test_assets = [
            {
                "asset_type_id": self.domain_type_id,
                "data": {
                    "domain_name": "example.com",
                    "registrar": "Example Registrar",
                    "expiry_date": "2024-12-31"
                },
                "tags": ["production", "main-site", "web"],
                "source": "manual"
            },
            {
                "asset_type_id": self.domain_type_id,
                "data": {
                    "domain_name": "test.example.com",
                    "registrar": "Test Registrar",
                    "expiry_date": "2024-06-30"
                },
                "tags": ["testing", "subdomain", "web"],
                "source": "subfinder"
            },
            {
                "asset_type_id": self.domain_type_id,
                "data": {
                    "domain_name": "api.example.com",
                    "registrar": "API Registrar",
                    "expiry_date": "2024-11-15"
                },
                "tags": ["api", "production", "critical"],
                "source": "manual"
            },
            {
                "asset_type_id": self.domain_type_id,
                "data": {
                    "domain_name": "staging.example.com",
                    "registrar": "Staging Registrar",
                    "expiry_date": "2024-09-30"
                },
                "tags": ["staging", "internal", "web"],
                "source": "nmap"
            }
        ]
        
        # Create assets individually
        for i, asset_data in enumerate(test_assets):
            async with self.session.post(
                f"{self.base_url}/api/assets/",
                json=asset_data,
                headers=self.headers
            ) as response:
                if response.status == 201:
                    data = await response.json()
                    self.created_assets.append(data["id"])
                    print(f"✓ Created asset {i+1}: {data['data']['domain_name']}")
                else:
                    error_text = await response.text()
                    print(f"❌ Failed to create asset {i+1}: {response.status} - {error_text}")
        
        return len(self.created_assets) == len(test_assets)
    
    async def test_basic_search(self):
        """Test basic search functionality"""
        print("\n🧪 Testing basic search...")
        
        # Search for "example"
        async with self.session.get(
            f"{self.base_url}/api/assets/?search=example",
            headers=self.headers
        ) as response:
            if response.status == 200:
                data = await response.json()
                print(f"✓ Search for 'example' returned {len(data)} results")
                return len(data) > 0
            else:
                print(f"❌ Basic search failed: {response.status}")
                return False
    
    async def test_tag_filtering(self):
        """Test tag filtering"""
        print("\n🧪 Testing tag filtering...")
        
        # Filter by production tag
        async with self.session.get(
            f"{self.base_url}/api/assets/?tags=production",
            headers=self.headers
        ) as response:
            if response.status == 200:
                data = await response.json()
                print(f"✓ Tag filter 'production' returned {len(data)} results")
                return len(data) > 0
            else:
                print(f"❌ Tag filtering failed: {response.status}")
                return False
    
    async def test_source_filtering(self):
        """Test source filtering"""
        print("\n🧪 Testing source filtering...")
        
        # Filter by manual source
        async with self.session.get(
            f"{self.base_url}/api/assets/?source=manual",
            headers=self.headers
        ) as response:
            if response.status == 200:
                data = await response.json()
                print(f"✓ Source filter 'manual' returned {len(data)} results")
                return len(data) > 0
            else:
                print(f"❌ Source filtering failed: {response.status}")
                return False
    
    async def test_sorting(self):
        """Test sorting functionality"""
        print("\n🧪 Testing sorting...")
        
        # Sort by created_at ascending
        async with self.session.get(
            f"{self.base_url}/api/assets/?sort_by=created_at&sort_order=asc",
            headers=self.headers
        ) as response:
            if response.status == 200:
                data = await response.json()
                print(f"✓ Sort by created_at ASC returned {len(data)} results")
                return len(data) > 0
            else:
                print(f"❌ Sorting failed: {response.status}")
                return False
    
    async def test_pagination(self):
        """Test pagination"""
        print("\n🧪 Testing pagination...")
        
        # Get first page
        async with self.session.get(
            f"{self.base_url}/api/assets/?skip=0&limit=2",
            headers=self.headers
        ) as response:
            if response.status == 200:
                data = await response.json()
                print(f"✓ Pagination (limit=2) returned {len(data)} results")
                return len(data) <= 2
            else:
                print(f"❌ Pagination failed: {response.status}")
                return False
    
    async def test_overview_stats(self):
        """Test overview statistics"""
        print("\n🧪 Testing overview statistics...")
        
        async with self.session.get(
            f"{self.base_url}/api/assets/stats/overview",
            headers=self.headers
        ) as response:
            if response.status == 200:
                data = await response.json()
                print(f"✓ Overview stats - Total assets: {data['total_assets']}")
                print(f"  - Recent assets: {data['recent_assets']}")
                print(f"  - Asset types: {len(data['asset_types'])}")
                print(f"  - Sources: {len(data['sources'])}")
                return data['total_assets'] > 0
            else:
                print(f"❌ Overview stats failed: {response.status}")
                return False
    
    async def test_tags_stats(self):
        """Test tags statistics"""
        print("\n🧪 Testing tags statistics...")
        
        async with self.session.get(
            f"{self.base_url}/api/assets/stats/tags",
            headers=self.headers
        ) as response:
            if response.status == 200:
                data = await response.json()
                print(f"✓ Tags stats returned {len(data)} popular tags")
                for tag in data[:5]:  # Show top 5
                    print(f"  - {tag['tag']}: {tag['count']}")
                return len(data) > 0
            else:
                print(f"❌ Tags stats failed: {response.status}")
                return False
    
    async def test_timeline_stats(self):
        """Test timeline statistics"""
        print("\n🧪 Testing timeline statistics...")
        
        async with self.session.get(
            f"{self.base_url}/api/assets/stats/timeline?interval=1d&days=7",
            headers=self.headers
        ) as response:
            if response.status == 200:
                data = await response.json()
                print(f"✓ Timeline stats returned {len(data)} data points")
                total_in_timeline = sum(point['count'] for point in data)
                print(f"  - Total assets in timeline: {total_in_timeline}")
                return len(data) > 0
            else:
                print(f"❌ Timeline stats failed: {response.status}")
                return False
    
    async def test_bulk_operations(self):
        """Test bulk operations"""
        print("\n🧪 Testing bulk operations...")
        
        bulk_assets = [
            {
                "asset_type_id": self.domain_type_id,
                "data": {
                    "domain_name": f"bulk-{i}.example.com",
                    "registrar": "Bulk Registrar"
                },
                "tags": ["bulk", "test"],
                "source": "bulk-import"
            }
            for i in range(3)
        ]
        
        async with self.session.post(
            f"{self.base_url}/api/assets/bulk",
            json=bulk_assets,
            headers=self.headers
        ) as response:
            if response.status == 200:
                data = await response.json()
                print(f"✓ Bulk operation created {data['created_count']} assets")
                print(f"  - Error count: {data['error_count']}")
                return data['created_count'] > 0
            else:
                error_text = await response.text()
                print(f"❌ Bulk operation failed: {response.status} - {error_text}")
                return False
    
    async def test_advanced_search_combinations(self):
        """Test advanced search combinations"""
        print("\n🧪 Testing advanced search combinations...")
        
        # Combine multiple filters
        async with self.session.get(
            f"{self.base_url}/api/assets/?search=example&tags=production&source=manual&sort_by=created_at&sort_order=desc",
            headers=self.headers
        ) as response:
            if response.status == 200:
                data = await response.json()
                print(f"✓ Combined search returned {len(data)} results")
                return True
            else:
                print(f"❌ Combined search failed: {response.status}")
                return False
    
    async def cleanup_test_assets(self):
        """Clean up created test assets"""
        print("\n🧹 Cleaning up test assets...")
        
        deleted_count = 0
        for asset_id in self.created_assets:
            async with self.session.delete(
                f"{self.base_url}/api/assets/{asset_id}",
                headers=self.headers
            ) as response:
                if response.status == 204:
                    deleted_count += 1
        
        print(f"✓ Deleted {deleted_count} test assets")
        return deleted_count > 0
    
    async def run_all_tests(self):
        """Run all asset API tests"""
        print("🚀 Starting Enhanced Asset Search and Statistics API Tests")
        print("=" * 80)
        
        # Login
        if not await self.login():
            return False
        
        # Get domain asset type ID
        if not await self.get_domain_asset_type_id():
            return False
        
        # Create test assets
        if not await self.create_test_assets():
            return False
        
        # Wait a bit for ES indexing
        await asyncio.sleep(2)
        
        tests = [
            self.test_basic_search(),
            self.test_tag_filtering(),
            self.test_source_filtering(),
            self.test_sorting(),
            self.test_pagination(),
            self.test_overview_stats(),
            self.test_tags_stats(),
            self.test_timeline_stats(),
            self.test_bulk_operations(),
            self.test_advanced_search_combinations()
        ]
        
        # Run all tests
        results = await asyncio.gather(*tests, return_exceptions=True)
        
        # Count successful tests
        successful_tests = sum(1 for result in results if result is True)
        total_tests = len(tests)
        
        print(f"\n" + "=" * 80)
        print(f"📊 Test Results: {successful_tests}/{total_tests} tests passed")
        
        # Cleanup
        await self.cleanup_test_assets()
        
        return successful_tests == total_tests

async def main():
    """Main test runner"""
    print("Bounty AMS - Enhanced Asset Search and Statistics API Test Suite")
    print("Make sure the API server is running on localhost:8090")
    print("And that the database is initialized with admin user")
    
    async with AssetAPITester() as tester:
        success = await tester.run_all_tests()
    
    return success

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)