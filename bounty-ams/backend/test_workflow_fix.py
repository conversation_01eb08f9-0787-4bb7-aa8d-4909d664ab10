#!/usr/bin/env python3
"""
Test Workflow Execution Fix
"""
import requests
import json
from datetime import datetime

BASE_URL = "http://localhost:8000"

def test_workflow_execution():
    """Test workflow execution after fix"""
    
    # Login first
    login_data = {
        "username": "testuser",
        "password": "testpass"
    }
    
    response = requests.post(f"{BASE_URL}/api/auth/login", json=login_data)
    if response.status_code != 200:
        print(f"❌ Login failed: {response.status_code}")
        return False
    
    token = response.json()["access_token"]
    headers = {"Authorization": f"Bearer {token}"}
    
    print("✅ Login successful")
    
    # Test workflow execution
    workflow_data = {
        "execution_id": f"workflow-test-{datetime.now().strftime('%Y%m%d%H%M%S')}",
        "workflow_id": "quick_port_scan",
        "target": "***********",
        "priority": 2,
        "variables": {
            "scan_type": "quick"
        }
    }
    
    print(f"Testing workflow execution: {workflow_data['execution_id']}")
    
    response = requests.post(
        f"{BASE_URL}/api/workflows/execute",
        json=workflow_data,
        headers=headers
    )
    
    print(f"Status Code: {response.status_code}")
    
    if response.status_code == 200:
        workflow_status = response.json()
        print(f"✅ Workflow execution successful!")
        print(f"Execution ID: {workflow_status['execution_id']}")
        print(f"Total tasks: {workflow_status['total_tasks']}")
        print(f"Status: {workflow_status['status']}")
        return True
    else:
        print(f"❌ Workflow execution failed: {response.status_code}")
        print(f"Response: {response.text}")
        return False

if __name__ == "__main__":
    test_workflow_execution()