#!/usr/bin/env python3
"""
Recreate agent_keys table with correct schema
"""
import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import text
from database import get_db

async def recreate_agent_keys_table():
    """Recreate agent_keys table"""
    
    drop_table_sql = "DROP TABLE IF EXISTS agent_keys CASCADE"
    
    create_table_sql = """
    CREATE TABLE agent_keys (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        key_id VARCHAR(100) UNIQUE NOT NULL,
        key_hash VARCHAR(255) NOT NULL,
        agent_id VARCHAR(100),
        name VARCHAR(200) NOT NULL,
        description TEXT,
        status VARCHAR(20) DEFAULT 'active',
        expires_at TIMESTAMP WITH TIME ZONE,
        created_by_user_id UUID REFERENCES users(id),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        last_used_at TIMESTAMP WITH TIME ZONE,
        usage_count INTEGER DEFAULT 0
    )
    """
    
    create_indexes_sql = [
        "CREATE INDEX idx_agent_keys_key_id ON agent_keys(key_id)",
        "CREATE INDEX idx_agent_keys_agent_id ON agent_keys(agent_id)",
        "CREATE INDEX idx_agent_keys_status ON agent_keys(status)"
    ]
    
    async for db in get_db():
        try:
            # 删除表
            await db.execute(text(drop_table_sql))
            print("✅ Dropped existing agent_keys table")
            
            # 创建表
            await db.execute(text(create_table_sql))
            print("✅ Agent keys table created successfully")
            
            # 创建索引
            for index_sql in create_indexes_sql:
                await db.execute(text(index_sql))
            print("✅ Agent keys indexes created successfully")
            
            await db.commit()
            break
        except Exception as e:
            print(f"❌ Failed to recreate agent keys table: {e}")
            await db.rollback()
            raise

if __name__ == "__main__":
    asyncio.run(recreate_agent_keys_table())