#!/usr/bin/env python3
"""
更新资产搜索端点以支持混合查询
"""

import asyncio
from typing import Dict, List, Any, Optional
from fastapi import Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, or_, and_
from elasticsearch.exceptions import NotFoundError

from models_dynamic import ModelType, DynamicEntity
from database import get_db
from elasticsearch_client import get_es_client
from auth import get_current_user

class HybridAssetSearchService:
    """混合资产搜索服务 - 同时查询PostgreSQL和Elasticsearch"""
    
    def __init__(self, db: AsyncSession, es_client):
        self.db = db
        self.es_client = es_client
    
    async def search_assets(
        self,
        skip: int = 0,
        limit: int = 100,
        search: Optional[str] = None,
        platform_id: Optional[str] = None,
        project_id: Optional[str] = None,
        asset_type: Optional[str] = None,
        confidence: Optional[str] = None,
        status: Optional[str] = None,
        use_elasticsearch: bool = True,
        use_postgresql: bool = True
    ) -> Dict[str, Any]:
        """
        混合搜索资产
        
        Args:
            skip: 跳过的记录数
            limit: 返回的记录数
            search: 搜索关键词
            platform_id: 平台ID
            project_id: 项目ID
            asset_type: 资产类型
            confidence: 置信度
            status: 状态
            use_elasticsearch: 是否使用Elasticsearch
            use_postgresql: 是否使用PostgreSQL
            
        Returns:
            搜索结果
        """
        results = []
        total = 0
        
        # 如果两个数据源都启用，优先使用PostgreSQL
        if use_postgresql:
            pg_results = await self.search_in_postgresql(
                skip=skip,
                limit=limit,
                search=search,
                platform_id=platform_id,
                project_id=project_id,
                asset_type=asset_type,
                confidence=confidence,
                status=status
            )
            results.extend(pg_results['assets'])
            total = pg_results['total']
        
        # 如果PostgreSQL结果不足且启用了Elasticsearch，补充查询
        if use_elasticsearch and len(results) < limit:
            remaining_limit = limit - len(results)
            es_results = await self.search_in_elasticsearch(
                skip=skip + len(results),
                limit=remaining_limit,
                search=search,
                platform_id=platform_id,
                project_id=project_id,
                asset_type=asset_type,
                confidence=confidence,
                status=status
            )
            
            # 去重合并结果
            existing_ids = {asset.get('_id') or asset.get('id') for asset in results}
            for asset in es_results['assets']:
                asset_id = asset.get('_id') or asset.get('id')
                if asset_id not in existing_ids:
                    results.append(asset)
            
            total = max(total, es_results['total'])
        
        return {
            'assets': results[:limit],
            'total': total,
            'source': 'hybrid'
        }
    
    async def search_in_postgresql(
        self,
        skip: int = 0,
        limit: int = 100,
        search: Optional[str] = None,
        platform_id: Optional[str] = None,
        project_id: Optional[str] = None,
        asset_type: Optional[str] = None,
        confidence: Optional[str] = None,
        status: Optional[str] = None
    ) -> Dict[str, Any]:
        """在PostgreSQL中搜索资产"""
        
        # 获取所有可能包含资产的模型类型
        exclude_models = ['platform', 'project', 'vulnerability_kb', 'agent', 'task', 'customer_info']
        
        model_types_query = select(ModelType).where(
            ModelType.is_active == True,
            ~ModelType.name.in_(exclude_models)
        )
        model_types_result = await self.db.execute(model_types_query)
        asset_model_types = model_types_result.scalars().all()
        
        if not asset_model_types:
            return {'assets': [], 'total': 0}
        
        # 获取这些模型类型的所有实体
        asset_model_ids = [mt.id for mt in asset_model_types]
        
        # 构建查询
        query = select(DynamicEntity).where(DynamicEntity.model_type_id.in_(asset_model_ids))
        count_query = select(DynamicEntity).where(DynamicEntity.model_type_id.in_(asset_model_ids))
        
        # 应用搜索条件
        if search:
            search_conditions = []
            search_conditions.append(DynamicEntity.entity_data.op('->>')('name').ilike(f"%{search}%"))
            search_conditions.append(DynamicEntity.entity_data.op('->>')('asset_value').ilike(f"%{search}%"))
            search_conditions.append(DynamicEntity.entity_data.op('->>')('asset_host').ilike(f"%{search}%"))
            search_conditions.append(DynamicEntity.entity_data.op('->>')('host').ilike(f"%{search}%"))
            search_conditions.append(DynamicEntity.entity_data.op('->>')('ip').ilike(f"%{search}%"))
            search_conditions.append(DynamicEntity.entity_data.op('->>')('domain').ilike(f"%{search}%"))
            search_conditions.append(DynamicEntity.entity_data.op('->>')('url').ilike(f"%{search}%"))
            search_condition = or_(*search_conditions)
            query = query.where(search_condition)
            count_query = count_query.where(search_condition)
        
        # 应用筛选条件
        if platform_id:
            condition = DynamicEntity.entity_data.op('->>')('platform_id') == platform_id
            query = query.where(condition)
            count_query = count_query.where(condition)
        
        if project_id:
            condition = DynamicEntity.entity_data.op('->>')('project_id') == project_id
            query = query.where(condition)
            count_query = count_query.where(condition)
        
        if asset_type:
            condition = DynamicEntity.entity_data.op('->>')('asset_type') == asset_type
            query = query.where(condition)
            count_query = count_query.where(condition)
        
        if confidence:
            condition = DynamicEntity.entity_data.op('->>')('confidence') == confidence
            query = query.where(condition)
            count_query = count_query.where(condition)
        
        if status:
            condition = DynamicEntity.entity_data.op('->>')('status') == status
            query = query.where(condition)
            count_query = count_query.where(condition)
        
        # 分页和排序
        query = query.order_by(DynamicEntity.created_at.desc()).offset(skip).limit(limit)
        
        # 执行查询
        result = await self.db.execute(query)
        entities = result.scalars().all()
        
        # 获取总数
        count_result = await self.db.execute(count_query)
        total = len(count_result.scalars().all())
        
        # 转换为统一格式
        assets = []
        for entity in entities:
            asset = {
                'id': str(entity.id),
                '_id': str(entity.id),
                **entity.entity_data,
                '_model_type_id': str(entity.model_type_id),
                '_created_at': entity.created_at.isoformat() if entity.created_at else None,
                '_updated_at': entity.updated_at.isoformat() if entity.updated_at else None,
                '_source': 'postgresql'
            }
            assets.append(asset)
        
        return {
            'assets': assets,
            'total': total
        }
    
    async def search_in_elasticsearch(
        self,
        skip: int = 0,
        limit: int = 100,
        search: Optional[str] = None,
        platform_id: Optional[str] = None,
        project_id: Optional[str] = None,
        asset_type: Optional[str] = None,
        confidence: Optional[str] = None,
        status: Optional[str] = None
    ) -> Dict[str, Any]:
        """在Elasticsearch中搜索资产"""
        
        # 构建ES查询
        query_body = {
            "from": skip,
            "size": limit,
            "query": {
                "bool": {
                    "must": []
                }
            },
            "sort": [
                {"entity_data.discovered_at": {"order": "desc", "missing": "_last"}},
                {"created_at": {"order": "desc", "missing": "_last"}},
                {"_id": {"order": "desc"}}
            ]
        }
        
        # 添加搜索条件
        if search:
            query_body["query"]["bool"]["must"].append({
                "multi_match": {
                    "query": search,
                    "fields": [
                        "entity_data.asset_value^3",
                        "entity_data.asset_host^2",
                        "entity_data.name^2",
                        "entity_data.ip^2",
                        "entity_data.domain^2",
                        "entity_data.url^2",
                        "entity_data.host^2",
                        "entity_data.tags^1.5",
                        "entity_data.metadata.*"
                    ],
                    "type": "best_fields",
                    "fuzziness": "AUTO"
                }
            })
        
        # 添加过滤条件
        if platform_id:
            query_body["query"]["bool"]["must"].append({
                "term": {"entity_data.platform_id": platform_id}
            })
        
        if project_id:
            query_body["query"]["bool"]["must"].append({
                "term": {"entity_data.project_id": project_id}
            })
        
        if asset_type:
            query_body["query"]["bool"]["must"].append({
                "term": {"entity_data.asset_type.keyword": asset_type}
            })
        
        if confidence:
            query_body["query"]["bool"]["must"].append({
                "term": {"entity_data.confidence.keyword": confidence}
            })
        
        if status:
            query_body["query"]["bool"]["must"].append({
                "term": {"entity_data.status.keyword": status}
            })
        
        # 如果没有搜索条件，使用match_all
        if not query_body["query"]["bool"]["must"]:
            query_body["query"] = {"match_all": {}}
        
        try:
            # 执行搜索
            response = await self.es_client.search(
                index="enhanced_asset-*,assets-*,dynamic_*",
                body=query_body,
                ignore=[404]
            )
            
            # 处理结果
            assets = []
            for hit in response.get("hits", {}).get("hits", []):
                asset = hit["_source"]
                asset["_id"] = hit["_id"]
                asset["_score"] = hit.get("_score", 0)
                asset["_source"] = "elasticsearch"
                assets.append(asset)
            
            total = response.get("hits", {}).get("total", {}).get("value", 0)
            
            return {
                'assets': assets,
                'total': total
            }
            
        except Exception as e:
            # 如果ES查询失败，返回空结果
            return {'assets': [], 'total': 0}
    
    async def get_asset_stats(self) -> Dict[str, Any]:
        """获取资产统计信息"""
        
        # PostgreSQL统计
        pg_stats = await self.get_postgresql_stats()
        
        # Elasticsearch统计
        es_stats = await self.get_elasticsearch_stats()
        
        return {
            'postgresql': pg_stats,
            'elasticsearch': es_stats,
            'total_unique_assets': max(pg_stats.get('total', 0), es_stats.get('total', 0))
        }
    
    async def get_postgresql_stats(self) -> Dict[str, Any]:
        """获取PostgreSQL资产统计"""
        # 获取所有可能包含资产的模型类型
        exclude_models = ['platform', 'project', 'vulnerability_kb', 'agent', 'task', 'customer_info']
        
        model_types_query = select(ModelType).where(
            ModelType.is_active == True,
            ~ModelType.name.in_(exclude_models)
        )
        model_types_result = await self.db.execute(model_types_query)
        asset_model_types = model_types_result.scalars().all()
        
        if not asset_model_types:
            return {'total': 0, 'by_type': {}}
        
        asset_model_ids = [mt.id for mt in asset_model_types]
        
        # 总数统计
        total_query = select(DynamicEntity).where(DynamicEntity.model_type_id.in_(asset_model_ids))
        total_result = await self.db.execute(total_query)
        total = len(total_result.scalars().all())
        
        # 按类型统计
        by_type = {}
        for model_type in asset_model_types:
            type_query = select(DynamicEntity).where(DynamicEntity.model_type_id == model_type.id)
            type_result = await self.db.execute(type_query)
            count = len(type_result.scalars().all())
            by_type[model_type.name] = count
        
        return {
            'total': total,
            'by_type': by_type
        }
    
    async def get_elasticsearch_stats(self) -> Dict[str, Any]:
        """获取Elasticsearch资产统计"""
        try:
            # 总数统计
            count_response = await self.es_client.count(
                index="enhanced_asset-*,assets-*,dynamic_*",
                ignore=[404]
            )
            total = count_response.get('count', 0)
            
            # 按类型统计
            agg_response = await self.es_client.search(
                index="enhanced_asset-*,assets-*,dynamic_*",
                body={
                    "size": 0,
                    "aggs": {
                        "by_type": {
                            "terms": {
                                "field": "entity_data.asset_type.keyword",
                                "missing": "unknown",
                                "size": 50
                            }
                        }
                    }
                },
                ignore=[404]
            )
            
            by_type = {}
            if "aggregations" in agg_response:
                for bucket in agg_response["aggregations"]["by_type"]["buckets"]:
                    by_type[bucket["key"]] = bucket["doc_count"]
            
            return {
                'total': total,
                'by_type': by_type
            }
            
        except Exception as e:
            return {'total': 0, 'by_type': {}}

# 更新路由函数
async def search_assets_hybrid(
    skip: int = 0,
    limit: int = 100,
    search: Optional[str] = Query(None),
    platform_id: Optional[str] = Query(None),
    project_id: Optional[str] = Query(None),
    asset_type: Optional[str] = Query(None),
    confidence: Optional[str] = Query(None),
    status: Optional[str] = Query(None),
    use_elasticsearch: bool = Query(True),
    use_postgresql: bool = Query(True),
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """混合搜索资产（新的端点）"""
    try:
        es_client = await get_es_client()
        search_service = HybridAssetSearchService(db, es_client)
        
        results = await search_service.search_assets(
            skip=skip,
            limit=limit,
            search=search,
            platform_id=platform_id,
            project_id=project_id,
            asset_type=asset_type,
            confidence=confidence,
            status=status,
            use_elasticsearch=use_elasticsearch,
            use_postgresql=use_postgresql
        )
        
        return results
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"搜索失败: {str(e)}")

async def get_asset_stats_hybrid(
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """获取混合资产统计"""
    try:
        es_client = await get_es_client()
        search_service = HybridAssetSearchService(db, es_client)
        
        stats = await search_service.get_asset_stats()
        return stats
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取统计失败: {str(e)}")

print("混合资产搜索服务已创建完成！")
print("\n使用说明：")
print("1. 运行 python fix_asset_management.py 来修复现有数据")
print("2. 将此文件中的函数集成到你的路由中")
print("3. 前端可以使用新的混合搜索端点") 