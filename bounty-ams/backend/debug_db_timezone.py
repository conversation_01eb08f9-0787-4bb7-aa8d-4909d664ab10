#!/usr/bin/env python3
"""
Debug database timezone settings
"""
import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import datetime, timezone
from sqlalchemy import text
from database import get_db

async def debug_db_timezone():
    """调试数据库时区设置"""
    
    print(f"Python datetime.now(timezone.utc): {datetime.now(timezone.utc)}")
    print()
    
    async for db in get_db():
        try:
            # 检查数据库时区设置
            result = await db.execute(text("SHOW timezone;"))
            db_timezone = result.scalar_one()
            print(f"Database timezone: {db_timezone}")
            
            # 检查当前数据库时间
            result = await db.execute(text("SELECT NOW();"))
            db_now = result.scalar_one()
            print(f"Database NOW(): {db_now}")
            
            # 检查UTC时间
            result = await db.execute(text("SELECT NOW() AT TIME ZONE 'UTC';"))
            db_utc = result.scalar_one()
            print(f"Database UTC: {db_utc}")
            
            break
        except Exception as e:
            print(f"❌ Error: {e}")
            raise

if __name__ == "__main__":
    asyncio.run(debug_db_timezone())