#!/usr/bin/env python3
"""
Clean up fake agent data
"""
import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import datetime, timedelta
from sqlalchemy import select, delete
from database import get_db
from models_dynamic import Agent

async def cleanup_fake_agents():
    """清理假数据agents"""
    
    # 定义在线判断标准：最后心跳时间在5分钟内
    online_threshold = datetime.utcnow() - timedelta(minutes=5)
    
    print(f"Current UTC time: {datetime.utcnow()}")
    print(f"Online threshold (5 minutes ago): {online_threshold}")
    print("="*60)
    
    async for db in get_db():
        try:
            # 查找所有离线的agents（心跳超过5分钟的）
            result = await db.execute(
                select(Agent).where(
                    (Agent.last_seen_at.is_(None)) |
                    (Agent.last_seen_at <= online_threshold)
                )
            )
            offline_agents = result.scalars().all()
            
            print(f"Found {len(offline_agents)} offline agents to remove:")
            
            for agent in offline_agents:
                print(f"  - {agent.name} ({agent.agent_id}) - Last seen: {agent.last_seen_at}")
            
            if offline_agents:
                # 删除这些离线agents
                await db.execute(
                    delete(Agent).where(
                        (Agent.last_seen_at.is_(None)) |
                        (Agent.last_seen_at <= online_threshold)
                    )
                )
                await db.commit()
                print(f"\n✅ Removed {len(offline_agents)} offline agents")
            else:
                print("\n✅ No offline agents to remove")
            
            # 查看剩余的agents
            result = await db.execute(select(Agent))
            remaining_agents = result.scalars().all()
            
            print(f"\nRemaining agents: {len(remaining_agents)}")
            for agent in remaining_agents:
                print(f"  - {agent.name} ({agent.agent_id}) - Last seen: {agent.last_seen_at}")
            
            break
        except Exception as e:
            print(f"❌ Error: {e}")
            await db.rollback()
            raise

if __name__ == "__main__":
    asyncio.run(cleanup_fake_agents())