#!/usr/bin/env python3

import asyncio
import sys
import os

# Add backend to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'backend'))

from elasticsearch_client import es_client
from config import settings

async def test_elasticsearch():
    """Test Elasticsearch connection"""
    try:
        print(f"Testing connection to Elasticsearch at {settings.ELASTICSEARCH_URL}")
        
        # Connect
        await es_client.connect()
        print("✓ Elasticsearch connection successful")
        
        # Test basic operations
        client = es_client.get_client()
        
        # Get cluster info
        info = await client.info()
        print(f"✓ Elasticsearch version: {info['version']['number']}")
        
        # Test index creation
        test_doc = {
            "asset_type": "test",
            "data": {"name": "test_asset"},
            "created_at": "2024-01-01T00:00:00"
        }
        
        result = await client.index(index="test_assets", body=test_doc)
        print(f"✓ Document indexed: {result['_id']}")
        
        # Test search
        search_result = await client.search(
            index="test_assets",
            body={"query": {"match_all": {}}}
        )
        print(f"✓ Search successful: {search_result['hits']['total']['value']} documents found")
        
        # Cleanup test index
        await client.indices.delete(index="test_assets", ignore=[400, 404])
        print("✓ Test index cleaned up")
        
        print("\n🎉 All Elasticsearch tests passed!")
        
    except Exception as e:
        print(f"❌ Elasticsearch test failed: {e}")
        return False
    
    finally:
        await es_client.disconnect()
    
    return True

if __name__ == "__main__":
    result = asyncio.run(test_elasticsearch())
    sys.exit(0 if result else 1)