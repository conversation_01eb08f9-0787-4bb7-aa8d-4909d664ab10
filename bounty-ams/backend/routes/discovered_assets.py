"""
Asset Discovery API Routes
Handles discovered assets stored in Elasticsearch
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional, Dict, Any
from datetime import datetime

from database import get_db
from models_dynamic import User
from auth import get_current_user
from asset_aggregation import AssetAggregationService, batch_process_unprocessed_tasks
from elasticsearch_client import get_es_client

router = APIRouter()

@router.get("/stats")
async def get_asset_statistics(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取资产统计信息"""
    try:
        es_client = await get_es_client()
        service = AssetAggregationService(db, es_client)
        stats = await service.get_asset_statistics()
        return stats
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting asset statistics: {str(e)}"
        )

@router.get("/search")
async def search_assets(
    q: Optional[str] = Query(None, description="搜索查询"),
    asset_type: Optional[str] = Query(None, description="资产类型过滤"),
    source: Optional[str] = Query(None, description="发现来源过滤"),
    confidence: Optional[str] = Query(None, description="置信度过滤"),
    target: Optional[str] = Query(None, description="目标过滤"),
    platform_id: Optional[str] = Query(None, description="平台ID过滤"),
    project_id: Optional[str] = Query(None, description="项目ID过滤"),
    date_from: Optional[str] = Query(None, description="起始日期 (YYYY-MM-DD)"),
    date_to: Optional[str] = Query(None, description="结束日期 (YYYY-MM-DD)"),
    size: int = Query(100, description="返回结果数量"),
    offset: int = Query(0, description="偏移量"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """搜索发现的资产"""
    try:
        es_client = await get_es_client()
        
        # 构建搜索查询
        search_body = {
            "from": offset,
            "size": size,
            "query": {
                "bool": {
                    "must": []
                }
            },
            "sort": [
                {"entity_data.discovered_at": {"order": "desc"}}
            ]
        }
        
        # 添加搜索条件
        if q:
            search_body["query"]["bool"]["must"].append({
                "multi_match": {
                    "query": q,
                    "fields": ["entity_data.asset_value", "entity_data.asset_host", "entity_data.platform_id", "entity_data.project_id"]
                }
            })
        
        # 添加过滤条件
        if asset_type:
            search_body["query"]["bool"]["must"].append({
                "term": {"entity_data.asset_type.keyword": asset_type}
            })
        
        if source:
            search_body["query"]["bool"]["must"].append({
                "term": {"entity_data.source_task_type.keyword": source}
            })
        
        if confidence:
            search_body["query"]["bool"]["must"].append({
                "term": {"entity_data.confidence.keyword": confidence}
            })
        
        # 平台ID过滤 - 支持target参数（向后兼容）和platform_id参数
        platform_filter_value = platform_id or target
        if platform_filter_value:
            search_body["query"]["bool"]["must"].append({
                "term": {"entity_data.platform_id": platform_filter_value}
            })
        
        # 项目ID过滤
        if project_id:
            search_body["query"]["bool"]["must"].append({
                "term": {"entity_data.project_id": project_id}
            })
        
        # 添加日期范围过滤
        if date_from or date_to:
            date_range = {"range": {"entity_data.discovered_at": {}}}
            if date_from:
                date_range["range"]["entity_data.discovered_at"]["gte"] = date_from
            if date_to:
                date_range["range"]["entity_data.discovered_at"]["lte"] = date_to
            search_body["query"]["bool"]["must"].append(date_range)
        
        # 如果没有搜索条件，使用match_all
        if not search_body["query"]["bool"]["must"]:
            search_body["query"] = {"match_all": {}}
        
        # 执行搜索 - 使用enhanced_asset索引以匹配测试数据
        response = await es_client.search(
            index="enhanced_asset-*",
            body=search_body
        )
        
        # 处理结果
        assets = []
        for hit in response.get("hits", {}).get("hits", []):
            asset = hit["_source"]
            asset["_id"] = hit["_id"]
            asset["_score"] = hit.get("_score", 0)
            assets.append(asset)
        
        return {
            "total": response.get("hits", {}).get("total", {}).get("value", 0),
            "assets": assets
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error searching assets: {str(e)}"
        )

@router.get("/types")
async def get_asset_types(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取所有资产类型"""
    try:
        es_client = await get_es_client()
        
        search_body = {
            "size": 0,
            "aggs": {
                "asset_types": {
                    "terms": {
                        "field": "entity_data.asset_type.keyword",
                        "size": 50
                    }
                }
            }
        }
        
        response = await es_client.search(
            index="enhanced_asset-*",
            body=search_body
        )
        
        types = []
        for bucket in response.get("aggregations", {}).get("asset_types", {}).get("buckets", []):
            types.append({
                "type": bucket["key"],
                "count": bucket["doc_count"]
            })
        
        return {"types": types}
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting asset types: {str(e)}"
        )

@router.get("/sources")
async def get_asset_sources(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取所有发现来源"""
    try:
        es_client = await get_es_client()
        
        search_body = {
            "size": 0,
            "aggs": {
                "sources": {
                    "terms": {
                        "field": "entity_data.source_task_type.keyword",
                        "size": 50
                    }
                }
            }
        }
        
        response = await es_client.search(
            index="enhanced_asset-*",
            body=search_body
        )
        
        sources = []
        for bucket in response.get("aggregations", {}).get("sources", {}).get("buckets", []):
            sources.append({
                "source": bucket["key"],
                "count": bucket["doc_count"]
            })
        
        return {"sources": sources}
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting asset sources: {str(e)}"
        )

@router.post("/process-pending")
async def process_pending_assets(
    limit: int = Query(100, description="处理任务数量限制"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """批量处理待处理的任务资产"""
    try:
        total_assets = await batch_process_unprocessed_tasks(db, limit)
        return {
            "message": f"Processed assets from tasks",
            "total_assets_processed": total_assets
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error processing pending assets: {str(e)}"
        )

@router.get("/timeline")
async def get_asset_timeline(
    days: int = Query(30, description="时间范围（天数）"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取资产发现时间线"""
    try:
        es_client = await get_es_client()
        
        search_body = {
            "size": 0,
            "query": {
                "range": {
                    "entity_data.discovered_at": {
                        "gte": f"now-{days}d/d"
                    }
                }
            },
            "aggs": {
                "timeline": {
                    "date_histogram": {
                        "field": "entity_data.discovered_at",
                        "calendar_interval": "1d",
                        "format": "yyyy-MM-dd"
                    },
                    "aggs": {
                        "by_type": {
                            "terms": {
                                "field": "entity_data.asset_type.keyword",
                                "size": 10
                            }
                        }
                    }
                }
            }
        }
        
        response = await es_client.search(
            index="enhanced_asset-*",
            body=search_body
        )
        
        timeline = []
        for bucket in response.get("aggregations", {}).get("timeline", {}).get("buckets", []):
            date_data = {
                "date": bucket["key_as_string"],
                "total": bucket["doc_count"],
                "by_type": {}
            }
            
            for type_bucket in bucket.get("by_type", {}).get("buckets", []):
                date_data["by_type"][type_bucket["key"]] = type_bucket["doc_count"]
            
            timeline.append(date_data)
        
        return {"timeline": timeline}
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting asset timeline: {str(e)}"
        )

@router.get("/{asset_id}")
async def get_asset_details(
    asset_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取资产详细信息"""
    try:
        es_client = await get_es_client()
        
        # 首先尝试通过文档ID获取
        try:
            response = await es_client.get(
                index="assets-*",
                id=asset_id
            )
            asset = response["_source"]
            asset["_id"] = response["_id"]
            return asset
        except:
            # 如果通过ID找不到，尝试通过asset_id字段搜索
            search_body = {
                "query": {
                    "term": {"asset_id.keyword": asset_id}
                },
                "size": 1
            }
            
            response = await es_client.search(
                index="assets-*",
                body=search_body
            )
            
            hits = response.get("hits", {}).get("hits", [])
            if not hits:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Asset not found"
                )
            
            asset = hits[0]["_source"]
            asset["_id"] = hits[0]["_id"]
            return asset
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting asset details: {str(e)}"
        )