"""
数据导入API路由
支持CSV和JSON格式的资产数据导入，自动字段解析和映射
"""
from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Form
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional, Dict, Any, Union
import csv
import json
import io
import uuid
from datetime import datetime

from database import get_db
from models_dynamic import AssetType, AssetTypeField, DynamicEntity, User, ModelType, ModelField
from auth import get_current_user, get_current_admin_user
from elasticsearch_client import get_es_client
from sqlalchemy import select
from sqlalchemy.orm import selectinload

router = APIRouter(prefix="/data-import", tags=["data-import"])


class FieldMapping:
    """字段映射类"""
    def __init__(self):
        self.source_field: str = ""
        self.target_field: str = ""
        self.field_type: str = "text"
        self.is_required: bool = False
        self.default_value: Optional[str] = None


class ImportPreview:
    """导入预览类"""
    def __init__(self):
        self.total_records: int = 0
        self.sample_data: List[Dict[str, Any]] = []
        self.detected_fields: List[Dict[str, Any]] = []
        self.suggested_mappings: List[Dict[str, Any]] = []


@router.post("/analyze")
async def analyze_import_file(
    file: UploadFile = File(...),
    asset_type_id: Optional[str] = Form(None),
    create_new_type: bool = Form(False),
    new_type_name: Optional[str] = Form(None),
    new_type_display_name: Optional[str] = Form(None),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    分析上传的文件，自动检测字段类型并建议映射
    """
    if not file.filename.lower().endswith(('.csv', '.json')):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="只支持CSV和JSON格式文件"
        )
    
    try:
        # 读取文件内容
        content = await file.read()
        
        if file.filename.lower().endswith('.csv'):
            preview = await _analyze_csv_file(content)
        else:
            preview = await _analyze_json_file(content)
        
        # 如果指定了资产类型，获取现有字段定义
        target_fields = []
        if asset_type_id and not create_new_type:
            asset_type = await _get_asset_type_with_fields(db, asset_type_id)
            if asset_type:
                target_fields = [
                    {
                        "field_name": field.field_name,
                        "display_name": field.display_name,
                        "field_type": field.field_type,
                        "is_required": field.is_required
                    }
                    for field in asset_type.fields
                ]
        
        # 生成字段映射建议
        suggested_mappings = _suggest_field_mappings(preview.detected_fields, target_fields)
        
        return {
            "file_info": {
                "filename": file.filename,
                "size": len(content),
                "type": "csv" if file.filename.lower().endswith('.csv') else "json"
            },
            "preview": {
                "total_records": preview.total_records,
                "sample_data": preview.sample_data[:5],  # 只返回前5条样本
                "detected_fields": preview.detected_fields
            },
            "target_fields": target_fields,
            "suggested_mappings": suggested_mappings,
            "can_create_new_type": create_new_type
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"文件分析失败: {str(e)}"
        )


@router.post("/execute")
async def execute_import(
    file: UploadFile = File(...),
    mappings: str = Form(...),  # JSON字符串格式的映射配置
    platform_id: Optional[str] = Form(None),  # 平台ID
    project_id: Optional[str] = Form(None),   # 项目ID
    asset_type_id: Optional[str] = Form(None),
    create_new_type: bool = Form(False),
    new_type_config: Optional[str] = Form(None),  # 新类型配置
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    执行数据导入
    """
    try:
        # 解析映射配置
        field_mappings = json.loads(mappings)
        
        # 读取文件数据
        content = await file.read()
        if file.filename.lower().endswith('.csv'):
            raw_data = await _parse_csv_file(content)
        else:
            raw_data = await _parse_json_file(content)
        
        # 确定目标资产类型
        target_asset_type = None
        if create_new_type and new_type_config:
            # 创建新的资产类型
            new_type_data = json.loads(new_type_config)
            target_asset_type = await _create_new_asset_type(db, new_type_data, current_user)
        elif asset_type_id:
            # 使用现有资产类型
            target_asset_type = await _get_asset_type_with_fields(db, asset_type_id)
            if not target_asset_type:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="指定的资产类型不存在"
                )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="必须指定资产类型或创建新类型"
            )
        
        # 执行数据转换和导入
        import_results = await _execute_data_import(
            db, raw_data, field_mappings, target_asset_type, current_user, 
            platform_id, project_id
        )
        
        return {
            "success": True,
            "results": import_results,
            "asset_type_id": str(target_asset_type.id),
            "message": f"成功导入 {import_results['successful_imports']} 条记录到 Elasticsearch"
        }
        
    except json.JSONDecodeError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="映射配置格式错误"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"导入失败: {str(e)}"
        )


# 辅助函数

async def _analyze_csv_file(content: bytes) -> ImportPreview:
    """分析CSV文件"""
    preview = ImportPreview()
    
    # 解码内容
    try:
        text_content = content.decode('utf-8')
    except UnicodeDecodeError:
        text_content = content.decode('gbk')  # 尝试GBK编码
    
    # 解析CSV - 只读取前1000行进行分析
    csv_reader = csv.DictReader(io.StringIO(text_content))
    rows = []
    total_count = 0
    
    for i, row in enumerate(csv_reader):
        total_count += 1
        if i < 1000:  # 只保留前1000行用于分析
            rows.append(row)
        elif i > 10000:  # 如果超过10000行，停止计数（避免大文件内存问题）
            total_count = f"{i}+"  # 显示为近似值
            break
    
    preview.total_records = total_count
    preview.sample_data = rows[:3]  # 只显示前3条样本
    
    # 自动检测字段类型 - 只使用前100行数据
    if rows:
        fieldnames = csv_reader.fieldnames or []
        preview.detected_fields = []
        
        for field_name in fieldnames:
            field_info = _detect_field_type(field_name, [row.get(field_name, '') for row in rows[:100]])
            preview.detected_fields.append(field_info)
    
    return preview


async def _analyze_json_file(content: bytes) -> ImportPreview:
    """分析JSON文件"""
    preview = ImportPreview()
    
    # 解析JSON
    try:
        json_data = json.loads(content.decode('utf-8'))
    except UnicodeDecodeError:
        json_data = json.loads(content.decode('gbk'))
    
    # 处理不同的JSON格式
    if isinstance(json_data, list):
        rows = json_data
    elif isinstance(json_data, dict):
        # 尝试找到数组字段
        for key, value in json_data.items():
            if isinstance(value, list) and value:
                rows = value
                break
        else:
            rows = [json_data]  # 单个对象
    else:
        raise ValueError("不支持的JSON格式")
    
    # 限制处理的数据量
    total_records = len(rows)
    analysis_rows = rows[:1000]  # 只分析前1000条
    
    preview.total_records = total_records
    
    # 扁平化嵌套结构，提取有用字段
    flattened_rows = []
    for i, row in enumerate(analysis_rows):
        if isinstance(row, dict):
            flattened_row = _flatten_json_object(row)
            flattened_rows.append(flattened_row)
        if i >= 100:  # 只扁平化前100条用于字段检测
            break
    
    preview.sample_data = flattened_rows[:3]  # 只显示前3条样本
    
    # 检测字段
    if flattened_rows:
        preview.detected_fields = []
        all_keys = set()
        
        # 收集所有可能的字段名 - 只从前100条数据收集
        for row in flattened_rows[:100]:
            if isinstance(row, dict):
                all_keys.update(row.keys())
        
        for field_name in all_keys:
            values = [row.get(field_name, '') for row in flattened_rows[:100] if isinstance(row, dict)]
            field_info = _detect_field_type(field_name, values)
            preview.detected_fields.append(field_info)
    
    return preview


def _flatten_json_object(obj: dict, parent_key: str = '', sep: str = '.') -> dict:
    """
    扁平化嵌套JSON对象，智能提取有用字段
    """
    items = []
    
    for key, value in obj.items():
        new_key = f"{parent_key}{sep}{key}" if parent_key else key
        
        if isinstance(value, dict):
            # 对于某些特殊字段，直接提取有用信息
            if key == 'service' and 'name' in value:
                items.append((f"{new_key}_name", value.get('name', '')))
                if 'http' in value and isinstance(value['http'], dict):
                    http_info = value['http']
                    items.append((f"{new_key}_http_host", http_info.get('host', '')))
                    items.append((f"{new_key}_http_status", http_info.get('status_code', '')))
                    items.append((f"{new_key}_http_title", http_info.get('title', '')))
            elif key == 'location' and isinstance(value, dict):
                # 位置信息扁平化
                items.append((f"{new_key}_country", value.get('country_cn', value.get('country_en', ''))))
                items.append((f"{new_key}_city", value.get('city_cn', value.get('city_en', ''))))
                items.append((f"{new_key}_isp", value.get('isp', '')))
            elif key == 'components' and isinstance(value, list) and value:
                # 组件信息提取第一个
                first_component = value[0] if value else {}
                if isinstance(first_component, dict):
                    items.append((f"{new_key}_vendor", first_component.get('product_vendor', '')))
                    items.append((f"{new_key}_name", first_component.get('product_name_cn', '')))
                    items.append((f"{new_key}_level", first_component.get('product_level', '')))
            else:
                # 递归扁平化，但限制深度
                if parent_key.count(sep) < 2:  # 限制嵌套深度
                    flattened = _flatten_json_object(value, new_key, sep)
                    items.extend(flattened.items())
                else:
                    # 深度过深，转为字符串
                    items.append((new_key, json.dumps(value, ensure_ascii=False)))
        elif isinstance(value, list):
            if value and not isinstance(value[0], (dict, list)):
                # 简单列表，转为逗号分隔字符串
                items.append((new_key, ', '.join(map(str, value))))
            elif value and isinstance(value[0], dict):
                # 复杂列表，提取第一个元素的关键信息
                first_item = value[0]
                if 'name' in first_item:
                    items.append((f"{new_key}_first_name", first_item['name']))
                elif 'product_name_cn' in first_item:
                    items.append((f"{new_key}_first_product", first_item['product_name_cn']))
                else:
                    items.append((new_key, json.dumps(value, ensure_ascii=False)))
            else:
                items.append((new_key, json.dumps(value, ensure_ascii=False)))
        else:
            items.append((new_key, value))
    
    return dict(items)


def _detect_field_type(field_name: str, values: List[Any]) -> Dict[str, Any]:
    """自动检测字段类型"""
    # 清理空值
    non_empty_values = [v for v in values if v is not None and str(v).strip()]
    
    field_info = {
        "field_name": field_name,
        "display_name": field_name,
        "detected_type": "text",
        "sample_values": non_empty_values[:5],
        "null_count": len(values) - len(non_empty_values),
        "confidence": 0.0
    }
    
    if not non_empty_values:
        return field_info
    
    # 基于字段名进行类型推断
    field_name_lower = field_name.lower()
    
    # IP地址字段
    if any(keyword in field_name_lower for keyword in ['ip', 'address', '地址']):
        if _is_ip_address(str(non_empty_values[0])):
            field_info["detected_type"] = "text"  # IP作为文本处理
            field_info["confidence"] = 0.9
    
    # 端口字段
    elif any(keyword in field_name_lower for keyword in ['port', '端口']):
        if _is_numeric(str(non_empty_values[0])):
            field_info["detected_type"] = "number"
            field_info["confidence"] = 0.9
    
    # URL字段
    elif any(keyword in field_name_lower for keyword in ['url', 'uri', 'link']):
        field_info["detected_type"] = "text"
        field_info["confidence"] = 0.8
    
    # 主机名/域名字段
    elif any(keyword in field_name_lower for keyword in ['host', 'hostname', 'domain', '主机', '域名']):
        field_info["detected_type"] = "text"
        field_info["confidence"] = 0.8
    
    # 服务类型字段
    elif any(keyword in field_name_lower for keyword in ['service', 'protocol', '服务', '协议']):
        field_info["detected_type"] = "text"
        field_info["confidence"] = 0.7
    
    # 组织/厂商字段
    elif any(keyword in field_name_lower for keyword in ['org', 'vendor', 'company', '组织', '厂商', '公司']):
        field_info["detected_type"] = "text"
        field_info["confidence"] = 0.7
    
    # 地理位置字段
    elif any(keyword in field_name_lower for keyword in ['country', 'city', 'location', 'isp', '国家', '城市', '位置']):
        field_info["detected_type"] = "text"
        field_info["confidence"] = 0.7
    
    # ASN字段
    elif any(keyword in field_name_lower for keyword in ['asn']):
        if _is_numeric(str(non_empty_values[0])):
            field_info["detected_type"] = "number"
            field_info["confidence"] = 0.9
    
    # 状态码字段
    elif any(keyword in field_name_lower for keyword in ['status', 'code', '状态']):
        if _is_numeric(str(non_empty_values[0])):
            field_info["detected_type"] = "number"
            field_info["confidence"] = 0.8
    
    # 时间字段
    elif any(keyword in field_name_lower for keyword in ['time', 'date', 'timestamp', '时间', '日期']):
        if _is_date_string(str(non_empty_values[0])) or _is_iso_timestamp(str(non_empty_values[0])):
            field_info["detected_type"] = "datetime"
            field_info["confidence"] = 0.9
    
    # 传输协议字段
    elif any(keyword in field_name_lower for keyword in ['transport', 'protocol', '传输', '协议']):
        field_info["detected_type"] = "text"
        field_info["confidence"] = 0.7
    
    # 标题字段
    elif any(keyword in field_name_lower for keyword in ['title', 'name', '标题', '名称']):
        field_info["detected_type"] = "text"
        field_info["confidence"] = 0.6
    
    # 基于值内容进行类型推断
    else:
        # 检查是否全部为数字
        numeric_count = sum(1 for v in non_empty_values if _is_numeric(str(v)))
        if numeric_count / len(non_empty_values) > 0.8:
            field_info["detected_type"] = "number"
            field_info["confidence"] = 0.6
        
        # 检查是否为时间戳
        timestamp_count = sum(1 for v in non_empty_values if _is_iso_timestamp(str(v)))
        if timestamp_count / len(non_empty_values) > 0.8:
            field_info["detected_type"] = "datetime"
            field_info["confidence"] = 0.8
        
        # 检查是否为日期
        date_count = sum(1 for v in non_empty_values if _is_date_string(str(v)))
        if date_count / len(non_empty_values) > 0.8:
            field_info["detected_type"] = "datetime"
            field_info["confidence"] = 0.6
        
        # 检查是否为布尔值
        bool_count = sum(1 for v in non_empty_values if str(v).lower() in ['true', 'false', 'yes', 'no', '是', '否', '1', '0'])
        if bool_count / len(non_empty_values) > 0.8:
            field_info["detected_type"] = "boolean"
            field_info["confidence"] = 0.7
    
    return field_info


def _is_iso_timestamp(value: str) -> bool:
    """检查是否为ISO时间戳"""
    iso_patterns = [
        '%Y-%m-%dT%H:%M:%S.%fZ',
        '%Y-%m-%dT%H:%M:%SZ',
        '%Y-%m-%dT%H:%M:%S.%f',
        '%Y-%m-%dT%H:%M:%S'
    ]
    
    for pattern in iso_patterns:
        try:
            datetime.strptime(value, pattern)
            return True
        except ValueError:
            continue
    return False


def _is_numeric(value: str) -> bool:
    """检查是否为数字"""
    try:
        float(value)
        return True
    except ValueError:
        return False


def _is_date_string(value: str) -> bool:
    """检查是否为日期字符串"""
    date_patterns = [
        '%Y-%m-%d',
        '%Y-%m-%d %H:%M:%S',
        '%Y/%m/%d',
        '%Y/%m/%d %H:%M:%S',
        '%m/%d/%Y',
        '%d/%m/%Y'
    ]
    
    for pattern in date_patterns:
        try:
            datetime.strptime(value, pattern)
            return True
        except ValueError:
            continue
    return False


def _is_ip_address(value: str) -> bool:
    """检查是否为IP地址"""
    import re
    # 简单的IP地址正则
    ip_pattern = r'^(\d{1,3}\.){3}\d{1,3}$'
    return bool(re.match(ip_pattern, value))


def _suggest_field_mappings(detected_fields: List[Dict], target_fields: List[Dict]) -> List[Dict]:
    """建议字段映射"""
    mappings = []
    
    for detected_field in detected_fields:
        mapping = {
            "source_field": detected_field["field_name"],
            "target_field": None,
            "mapping_confidence": 0.0,
            "detected_type": detected_field["detected_type"],
            "target_type": None,
            "auto_mapped": False
        }
        
        # 如果有目标字段，尝试自动匹配
        if target_fields:
            best_match = None
            best_score = 0.0
            
            for target_field in target_fields:
                score = _calculate_field_similarity(detected_field, target_field)
                if score > best_score and score > 0.5:  # 阈值0.5
                    best_match = target_field
                    best_score = score
            
            if best_match:
                mapping["target_field"] = best_match["field_name"]
                mapping["target_type"] = best_match["field_type"]
                mapping["mapping_confidence"] = best_score
                mapping["auto_mapped"] = True
        else:
            # 没有目标字段时，建议创建新字段
            mapping["target_field"] = detected_field["field_name"]
            mapping["target_type"] = detected_field["detected_type"]
            mapping["mapping_confidence"] = detected_field.get("confidence", 0.5)
            mapping["auto_mapped"] = True
        
        mappings.append(mapping)
    
    return mappings


def _calculate_field_similarity(detected_field: Dict, target_field: Dict) -> float:
    """计算字段相似度"""
    score = 0.0
    
    # 字段名相似度（最重要）
    detected_name = detected_field["field_name"].lower()
    target_name = target_field["field_name"].lower()
    
    # 完全匹配
    if detected_name == target_name:
        score += 0.8
    # 包含关系
    elif detected_name in target_name or target_name in detected_name:
        score += 0.6
    # 关键词匹配
    else:
        common_keywords = set(detected_name.split('_')) & set(target_name.split('_'))
        if common_keywords:
            score += 0.3 * len(common_keywords)
    
    # 类型匹配
    if detected_field["detected_type"] == target_field["field_type"]:
        score += 0.2
    elif _are_compatible_types(detected_field["detected_type"], target_field["field_type"]):
        score += 0.1
    
    return min(score, 1.0)


def _are_compatible_types(detected_type: str, target_type: str) -> bool:
    """检查类型是否兼容"""
    compatibility_map = {
        "text": ["textarea", "email", "select"],
        "number": ["text"],
        "datetime": ["date", "text"],
        "boolean": ["text", "select"],
        "email": ["text"]
    }
    
    return target_type in compatibility_map.get(detected_type, [])


async def _parse_csv_file(content: bytes) -> List[Dict[str, Any]]:
    """解析CSV文件为数据列表"""
    try:
        text_content = content.decode('utf-8')
    except UnicodeDecodeError:
        text_content = content.decode('gbk')
    
    csv_reader = csv.DictReader(io.StringIO(text_content))
    return list(csv_reader)


async def _parse_json_file(content: bytes) -> List[Dict[str, Any]]:
    """解析JSON文件为数据列表"""
    try:
        json_data = json.loads(content.decode('utf-8'))
    except UnicodeDecodeError:
        json_data = json.loads(content.decode('gbk'))
    
    if isinstance(json_data, list):
        rows = json_data
    elif isinstance(json_data, dict):
        # 尝试找到数组字段
        for value in json_data.values():
            if isinstance(value, list):
                rows = value
                break
        else:
            rows = [json_data]
    else:
        rows = [{"data": json_data}]
    
    # 扁平化每个对象
    flattened_rows = []
    for row in rows:
        if isinstance(row, dict):
            flattened_row = _flatten_json_object(row)
            flattened_rows.append(flattened_row)
        else:
            flattened_rows.append({"value": str(row)})
    
    return flattened_rows


async def _get_asset_type_with_fields(db: AsyncSession, asset_type_id: str) -> Optional[AssetType]:
    """获取带字段的资产类型"""
    try:
        query = select(AssetType).options(selectinload(AssetType.fields)).where(
            AssetType.id == uuid.UUID(asset_type_id)
        )
        result = await db.execute(query)
        return result.scalar_one_or_none()
    except ValueError:
        return None


async def _create_new_asset_type(db: AsyncSession, type_config: Dict[str, Any], current_user: User) -> AssetType:
    """创建新的资产类型"""
    # 创建资产类型
    asset_type = AssetType(
        name=type_config["name"],
        display_name=type_config["display_name"],
        description=type_config.get("description", ""),
        created_by_user_id=current_user.id
    )
    
    db.add(asset_type)
    await db.flush()  # 获取ID
    
    # 创建字段
    for field_config in type_config["fields"]:
        field = AssetTypeField(
            asset_type_id=asset_type.id,
            field_name=field_config["field_name"],
            field_type=field_config["field_type"],
            display_name=field_config["display_name"],
            description=field_config.get("description", ""),
            is_required=field_config.get("is_required", False),
            is_searchable=field_config.get("is_searchable", True),
            is_filterable=field_config.get("is_filterable", True),
            sort_order=field_config.get("sort_order", 0)
        )
        db.add(field)
    
    await db.commit()
    await db.refresh(asset_type)
    
    # 重新加载字段
    query = select(AssetType).options(selectinload(AssetType.fields)).where(AssetType.id == asset_type.id)
    result = await db.execute(query)
    return result.scalar_one()


async def _execute_data_import(
    db: AsyncSession,
    raw_data: List[Dict[str, Any]],
    field_mappings: List[Dict[str, Any]],
    asset_type: AssetType,
    current_user: User,
    platform_id: Optional[str] = None,
    project_id: Optional[str] = None
) -> Dict[str, Any]:
    """执行数据导入 - 支持大文件批量处理"""
    results = {
        "total_records": len(raw_data),
        "successful_imports": 0,
        "failed_imports": 0,
        "errors": []
    }
    
    # 获取ES客户端
    es_client = await get_es_client()
    
    # 确定ES索引名 - 使用动态模型系统的索引命名约定
    index_name = f"dynamic_{asset_type.name}"
    
    # 批量处理大文件，每批处理1000条记录
    batch_size = 1000
    total_records = len(raw_data)
    
    for batch_start in range(0, total_records, batch_size):
        batch_end = min(batch_start + batch_size, total_records)
        batch_data = raw_data[batch_start:batch_end]
        
        # 准备批量插入的数据
        bulk_operations = []
        
        for i, raw_record in enumerate(batch_data):
            try:
                # 根据映射转换数据
                converted_data = {}
                for mapping in field_mappings:
                    if mapping.get("target_field") and mapping.get("source_field"):
                        source_value = raw_record.get(mapping["source_field"])
                        if source_value is not None:
                            # 类型转换
                            converted_value = _convert_field_value(
                                source_value, 
                                mapping.get("target_type", "text")
                            )
                            converted_data[mapping["target_field"]] = converted_value
                
                # 添加导入和平台项目元数据
                import_metadata = {
                    "imported_at": datetime.utcnow().isoformat(),
                    "import_source": "data_import",
                    "original_record_index": batch_start + i,
                    "asset_type": asset_type.name,
                    "asset_type_id": str(asset_type.id),
                    "imported_by": current_user.username
                }
                
                # 添加平台项目信息（如果提供）- 确保存储到entity_data中
                if platform_id:
                    import_metadata["platform_id"] = platform_id
                    converted_data["platform_id"] = platform_id  # 直接添加到转换数据中
                if project_id:
                    import_metadata["project_id"] = project_id
                    converted_data["project_id"] = project_id  # 直接添加到转换数据中
                
                converted_data.update(import_metadata)
                
                # 构建ES文档 - 使用与动态模型系统一致的结构
                es_doc = {
                    "model_type_id": str(asset_type.id),
                    "entity_data": converted_data,
                    "created_at": datetime.utcnow().isoformat(),
                    "updated_at": datetime.utcnow().isoformat(),
                    "created_by_user_id": str(current_user.id),
                    # 为了高级搜索兼容性，也添加传统字段
                    "asset_type": asset_type.name,
                    "platform_id": platform_id or "imported",
                    "project_id": project_id or "imported",
                    "discovered_at": datetime.utcnow().isoformat(),
                    "source_task_type": "data_import",
                    "confidence": "high"
                }
                
                # 添加到批量操作
                bulk_operations.append({
                    "_index": index_name,
                    "_source": es_doc
                })
                
            except Exception as e:
                results["failed_imports"] += 1
                results["errors"].append({
                    "record_index": batch_start + i,
                    "error": str(e),
                    "record_data": raw_record
                })
        
        # 执行批量插入到Elasticsearch
        if bulk_operations:
            try:
                from elasticsearch.helpers import async_bulk
                success_count, failed_items = await async_bulk(
                    es_client,
                    bulk_operations,
                    index=index_name,
                    refresh='wait_for'  # 等待索引刷新，确保数据可查询
                )
                results["successful_imports"] += success_count
                
                # 处理失败的项目
                for failed_item in failed_items:
                    results["failed_imports"] += 1
                    results["errors"].append({
                        "record_index": "bulk_operation_failed",
                        "error": str(failed_item),
                        "record_data": "bulk_data"
                    })
                    
            except Exception as e:
                # 如果批量插入失败，尝试逐个插入
                print(f"批量插入失败，尝试逐个插入: {e}")
                es_doc_ids = []
                for i, operation in enumerate(bulk_operations):
                    try:
                        resp = await es_client.index(index=index_name, body=operation["_source"])
                        es_doc_ids.append(resp["_id"])
                        results["successful_imports"] += 1
                    except Exception as single_error:
                        es_doc_ids.append(None)
                        results["failed_imports"] += 1
                        results["errors"].append({
                            "record_index": f"batch_{batch_start}_{i}",
                            "error": str(single_error),
                            "record_data": "single_operation"
                        })
                
                # 存储ES文档ID用于后续DynamicEntity创建
                for j, doc_id in enumerate(es_doc_ids):
                    if doc_id and j < len(bulk_operations):
                        bulk_operations[j]["_es_doc_id"] = doc_id
        
        # 关键修复：确保同时存储到PostgreSQL的dynamic_entities表
        if bulk_operations:
            try:
                # 检查或创建对应的ModelType
                model_type_query = select(ModelType).where(ModelType.name == asset_type.name)
                model_type_result = await db.execute(model_type_query)
                model_type = model_type_result.scalar_one_or_none()
                
                if not model_type:
                    # 如果没有对应的ModelType，创建一个
                    model_type = ModelType(
                        name=asset_type.name,
                        display_name=asset_type.display_name,
                        description=f"从资产类型 {asset_type.display_name} 自动创建",
                        is_system=False,
                        created_by_user_id=current_user.id
                    )
                    db.add(model_type)
                    await db.flush()
                    
                    # 创建对应的ModelField
                    for field in asset_type.fields:
                        model_field = ModelField(
                            model_type_id=model_type.id,
                            field_name=field.field_name,
                            field_type=field.field_type,
                            display_name=field.display_name,
                            description=field.description,
                            is_required=field.is_required,
                            is_searchable=field.is_searchable,
                            is_filterable=field.is_filterable,
                            sort_order=field.sort_order
                        )
                        db.add(model_field)
                    await db.flush()
                
                # 批量创建DynamicEntity记录
                entities = []
                for j, operation in enumerate(bulk_operations):
                    entity_data = operation["_source"]["entity_data"]
                    
                    # 生成ES文档ID
                    es_doc_id = f"import_batch_{batch_start}_{j}_{datetime.utcnow().strftime('%Y%m%d%H%M%S')}"
                    
                    entity = DynamicEntity(
                        model_type_id=model_type.id,
                        entity_data=entity_data,
                        es_index=index_name,
                        es_doc_id=es_doc_id,
                        created_by_user_id=current_user.id
                    )
                    entities.append(entity)
                    
                    # 更新bulk_operations中的ES文档ID
                    operation["_id"] = es_doc_id
                
                db.add_all(entities)
                await db.flush()  # 刷新获取ID
                    
            except Exception as e:
                # PostgreSQL存储失败不影响ES存储
                print(f"Warning: Failed to store batch to PostgreSQL: {e}")
    
    # 提交数据库更改（如果有的话）
    try:
        await db.commit()
    except Exception as e:
        print(f"Warning: Database commit failed: {e}")
    
    return results


def _convert_field_value(value: Any, target_type: str) -> Any:
    """转换字段值到目标类型"""
    if value is None or value == "":
        return None
    
    try:
        if target_type == "number":
            return float(value)
        elif target_type == "boolean":
            if isinstance(value, bool):
                return value
            str_value = str(value).lower()
            return str_value in ['true', 'yes', '是', '1', 'on']
        elif target_type in ["date", "datetime"]:
            if isinstance(value, str):
                # 尝试解析日期
                for pattern in ['%Y-%m-%d', '%Y-%m-%d %H:%M:%S', '%Y/%m/%d', '%m/%d/%Y']:
                    try:
                        return datetime.strptime(value, pattern).isoformat()
                    except ValueError:
                        continue
            return str(value)  # 如果无法解析，作为字符串返回
        else:
            return str(value)
    except Exception:
        return str(value)  # 转换失败时返回字符串


@router.get("/asset-types")
async def get_available_asset_types(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取可用的资产类型列表"""
    query = select(AssetType).options(selectinload(AssetType.fields)).where(
        AssetType.is_active == True
    )
    result = await db.execute(query)
    asset_types = result.scalars().all()
    
    return [
        {
            "id": str(asset_type.id),
            "name": asset_type.name,
            "display_name": asset_type.display_name,
            "description": asset_type.description,
            "field_count": len(asset_type.fields)
        }
        for asset_type in asset_types
    ]


@router.get("/field-types")
async def get_supported_field_types():
    """获取支持的字段类型"""
    return [
        {"type": "text", "display_name": "文本", "description": "单行文本"},
        {"type": "textarea", "display_name": "多行文本", "description": "多行文本"},
        {"type": "number", "display_name": "数字", "description": "数字类型"},
        {"type": "email", "display_name": "邮箱", "description": "邮箱地址"},
        {"type": "date", "display_name": "日期", "description": "日期类型"},
        {"type": "datetime", "display_name": "日期时间", "description": "日期时间类型"},
        {"type": "boolean", "display_name": "布尔值", "description": "真/假值"},
        {"type": "select", "display_name": "单选", "description": "下拉单选"},
    ]


@router.get("/platforms")
async def get_platforms(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取平台项目列表"""
    try:
        # 从动态模型系统获取平台和项目数据
        
        # 获取平台模型类型
        platform_model_query = select(ModelType).where(ModelType.name == "platform")
        platform_model_result = await db.execute(platform_model_query)
        platform_model = platform_model_result.scalar_one_or_none()
        
        # 获取项目模型类型
        project_model_query = select(ModelType).where(ModelType.name == "project")
        project_model_result = await db.execute(project_model_query)
        project_model = project_model_result.scalar_one_or_none()
        
        platforms_data = []
        
        if platform_model and project_model:
            # 获取所有平台
            platforms_query = select(DynamicEntity).where(DynamicEntity.model_type_id == platform_model.id)
            platforms_result = await db.execute(platforms_query)
            platforms = platforms_result.scalars().all()
            
            # 获取所有项目
            projects_query = select(DynamicEntity).where(DynamicEntity.model_type_id == project_model.id)
            projects_result = await db.execute(projects_query)
            projects = projects_result.scalars().all()
            
            # 组合平台项目数据
            for platform in platforms:
                platform_data = platform.entity_data
                # 使用UUID作为platform_id，确保唯一性
                platform_uuid_str = str(platform.id)
                platform_name = platform_data.get("display_name", platform_data.get("name", f"平台{platform_uuid_str}"))
                
                # 找到属于该平台的项目 - 主要使用UUID匹配
                platform_projects = []
                for p in projects:
                    project_platform_id = p.entity_data.get("platform_id")
                    if project_platform_id:
                        # 转换为字符串进行比较，优先UUID匹配
                        project_platform_id_str = str(project_platform_id)
                        if project_platform_id_str == platform_uuid_str:
                            platform_projects.append(p)
                
                if platform_projects:
                    for project in platform_projects:
                        project_data = project.entity_data
                        project_uuid_str = str(project.id)
                        project_name = project_data.get("name", f"项目{project_uuid_str}")
                        
                        platforms_data.append({
                            "platform_id": platform_uuid_str,  # 使用UUID
                            "project_id": project_uuid_str,    # 使用UUID
                            "platform_name": platform_name,
                            "project_name": project_name
                        })
                else:
                    # 如果平台没有项目，创建一个默认项目条目
                    platforms_data.append({
                        "platform_id": platform_uuid_str,
                        "project_id": f"{platform_uuid_str}_default",
                        "platform_name": platform_name,
                        "project_name": f"{platform_name}默认项目"
                    })
        
        # 如果没有找到平台项目数据，返回默认选项
        if not platforms_data:
            platforms_data = [
                {
                    "platform_id": "default",
                    "project_id": "default",
                    "platform_name": "默认平台",
                    "project_name": "默认项目"
                }
            ]
        
        return platforms_data
        
    except Exception as e:
        print(f"获取平台项目数据失败: {e}")
        # 如果查询失败，返回默认选项
        return [
            {
                "platform_id": "default",
                "project_id": "default",
                "platform_name": "默认平台",
                "project_name": "默认项目"
            }
        ]


@router.post("/asset-types/{asset_type_id}/fields")
async def add_fields_to_asset_type(
    asset_type_id: str,
    request: Dict[str, List[Dict[str, Any]]],
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """为资产类型添加字段"""
    try:
        # 获取资产类型
        asset_type = await _get_asset_type_with_fields(db, asset_type_id)
        if not asset_type:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="资产类型不存在"
            )
        
        fields_data = request.get("fields", [])
        if not fields_data:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="字段数据不能为空"
            )
        
        # 获取当前最大的sort_order
        max_sort_order = 0
        if asset_type.fields:
            max_sort_order = max(field.sort_order for field in asset_type.fields)
        
        # 添加新字段
        for i, field_data in enumerate(fields_data):
            # 检查字段名是否已存在
            existing_field = next(
                (f for f in asset_type.fields if f.field_name == field_data["field_name"]), 
                None
            )
            if existing_field:
                continue  # 跳过已存在的字段
            
            field = AssetTypeField(
                asset_type_id=asset_type.id,
                field_name=field_data["field_name"],
                field_type=field_data["field_type"],
                display_name=field_data.get("display_name", field_data["field_name"]),
                description=field_data.get("description", ""),
                is_required=field_data.get("is_required", False),
                is_searchable=field_data.get("is_searchable", True),
                is_filterable=field_data.get("is_filterable", True),
                sort_order=max_sort_order + i + 1
            )
            db.add(field)
        
        await db.commit()
        
        return {"message": f"成功添加 {len(fields_data)} 个字段"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"添加字段失败: {str(e)}"
        )