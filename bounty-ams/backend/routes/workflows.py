from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, func
from typing import List, Optional, Dict, Any
from datetime import datetime
import uuid

from database import get_db
from models_dynamic import WorkflowExecution, Task, User
from schemas_agent import (
    WorkflowTemplate, WorkflowExecution as WorkflowExecutionSchema,
    WorkflowStatus, TaskDefinition, TaskInfo, TaskPriority, ErrorResponse
)
from auth import get_current_user, require_admin
from agent_architecture import WORKFLOW_TEMPLATES

router = APIRouter()

@router.get("/")
async def list_workflows(
    current_user: User = Depends(get_current_user)
):
    """获取工作流列表（包括模板和执行记录）"""
    # 获取模板
    templates = []
    for template_id, template in WORKFLOW_TEMPLATES.items():
        templates.append({
            "workflow_id": template.workflow_id,
            "name": template.name,
            "description": template.description,
            "type": "template",
            "status": "ready",
            "task_count": len(template.tasks),
            "created_at": datetime.utcnow().isoformat()
        })
    
    return {"workflows": templates}

@router.get("/templates", response_model=List[WorkflowTemplate])
async def list_workflow_templates(
    current_user: User = Depends(get_current_user)
):
    """获取可用的工作流模板"""
    templates = []
    for template_id, template in WORKFLOW_TEMPLATES.items():
        templates.append(WorkflowTemplate(
            workflow_id=template.workflow_id,
            name=template.name,
            description=template.description,
            tasks=[
                TaskDefinition(
                    task_id=task.task_id,
                    task_type=task.task_type.value,  # Convert enum to string
                    target=task.target,
                    parameters=task.parameters,
                    priority=task.priority.value,  # Convert enum to int
                    timeout=task.timeout,
                    retry_count=task.retry_count,
                    dependencies=task.dependencies,
                    workflow_id=task.workflow_id
                )
                for task in template.tasks
            ],
            variables=template.variables
        ))
    return templates

@router.get("/templates/{template_id}", response_model=WorkflowTemplate)
async def get_workflow_template(
    template_id: str,
    current_user: User = Depends(get_current_user)
):
    """获取工作流模板详情"""
    if template_id not in WORKFLOW_TEMPLATES:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Workflow template not found"
        )
    
    template = WORKFLOW_TEMPLATES[template_id]
    return WorkflowTemplate(
        workflow_id=template.workflow_id,
        name=template.name,
        description=template.description,
        tasks=[
            TaskDefinition(
                task_id=task.task_id,
                task_type=task.task_type.value,  # Convert enum to string
                target=task.target,
                parameters=task.parameters,
                priority=task.priority.value,  # Convert enum to int
                timeout=task.timeout,
                retry_count=task.retry_count,
                dependencies=task.dependencies,
                workflow_id=task.workflow_id
            )
            for task in template.tasks
        ],
        variables=template.variables
    )

@router.post("/execute", response_model=WorkflowStatus)
async def execute_workflow(
    workflow_data: WorkflowExecutionSchema,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """执行工作流"""
    # 检查工作流模板是否存在
    if workflow_data.workflow_id not in WORKFLOW_TEMPLATES:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Workflow template not found"
        )
    
    template = WORKFLOW_TEMPLATES[workflow_data.workflow_id]
    
    # 创建工作流执行记录
    execution = WorkflowExecution(
        execution_id=workflow_data.execution_id,
        workflow_id=workflow_data.workflow_id,
        target=workflow_data.target,
        priority=workflow_data.priority,
        variables=workflow_data.variables,
        status="running",
        started_at=datetime.utcnow(),
        created_by_user_id=current_user.id
    )
    
    db.add(execution)
    await db.commit()
    await db.refresh(execution)
    
    # 创建任务
    tasks = []
    for task_template in template.tasks:
        # 替换模板变量
        target = substitute_variables(
            task_template.target,
            workflow_data.target,
            workflow_data.variables or {}
        )
        
        task = Task(
            task_id=f"{workflow_data.execution_id}_{task_template.task_id}",
            task_type=task_template.task_type.value,  # Convert enum to string
            target=target,
            parameters=task_template.parameters,
            priority=task_template.priority.value,  # Convert enum to int
            timeout=task_template.timeout,
            retry_count=task_template.retry_count,
            dependencies=[
                f"{workflow_data.execution_id}_{dep}"
                for dep in (task_template.dependencies or [])
            ],
            workflow_id=workflow_data.execution_id,
            created_by_user_id=current_user.id
        )
        
        db.add(task)
        tasks.append(task)
    
    await db.commit()
    
    # 更新工作流执行状态
    await db.execute(
        update(WorkflowExecution)
        .where(WorkflowExecution.execution_id == workflow_data.execution_id)
        .values(
            total_tasks=len(tasks),
            status="running"
        )
    )
    await db.commit()
    
    # 构造返回数据
    return await get_workflow_status_internal(db, workflow_data.execution_id)

@router.get("/executions", response_model=List[WorkflowStatus])
async def list_workflow_executions(
    status: Optional[str] = None,
    limit: int = 100,
    offset: int = 0,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取工作流执行列表"""
    query = select(WorkflowExecution)
    
    if status:
        query = query.where(WorkflowExecution.status == status)
    
    query = query.order_by(WorkflowExecution.created_at.desc())
    query = query.offset(offset).limit(limit)
    
    result = await db.execute(query)
    executions = result.scalars().all()
    
    # 构造返回数据
    workflow_statuses = []
    for execution in executions:
        workflow_status = await get_workflow_status_internal(db, execution.execution_id)
        workflow_statuses.append(workflow_status)
    
    return workflow_statuses

@router.get("/executions/{execution_id}", response_model=WorkflowStatus)
async def get_workflow_execution(
    execution_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取工作流执行详情"""
    result = await db.execute(
        select(WorkflowExecution).where(WorkflowExecution.execution_id == execution_id)
    )
    execution = result.scalar_one_or_none()
    
    if not execution:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Workflow execution not found"
        )
    
    return await get_workflow_status_internal(db, execution_id)

@router.post("/executions/{execution_id}/cancel")
async def cancel_workflow_execution(
    execution_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """取消工作流执行"""
    result = await db.execute(
        select(WorkflowExecution).where(WorkflowExecution.execution_id == execution_id)
    )
    execution = result.scalar_one_or_none()
    
    if not execution:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Workflow execution not found"
        )
    
    if execution.status in ["completed", "failed", "cancelled"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot cancel completed workflow"
        )
    
    # 取消所有相关任务
    await db.execute(
        update(Task)
        .where(
            Task.workflow_id == execution_id,
            Task.status.in_(["pending", "assigned"])
        )
        .values(
            status="cancelled",
            completed_at=datetime.utcnow()
        )
    )
    
    # 更新工作流状态
    await db.execute(
        update(WorkflowExecution)
        .where(WorkflowExecution.execution_id == execution_id)
        .values(
            status="cancelled",
            completed_at=datetime.utcnow()
        )
    )
    
    await db.commit()
    
    return {"message": "Workflow execution cancelled"}

@router.delete("/executions/{execution_id}")
async def delete_workflow_execution(
    execution_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(require_admin)
):
    """删除工作流执行"""
    result = await db.execute(
        select(WorkflowExecution).where(WorkflowExecution.execution_id == execution_id)
    )
    execution = result.scalar_one_or_none()
    
    if not execution:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Workflow execution not found"
        )
    
    # 删除所有相关任务
    await db.execute(
        delete(Task).where(Task.workflow_id == execution_id)
    )
    
    # 删除工作流执行记录
    await db.delete(execution)
    await db.commit()
    
    return {"message": "Workflow execution deleted"}

@router.get("/stats/overview")
async def get_workflow_stats(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取工作流统计信息"""
    # 基础统计
    stats_result = await db.execute(
        select(
            func.count(WorkflowExecution.id).label("total"),
            func.count(WorkflowExecution.id).filter(WorkflowExecution.status == "pending").label("pending"),
            func.count(WorkflowExecution.id).filter(WorkflowExecution.status == "running").label("running"),
            func.count(WorkflowExecution.id).filter(WorkflowExecution.status == "completed").label("completed"),
            func.count(WorkflowExecution.id).filter(WorkflowExecution.status == "failed").label("failed"),
            func.count(WorkflowExecution.id).filter(WorkflowExecution.status == "cancelled").label("cancelled")
        )
    )
    stats = stats_result.first()
    
    # 按工作流类型统计
    type_stats_result = await db.execute(
        select(
            WorkflowExecution.workflow_id,
            func.count(WorkflowExecution.id).label("count")
        )
        .group_by(WorkflowExecution.workflow_id)
        .order_by(func.count(WorkflowExecution.id).desc())
    )
    type_stats = type_stats_result.all()
    
    return {
        "total_workflows": stats.total or 0,
        "by_status": {
            "pending": stats.pending or 0,
            "running": stats.running or 0,
            "completed": stats.completed or 0,
            "failed": stats.failed or 0,
            "cancelled": stats.cancelled or 0
        },
        "by_type": [
            {
                "workflow_id": item.workflow_id,
                "workflow_name": WORKFLOW_TEMPLATES.get(item.workflow_id, {}).get("name", item.workflow_id),
                "count": item.count
            }
            for item in type_stats
        ]
    }

async def get_workflow_status_internal(db: AsyncSession, execution_id: str) -> WorkflowStatus:
    """获取工作流状态（内部函数）"""
    # 获取工作流执行记录
    execution_result = await db.execute(
        select(WorkflowExecution).where(WorkflowExecution.execution_id == execution_id)
    )
    execution = execution_result.scalar_one()
    
    # 获取所有任务
    tasks_result = await db.execute(
        select(Task).where(Task.workflow_id == execution_id)
        .order_by(Task.created_at.asc())
    )
    tasks = tasks_result.scalars().all()
    
    # 构造任务信息
    task_infos = []
    for task in tasks:
        task_info = TaskInfo(
            id=task.id,
            task_id=task.task_id,
            task_type=task.task_type,
            target=task.target,
            parameters=task.parameters,
            priority=task.priority,
            status=task.status,
            agent_id=task.agent_id,
            workflow_id=task.workflow_id,
            timeout=task.timeout,
            retry_count=task.retry_count,
            current_retry=task.current_retry,
            dependencies=task.dependencies,
            result_data=task.result_data,
            error_message=task.error_message,
            execution_time=task.execution_time,
            assets_discovered=task.assets_discovered,
            created_at=task.created_at,
            assigned_at=task.assigned_at,
            started_at=task.started_at,
            completed_at=task.completed_at,
            created_by_user_id=task.created_by_user_id
        )
        task_infos.append(task_info)
    
    return WorkflowStatus(
        execution_id=execution.execution_id,
        workflow_id=execution.workflow_id,
        status=execution.status,
        target=execution.target,
        total_tasks=execution.total_tasks,
        completed_tasks=execution.completed_tasks,
        failed_tasks=execution.failed_tasks,
        running_tasks=execution.running_tasks,
        progress=execution.progress,
        started_at=execution.started_at,
        updated_at=execution.updated_at or execution.created_at,  # Use created_at if updated_at is None
        completed_at=execution.completed_at,
        tasks=task_infos
    )

def substitute_variables(template: str, primary_target: str, variables: Dict[str, Any]) -> str:
    """替换模板变量"""
    # 替换主要目标
    result = template.replace("{{domain}}", primary_target)
    result = result.replace("{{target}}", primary_target)
    
    # 替换其他变量
    for key, value in variables.items():
        result = result.replace(f"{{{{{key}}}}}", str(value))
    
    # 处理依赖任务结果的引用（这里简化处理，实际中需要更复杂的逻辑）
    # 例如：{{subdomain_discovery.subdomains}}
    
    return result