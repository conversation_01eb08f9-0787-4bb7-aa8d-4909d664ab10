from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, delete
from sqlalchemy.orm import selectinload
from typing import List, Optional, Dict, Any
from uuid import UUID
from datetime import datetime

from database import get_db
from models_dynamic import ModelType, ModelField, DynamicEntity, User
from schemas_dynamic import (
    ModelTypeCreate, ModelTypeUpdate, ModelType as ModelTypeSchema,
    ModelFieldCreate, ModelFieldUpdate, ModelField as ModelFieldSchema,
    DynamicEntityCreate, DynamicEntityUpdate, DynamicEntity as DynamicEntitySchema
)
from auth import get_current_user, get_current_admin_user
from elasticsearch_client import get_es_client

router = APIRouter(prefix="/dynamic-models", tags=["dynamic-models"])


# 动态模型类型管理
@router.get("/types", response_model=List[ModelTypeSchema])
async def get_model_types(
    skip: int = 0,
    limit: int = 100,
    include_inactive: bool = False,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取所有动态模型类型"""
    query = select(ModelType).options(selectinload(ModelType.fields))
    
    if not include_inactive:
        query = query.where(ModelType.is_active == True)
    
    query = query.offset(skip).limit(limit)
    result = await db.execute(query)
    model_types = result.scalars().all()
    
    return model_types


@router.get("/types/{model_type_id}", response_model=ModelTypeSchema)
async def get_model_type(
    model_type_id: UUID,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取特定的动态模型类型"""
    query = select(ModelType).options(selectinload(ModelType.fields)).where(ModelType.id == model_type_id)
    result = await db.execute(query)
    model_type = result.scalar_one_or_none()
    
    if not model_type:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Model type not found"
        )
    
    return model_type


@router.post("/types", response_model=ModelTypeSchema, status_code=status.HTTP_201_CREATED)
async def create_model_type(
    model_type_data: ModelTypeCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """创建新的动态模型类型"""
    # 检查模型类型名称是否已存在
    existing_query = select(ModelType).where(ModelType.name == model_type_data.name)
    existing_result = await db.execute(existing_query)
    if existing_result.scalar_one_or_none():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Model type with this name already exists"
        )
    
    # 创建模型类型
    model_type = ModelType(
        name=model_type_data.name,
        display_name=model_type_data.display_name,
        description=model_type_data.description,
        icon=model_type_data.icon,
        color=model_type_data.color,
        is_active=model_type_data.is_active,
        created_by_user_id=current_user.id
    )
    
    db.add(model_type)
    await db.flush()  # 获取ID
    
    # 创建字段
    for field_data in model_type_data.fields:
        field = ModelField(
            model_type_id=model_type.id,
            field_name=field_data.field_name,
            field_type=field_data.field_type,
            display_name=field_data.display_name,
            description=field_data.description,
            is_required=field_data.is_required,
            is_searchable=field_data.is_searchable,
            is_filterable=field_data.is_filterable,
            is_unique=field_data.is_unique,
            default_value=field_data.default_value,
            validation_rules=field_data.validation_rules,
            field_options=field_data.field_options,
            sort_order=field_data.sort_order
        )
        db.add(field)
    
    await db.commit()
    await db.refresh(model_type)
    
    # 创建对应的Elasticsearch索引
    await create_elasticsearch_index(model_type)
    
    # 加载字段信息返回
    query = select(ModelType).options(selectinload(ModelType.fields)).where(ModelType.id == model_type.id)
    result = await db.execute(query)
    return result.scalar_one()


@router.put("/types/{model_type_id}", response_model=ModelTypeSchema)
async def update_model_type(
    model_type_id: UUID,
    model_type_data: ModelTypeUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """更新动态模型类型"""
    # 获取现有模型类型
    query = select(ModelType).options(selectinload(ModelType.fields)).where(ModelType.id == model_type_id)
    result = await db.execute(query)
    model_type = result.scalar_one_or_none()
    
    if not model_type:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Model type not found"
        )
    
    # 检查是否为系统类型
    if model_type.is_system:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot modify system model types"
        )
    
    # 更新模型类型字段
    update_data = model_type_data.dict(exclude_unset=True)
    fields_data = update_data.pop("fields", None)
    
    for field, value in update_data.items():
        setattr(model_type, field, value)
    
    # 更新字段（如果提供）
    if fields_data is not None:
        # 删除现有字段
        await db.execute(delete(ModelField).where(ModelField.model_type_id == model_type_id))
        
        # 创建新字段
        for field_data in fields_data:
            field = ModelField(
                model_type_id=model_type.id,
                field_name=field_data.field_name,
                field_type=field_data.field_type,
                display_name=field_data.display_name,
                description=field_data.description,
                is_required=field_data.is_required,
                is_searchable=field_data.is_searchable,
                is_filterable=field_data.is_filterable,
                is_unique=field_data.is_unique,
                default_value=field_data.default_value,
                validation_rules=field_data.validation_rules,
                field_options=field_data.field_options,
                sort_order=field_data.sort_order
            )
            db.add(field)
    
    await db.commit()
    await db.refresh(model_type)
    
    # 更新Elasticsearch索引
    await update_elasticsearch_index(model_type)
    
    # 重新加载字段信息
    query = select(ModelType).options(selectinload(ModelType.fields)).where(ModelType.id == model_type_id)
    result = await db.execute(query)
    return result.scalar_one()


@router.delete("/types/{model_type_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_model_type(
    model_type_id: UUID,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """删除动态模型类型"""
    # 获取现有模型类型
    query = select(ModelType).where(ModelType.id == model_type_id)
    result = await db.execute(query)
    model_type = result.scalar_one_or_none()
    
    if not model_type:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Model type not found"
        )
    
    # 检查是否为系统类型
    if model_type.is_system:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot delete system model types"
        )
    
    # 软删除
    model_type.is_active = False
    await db.commit()


# 动态实体管理
@router.get("/entities/count")
async def get_dynamic_entities_count(
    model_type_id: Optional[UUID] = Query(None),
    search: Optional[str] = Query(None),
    # 支持实体数据字段筛选
    platform_id: Optional[str] = Query(None),
    project_id: Optional[str] = Query(None),
    asset_type: Optional[str] = Query(None),
    confidence: Optional[str] = Query(None),
    status: Optional[str] = Query(None),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取动态实体数量"""
    from sqlalchemy import func, or_
    
    query = select(func.count(DynamicEntity.id))
    
    if model_type_id:
        query = query.where(DynamicEntity.model_type_id == model_type_id)
    
    # 应用与获取实体相同的筛选条件
    if search:
        search_conditions = []
        search_conditions.append(DynamicEntity.entity_data.op('->>')('name').ilike(f"%{search}%"))
        search_conditions.append(DynamicEntity.entity_data.op('->>')('display_name').ilike(f"%{search}%"))
        search_conditions.append(DynamicEntity.entity_data.op('->>')('asset_value').ilike(f"%{search}%"))
        search_conditions.append(DynamicEntity.entity_data.op('->>')('asset_host').ilike(f"%{search}%"))
        query = query.where(or_(*search_conditions))
    
    if platform_id:
        query = query.where(DynamicEntity.entity_data.op('->>')('platform_id') == platform_id)
    
    if project_id:
        query = query.where(DynamicEntity.entity_data.op('->>')('project_id') == project_id)
        
    if asset_type:
        query = query.where(DynamicEntity.entity_data.op('->>')('asset_type') == asset_type)
        
    if confidence:
        query = query.where(DynamicEntity.entity_data.op('->>')('confidence') == confidence)
        
    if status:
        query = query.where(DynamicEntity.entity_data.op('->>')('status') == status)
    
    result = await db.execute(query)
    count = result.scalar()
    
    return {"count": count}


@router.post("/entities", response_model=DynamicEntitySchema, status_code=status.HTTP_201_CREATED)
async def create_dynamic_entity(
    entity_data: DynamicEntityCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """创建动态实体"""
    # 验证模型类型存在
    model_type_query = select(ModelType).options(selectinload(ModelType.fields)).where(ModelType.id == entity_data.model_type_id)
    model_type_result = await db.execute(model_type_query)
    model_type = model_type_result.scalar_one_or_none()
    
    if not model_type:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Model type not found"
        )
    
    # 验证实体数据
    validated_data = await validate_entity_data(db, model_type, entity_data.entity_data)
    
    # 创建实体
    entity = DynamicEntity(
        model_type_id=entity_data.model_type_id,
        entity_data=validated_data,
        created_by_user_id=current_user.id
    )
    
    db.add(entity)
    await db.commit()
    await db.refresh(entity)
    
    # 同步到Elasticsearch
    await sync_entity_to_elasticsearch(entity, model_type)
    
    return entity


@router.get("/entities", response_model=List[DynamicEntitySchema])
async def get_dynamic_entities(
    model_type_id: Optional[UUID] = Query(None),
    skip: int = 0,
    limit: int = 100,
    search: Optional[str] = Query(None),
    # 支持实体数据字段筛选
    platform_id: Optional[str] = Query(None),
    project_id: Optional[str] = Query(None),
    asset_type: Optional[str] = Query(None),
    confidence: Optional[str] = Query(None),
    status: Optional[str] = Query(None),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取动态实体列表"""
    query = select(DynamicEntity)
    
    if model_type_id:
        query = query.where(DynamicEntity.model_type_id == model_type_id)
    
    # 简单搜索实现
    if search:
        # 搜索多个可能的字段
        search_conditions = []
        search_conditions.append(DynamicEntity.entity_data.op('->>')('name').ilike(f"%{search}%"))
        search_conditions.append(DynamicEntity.entity_data.op('->>')('display_name').ilike(f"%{search}%"))
        search_conditions.append(DynamicEntity.entity_data.op('->>')('asset_value').ilike(f"%{search}%"))
        search_conditions.append(DynamicEntity.entity_data.op('->>')('asset_host').ilike(f"%{search}%"))
        
        from sqlalchemy import or_
        query = query.where(or_(*search_conditions))
    
    # 实体数据字段筛选
    if platform_id:
        query = query.where(DynamicEntity.entity_data.op('->>')('platform_id') == platform_id)
    
    if project_id:
        query = query.where(DynamicEntity.entity_data.op('->>')('project_id') == project_id)
        
    if asset_type:
        query = query.where(DynamicEntity.entity_data.op('->>')('asset_type') == asset_type)
        
    if confidence:
        query = query.where(DynamicEntity.entity_data.op('->>')('confidence') == confidence)
        
    if status:
        query = query.where(DynamicEntity.entity_data.op('->>')('status') == status)
    
    query = query.offset(skip).limit(limit)
    result = await db.execute(query)
    entities = result.scalars().all()
    
    return entities


@router.get("/entities/{entity_id}", response_model=DynamicEntitySchema)
async def get_dynamic_entity(
    entity_id: UUID,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取特定动态实体"""
    query = select(DynamicEntity).where(DynamicEntity.id == entity_id)
    result = await db.execute(query)
    entity = result.scalar_one_or_none()
    
    if not entity:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Entity not found"
        )
    
    return entity


@router.put("/entities/{entity_id}", response_model=DynamicEntitySchema)
async def update_dynamic_entity(
    entity_id: UUID,
    entity_data: DynamicEntityUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """更新动态实体"""
    # 获取现有实体
    entity_query = select(DynamicEntity).where(DynamicEntity.id == entity_id)
    entity_result = await db.execute(entity_query)
    entity = entity_result.scalar_one_or_none()
    
    if not entity:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Entity not found"
        )
    
    # 获取模型类型
    model_type_query = select(ModelType).options(selectinload(ModelType.fields)).where(ModelType.id == entity.model_type_id)
    model_type_result = await db.execute(model_type_query)
    model_type = model_type_result.scalar_one()
    
    # 验证更新数据
    if entity_data.entity_data:
        validated_data = await validate_entity_data(db, model_type, entity_data.entity_data, entity.id)
        entity.entity_data = validated_data
    
    entity.updated_at = datetime.utcnow()
    await db.commit()
    await db.refresh(entity)
    
    # 同步到Elasticsearch
    await sync_entity_to_elasticsearch(entity, model_type)
    
    return entity


@router.delete("/entities/{entity_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_dynamic_entity(
    entity_id: UUID,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """删除动态实体"""
    # 获取现有实体
    entity_query = select(DynamicEntity).where(DynamicEntity.id == entity_id)
    entity_result = await db.execute(entity_query)
    entity = entity_result.scalar_one_or_none()
    
    if not entity:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Entity not found"
        )
    
    # 从Elasticsearch删除
    if entity.es_doc_id:
        try:
            es = await get_es_client()
            await es.delete(index=entity.es_index, id=entity.es_doc_id)
        except Exception:
            pass  # 忽略ES删除错误
    
    # 从数据库删除
    await db.execute(delete(DynamicEntity).where(DynamicEntity.id == entity_id))
    await db.commit()


# 辅助函数
async def validate_entity_data(db: AsyncSession, model_type: ModelType, entity_data: Dict[str, Any], entity_id: UUID = None) -> Dict[str, Any]:
    """验证实体数据"""
    validated_data = {}
    errors = []
    
    for field in model_type.fields:
        field_name = field.field_name
        field_value = entity_data.get(field_name)
        
        # 检查必填字段
        if field.is_required and (field_value is None or field_value == ""):
            errors.append(f"Field '{field.display_name}' is required")
            continue
        
        # 使用默认值
        if field_value is None and field.default_value:
            field_value = field.default_value
        
        # 基础类型验证
        if field_value is not None:
            try:
                validated_value = validate_field_value(field_value, field.field_type, field.validation_rules)
                validated_data[field_name] = validated_value
            except ValueError as e:
                errors.append(f"Field '{field.display_name}': {str(e)}")
        
        # 唯一性检查
        if field.is_unique and field_value is not None:
            # 检查字段值是否已存在
            unique_query = select(DynamicEntity).where(
                DynamicEntity.model_type_id == model_type.id,
                DynamicEntity.entity_data.op('->>')((field_name)) == str(field_value)
            )
            # 如果是更新操作，排除当前实体
            if entity_id:
                unique_query = unique_query.where(DynamicEntity.id != entity_id)
            
            existing_result = await db.execute(unique_query)
            existing_entity = existing_result.scalar_one_or_none()
            if existing_entity:
                errors.append(f"Field '{field.display_name}' value '{field_value}' already exists")
    
    if errors:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={"message": "Validation errors", "errors": errors}
        )
    
    return validated_data


def validate_field_value(value: Any, field_type: str, validation_rules: Dict[str, Any] = None) -> Any:
    """验证字段值"""
    if validation_rules is None:
        validation_rules = {}
    
    if field_type == "text":
        value = str(value)
        if "minLength" in validation_rules and len(value) < validation_rules["minLength"]:
            raise ValueError(f"Must be at least {validation_rules['minLength']} characters")
        if "maxLength" in validation_rules and len(value) > validation_rules["maxLength"]:
            raise ValueError(f"Must be at most {validation_rules['maxLength']} characters")
    
    elif field_type == "number":
        try:
            value = float(value)
            if "min" in validation_rules and value < validation_rules["min"]:
                raise ValueError(f"Must be at least {validation_rules['min']}")
            if "max" in validation_rules and value > validation_rules["max"]:
                raise ValueError(f"Must be at most {validation_rules['max']}")
        except (ValueError, TypeError):
            raise ValueError("Must be a valid number")
    
    elif field_type == "email":
        import re
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, str(value)):
            raise ValueError("Must be a valid email address")
    
    elif field_type == "date":
        try:
            datetime.fromisoformat(str(value))
        except ValueError:
            raise ValueError("Must be a valid date")
    
    return value


async def create_elasticsearch_index(model_type: ModelType):
    """为模型类型创建Elasticsearch索引"""
    try:
        es = await get_es_client()
        index_name = f"dynamic_{model_type.name}"
        
        # 创建索引映射
        mapping = {
            "mappings": {
                "properties": {
                    "model_type_id": {"type": "keyword"},
                    "created_at": {"type": "date"},
                    "updated_at": {"type": "date"},
                    "entity_data": {
                        "type": "object",
                        "dynamic": True
                    }
                }
            }
        }
        
        # 为每个字段添加特定映射
        for field in model_type.fields:
            if field.field_type == "text":
                mapping["mappings"]["properties"][f"entity_data.{field.field_name}"] = {
                    "type": "text",
                    "analyzer": "standard"
                }
            elif field.field_type == "number":
                mapping["mappings"]["properties"][f"entity_data.{field.field_name}"] = {
                    "type": "double"
                }
            elif field.field_type == "date":
                mapping["mappings"]["properties"][f"entity_data.{field.field_name}"] = {
                    "type": "date"
                }
            elif field.field_type == "boolean":
                mapping["mappings"]["properties"][f"entity_data.{field.field_name}"] = {
                    "type": "boolean"
                }
        
        # 创建索引
        if not await es.indices.exists(index=index_name):
            await es.indices.create(index=index_name, body=mapping)
            
    except Exception as e:
        print(f"Failed to create Elasticsearch index: {e}")


async def update_elasticsearch_index(model_type: ModelType):
    """更新Elasticsearch索引"""
    # 重新创建索引
    await create_elasticsearch_index(model_type)


async def sync_entity_to_elasticsearch(entity: DynamicEntity, model_type: ModelType):
    """同步实体到Elasticsearch"""
    try:
        es = await get_es_client()
        index_name = f"dynamic_{model_type.name}"
        
        doc = {
            "model_type_id": str(entity.model_type_id),
            "entity_data": entity.entity_data,
            "created_at": entity.created_at.isoformat(),
            "updated_at": entity.updated_at.isoformat() if entity.updated_at else None
        }
        
        if entity.es_doc_id:
            # 更新现有文档
            await es.update(index=index_name, id=entity.es_doc_id, body={"doc": doc})
        else:
            # 创建新文档
            result = await es.index(index=index_name, body=doc)
            entity.es_index = index_name
            entity.es_doc_id = result["_id"]
            
    except Exception as e:
        print(f"Failed to sync entity to Elasticsearch: {e}")


@router.get("/field-types/", response_model=List[dict])
async def get_supported_field_types(
    current_user: User = Depends(get_current_user)
):
    """获取支持的字段类型"""
    field_types = [
        {
            "type": "text",
            "display_name": "文本",
            "description": "单行文本输入",
            "validation_options": ["minLength", "maxLength", "pattern"]
        },
        {
            "type": "textarea",
            "display_name": "多行文本",
            "description": "多行文本输入",
            "validation_options": ["minLength", "maxLength"]
        },
        {
            "type": "number",
            "display_name": "数字",
            "description": "数字输入",
            "validation_options": ["min", "max", "step"]
        },
        {
            "type": "email",
            "display_name": "邮箱",
            "description": "邮箱地址",
            "validation_options": []
        },
        {
            "type": "date",
            "display_name": "日期",
            "description": "日期选择器",
            "validation_options": ["min", "max"]
        },
        {
            "type": "datetime",
            "display_name": "日期时间",
            "description": "日期时间选择器",
            "validation_options": ["min", "max"]
        },
        {
            "type": "select",
            "display_name": "单选",
            "description": "下拉单选",
            "validation_options": [],
            "requires_options": True
        },
        {
            "type": "multi_select",
            "display_name": "多选",
            "description": "下拉多选",
            "validation_options": ["minItems", "maxItems"],
            "requires_options": True
        },
        {
            "type": "boolean",
            "display_name": "布尔值",
            "description": "是/否选择",
            "validation_options": []
        },
        {
            "type": "json",
            "display_name": "JSON",
            "description": "JSON对象",
            "validation_options": ["schema"]
        }
    ]
    
    return field_types


@router.get("/all-assets", response_model=List[DynamicEntitySchema])
async def get_all_assets(
    skip: int = 0,
    limit: int = 100,
    search: Optional[str] = Query(None),
    platform_id: Optional[str] = Query(None),
    project_id: Optional[str] = Query(None),
    asset_type: Optional[str] = Query(None),
    confidence: Optional[str] = Query(None),
    status: Optional[str] = Query(None),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取所有资产类型的动态实体（聚合查询）"""
    from sqlalchemy import or_
    
    # 获取所有可能包含资产的模型类型
    # 排除明显的非资产模型类型
    exclude_models = ['platform', 'project', 'vulnerability_kb', 'agent', 'task', 'customer_info']
    
    model_types_query = select(ModelType).where(
        ModelType.is_active == True,
        ~ModelType.name.in_(exclude_models)
    )
    model_types_result = await db.execute(model_types_query)
    asset_model_types = model_types_result.scalars().all()
    
    if not asset_model_types:
        return []
    
    # 获取这些模型类型的所有实体
    asset_model_ids = [mt.id for mt in asset_model_types]
    
    query = select(DynamicEntity).where(DynamicEntity.model_type_id.in_(asset_model_ids))
    
    # 应用搜索条件
    if search:
        search_conditions = []
        search_conditions.append(DynamicEntity.entity_data.op('->>')('name').ilike(f"%{search}%"))
        search_conditions.append(DynamicEntity.entity_data.op('->>')('asset_value').ilike(f"%{search}%"))
        search_conditions.append(DynamicEntity.entity_data.op('->>')('asset_host').ilike(f"%{search}%"))
        search_conditions.append(DynamicEntity.entity_data.op('->>')('host').ilike(f"%{search}%"))
        search_conditions.append(DynamicEntity.entity_data.op('->>')('ip').ilike(f"%{search}%"))
        search_conditions.append(DynamicEntity.entity_data.op('->>')('domain').ilike(f"%{search}%"))
        search_conditions.append(DynamicEntity.entity_data.op('->>')('url').ilike(f"%{search}%"))
        query = query.where(or_(*search_conditions))
    
    # 应用筛选条件
    if platform_id:
        query = query.where(DynamicEntity.entity_data.op('->>')('platform_id') == platform_id)
    
    if project_id:
        query = query.where(DynamicEntity.entity_data.op('->>')('project_id') == project_id)
        
    if asset_type:
        query = query.where(DynamicEntity.entity_data.op('->>')('asset_type') == asset_type)
        
    if confidence:
        query = query.where(DynamicEntity.entity_data.op('->>')('confidence') == confidence)
        
    if status:
        query = query.where(DynamicEntity.entity_data.op('->>')('status') == status)
    
    # 分页和排序
    query = query.order_by(DynamicEntity.created_at.desc()).offset(skip).limit(limit)
    
    result = await db.execute(query)
    entities = result.scalars().all()
    
    return entities


@router.get("/all-assets/count")
async def get_all_assets_count(
    search: Optional[str] = Query(None),
    platform_id: Optional[str] = Query(None),
    project_id: Optional[str] = Query(None),
    asset_type: Optional[str] = Query(None),
    confidence: Optional[str] = Query(None),
    status: Optional[str] = Query(None),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取所有资产类型的动态实体数量"""
    from sqlalchemy import func, or_
    
    # 获取所有可能包含资产的模型类型
    exclude_models = ['platform', 'project', 'vulnerability_kb', 'agent', 'task', 'customer_info']
    
    model_types_query = select(ModelType).where(
        ModelType.is_active == True,
        ~ModelType.name.in_(exclude_models)
    )
    model_types_result = await db.execute(model_types_query)
    asset_model_types = model_types_result.scalars().all()
    
    if not asset_model_types:
        return {"count": 0}
    
    asset_model_ids = [mt.id for mt in asset_model_types]
    
    query = select(func.count(DynamicEntity.id)).where(DynamicEntity.model_type_id.in_(asset_model_ids))
    
    # 应用相同的筛选条件
    if search:
        search_conditions = []
        search_conditions.append(DynamicEntity.entity_data.op('->>')('name').ilike(f"%{search}%"))
        search_conditions.append(DynamicEntity.entity_data.op('->>')('asset_value').ilike(f"%{search}%"))
        search_conditions.append(DynamicEntity.entity_data.op('->>')('asset_host').ilike(f"%{search}%"))
        search_conditions.append(DynamicEntity.entity_data.op('->>')('host').ilike(f"%{search}%"))
        search_conditions.append(DynamicEntity.entity_data.op('->>')('ip').ilike(f"%{search}%"))
        search_conditions.append(DynamicEntity.entity_data.op('->>')('domain').ilike(f"%{search}%"))
        search_conditions.append(DynamicEntity.entity_data.op('->>')('url').ilike(f"%{search}%"))
        query = query.where(or_(*search_conditions))
    
    if platform_id:
        query = query.where(DynamicEntity.entity_data.op('->>')('platform_id') == platform_id)
    
    if project_id:
        query = query.where(DynamicEntity.entity_data.op('->>')('project_id') == project_id)
        
    if asset_type:
        query = query.where(DynamicEntity.entity_data.op('->>')('asset_type') == asset_type)
        
    if confidence:
        query = query.where(DynamicEntity.entity_data.op('->>')('confidence') == confidence)
        
    if status:
        query = query.where(DynamicEntity.entity_data.op('->>')('status') == status)
    
    result = await db.execute(query)
    count = result.scalar()
    
    return {"count": count}