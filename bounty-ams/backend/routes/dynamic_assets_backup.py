from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from typing import List, Dict, Any, Optional
from uuid import UUID
from datetime import datetime
import json

from database import get_db
from models import AssetType, AssetTypeField, User
from schemas import DynamicAssetCreate, DynamicAssetUpdate, DynamicAsset
from auth import get_current_user, get_current_admin_user
from elasticsearch_client import get_es_client

router = APIRouter(prefix="/assets", tags=["assets"])


async def validate_asset_data(asset_type_id: UUID, data: Dict[str, Any], db: AsyncSession) -> Dict[str, Any]:
    """Validate asset data against asset type fields"""
    # Get asset type with fields
    query = select(AssetType).where(AssetType.id == asset_type_id)
    result = await db.execute(query)
    asset_type = result.scalar_one_or_none()
    
    if not asset_type:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Asset type not found"
        )
    
    # Get fields
    fields_query = select(AssetTypeField).where(AssetTypeField.asset_type_id == asset_type_id)
    fields_result = await db.execute(fields_query)
    fields = fields_result.scalars().all()
    
    validated_data = {}
    errors = []
    
    for field in fields:
        field_name = field.field_name
        field_value = data.get(field_name)
        
        # Check required fields
        if field.is_required and (field_value is None or field_value == ""):
            errors.append(f"Field '{field.display_name}' is required")
            continue
        
        # Use default value if not provided
        if field_value is None and field.default_value:
            field_value = field.default_value
        
        # Type validation (basic)
        if field_value is not None:
            try:
                validated_value = validate_field_value(field_value, field.field_type, field.validation_rules)
                validated_data[field_name] = validated_value
            except ValueError as e:
                errors.append(f"Field '{field.display_name}': {str(e)}")
    
    if errors:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={"message": "Validation errors", "errors": errors}
        )
    
    return validated_data


def validate_field_value(value: Any, field_type: str, validation_rules: Dict[str, Any] = None) -> Any:
    """Validate individual field value"""
    if validation_rules is None:
        validation_rules = {}
    
    if field_type == "text":
        value = str(value)
        if "minLength" in validation_rules and len(value) < validation_rules["minLength"]:
            raise ValueError(f"Must be at least {validation_rules['minLength']} characters")
        if "maxLength" in validation_rules and len(value) > validation_rules["maxLength"]:
            raise ValueError(f"Must be at most {validation_rules['maxLength']} characters")
    
    elif field_type == "number":
        try:
            value = float(value)
            if "min" in validation_rules and value < validation_rules["min"]:
                raise ValueError(f"Must be at least {validation_rules['min']}")
            if "max" in validation_rules and value > validation_rules["max"]:
                raise ValueError(f"Must be at most {validation_rules['max']}")
        except (ValueError, TypeError):
            raise ValueError("Must be a valid number")
    
    elif field_type == "ip":
        import ipaddress
        try:
            ipaddress.ip_address(value)
        except ValueError:
            raise ValueError("Must be a valid IP address")
    
    elif field_type == "url":
        from urllib.parse import urlparse
        try:
            result = urlparse(str(value))
            if not all([result.scheme, result.netloc]):
                raise ValueError("Must be a valid URL")
        except Exception:
            raise ValueError("Must be a valid URL")
    
    elif field_type == "email":
        import re
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, str(value)):
            raise ValueError("Must be a valid email address")
    
    elif field_type == "date":
        try:
            datetime.fromisoformat(str(value))
        except ValueError:
            raise ValueError("Must be a valid date")
    
    elif field_type == "json":
        try:
            if isinstance(value, str):
                json.loads(value)
        except json.JSONDecodeError:
            raise ValueError("Must be valid JSON")
    
    return value


@router.post("/", response_model=DynamicAsset, status_code=status.HTTP_201_CREATED)
async def create_asset(
    asset_data: DynamicAssetCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Create a new asset"""
    # Validate asset data
    validated_data = await validate_asset_data(asset_data.asset_type_id, asset_data.data, db)
    
    # Get asset type info
    asset_type_query = select(AssetType).where(AssetType.id == asset_data.asset_type_id)
    asset_type_result = await db.execute(asset_type_query)
    asset_type = asset_type_result.scalar_one()
    
    # Prepare document for Elasticsearch
    doc = {
        "asset_type": asset_type.name,
        "asset_type_id": str(asset_data.asset_type_id),
        "data": validated_data,
        "tags": asset_data.tags or [],
        "source": asset_data.source,
        "created_at": datetime.utcnow().isoformat(),
        "updated_at": datetime.utcnow().isoformat()
    }
    
    # Index in Elasticsearch
    es = await get_es_client()
    result = await es.index(index="assets", body=doc)
    
    return DynamicAsset(
        id=result["_id"],
        asset_type_id=asset_data.asset_type_id,
        data=validated_data,
        tags=asset_data.tags,
        source=asset_data.source,
        created_at=datetime.utcnow()
    )


@router.get("/", response_model=List[DynamicAsset])
async def get_assets(
    asset_type_id: Optional[UUID] = Query(None, description="Filter by asset type ID"),
    skip: int = Query(0, ge=0, description="Number of results to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of results to return"),
    search: Optional[str] = Query(None, description="Search in asset data and tags"),
    tags: Optional[str] = Query(None, description="Filter by tags (comma-separated)"),
    source: Optional[str] = Query(None, description="Filter by source"),
    date_from: Optional[str] = Query(None, description="Filter assets created after this date (ISO format)"),
    date_to: Optional[str] = Query(None, description="Filter assets created before this date (ISO format)"),
    sort_by: Optional[str] = Query("created_at", description="Sort field (created_at, updated_at)"),
    sort_order: Optional[str] = Query("desc", description="Sort order (asc, desc)"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get assets with advanced filtering and sorting"""
    query_body = {
        "from": skip,
        "size": limit,
        "query": {"bool": {"must": [{"match_all": {}}], "filter": []}},
        "sort": []
    }
    
    # Add filters
    filters = []
    
    if asset_type_id:
        filters.append({"term": {"asset_type_id": str(asset_type_id)}})
    
    if source:
        filters.append({"term": {"source": source}})
    
    if tags:
        tag_list = [tag.strip() for tag in tags.split(",") if tag.strip()]
        if tag_list:
            filters.append({"terms": {"tags": tag_list}})
    
    # Date range filter
    if date_from or date_to:
        date_range = {}
        if date_from:
            date_range["gte"] = date_from
        if date_to:
            date_range["lte"] = date_to
        if date_range:
            filters.append({"range": {"created_at": date_range}})
    
    # Search query
    if search:
        search_query = {
            "bool": {
                "should": [
                    {"multi_match": {
                        "query": search,
                        "fields": ["data.*^2", "tags^1.5"],
                        "type": "best_fields",
                        "fuzziness": "AUTO"
                    }},
                    {"wildcard": {"data.*": f"*{search}*"}},
                    {"nested": {
                        "path": "data",
                        "query": {
                            "multi_match": {
                                "query": search,
                                "fields": ["data.*"]
                            }
                        }
                    }}
                ],
                "minimum_should_match": 1
            }
        }
        query_body["query"]["bool"]["must"] = [search_query]
    
    # Apply filters
    if filters:
        query_body["query"]["bool"]["filter"] = filters
    
    # Sorting
    valid_sort_fields = ["created_at", "updated_at"]
    if sort_by in valid_sort_fields:
        sort_direction = "desc" if sort_order.lower() == "desc" else "asc"
        query_body["sort"] = [{sort_by: {"order": sort_direction}}]
    
    # Search in Elasticsearch
    es = await get_es_client()
    result = await es.search(index="assets", body=query_body)
    
    assets = []
    for hit in result["hits"]["hits"]:
        source = hit["_source"]
        assets.append(DynamicAsset(
            id=hit["_id"],
            asset_type_id=UUID(source["asset_type_id"]),
            data=source["data"],
            tags=source["tags"],
            source=source["source"],
            created_at=datetime.fromisoformat(source["created_at"]),
            updated_at=datetime.fromisoformat(source["updated_at"]) if source.get("updated_at") else None
        ))
    
    return assets


@router.get("/{asset_id}", response_model=DynamicAsset)
async def get_asset(
    asset_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get a specific asset by ID"""
    try:
        es = await get_es_client()
        result = await es.get(index="assets", id=asset_id)
        source = result["_source"]
        
        return DynamicAsset(
            id=result["_id"],
            asset_type_id=UUID(source["asset_type_id"]),
            data=source["data"],
            tags=source["tags"],
            source=source["source"],
            created_at=datetime.fromisoformat(source["created_at"]),
            updated_at=datetime.fromisoformat(source["updated_at"]) if source.get("updated_at") else None
        )
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Asset not found"
        )


@router.put("/{asset_id}", response_model=DynamicAsset)
async def update_asset(
    asset_id: str,
    asset_data: DynamicAssetUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Update an asset"""
    try:
        es = await get_es_client()
        # Get existing asset
        existing = await es.get(index="assets", id=asset_id)
        existing_source = existing["_source"]
        
        # Validate new data if provided
        if asset_data.data:
            validated_data = await validate_asset_data(
                UUID(existing_source["asset_type_id"]), 
                asset_data.data, 
                db
            )
        else:
            validated_data = existing_source["data"]
        
        # Prepare update document
        update_doc = {
            "data": validated_data,
            "tags": asset_data.tags if asset_data.tags is not None else existing_source["tags"],
            "source": asset_data.source if asset_data.source is not None else existing_source["source"],
            "updated_at": datetime.utcnow().isoformat()
        }
        
        # Update in Elasticsearch
        await es.update(index="assets", id=asset_id, body={"doc": update_doc})
        
        return DynamicAsset(
            id=asset_id,
            asset_type_id=UUID(existing_source["asset_type_id"]),
            data=validated_data,
            tags=update_doc["tags"],
            source=update_doc["source"],
            created_at=datetime.fromisoformat(existing_source["created_at"]),
            updated_at=datetime.fromisoformat(update_doc["updated_at"])
        )
    except Exception as e:
        if "not found" in str(e):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Asset not found"
            )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.delete("/{asset_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_asset(
    asset_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Delete an asset"""
    try:
        es = await get_es_client()
        await es.delete(index="assets", id=asset_id)
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Asset not found"
        )


@router.get("/stats/overview", response_model=dict)
async def get_assets_overview(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get assets overview statistics"""
    es = await get_es_client()
    
    # Total assets count
    total_result = await es.count(index="assets")
    total_assets = total_result["count"]
    
    # Assets by type aggregation
    type_agg_query = {
        "size": 0,
        "aggs": {
            "asset_types": {
                "terms": {
                    "field": "asset_type",
                    "size": 20
                }
            }
        }
    }
    
    type_result = await es.search(index="assets", body=type_agg_query)
    asset_types = [
        {"type": bucket["key"], "count": bucket["doc_count"]}
        for bucket in type_result["aggregations"]["asset_types"]["buckets"]
    ]
    
    # Assets by source aggregation
    source_agg_query = {
        "size": 0,
        "aggs": {
            "sources": {
                "terms": {
                    "field": "source",
                    "size": 10
                }
            }
        }
    }
    
    source_result = await es.search(index="assets", body=source_agg_query)
    sources = [
        {"source": bucket["key"], "count": bucket["doc_count"]}
        for bucket in source_result["aggregations"]["sources"]["buckets"]
    ]
    
    # Assets created in last 30 days
    recent_query = {
        "query": {
            "range": {
                "created_at": {
                    "gte": "now-30d"
                }
            }
        }
    }
    
    recent_result = await es.count(index="assets", body=recent_query)
    recent_assets = recent_result["count"]
    
    return {
        "total_assets": total_assets,
        "recent_assets": recent_assets,
        "asset_types": asset_types,
        "sources": sources
    }


@router.get("/stats/tags", response_model=List[dict])
async def get_popular_tags(
    limit: int = Query(20, ge=1, le=100),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get popular tags statistics"""
    es = await get_es_client()
    
    tags_agg_query = {
        "size": 0,
        "aggs": {
            "popular_tags": {
                "terms": {
                    "field": "tags",
                    "size": limit
                }
            }
        }
    }
    
    result = await es.search(index="assets", body=tags_agg_query)
    tags = [
        {"tag": bucket["key"], "count": bucket["doc_count"]}
        for bucket in result["aggregations"]["popular_tags"]["buckets"]
    ]
    
    return tags


@router.get("/stats/timeline", response_model=List[dict])
async def get_assets_timeline(
    interval: str = Query("1d", description="Time interval (1h, 1d, 1w, 1M)"),
    days: int = Query(30, ge=1, le=365, description="Number of days to look back"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get assets creation timeline"""
    es = await get_es_client()
    
    timeline_query = {
        "size": 0,
        "query": {
            "range": {
                "created_at": {
                    "gte": f"now-{days}d"
                }
            }
        },
        "aggs": {
            "timeline": {
                "date_histogram": {
                    "field": "created_at",
                    "fixed_interval": interval,
                    "min_doc_count": 0,
                    "extended_bounds": {
                        "min": f"now-{days}d",
                        "max": "now"
                    }
                }
            }
        }
    }
    
    result = await es.search(index="assets", body=timeline_query)
    timeline = [
        {
            "timestamp": bucket["key_as_string"],
            "count": bucket["doc_count"]
        }
        for bucket in result["aggregations"]["timeline"]["buckets"]
    ]
    
    return timeline


@router.post("/bulk", response_model=dict)
async def bulk_create_assets(
    assets: List[DynamicAssetCreate],
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Bulk create assets"""
    if len(assets) > 100:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot create more than 100 assets at once"
        )
    
    es = await get_es_client()
    bulk_body = []
    created_assets = []
    errors = []
    
    for i, asset_data in enumerate(assets):
        try:
            # Validate asset data
            validated_data = await validate_asset_data(asset_data.asset_type_id, asset_data.data, db)
            
            # Get asset type info
            asset_type_query = select(AssetType).where(AssetType.id == asset_data.asset_type_id)
            asset_type_result = await db.execute(asset_type_query)
            asset_type = asset_type_result.scalar_one()
            
            # Prepare document for Elasticsearch
            doc = {
                "asset_type": asset_type.name,
                "asset_type_id": str(asset_data.asset_type_id),
                "data": validated_data,
                "tags": asset_data.tags or [],
                "source": asset_data.source,
                "created_at": datetime.utcnow().isoformat(),
                "updated_at": datetime.utcnow().isoformat()
            }
            
            # Add to bulk body
            bulk_body.append({"index": {"_index": "assets"}})
            bulk_body.append(doc)
            
            created_assets.append({
                "index": i,
                "asset_type_id": str(asset_data.asset_type_id),
                "data": validated_data
            })
            
        except Exception as e:
            errors.append({
                "index": i,
                "error": str(e)
            })
    
    # Execute bulk request
    if bulk_body:
        try:
            bulk_result = await es.bulk(body=bulk_body)
            
            # Check for bulk errors
            for item in bulk_result.get("items", []):
                if "index" in item and "error" in item["index"]:
                    errors.append({
                        "error": item["index"]["error"]
                    })
                    
        except Exception as e:
            errors.append({"error": f"Bulk indexing failed: {str(e)}"})
    
    return {
        "created_count": len(created_assets),
        "error_count": len(errors),
        "created_assets": created_assets,
        "errors": errors
    }