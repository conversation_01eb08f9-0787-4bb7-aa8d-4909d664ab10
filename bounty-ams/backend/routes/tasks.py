from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, func, delete
from typing import List, Optional, Dict, Any
from datetime import datetime
import uuid

from database import get_db
from models_dynamic import Task, Agent, WorkflowExecution, User
from schemas_agent import (
    TaskDefinition, TaskInfo, TaskResult, TaskStatus, TaskPriority,
    AgentCapability, ErrorResponse
)
from auth import get_current_user, require_admin
from pydantic import BaseModel, Field

router = APIRouter()

# 批量任务创建请求模型
class BatchTaskRequest(BaseModel):
    """批量任务创建请求"""
    task_type: str = Field(..., description="任务类型")
    targets: List[str] = Field(..., description="目标列表")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="任务参数")
    priority: str = Field(default="medium", description="优先级")
    timeout: int = Field(default=300, description="超时时间（秒）")
    retry_count: int = Field(default=1, description="重试次数")
    workflow_id: Optional[str] = Field(None, description="工作流ID")
    preferred_agent_id: Optional[str] = Field(None, description="首选Agent ID")

@router.post("/batch", response_model=List[TaskInfo])
async def create_batch_tasks(
    batch_request: BatchTaskRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """批量创建任务"""
    created_tasks = []
    
    # 优先级映射
    priority_map = {
        "low": 1,
        "medium": 2, 
        "high": 3,
        "urgent": 4
    }
    
    for target in batch_request.targets:
        # 为每个目标创建一个任务
        task_id = str(uuid.uuid4())
        task = Task(
            task_id=task_id,
            task_type=batch_request.task_type,
            target=target,
            parameters=batch_request.parameters,
            priority=priority_map.get(batch_request.priority, 2),
            timeout=batch_request.timeout,
            retry_count=batch_request.retry_count,
            workflow_id=batch_request.workflow_id,
            preferred_agent_id=batch_request.preferred_agent_id,
            created_by_user_id=current_user.id
        )
        
        db.add(task)
        created_tasks.append(task)
    
    await db.commit()
    
    # 刷新所有任务以获取生成的字段
    for task in created_tasks:
        await db.refresh(task)
    
    return created_tasks

@router.post("/", response_model=TaskInfo)
async def create_task(
    task_data: TaskDefinition,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """创建任务"""
    # 创建任务
    task = Task(
        task_id=task_data.task_id,
        task_type=task_data.task_type.value if hasattr(task_data.task_type, 'value') else task_data.task_type,
        target=task_data.target,
        parameters=task_data.parameters,
        priority=task_data.priority.value if hasattr(task_data.priority, 'value') else task_data.priority,
        timeout=task_data.timeout,
        retry_count=task_data.retry_count,
        dependencies=task_data.dependencies,
        workflow_id=task_data.workflow_id,
        preferred_agent_id=task_data.preferred_agent_id,
        created_by_user_id=current_user.id
    )
    
    db.add(task)
    await db.commit()
    await db.refresh(task)
    
    return task

@router.get("/")
async def list_tasks(
    status: Optional[str] = None,
    task_type: Optional[str] = None,
    workflow_id: Optional[str] = None,
    limit: int = 100,
    offset: int = 0,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取任务列表"""
    query = select(Task)
    
    # 添加过滤条件
    if status:
        query = query.where(Task.status == status)
    if task_type:
        query = query.where(Task.task_type == task_type)
    if workflow_id:
        query = query.where(Task.workflow_id == workflow_id)
    
    # 排序和分页
    query = query.order_by(Task.priority.desc(), Task.created_at.desc())
    query = query.offset(offset).limit(limit)
    
    result = await db.execute(query)
    tasks = result.scalars().all()
    
    return {"tasks": tasks}

@router.get("/stats")
async def get_task_stats(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取任务统计信息"""
    # 获取各状态任务数量
    result = await db.execute(
        select(Task.status, func.count(Task.id).label('count'))
        .group_by(Task.status)
    )
    status_counts = dict(result.fetchall())
    
    return {
        "total": sum(status_counts.values()),
        "pending": status_counts.get("pending", 0),
        "running": status_counts.get("running", 0), 
        "completed": status_counts.get("completed", 0),
        "failed": status_counts.get("failed", 0)
    }

@router.get("/{task_id}", response_model=TaskInfo)
async def get_task(
    task_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取任务详情"""
    result = await db.execute(
        select(Task).where(Task.task_id == task_id)
    )
    task = result.scalar_one_or_none()
    
    if not task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Task not found"
        )
    
    return task

@router.put("/{task_id}", response_model=TaskInfo)
async def update_task(
    task_id: str,
    task_data: TaskDefinition,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """更新任务"""
    result = await db.execute(
        select(Task).where(Task.task_id == task_id)
    )
    task = result.scalar_one_or_none()
    
    if not task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Task not found"
        )
    
    # 只允许更新pending状态的任务
    if task.status != "pending":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Can only update pending tasks"
        )
    
    # 更新任务字段
    update_data = {
        "task_type": task_data.task_type.value if hasattr(task_data.task_type, 'value') else task_data.task_type,
        "target": task_data.target,
        "parameters": task_data.parameters,
        "priority": task_data.priority.value if hasattr(task_data.priority, 'value') else task_data.priority,
        "timeout": task_data.timeout,
        "retry_count": task_data.retry_count,
        "dependencies": task_data.dependencies,
        "workflow_id": task_data.workflow_id
    }
    
    await db.execute(
        update(Task)
        .where(Task.task_id == task_id)
        .values(**update_data)
    )
    
    await db.commit()
    
    # 重新获取更新后的任务
    result = await db.execute(
        select(Task).where(Task.task_id == task_id)
    )
    task = result.scalar_one()
    
    return task

@router.delete("/{task_id}")
async def delete_task(
    task_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(require_admin)
):
    """删除任务"""
    result = await db.execute(
        select(Task).where(Task.task_id == task_id)
    )
    task = result.scalar_one_or_none()
    
    if not task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Task not found"
        )
    
    # 如果任务正在运行，不能删除
    if task.status == "running":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot delete running task"
        )
    
    await db.delete(task)
    await db.commit()
    
    return {"message": "Task deleted successfully"}

@router.post("/{task_id}/cancel")
async def cancel_task(
    task_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """取消任务"""
    result = await db.execute(
        select(Task).where(Task.task_id == task_id)
    )
    task = result.scalar_one_or_none()
    
    if not task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Task not found"
        )
    
    # 只能取消pending或assigned状态的任务
    if task.status not in ["pending", "assigned"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Can only cancel pending or assigned tasks"
        )
    
    # 更新任务状态
    await db.execute(
        update(Task)
        .where(Task.task_id == task_id)
        .values(
            status="cancelled",
            completed_at=datetime.utcnow()
        )
    )
    
    # 如果任务已分配给Agent，减少Agent的当前任务数
    if task.agent_id:
        agent_result = await db.execute(
            select(Agent).where(Agent.agent_id == task.agent_id)
        )
        agent = agent_result.scalar_one_or_none()
        
        if agent:
            await db.execute(
                update(Agent)
                .where(Agent.agent_id == task.agent_id)
                .values(current_tasks=max(0, agent.current_tasks - 1))
            )
    
    await db.commit()
    
    return {"message": "Task cancelled successfully"}

@router.post("/{task_id}/retry")
async def retry_task(
    task_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """重试任务"""
    result = await db.execute(
        select(Task).where(Task.task_id == task_id)
    )
    task = result.scalar_one_or_none()
    
    if not task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Task not found"
        )
    
    # 只能重试failed状态的任务
    if task.status != "failed":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Can only retry failed tasks"
        )
    
    # 检查重试次数
    if task.current_retry >= task.retry_count:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Task has exceeded maximum retry count"
        )
    
    # 重置任务状态
    await db.execute(
        update(Task)
        .where(Task.task_id == task_id)
        .values(
            status="pending",
            agent_id=None,
            current_retry=task.current_retry + 1,
            assigned_at=None,
            started_at=None,
            completed_at=None,
            error_message=None
        )
    )
    
    await db.commit()
    
    return {"message": "Task retry initiated"}

@router.get("/stats/overview")
async def get_task_stats(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取任务统计信息"""
    # 基础统计
    stats_result = await db.execute(
        select(
            func.count(Task.id).label("total"),
            func.count(Task.id).filter(Task.status == "pending").label("pending"),
            func.count(Task.id).filter(Task.status == "assigned").label("assigned"),
            func.count(Task.id).filter(Task.status == "running").label("running"),
            func.count(Task.id).filter(Task.status == "completed").label("completed"),
            func.count(Task.id).filter(Task.status == "failed").label("failed"),
            func.count(Task.id).filter(Task.status == "cancelled").label("cancelled")
        )
    )
    stats = stats_result.first()
    
    # 按类型统计
    type_stats_result = await db.execute(
        select(
            Task.task_type,
            func.count(Task.id).label("count")
        )
        .group_by(Task.task_type)
        .order_by(func.count(Task.id).desc())
    )
    type_stats = type_stats_result.all()
    
    # 按优先级统计
    priority_stats_result = await db.execute(
        select(
            Task.priority,
            func.count(Task.id).label("count")
        )
        .group_by(Task.priority)
        .order_by(Task.priority.desc())
    )
    priority_stats = priority_stats_result.all()
    
    return {
        "total_tasks": stats.total or 0,
        "by_status": {
            "pending": stats.pending or 0,
            "assigned": stats.assigned or 0,
            "running": stats.running or 0,
            "completed": stats.completed or 0,
            "failed": stats.failed or 0,
            "cancelled": stats.cancelled or 0
        },
        "by_type": [
            {"task_type": item.task_type, "count": item.count}
            for item in type_stats
        ],
        "by_priority": [
            {"priority": item.priority, "count": item.count}
            for item in priority_stats
        ]
    }

@router.post("/bulk-create")
async def bulk_create_tasks(
    tasks: List[TaskDefinition],
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """批量创建任务"""
    created_tasks = []
    
    for task_data in tasks:
        task = Task(
            task_id=task_data.task_id,
            task_type=task_data.task_type.value if hasattr(task_data.task_type, 'value') else task_data.task_type,
            target=task_data.target,
            parameters=task_data.parameters,
            priority=task_data.priority.value if hasattr(task_data.priority, 'value') else task_data.priority,
            timeout=task_data.timeout,
            retry_count=task_data.retry_count,
            dependencies=task_data.dependencies,
            workflow_id=task_data.workflow_id,
            created_by_user_id=current_user.id
        )
        db.add(task)
        created_tasks.append(task)
    
    await db.commit()
    
    # 刷新所有任务
    for task in created_tasks:
        await db.refresh(task)
    
    return {
        "message": f"Created {len(created_tasks)} tasks",
        "tasks": created_tasks
    }

@router.post("/bulk-cancel")
async def bulk_cancel_tasks(
    task_ids: List[str],
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """批量取消任务"""
    # 找到所有可取消的任务
    result = await db.execute(
        select(Task).where(
            Task.task_id.in_(task_ids),
            Task.status.in_(["pending", "assigned"])
        )
    )
    tasks = result.scalars().all()
    
    if not tasks:
        return {"message": "No tasks to cancel", "cancelled_count": 0}
    
    # 更新任务状态
    await db.execute(
        update(Task)
        .where(Task.task_id.in_([task.task_id for task in tasks]))
        .values(
            status="cancelled",
            completed_at=datetime.utcnow()
        )
    )
    
    # 减少相关Agent的当前任务数
    agent_updates = {}
    for task in tasks:
        if task.agent_id:
            agent_updates[task.agent_id] = agent_updates.get(task.agent_id, 0) + 1
    
    for agent_id, count in agent_updates.items():
        await db.execute(
            update(Agent)
            .where(Agent.agent_id == agent_id)
            .values(current_tasks=func.greatest(0, Agent.current_tasks - count))
        )
    
    await db.commit()
    
    return {
        "message": f"Cancelled {len(tasks)} tasks",
        "cancelled_count": len(tasks)
    }