"""
资产管理 V2.0 统一API路由
基于Elasticsearch的统一资产管理系统API
"""

from fastapi import APIRouter, Depends, HTTPException, Query, UploadFile, File, Form
from fastapi.responses import JSONResponse
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Dict, List, Any, Optional, Union
from pydantic import BaseModel, Field
from datetime import datetime
import json
import csv
import io

from database import get_db
from auth import get_current_user
from models_dynamic import User
from asset_management_v2_unified import (
    UnifiedAssetManager,
    create_unified_asset_manager,
    DataSource,
    AssetStatus,
    ConfidenceLevel,
    AssetMetadata,
    ProcessingResult
)
from kibana_integration import KibanaIntegration, create_kibana_integration
from elasticsearch_client import get_es_client

router = APIRouter(prefix="/api/assets-v2-unified", tags=["Assets V2 Unified"])

# Pydantic模型
class AssetSearchRequest(BaseModel):
    query: str = "*"
    filters: Optional[Dict[str, Any]] = None
    page: int = Field(default=1, ge=1)
    size: int = Field(default=20, ge=1, le=1000)
    sort: Optional[List[Dict[str, Any]]] = None
    include_aggregations: bool = True

class AssetImportRequest(BaseModel):
    source: str = "manual_import"
    platform_id: Optional[str] = None
    project_id: Optional[str] = None
    confidence: str = "medium"
    status: str = "active"
    enable_dedup: bool = True
    enable_cleaning: bool = True

class BulkAssetData(BaseModel):
    assets: List[Dict[str, Any]]
    metadata: AssetImportRequest

# 依赖注入
async def get_asset_manager(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> UnifiedAssetManager:
    """获取资产管理器实例"""
    return await create_unified_asset_manager(db)

@router.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy", "service": "Assets V2 Unified", "timestamp": datetime.utcnow()}

@router.post("/search")
async def search_assets(
    request: AssetSearchRequest,
    manager: UnifiedAssetManager = Depends(get_asset_manager)
):
    """高级资产搜索"""
    try:
        # 构建聚合查询（如果需要）
        aggregations = None
        if request.include_aggregations:
            aggregations = {
                "asset_types": {"terms": {"field": "asset_type", "size": 50}},
                "confidence_levels": {"terms": {"field": "confidence", "size": 10}},
                "sources": {"terms": {"field": "source", "size": 20}},
                "platforms": {"terms": {"field": "platform_name.keyword", "size": 50}},
                "projects": {"terms": {"field": "project_name.keyword", "size": 100}},
                "discovery_timeline": {
                    "date_histogram": {
                        "field": "discovered_at",
                        "calendar_interval": "day",
                        "min_doc_count": 1
                    }
                }
            }
        
        result = await manager.advanced_search(
            query=request.query,
            filters=request.filters or {},
            aggregations=aggregations,
            page=request.page,
            size=request.size,
            sort=request.sort
        )
        
        return {
            "success": True,
            "data": result,
            "message": f"找到 {result['total']} 个资产"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"搜索失败: {str(e)}")

@router.get("/statistics")
async def get_statistics(
    manager: UnifiedAssetManager = Depends(get_asset_manager)
):
    """获取资产统计信息"""
    try:
        stats = await manager.get_asset_statistics()
        return {
            "success": True,
            "data": stats,
            "message": "统计信息获取成功"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")

@router.post("/import/bulk")
async def import_bulk_assets(
    request: BulkAssetData,
    manager: UnifiedAssetManager = Depends(get_asset_manager)
):
    """批量导入资产"""
    try:
        # 构建元数据
        metadata = AssetMetadata(
            source=DataSource(request.metadata.source),
            confidence=ConfidenceLevel(request.metadata.confidence),
            status=AssetStatus(request.metadata.status),
            platform_id=request.metadata.platform_id,
            project_id=request.metadata.project_id,
            tags=[],
            discovered_at=datetime.utcnow()
        )
        
        # 处理资产
        result = await manager.process_assets_unified(
            raw_assets=request.assets,
            source=DataSource(request.metadata.source),
            metadata=metadata,
            enable_dedup=request.metadata.enable_dedup,
            enable_cleaning=request.metadata.enable_cleaning
        )
        
        return {
            "success": True,
            "data": {
                "total_processed": result.total_processed,
                "success_count": result.success_count,
                "duplicate_count": result.duplicate_count,
                "error_count": result.error_count,
                "cleaned_count": result.cleaned_count,
                "errors": result.errors[:10]  # 只返回前10个错误
            },
            "message": f"处理完成: 成功 {result.success_count}, 重复 {result.duplicate_count}, 错误 {result.error_count}"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"批量导入失败: {str(e)}")

@router.post("/import/file")
async def import_file_assets(
    file: UploadFile = File(...),
    source: str = Form("manual_import"),
    platform_id: Optional[str] = Form(None),
    project_id: Optional[str] = Form(None),
    confidence: str = Form("medium"),
    status: str = Form("active"),
    enable_dedup: bool = Form(True),
    enable_cleaning: bool = Form(True),
    manager: UnifiedAssetManager = Depends(get_asset_manager)
):
    """文件导入资产"""
    try:
        # 读取文件内容
        content = await file.read()
        
        # 解析文件
        assets = []
        if file.filename.endswith('.json'):
            assets = json.loads(content.decode('utf-8'))
        elif file.filename.endswith('.csv'):
            csv_content = content.decode('utf-8')
            csv_reader = csv.DictReader(io.StringIO(csv_content))
            assets = list(csv_reader)
        else:
            raise HTTPException(status_code=400, detail="不支持的文件格式，请使用JSON或CSV")
        
        # 构建元数据
        metadata = AssetMetadata(
            source=DataSource(source),
            confidence=ConfidenceLevel(confidence),
            status=AssetStatus(status),
            platform_id=platform_id,
            project_id=project_id,
            tags=[],
            discovered_at=datetime.utcnow()
        )
        
        # 处理资产
        result = await manager.process_assets_unified(
            raw_assets=assets,
            source=DataSource(source),
            metadata=metadata,
            enable_dedup=enable_dedup,
            enable_cleaning=enable_cleaning
        )
        
        return {
            "success": True,
            "data": {
                "filename": file.filename,
                "total_processed": result.total_processed,
                "success_count": result.success_count,
                "duplicate_count": result.duplicate_count,
                "error_count": result.error_count,
                "cleaned_count": result.cleaned_count,
                "errors": result.errors[:10]
            },
            "message": f"文件导入完成: 成功 {result.success_count}, 重复 {result.duplicate_count}, 错误 {result.error_count}"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"文件导入失败: {str(e)}")

@router.post("/sync/dynamic-models")
async def sync_from_dynamic_models(
    model_type_name: str = Query(..., description="模型类型名称，如 'discovered_asset'"),
    platform_id: Optional[str] = Query(None),
    project_id: Optional[str] = Query(None),
    manager: UnifiedAssetManager = Depends(get_asset_manager)
):
    """从动态模型同步资产数据"""
    try:
        # TODO: 实现从动态模型同步的逻辑
        # 这里需要查询指定的动态模型数据，转换为统一格式，然后导入
        
        return {
            "success": True,
            "data": {"message": "动态模型同步功能开发中"},
            "message": "同步请求已接收"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"同步失败: {str(e)}")

@router.get("/analysis/duplicates")
async def analyze_duplicates(
    similarity_threshold: float = Query(0.85, description="相似度阈值"),
    time_window_hours: int = Query(24, description="时间窗口（小时）"),
    manager: UnifiedAssetManager = Depends(get_asset_manager)
):
    """分析重复资产"""
    try:
        analysis = await manager.advanced_deduplication_analysis(
            similarity_threshold=similarity_threshold,
            time_window_hours=time_window_hours
        )

        return {
            "success": True,
            "data": analysis,
            "message": f"发现 {analysis.get('total_duplicate_groups', 0)} 个重复组"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"分析失败: {str(e)}")

@router.delete("/cleanup/duplicates")
async def cleanup_duplicates(
    dry_run: bool = Query(True, description="是否为试运行"),
    merge_strategy: str = Query("keep_latest", description="合并策略: keep_latest, keep_highest_confidence"),
    manager: UnifiedAssetManager = Depends(get_asset_manager)
):
    """清理重复资产"""
    try:
        result = await manager.cleanup_duplicates(
            dry_run=dry_run,
            merge_strategy=merge_strategy
        )

        return {
            "success": True,
            "data": result,
            "message": result["message"]
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"清理失败: {str(e)}")

@router.get("/quality/assessment")
async def assess_data_quality(
    manager: UnifiedAssetManager = Depends(get_asset_manager)
):
    """数据质量评估"""
    try:
        assessment = await manager.data_quality_assessment()

        return {
            "success": True,
            "data": assessment,
            "message": "数据质量评估完成"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"评估失败: {str(e)}")

@router.post("/quality/enhance")
async def enhance_data_quality(
    fix_missing_fields: bool = Query(True, description="修复缺失字段"),
    normalize_values: bool = Query(True, description="标准化值"),
    validate_formats: bool = Query(True, description="格式验证"),
    manager: UnifiedAssetManager = Depends(get_asset_manager)
):
    """提升数据质量"""
    try:
        result = await manager.enhance_data_quality(
            fix_missing_fields=fix_missing_fields,
            normalize_values=normalize_values,
            validate_formats=validate_formats
        )

        return {
            "success": True,
            "data": result,
            "message": result["message"]
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"质量提升失败: {str(e)}")

@router.get("/export")
async def export_assets(
    format: str = Query("json", description="导出格式: json, csv"),
    query: str = Query("*"),
    filters: Optional[str] = Query(None, description="JSON格式的过滤条件"),
    manager: UnifiedAssetManager = Depends(get_asset_manager)
):
    """导出资产数据"""
    try:
        # 解析过滤条件
        filter_dict = {}
        if filters:
            filter_dict = json.loads(filters)
        
        # 搜索资产（大批量）
        result = await manager.advanced_search(
            query=query,
            filters=filter_dict,
            page=1,
            size=10000,  # 大批量导出
            aggregations=None
        )
        
        assets = result["hits"]
        
        if format.lower() == "csv":
            # 生成CSV
            if not assets:
                raise HTTPException(status_code=404, detail="没有数据可导出")
            
            output = io.StringIO()
            fieldnames = assets[0].keys()
            writer = csv.DictWriter(output, fieldnames=fieldnames)
            writer.writeheader()
            
            for asset in assets:
                # 处理复杂字段
                row = {}
                for key, value in asset.items():
                    if isinstance(value, (list, dict)):
                        row[key] = json.dumps(value, ensure_ascii=False)
                    else:
                        row[key] = value
                writer.writerow(row)
            
            csv_content = output.getvalue()
            output.close()
            
            return JSONResponse(
                content={"data": csv_content, "total": len(assets)},
                headers={"Content-Type": "text/csv"}
            )
        else:
            # 返回JSON
            return {
                "success": True,
                "data": {
                    "assets": assets,
                    "total": result["total"],
                    "exported_count": len(assets)
                },
                "message": f"导出 {len(assets)} 个资产"
            }
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"导出失败: {str(e)}")

@router.get("/admin/index/health")
async def get_index_health(
    manager: UnifiedAssetManager = Depends(get_asset_manager)
):
    """获取索引健康状态"""
    try:
        if not manager.index_manager:
            raise HTTPException(status_code=500, detail="索引管理器未初始化")

        health = await manager.index_manager.get_index_health()

        return {
            "success": True,
            "data": health,
            "message": "索引健康状态获取成功"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取索引健康状态失败: {str(e)}")

@router.post("/admin/index/optimize")
async def optimize_indices(
    manager: UnifiedAssetManager = Depends(get_asset_manager)
):
    """优化索引性能"""
    try:
        if not manager.index_manager:
            raise HTTPException(status_code=500, detail="索引管理器未初始化")

        result = await manager.index_manager.optimize_indices()

        return {
            "success": True,
            "data": result,
            "message": "索引优化已启动"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"索引优化失败: {str(e)}")

@router.delete("/admin/index/cleanup")
async def cleanup_old_indices(
    retention_days: int = Query(90, description="保留天数"),
    manager: UnifiedAssetManager = Depends(get_asset_manager)
):
    """清理旧索引"""
    try:
        if not manager.index_manager:
            raise HTTPException(status_code=500, detail="索引管理器未初始化")

        result = await manager.index_manager.cleanup_old_indices(retention_days)

        return {
            "success": True,
            "data": result,
            "message": result["message"]
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"清理旧索引失败: {str(e)}")

@router.post("/admin/index/create-monthly")
async def create_monthly_index(
    date: Optional[str] = Query(None, description="日期格式: YYYY-MM"),
    manager: UnifiedAssetManager = Depends(get_asset_manager)
):
    """创建月度索引"""
    try:
        if not manager.index_manager:
            raise HTTPException(status_code=500, detail="索引管理器未初始化")

        target_date = None
        if date:
            try:
                target_date = datetime.strptime(date, "%Y-%m")
            except ValueError:
                raise HTTPException(status_code=400, detail="日期格式错误，请使用 YYYY-MM")

        index_name = await manager.index_manager.create_monthly_index(target_date)

        return {
            "success": True,
            "data": {"index_name": index_name},
            "message": f"月度索引创建成功: {index_name}"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建月度索引失败: {str(e)}")

@router.get("/kibana/setup")
async def get_kibana_setup():
    """获取Kibana设置说明"""
    try:
        es_client = await get_es_client()
        kibana = await create_kibana_integration(es_client)

        setup_info = await kibana.get_kibana_setup_instructions()

        return {
            "success": True,
            "data": setup_info,
            "message": "Kibana设置说明获取成功"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取Kibana设置说明失败: {str(e)}")

@router.get("/kibana/index-pattern")
async def get_kibana_index_pattern():
    """获取Kibana索引模式配置"""
    try:
        es_client = await get_es_client()
        kibana = await create_kibana_integration(es_client)

        index_pattern = await kibana.create_index_pattern()

        return {
            "success": True,
            "data": index_pattern,
            "message": "索引模式配置获取成功"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取索引模式配置失败: {str(e)}")

@router.get("/kibana/visualizations")
async def get_kibana_visualizations():
    """获取Kibana可视化配置"""
    try:
        es_client = await get_es_client()
        kibana = await create_kibana_integration(es_client)

        visualizations = kibana.get_visualization_configs()

        return {
            "success": True,
            "data": {
                "visualizations": visualizations,
                "count": len(visualizations)
            },
            "message": "可视化配置获取成功"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取可视化配置失败: {str(e)}")

@router.get("/kibana/dashboard/{dashboard_name}")
async def get_kibana_dashboard(dashboard_name: str):
    """获取Kibana仪表板配置"""
    try:
        es_client = await get_es_client()
        kibana = await create_kibana_integration(es_client)

        dashboard_config = kibana.get_dashboard_export_config(dashboard_name)

        if "error" in dashboard_config:
            raise HTTPException(status_code=404, detail=dashboard_config["error"])

        return {
            "success": True,
            "data": dashboard_config,
            "message": f"仪表板 {dashboard_name} 配置获取成功"
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取仪表板配置失败: {str(e)}")

@router.get("/kibana/dashboards")
async def list_kibana_dashboards():
    """列出所有可用的Kibana仪表板"""
    try:
        es_client = await get_es_client()
        kibana = await create_kibana_integration(es_client)

        dashboards = kibana.dashboard_configs

        return {
            "success": True,
            "data": {
                "dashboards": [
                    {
                        "name": name,
                        "title": config["title"],
                        "description": config["description"],
                        "visualization_count": len(config["visualizations"])
                    }
                    for name, config in dashboards.items()
                ],
                "total": len(dashboards)
            },
            "message": "仪表板列表获取成功"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取仪表板列表失败: {str(e)}")

@router.get("/analysis/trends")
async def get_trend_analysis(
    time_range: str = Query("30d", description="时间范围，如: 7d, 30d, 90d"),
    interval: str = Query("1d", description="时间间隔，如: 1h, 1d, 1w"),
    metrics: Optional[str] = Query(None, description="指标列表，逗号分隔"),
    manager: UnifiedAssetManager = Depends(get_asset_manager)
):
    """趋势分析"""
    try:
        metric_list = metrics.split(",") if metrics else None

        trends = await manager.trend_analysis(
            time_range=time_range,
            interval=interval,
            metrics=metric_list
        )

        return {
            "success": True,
            "data": trends,
            "message": "趋势分析完成"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"趋势分析失败: {str(e)}")

@router.get("/analysis/correlation")
async def get_correlation_analysis(
    field1: str = Query(..., description="第一个字段"),
    field2: str = Query(..., description="第二个字段"),
    sample_size: int = Query(1000, description="样本大小"),
    manager: UnifiedAssetManager = Depends(get_asset_manager)
):
    """相关性分析"""
    try:
        correlation = await manager.correlation_analysis(
            field1=field1,
            field2=field2,
            sample_size=sample_size
        )

        return {
            "success": True,
            "data": correlation,
            "message": "相关性分析完成"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"相关性分析失败: {str(e)}")

@router.get("/analysis/anomalies")
async def get_anomaly_detection(
    field: str = Query("data_quality_score", description="分析字段"),
    threshold_std: float = Query(2.0, description="标准差阈值"),
    manager: UnifiedAssetManager = Depends(get_asset_manager)
):
    """异常检测"""
    try:
        anomalies = await manager.anomaly_detection(
            field=field,
            threshold_std=threshold_std
        )

        return {
            "success": True,
            "data": anomalies,
            "message": f"检测到 {anomalies.get('anomaly_count', 0)} 个异常"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"异常检测失败: {str(e)}")

@router.get("/analysis/security-risk")
async def get_security_risk_assessment(
    manager: UnifiedAssetManager = Depends(get_asset_manager)
):
    """安全风险评估"""
    try:
        risk_assessment = await manager.security_risk_assessment()

        return {
            "success": True,
            "data": risk_assessment,
            "message": f"风险等级: {risk_assessment.get('overall_risk', {}).get('level', 'unknown')}"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"安全风险评估失败: {str(e)}")
