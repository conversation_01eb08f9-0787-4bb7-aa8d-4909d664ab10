"""
资产管理 V2.0 API路由
基于Elasticsearch的统一资产管理接口
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query, Form, UploadFile, File
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Dict, Any, Optional
import json
from datetime import datetime

from database import get_db
from auth import get_current_user, get_current_admin_user
from models_dynamic import User
from asset_management_v2 import AssetManagerV2, DataSource, create_asset_manager

router = APIRouter(prefix="/assets-v2", tags=["assets-v2"])

@router.post("/import")
async def import_assets_v2(
    file: UploadFile = File(...),
    mappings: str = Form(...),
    platform_id: Optional[str] = Form(None),
    project_id: Optional[str] = Form(None),
    auto_clean: bool = Form(True),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    V2.0 资产导入接口 - 使用ES为主的架构
    """
    try:
        # 解析文件和映射
        content = await file.read()
        field_mappings = json.loads(mappings)
        
        # 解析数据
        if file.filename.lower().endswith('.csv'):
            raw_data = await _parse_csv_content(content)
        else:
            raw_data = await _parse_json_content(content)
        
        if not raw_data:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="没有有效的数据可导入"
            )
        
        # 应用字段映射
        mapped_data = []
        for record in raw_data:
            # 如果数据已经标准化，直接使用
            if 'asset_value' in record and 'asset_type' in record:
                mapped_record = record.copy()
            else:
                # 否则应用映射
                mapped_record = {}
                for mapping in field_mappings:
                    source_field = mapping.get("source_field")
                    target_field = mapping.get("target_field")
                    if source_field and target_field and source_field in record:
                        mapped_record[target_field] = record[source_field]
            
            # 添加导入元数据
            mapped_record.update({
                "import_source": "file_upload",
                "import_filename": file.filename,
                "imported_by": current_user.username,
                "discovered_at": datetime.utcnow().isoformat(),  # 添加发现时间
                "last_seen_at": datetime.utcnow().isoformat(),  # 添加最后见到时间
                "source_task_type": "data_import"  # 添加来源类型
            })
            
            # 确保必要字段存在
            if not mapped_record.get('asset_value'):
                continue
                
            if 'asset_type' not in mapped_record:
                mapped_record['asset_type'] = 'unknown'
            if 'status' not in mapped_record:
                mapped_record['status'] = 'active'
            if 'confidence' not in mapped_record:
                mapped_record['confidence'] = 'medium'
            
            mapped_data.append(mapped_record)
        
        # 使用V2.0资产管理器处理
        manager = await create_asset_manager(db)
        
        result = await manager.process_assets(
            raw_assets=mapped_data,
            platform_id=platform_id,
            project_id=project_id,
            source=DataSource.IMPORT,
            auto_clean=auto_clean
        )
        
        return {
            "status": "success",
            "message": f"成功导入 {result.success_count} 个资产",
            "details": {
                "total_processed": result.total_processed,
                "success_count": result.success_count,
                "duplicate_count": result.duplicate_count,
                "error_count": result.error_count,
                "errors": result.errors[:5]  # 只返回前5个错误
            }
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"导入失败: {str(e)}"
        )

@router.get("/search")
async def search_assets_v2(
    q: str = Query("*", description="搜索查询"),
    platform_id: Optional[str] = Query(None),
    project_id: Optional[str] = Query(None),
    asset_type: Optional[str] = Query(None),
    confidence: Optional[str] = Query(None),
    status: Optional[str] = Query(None),
    tags: Optional[str] = Query(None),
    sort_by: str = Query("discovered_at"),
    sort_order: str = Query("desc"),
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    include_aggs: bool = Query(False),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    V2.0 统一资产搜索接口 - 基于Elasticsearch
    """
    try:
        # 构建过滤条件
        filters = {}
        if platform_id:
            filters["platform_id"] = platform_id
        if project_id:
            filters["project_id"] = project_id
        if asset_type:
            filters["asset_type"] = asset_type
        if confidence:
            filters["confidence"] = confidence
        if status:
            filters["status"] = status
        if tags:
            # 支持多个标签，逗号分隔
            tag_list = [tag.strip() for tag in tags.split(",") if tag.strip()]
            if tag_list:
                filters["tags"] = tag_list
        
        # 使用V2.0资产管理器搜索
        manager = await create_asset_manager(db)
        
        result = await manager.search_assets(
            query=q,
            filters=filters,
            sort_by=sort_by,
            sort_order=sort_order,
            page=page,
            size=size,
            include_aggregations=include_aggs
        )
        
        return result
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"搜索失败: {str(e)}"
        )

@router.get("/stats")
async def get_asset_stats_v2(
    platform_id: Optional[str] = Query(None),
    project_id: Optional[str] = Query(None),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取资产统计信息
    """
    try:
        manager = await create_asset_manager(db)
        
        # 构建统计查询的过滤条件
        filters = {}
        if platform_id:
            filters["platform_id"] = platform_id
        if project_id:
            filters["project_id"] = project_id
        
        # 执行聚合查询
        result = await manager.search_assets(
            query="*",
            filters=filters,
            size=0,  # 不返回具体资产，只要聚合结果
            include_aggregations=True
        )
        
        # 处理聚合结果
        aggs = result.get("aggregations", {})
        
        stats = {
            "total_assets": result["total"],
            "asset_types": _process_terms_agg(aggs.get("asset_types", {})),
            "platforms": _process_terms_agg(aggs.get("platforms", {})),
            "projects": _process_terms_agg(aggs.get("projects", {})),
            "confidence_levels": _process_terms_agg(aggs.get("confidence_levels", {})),
            "discovery_timeline": _process_date_histogram_agg(aggs.get("discovery_timeline", {}))
        }
        
        return stats
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取统计失败: {str(e)}"
        )

@router.get("/platforms/{platform_id}/assets")
async def get_platform_assets(
    platform_id: str,
    page: int = Query(1, ge=1),
    size: int = Query(50, ge=1, le=200),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取特定平台的资产
    """
    try:
        manager = await create_asset_manager(db)
        
        result = await manager.search_assets(
            query="*",
            filters={"platform_id": platform_id},
            page=page,
            size=size,
            include_aggregations=True
        )
        
        return result
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取平台资产失败: {str(e)}"
        )

@router.get("/projects/{project_id}/assets")
async def get_project_assets(
    project_id: str,
    page: int = Query(1, ge=1),
    size: int = Query(50, ge=1, le=200),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取特定项目的资产
    """
    try:
        manager = await create_asset_manager(db)
        
        result = await manager.search_assets(
            query="*",
            filters={"project_id": project_id},
            page=page,
            size=size,
            include_aggregations=True
        )
        
        return result
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取项目资产失败: {str(e)}"
        )

@router.post("/bulk-update")
async def bulk_update_assets(
    updates: List[Dict[str, Any]],
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """
    批量更新资产
    """
    try:
        manager = await create_asset_manager(db)
        
        # TODO: 实现批量更新逻辑
        # 这里需要实现ES的批量更新操作
        
        return {
            "status": "success",
            "message": f"批量更新了 {len(updates)} 个资产"
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"批量更新失败: {str(e)}"
        )

@router.delete("/cleanup")
async def cleanup_duplicate_assets(
    dry_run: bool = Query(True, description="是否为预演模式"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """
    清理重复资产
    """
    try:
        manager = await create_asset_manager(db)
        
        # 查找重复资产
        duplicate_query = {
            "query": {"term": {"is_duplicate": True}},
            "size": 1000
        }
        
        response = await manager.es_client.search(
            index="assets-*",
            body=duplicate_query
        )
        
        duplicates = response["hits"]["hits"]
        
        if not dry_run and duplicates:
            # 删除重复资产
            delete_actions = []
            for hit in duplicates:
                delete_actions.append({
                    "_op_type": "delete",
                    "_index": hit["_index"],
                    "_id": hit["_id"]
                })
            
            from elasticsearch.helpers import async_bulk
            await async_bulk(manager.es_client, delete_actions)
        
        return {
            "status": "success",
            "message": f"{'找到' if dry_run else '删除了'} {len(duplicates)} 个重复资产",
            "duplicate_count": len(duplicates),
            "dry_run": dry_run
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"清理失败: {str(e)}"
        )

# 辅助函数

async def _parse_csv_content(content: bytes) -> List[Dict[str, Any]]:
    """解析CSV内容"""
    import csv
    import io
    
    text_content = content.decode('utf-8')
    csv_reader = csv.DictReader(io.StringIO(text_content))
    
    # 标准化字段名
    rows = []
    for row in csv_reader:
        normalized_row = {}
        for key, value in row.items():
            if not value:  # 跳过空值
                continue
                
            # 移除BOM标记和空格，转小写
            clean_key = key.strip().lower().replace('\ufeff', '')
            
            # 标准化常见字段名
            if clean_key in ['domain', 'subdomain', 'host']:
                normalized_row['asset_value'] = value.strip()
                normalized_row['asset_type'] = clean_key
            elif clean_key == 'ip':
                normalized_row['asset_value'] = value.strip()
                normalized_row['asset_type'] = 'ip'
            elif clean_key == 'url':
                normalized_row['asset_value'] = value.strip()
                normalized_row['asset_type'] = 'url'
            elif clean_key == 'type':
                normalized_row['asset_type'] = value.strip().lower()
            else:
                normalized_row[clean_key] = value.strip()
        
        # 确保必要字段存在
        if not normalized_row.get('asset_value'):
            continue  # 跳过没有资产值的行
            
        if 'asset_type' not in normalized_row:
            normalized_row['asset_type'] = 'unknown'
        if 'status' not in normalized_row:
            normalized_row['status'] = 'active'
        if 'confidence' not in normalized_row:
            normalized_row['confidence'] = 'medium'
        
        rows.append(normalized_row)
    
    return rows

async def _parse_json_content(content: bytes) -> List[Dict[str, Any]]:
    """解析JSON内容"""
    text_content = content.decode('utf-8')
    data = json.loads(text_content)
    
    if isinstance(data, list):
        return data
    elif isinstance(data, dict):
        return [data]
    else:
        return []

def _process_terms_agg(agg_result: Dict[str, Any]) -> List[Dict[str, Any]]:
    """处理terms聚合结果"""
    buckets = agg_result.get("buckets", [])
    return [
        {"key": bucket["key"], "count": bucket["doc_count"]}
        for bucket in buckets
    ]

def _process_date_histogram_agg(agg_result: Dict[str, Any]) -> List[Dict[str, Any]]:
    """处理日期直方图聚合结果"""
    buckets = agg_result.get("buckets", [])
    return [
        {"date": bucket["key_as_string"], "count": bucket["doc_count"]}
        for bucket in buckets
    ] 