"""
自定义仪表板API路由
提供仪表板数据查询、组件配置管理等功能
"""

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional, Dict, Any
from datetime import datetime
import json

from database import get_db
from models_dynamic import User
from auth import get_current_user
from elasticsearch_client import get_es_client
from dashboard_service import DashboardDataService, WIDGET_TEMPLATES

router = APIRouter(prefix="/dashboard", tags=["dashboard"])

async def get_dashboard_service():
    """获取仪表板数据服务实例"""
    es_client = await get_es_client()
    return DashboardDataService(es_client)

@router.get("/overview")
async def get_dashboard_overview(
    current_user: User = Depends(get_current_user),
    dashboard_service: DashboardDataService = Depends(get_dashboard_service)
):
    """
    获取仪表板概览数据
    提供资产统计、分布和趋势等关键指标
    """
    try:
        overview_data = await dashboard_service.get_asset_overview()
        return {
            "status": "success",
            "data": overview_data,
            "generated_at": datetime.now().isoformat()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取概览数据失败: {str(e)}")

@router.post("/widget/data")
async def get_widget_data(
    widget_config: Dict[str, Any],
    current_user: User = Depends(get_current_user),
    dashboard_service: DashboardDataService = Depends(get_dashboard_service)
):
    """
    获取组件数据
    根据组件配置执行相应的ES查询并返回格式化数据
    """
    try:
        result = await dashboard_service.execute_widget_query(widget_config)
        return {
            "status": "success",
            "data": result,
            "config": widget_config,
            "generated_at": datetime.now().isoformat()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取组件数据失败: {str(e)}")

@router.get("/widget/templates")
async def get_widget_templates(
    current_user: User = Depends(get_current_user)
):
    """
    获取预定义的组件配置模板
    用户可以基于这些模板快速创建仪表板组件
    """
    return {
        "status": "success",
        "templates": WIDGET_TEMPLATES,
        "categories": {
            "基础统计": ["total_assets_count", "asset_type_pie"],
            "分布分析": ["confidence_distribution", "platform_distribution"],
            "时间趋势": ["discovery_timeline"]
        }
    }

@router.get("/risk-analysis")
async def get_risk_analysis(
    current_user: User = Depends(get_current_user),
    dashboard_service: DashboardDataService = Depends(get_dashboard_service)
):
    """
    获取风险分析数据
    提供资产风险评估和安全态势分析
    """
    try:
        risk_data = await dashboard_service.get_risk_analysis()
        return {
            "status": "success",
            "data": risk_data,
            "generated_at": datetime.now().isoformat()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取风险分析数据失败: {str(e)}")

@router.post("/query/custom")
async def execute_custom_query(
    query_config: Dict[str, Any],
    current_user: User = Depends(get_current_user),
    dashboard_service: DashboardDataService = Depends(get_dashboard_service)
):
    """
    执行自定义ES查询
    允许高级用户直接提交ES查询DSL
    """
    try:
        # 验证用户权限（可选：只允许管理员执行自定义查询）
        if not current_user.is_admin:
            raise HTTPException(status_code=403, detail="需要管理员权限执行自定义查询")
        
        result = await dashboard_service._execute_custom_query(query_config)
        return {
            "status": "success",
            "data": result,
            "query": query_config,
            "generated_at": datetime.now().isoformat()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"执行自定义查询失败: {str(e)}")

@router.get("/aggregations/fields")
async def get_aggregatable_fields(
    current_user: User = Depends(get_current_user),
    dashboard_service: DashboardDataService = Depends(get_dashboard_service)
):
    """
    获取可用于聚合的字段列表
    帮助用户了解可以用于创建图表的数据字段
    """
    # 返回常用的可聚合字段
    aggregatable_fields = {
        "categorical": [
            {
                "field": "entity_data.asset_type.keyword",
                "display_name": "资产类型",
                "description": "资产的分类（域名、IP、URL等）"
            },
            {
                "field": "entity_data.confidence.keyword", 
                "display_name": "置信度",
                "description": "资产发现的置信度级别"
            },
            {
                "field": "entity_data.status.keyword",
                "display_name": "状态",
                "description": "资产的当前状态"
            },
            {
                "field": "entity_data.platform_id.keyword",
                "display_name": "平台",
                "description": "资产所属的平台"
            },
            {
                "field": "entity_data.project_id.keyword",
                "display_name": "项目",
                "description": "资产所属的项目"
            },
            {
                "field": "entity_data.source_task_type.keyword",
                "display_name": "发现来源",
                "description": "发现该资产的工具或方法"
            }
        ],
        "temporal": [
            {
                "field": "entity_data.discovered_at",
                "display_name": "发现时间", 
                "description": "资产被发现的时间"
            },
            {
                "field": "created_at",
                "display_name": "创建时间",
                "description": "记录创建时间"
            },
            {
                "field": "updated_at",
                "display_name": "更新时间",
                "description": "记录最后更新时间"
            }
        ],
        "textual": [
            {
                "field": "entity_data.asset_value",
                "display_name": "资产值",
                "description": "资产的具体值（域名、IP地址等）"
            },
            {
                "field": "entity_data.asset_host",
                "display_name": "资产主机",
                "description": "资产对应的主机"
            }
        ]
    }
    
    return {
        "status": "success",
        "fields": aggregatable_fields,
        "total_fields": sum(len(fields) for fields in aggregatable_fields.values())
    }

@router.get("/health")
async def dashboard_health_check(
    current_user: User = Depends(get_current_user),
    dashboard_service: DashboardDataService = Depends(get_dashboard_service)
):
    """
    仪表板服务健康检查
    验证ES连接和基础数据可用性
    """
    try:
        # 执行简单的ES查询测试连接
        test_query = {
            "size": 0,
            "query": {"match_all": {}},
            "aggs": {
                "total_count": {
                    "value_count": {
                        "field": "entity_data.asset_value.keyword"
                    }
                }
            }
        }
        
        response = await dashboard_service.es_client.search(
            index=dashboard_service.index_pattern,
            body=test_query
        )
        
        total_assets = response["hits"]["total"]["value"]
        
        return {
            "status": "healthy",
            "elasticsearch_connected": True,
            "index_pattern": dashboard_service.index_pattern,
            "total_assets": total_assets,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        return {
            "status": "unhealthy",
            "elasticsearch_connected": False,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }