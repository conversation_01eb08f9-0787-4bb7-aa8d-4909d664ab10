"""
增强的资产搜索API路由
集成高级搜索服务和数据处理功能
"""

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional, Dict, Any
from datetime import datetime
import json

from database import get_db
from models_dynamic import User
from auth import get_current_user
from elasticsearch_client import get_es_client
from advanced_search_service import AdvancedAssetSearchService, AssetDataProcessor, SearchQuery
from query_builder import QueryBuilder, AdvancedQueryTemplates, QueryValidator
from schemas_enhanced import AdvancedSearchRequest, AdvancedSearchResponse

router = APIRouter(prefix="/enhanced-search", tags=["enhanced-search"])

async def get_search_service():
    """获取搜索服务实例"""
    es_client = await get_es_client()
    return AdvancedAssetSearchService(es_client)

async def get_data_processor():
    """获取数据处理器实例"""
    search_service = await get_search_service()
    return AssetDataProcessor(search_service)

@router.post("/assets", response_model=AdvancedSearchResponse)
async def advanced_asset_search(
    request: AdvancedSearchRequest,
    current_user: User = Depends(get_current_user),
    search_service: AdvancedAssetSearchService = Depends(get_search_service)
):
    """
    高级资产搜索
    支持复杂查询、多维度过滤、智能聚合
    """
    try:
        # 构建搜索查询
        search_query = SearchQuery(
            query=request.query or "",
            filters=request.filters or {},
            platform_id=request.platform_id,
            project_id=request.project_id,
            asset_types=request.asset_types or [],
            confidence=request.confidence,
            date_from=request.date_from,
            date_to=request.date_to,
            tags=request.tags or [],
            sort_by=request.sort_by or "discovered_at",
            sort_order=request.sort_order or "desc",
            page=request.page or 1,
            size=request.size or 20,
            include_aggregations=request.include_aggregations
        )
        
        # 执行搜索
        result = await search_service.search_assets(search_query)
        
        return AdvancedSearchResponse(
            total=result.total,
            hits=result.hits,
            aggregations=result.aggregations,
            query_time=result.query_time,
            page=search_query.page,
            size=search_query.size,
            suggestions=result.suggestions
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"搜索失败: {str(e)}")

@router.get("/assets/insights")
async def get_asset_insights(
    platform_id: Optional[str] = Query(None),
    project_id: Optional[str] = Query(None),
    current_user: User = Depends(get_current_user),
    search_service: AdvancedAssetSearchService = Depends(get_search_service)
):
    """
    获取资产洞察分析
    提供详细的统计分析和趋势数据
    """
    try:
        insights = await search_service.get_asset_insights(
            platform_id=platform_id,
            project_id=project_id
        )
        
        return {
            "status": "success",
            "data": insights,
            "generated_at": datetime.now().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取洞察失败: {str(e)}")

@router.get("/assets/{asset_id}/similar")
async def get_similar_assets(
    asset_id: str,
    size: int = Query(10, ge=1, le=50),
    current_user: User = Depends(get_current_user),
    search_service: AdvancedAssetSearchService = Depends(get_search_service)
):
    """
    获取相似资产
    基于机器学习算法找到相似的资产
    """
    try:
        similar_assets = await search_service.search_similar_assets(
            asset_id=asset_id,
            size=size
        )
        
        return {
            "status": "success",
            "data": similar_assets,
            "total": len(similar_assets)
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取相似资产失败: {str(e)}")

@router.get("/suggestions")
async def get_search_suggestions(
    query: str = Query(..., min_length=2),
    field: str = Query("asset_value", pattern="^(asset_value|asset_host|tags)$"),
    current_user: User = Depends(get_current_user),
    search_service: AdvancedAssetSearchService = Depends(get_search_service)
):
    """
    获取搜索建议
    提供实时的搜索建议和自动完成
    """
    try:
        suggestions = await search_service.get_search_suggestions(
            query=query,
            field=field
        )
        
        return {
            "status": "success",
            "suggestions": suggestions
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取搜索建议失败: {str(e)}")

@router.post("/assets/bulk-update")
async def bulk_update_assets(
    updates: List[Dict[str, Any]],
    current_user: User = Depends(get_current_user),
    search_service: AdvancedAssetSearchService = Depends(get_search_service)
):
    """
    批量更新资产
    支持批量修改资产属性
    """
    try:
        # 验证用户权限
        if not current_user.is_admin:
            raise HTTPException(status_code=403, detail="需要管理员权限")
        
        result = await search_service.bulk_update_assets(updates)
        
        return {
            "status": "success",
            "data": result
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"批量更新失败: {str(e)}")

@router.post("/assets/process")
async def process_raw_assets(
    raw_assets: List[Dict[str, Any]],
    current_user: User = Depends(get_current_user),
    data_processor: AssetDataProcessor = Depends(get_data_processor)
):
    """
    处理原始资产数据
    清洗、标准化、去重和增强原始资产数据
    """
    try:
        # 验证用户权限
        if not current_user.is_admin:
            raise HTTPException(status_code=403, detail="需要管理员权限")
        
        # 清洗和标准化数据
        cleaned_assets = await data_processor.clean_and_normalize_assets(raw_assets)
        
        # 去重
        unique_assets = await data_processor.deduplicate_assets(cleaned_assets)
        
        return {
            "status": "success",
            "data": {
                "processed_assets": unique_assets,
                "original_count": len(raw_assets),
                "cleaned_count": len(cleaned_assets),
                "unique_count": len(unique_assets),
                "duplicates_removed": len(cleaned_assets) - len(unique_assets)
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"处理资产数据失败: {str(e)}")

@router.get("/platforms/{platform_id}/assets/summary")
async def get_platform_asset_summary(
    platform_id: str,
    current_user: User = Depends(get_current_user),
    search_service: AdvancedAssetSearchService = Depends(get_search_service)
):
    """
    获取平台资产汇总
    按平台维度汇总资产统计信息
    """
    try:
        # 构建平台资产查询
        search_query = SearchQuery(
            platform_id=platform_id,
            size=0,  # 只需要聚合数据
            include_aggregations=True
        )
        
        result = await search_service.search_assets(search_query)
        
        # 获取平台详细洞察
        insights = await search_service.get_asset_insights(platform_id=platform_id)
        
        return {
            "status": "success",
            "data": {
                "platform_id": platform_id,
                "total_assets": result.total,
                "aggregations": result.aggregations,
                "insights": insights,
                "query_time": result.query_time
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取平台资产汇总失败: {str(e)}")

@router.get("/projects/{project_id}/assets/summary")
async def get_project_asset_summary(
    project_id: str,
    current_user: User = Depends(get_current_user),
    search_service: AdvancedAssetSearchService = Depends(get_search_service)
):
    """
    获取项目资产汇总
    按项目维度汇总资产统计信息
    """
    try:
        # 构建项目资产查询
        search_query = SearchQuery(
            project_id=project_id,
            size=0,  # 只需要聚合数据
            include_aggregations=True
        )
        
        result = await search_service.search_assets(search_query)
        
        # 获取项目详细洞察
        insights = await search_service.get_asset_insights(project_id=project_id)
        
        return {
            "status": "success",
            "data": {
                "project_id": project_id,
                "total_assets": result.total,
                "aggregations": result.aggregations,
                "insights": insights,
                "query_time": result.query_time
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取项目资产汇总失败: {str(e)}")

@router.post("/security-analysis", response_model=AdvancedSearchResponse)
async def security_analysis(
    platform_id: Optional[str] = Query(None),
    risk_level: str = Query("high", pattern="^(high|medium|low)$"),
    current_user: User = Depends(get_current_user),
    search_service: AdvancedAssetSearchService = Depends(get_search_service)
):
    """
    高级安全分析
    使用预定义模板进行安全风险评估和威胁识别
    """
    try:
        result = await search_service.advanced_security_analysis(
            platform_id=platform_id,
            risk_level=risk_level
        )
        
        return AdvancedSearchResponse(
            total=result.total,
            hits=result.hits,
            aggregations=result.aggregations,
            query_time=result.query_time,
            page=1,
            size=100
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"安全分析失败: {str(e)}")

@router.post("/query-builder")
async def build_custom_query(
    query_config: Dict[str, Any],
    execute: bool = Query(True, description="是否立即执行查询"),
    current_user: User = Depends(get_current_user),
    search_service: AdvancedAssetSearchService = Depends(get_search_service)
):
    """
    自定义查询构建器
    支持用户构建复杂的Elasticsearch查询
    """
    try:
        # 创建查询构建器
        builder = QueryBuilder()
        
        # 根据配置构建查询
        if "text_search" in query_config:
            config = query_config["text_search"]
            builder.text_search(
                query=config.get("query", ""),
                fields=config.get("fields", []),
                fuzziness=config.get("fuzziness", "AUTO")
            )
        
        if "filters" in query_config:
            for filter_config in query_config["filters"]:
                filter_type = filter_config.get("type")
                field = filter_config.get("field")
                value = filter_config.get("value")
                
                if filter_type == "exact":
                    builder.exact_match(field, value)
                elif filter_type == "range":
                    builder.range_filter(field, **filter_config.get("range", {}))
                elif filter_type == "regex":
                    builder.regex_match(field, value)
        
        if "aggregations" in query_config:
            for agg_config in query_config["aggregations"]:
                agg_type = agg_config.get("type")
                name = agg_config.get("name")
                
                if agg_type == "terms":
                    builder.terms_aggregation(
                        name=name,
                        field=agg_config.get("field"),
                        size=agg_config.get("size", 10)
                    )
                elif agg_type == "date_histogram":
                    builder.date_histogram(
                        name=name,
                        field=agg_config.get("field"),
                        interval=agg_config.get("interval", "1d")
                    )
        
        if "sort" in query_config:
            for sort_config in query_config["sort"]:
                builder.add_sort(
                    field=sort_config.get("field"),
                    order=sort_config.get("order", "desc")
                )
        
        # 构建查询
        size = query_config.get("size", 20)
        from_ = query_config.get("from", 0)
        query = builder.build(size=size, from_=from_)
        
        # 验证查询
        is_valid, error_msg = QueryValidator.validate_query(query)
        if not is_valid:
            raise HTTPException(status_code=400, detail=f"查询验证失败: {error_msg}")
        
        response_data = {
            "status": "success",
            "query": query,
            "query_size": len(str(query))
        }
        
        # 执行查询（可选）
        if execute:
            result = await search_service.es.search(
                index=search_service.index_patterns["assets"],
                body=query
            )
            
            response_data["result"] = {
                "total": result["hits"]["total"]["value"],
                "hits_count": len(result["hits"]["hits"]),
                "took": result["took"],
                "has_aggregations": "aggregations" in result
            }
        
        return response_data
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询构建失败: {str(e)}")

@router.post("/data-pipeline/process")
async def advanced_data_processing(
    raw_assets: List[Dict[str, Any]],
    auto_correlate: bool = Query(True, description="是否进行关联分析"),
    auto_index: bool = Query(True, description="是否自动索引"),
    current_user: User = Depends(get_current_user),
    data_processor: AssetDataProcessor = Depends(get_data_processor)
):
    """
    高级数据处理管道
    集成清洗、去重、关联分析和自动索引
    """
    try:
        # 验证用户权限
        if not current_user.is_admin:
            raise HTTPException(status_code=403, detail="需要管理员权限")
        
        if auto_index:
            # 完整处理流程
            result = await data_processor.process_and_index_assets(
                raw_assets=raw_assets,
                auto_correlate=auto_correlate
            )
        else:
            # 仅处理不索引
            cleaned_assets = await data_processor.clean_and_normalize_assets(raw_assets)
            unique_assets = await data_processor.deduplicate_assets(cleaned_assets)
            
            correlations = {}
            if auto_correlate:
                correlations = await data_processor.correlate_assets(unique_assets)
            
            result = {
                "status": "success",
                "processing_summary": {
                    "original_count": len(raw_assets),
                    "cleaned_count": len(cleaned_assets),
                    "unique_count": len(unique_assets),
                    "duplicates_removed": len(cleaned_assets) - len(unique_assets)
                },
                "correlations": correlations,
                "processed_assets": unique_assets
            }
        
        return result
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"数据处理失败: {str(e)}")

@router.get("/intelligence/correlations")
async def get_asset_correlations(
    platform_id: Optional[str] = Query(None),
    project_id: Optional[str] = Query(None),
    correlation_type: str = Query("all", pattern="^(all|domain|ip|technology|network)$"),
    current_user: User = Depends(get_current_user),
    search_service: AdvancedAssetSearchService = Depends(get_search_service)
):
    """
    获取资产关联情报
    分析资产间的关系和依赖
    """
    try:
        # 构建查询获取相关资产
        search_query = SearchQuery(
            platform_id=platform_id,
            project_id=project_id,
            size=1000,  # 获取更多数据用于关联分析
            include_aggregations=False
        )
        
        result = await search_service.search_assets(search_query)
        
        # 提取资产数据
        assets = [hit["source"]["entity_data"] for hit in result.hits]
        
        # 进行关联分析
        data_processor = AssetDataProcessor(search_service)
        correlations = await data_processor.correlate_assets(assets)
        
        # 根据类型过滤结果
        if correlation_type != "all":
            filtered_correlations = {}
            if correlation_type == "domain" and "domain_groups" in correlations:
                filtered_correlations["domain_groups"] = correlations["domain_groups"]
            elif correlation_type == "ip" and "ip_clusters" in correlations:
                filtered_correlations["ip_clusters"] = correlations["ip_clusters"]
            elif correlation_type == "technology" and "technology_stacks" in correlations:
                filtered_correlations["technology_stacks"] = correlations["technology_stacks"]
            elif correlation_type == "network" and "network_relationships" in correlations:
                filtered_correlations["network_relationships"] = correlations["network_relationships"]
            
            correlations = filtered_correlations
        
        return {
            "status": "success",
            "data": {
                "correlation_type": correlation_type,
                "total_assets_analyzed": len(assets),
                "correlations": correlations,
                "analysis_scope": {
                    "platform_id": platform_id,
                    "project_id": project_id
                }
            },
            "generated_at": datetime.now().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"关联分析失败: {str(e)}")

@router.get("/intelligence/risk-assessment")
async def comprehensive_risk_assessment(
    platform_id: Optional[str] = Query(None),
    project_id: Optional[str] = Query(None),
    include_remediation: bool = Query(True, description="是否包含修复建议"),
    current_user: User = Depends(get_current_user),
    search_service: AdvancedAssetSearchService = Depends(get_search_service)
):
    """
    综合风险评估
    结合安全分析和威胁情报的全面风险评估
    """
    try:
        # 高风险资产分析
        high_risk_result = await search_service.advanced_security_analysis(
            platform_id=platform_id,
            risk_level="high"
        )
        
        # 中等风险资产分析
        medium_risk_result = await search_service.advanced_security_analysis(
            platform_id=platform_id,
            risk_level="medium"
        )
        
        # 获取资产洞察
        insights = await search_service.get_asset_insights(
            platform_id=platform_id,
            project_id=project_id
        )
        
        # 计算风险评分
        total_assets = high_risk_result.total + medium_risk_result.total
        high_risk_count = high_risk_result.total
        medium_risk_count = medium_risk_result.total
        
        risk_score = 0
        if total_assets > 0:
            risk_score = (high_risk_count * 0.8 + medium_risk_count * 0.4) / total_assets
        
        risk_level = "critical" if risk_score > 0.7 else "high" if risk_score > 0.4 else "medium" if risk_score > 0.2 else "low"
        
        assessment = {
            "overall_risk_level": risk_level,
            "risk_score": round(risk_score, 3),
            "asset_statistics": {
                "total_assets": total_assets,
                "high_risk_assets": high_risk_count,
                "medium_risk_assets": medium_risk_count,
                "risk_distribution": {
                    "high": round(high_risk_count / total_assets * 100, 1) if total_assets > 0 else 0,
                    "medium": round(medium_risk_count / total_assets * 100, 1) if total_assets > 0 else 0
                }
            },
            "threat_vectors": {
                "exposed_admin_interfaces": len([hit for hit in high_risk_result.hits if "admin" in hit["source"]["entity_data"].get("asset_value", "")]),
                "api_endpoints": len([hit for hit in high_risk_result.hits if "api" in hit["source"]["entity_data"].get("asset_value", "")]),
                "development_assets": len([hit for hit in medium_risk_result.hits if any(env in hit["source"]["entity_data"].get("asset_value", "") for env in ["dev", "test", "staging"])])
            },
            "insights": insights
        }
        
        # 添加修复建议
        if include_remediation:
            assessment["remediation_recommendations"] = {
                "immediate_actions": [
                    "Review and secure admin interfaces",
                    "Implement API authentication and rate limiting",
                    "Remove or secure development/test environments"
                ],
                "monitoring_recommendations": [
                    "Set up continuous asset discovery",
                    "Implement security monitoring for high-risk assets",
                    "Regular vulnerability assessments"
                ],
                "governance_actions": [
                    "Establish asset management policies",
                    "Define security baselines for different asset types",
                    "Implement change management processes"
                ]
            }
        
        return {
            "status": "success",
            "assessment": assessment,
            "generated_at": datetime.now().isoformat(),
            "scope": {
                "platform_id": platform_id,
                "project_id": project_id
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"风险评估失败: {str(e)}")

@router.get("/processing/statistics")
async def get_processing_statistics(
    current_user: User = Depends(get_current_user),
    data_processor: AssetDataProcessor = Depends(get_data_processor)
):
    """
    获取数据处理统计信息
    监控数据处理管道的健康状态和性能指标
    """
    try:
        stats = await data_processor.get_processing_statistics()
        
        return {
            "status": "success",
            "statistics": stats,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")
async def export_assets(
    request: AdvancedSearchRequest,
    export_format: str = Query("json", pattern="^(json|csv|xlsx)$"),
    current_user: User = Depends(get_current_user),
    search_service: AdvancedAssetSearchService = Depends(get_search_service)
):
    """
    导出资产数据
    支持多种格式的资产数据导出
    """
    try:
        # 构建搜索查询（导出时不限制数量）
        search_query = SearchQuery(
            query=request.query or "",
            filters=request.filters or {},
            platform_id=request.platform_id,
            project_id=request.project_id,
            asset_types=request.asset_types or [],
            confidence=request.confidence,
            date_from=request.date_from,
            date_to=request.date_to,
            tags=request.tags or [],
            sort_by=request.sort_by or "discovered_at",
            sort_order=request.sort_order or "desc",
            page=1,
            size=10000,  # 导出限制
            include_aggregations=False
        )
        
        result = await search_service.search_assets(search_query)
        
        # 处理导出数据
        export_data = []
        for hit in result.hits:
            asset_data = hit["source"]["entity_data"]
            export_data.append({
                "platform_id": asset_data.get("platform_id"),
                "project_id": asset_data.get("project_id"),
                "asset_type": asset_data.get("asset_type"),
                "asset_value": asset_data.get("asset_value"),
                "asset_host": asset_data.get("asset_host"),
                "confidence": asset_data.get("confidence"),
                "status": asset_data.get("status"),
                "discovered_at": asset_data.get("discovered_at"),
                "tags": asset_data.get("tags", [])
            })
        
        return {
            "status": "success",
            "data": {
                "format": export_format,
                "total_exported": len(export_data),
                "assets": export_data,
                "exported_at": datetime.now().isoformat()
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"导出资产失败: {str(e)}")

@router.post("/maintenance/reindex")
async def reindex_assets(
    current_user: User = Depends(get_current_user),
    search_service: AdvancedAssetSearchService = Depends(get_search_service)
):
    """
    重建索引
    重新创建和配置Elasticsearch索引
    """
    try:
        # 验证管理员权限
        if not current_user.is_admin:
            raise HTTPException(status_code=403, detail="需要管理员权限")
        
        # 创建索引模板
        await search_service.create_asset_index_template()
        
        return {
            "status": "success",
            "message": "索引模板创建成功",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"重建索引失败: {str(e)}")

@router.get("/health")
async def search_health_check(
    search_service: AdvancedAssetSearchService = Depends(get_search_service)
):
    """
    搜索服务健康检查
    检查Elasticsearch连接和索引状态
    """
    try:
        # 检查ES连接
        cluster_health = await search_service.es.cluster.health()
        
        # 检查索引存在性
        indices_exist = await search_service.es.indices.exists(
            index=search_service.index_patterns["assets"]
        )
        
        return {
            "status": "healthy",
            "elasticsearch": {
                "cluster_status": cluster_health["status"],
                "active_shards": cluster_health["active_shards"],
                "indices_exist": indices_exist
            },
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }