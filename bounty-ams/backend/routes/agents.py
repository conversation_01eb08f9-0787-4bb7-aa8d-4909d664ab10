from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, func, delete
from typing import List, Optional
from datetime import datetime, timedelta, timezone
import json
import uuid

from database import get_db
from models_dynamic import Agent, Task, WorkflowExecution, User
from schemas_agent import (
    AgentRegisterRequest, AgentHeartbeatRequest, AgentInfo, 
    TaskDefinition, TaskResult, TaskInfo, TaskPollResponse,
    WorkflowExecution as WorkflowExecutionSchema, WorkflowStatus,
    SystemStats, AgentStatus, TaskStatus, ErrorResponse
)
from schemas_agent_key import (
    AgentRegisterWithKeyRequest, AgentHeartbeatWithKeyRequest
)
from auth import get_current_user, require_admin
from agent_architecture import WORKFLOW_TEMPLATES, AgentCommunicationProtocol
from agent_key_service import AgentKeyService

router = APIRouter()

@router.post("/register", response_model=AgentInfo)
async def register_agent(
    agent_data: AgentRegisterRequest,
    db: AsyncSession = Depends(get_db)
):
    """Agent注册"""
    # 检查是否已经注册
    result = await db.execute(
        select(Agent).where(Agent.agent_id == agent_data.agent_id)
    )
    existing_agent = result.scalar_one_or_none()
    
    if existing_agent:
        # 更新现有Agent
        await db.execute(
            update(Agent)
            .where(Agent.agent_id == agent_data.agent_id)
            .values(
                name=agent_data.name,
                version=agent_data.version,
                capabilities=agent_data.capabilities,
                max_concurrent_tasks=agent_data.max_concurrent_tasks,
                hostname=agent_data.hostname,
                ip_address=agent_data.ip_address,
                agent_metadata=agent_data.metadata,
                status="online",
                last_seen_at=datetime.now(timezone.utc),
                updated_at=datetime.now(timezone.utc)
            )
        )
        await db.commit()
        
        # 重新获取更新后的Agent
        result = await db.execute(
            select(Agent).where(Agent.agent_id == agent_data.agent_id)
        )
        agent = result.scalar_one()
    else:
        # 创建新Agent
        agent = Agent(
            agent_id=agent_data.agent_id,
            name=agent_data.name,
            version=agent_data.version,
            capabilities=agent_data.capabilities,
            max_concurrent_tasks=agent_data.max_concurrent_tasks,
            hostname=agent_data.hostname,
            ip_address=agent_data.ip_address,
            agent_metadata=agent_data.metadata,
            status="online",
            last_seen_at=datetime.now(timezone.utc)
        )
        db.add(agent)
        await db.commit()
        await db.refresh(agent)
    
    return agent

@router.post("/heartbeat")
async def agent_heartbeat(
    heartbeat_data: AgentHeartbeatRequest,
    db: AsyncSession = Depends(get_db)
):
    """Agent心跳"""
    result = await db.execute(
        select(Agent).where(Agent.agent_id == heartbeat_data.agent_id)
    )
    agent = result.scalar_one_or_none()
    
    if not agent:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Agent not found"
        )
    
    # 更新Agent状态
    await db.execute(
        update(Agent)
        .where(Agent.agent_id == heartbeat_data.agent_id)
        .values(
            status=heartbeat_data.status,
            current_tasks=heartbeat_data.current_tasks,
            last_seen_at=datetime.now(timezone.utc),
            agent_metadata=heartbeat_data.system_info
        )
    )
    await db.commit()
    
    return {"message": "Heartbeat received", "status": "ok"}

@router.get("/tasks/poll", response_model=TaskPollResponse)
async def poll_tasks(
    agent_id: str,
    capabilities: str,  # comma-separated capabilities
    db: AsyncSession = Depends(get_db)
):
    """Agent轮询任务"""
    # 验证Agent是否存在
    result = await db.execute(
        select(Agent).where(Agent.agent_id == agent_id)
    )
    agent = result.scalar_one_or_none()
    
    if not agent:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Agent not found"
        )
    
    # 检查Agent是否有空闲槽位
    if agent.current_tasks >= agent.max_concurrent_tasks:
        return TaskPollResponse(
            has_task=False,
            message="Agent at maximum capacity"
        )
    
    # 解析Agent能力
    agent_capabilities = capabilities.split(',')
    
    # 查找符合条件的任务
    result = await db.execute(
        select(Task)
        .where(
            Task.status == "pending",
            Task.task_type.in_(agent_capabilities)
        )
        .order_by(Task.priority.desc(), Task.created_at.asc())
        .limit(1)
    )
    
    task = result.scalar_one_or_none()
    
    if not task:
        return TaskPollResponse(
            has_task=False,
            message="No tasks available"
        )
    
    # 检查任务依赖
    dependencies = task.dependencies
    # Handle various null/empty dependency representations
    has_dependencies = False
    if dependencies:
        # If it's a string "null", treat as no dependencies
        if isinstance(dependencies, str) and dependencies.lower() == "null":
            has_dependencies = False
        # If it's a list with actual items, check dependencies
        elif isinstance(dependencies, list) and len(dependencies) > 0:
            has_dependencies = True
            
    if has_dependencies:
        dependency_result = await db.execute(
            select(Task)
            .where(
                Task.task_id.in_(dependencies),
                Task.status != "completed"
            )
        )
        pending_dependencies = dependency_result.scalars().all()
        
        if pending_dependencies:
            return TaskPollResponse(
                has_task=False,
                message="Task dependencies not met"
            )
    
    # 分配任务给Agent
    await db.execute(
        update(Task)
        .where(Task.id == task.id)
        .values(
            status="assigned",
            agent_id=agent_id,
            assigned_at=datetime.now(timezone.utc)
        )
    )
    
    # 更新Agent当前任务数
    await db.execute(
        update(Agent)
        .where(Agent.agent_id == agent_id)
        .values(current_tasks=agent.current_tasks + 1)
    )
    
    await db.commit()
    
    # 构造任务定义
    task_def = TaskDefinition(
        task_id=task.task_id,
        task_type=task.task_type,
        target=task.target,
        parameters=task.parameters,
        priority=task.priority,
        timeout=task.timeout,
        retry_count=task.retry_count,
        dependencies=task.dependencies,
        workflow_id=task.workflow_id
    )
    
    return TaskPollResponse(
        has_task=True,
        task=task_def,
        message="Task assigned successfully"
    )

@router.post("/tasks/result")
async def submit_task_result(
    task_result: TaskResult,
    db: AsyncSession = Depends(get_db)
):
    """提交任务结果"""
    # 查找任务
    result = await db.execute(
        select(Task).where(Task.task_id == task_result.task_id)
    )
    task = result.scalar_one_or_none()
    
    if not task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Task not found"
        )
    
    # 更新任务状态
    update_data = {
        "status": task_result.status.value if hasattr(task_result.status, 'value') else task_result.status,
        "result_data": task_result.result_data,
        "error_message": task_result.error_message,
        "execution_time": task_result.execution_time,
        "assets_discovered": task_result.assets_discovered,
        "completed_at": datetime.now(timezone.utc)
    }
    
    status_value = task_result.status.value if hasattr(task_result.status, 'value') else task_result.status
    
    if status_value == "running":
        update_data["started_at"] = datetime.now(timezone.utc)
    
    await db.execute(
        update(Task)
        .where(Task.task_id == task_result.task_id)
        .values(**update_data)
    )
    
    # 更新Agent当前任务数
    if status_value in ["completed", "failed", "cancelled"]:
        agent_result = await db.execute(
            select(Agent).where(Agent.agent_id == task_result.agent_id)
        )
        agent = agent_result.scalar_one_or_none()
        
        if agent:
            await db.execute(
                update(Agent)
                .where(Agent.agent_id == task_result.agent_id)
                .values(current_tasks=max(0, agent.current_tasks - 1))
            )
    
    await db.commit()
    
    # 如果任务属于工作流，更新工作流状态
    if task.workflow_id:
        await update_workflow_status(db, task.workflow_id)
    
    # 处理任务发现的资产（如果任务已完成且有资产数据）
    if status_value == "completed" and task_result.assets_discovered:
        try:
            from asset_aggregation import process_task_assets_on_completion
            assets_processed = await process_task_assets_on_completion(task_result.task_id, db)
            if assets_processed > 0:
                print(f"Processed {assets_processed} assets from task {task_result.task_id}")
        except Exception as e:
            print(f"Error processing assets for task {task_result.task_id}: {e}")
    
    return {"message": "Task result submitted", "status": "ok"}

@router.get("/")
async def list_agents(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取Agent列表 - 动态判断在线状态"""
    # 定义在线判断标准：最后心跳时间在30秒内（快速检测离线状态）
    online_threshold = datetime.now(timezone.utc) - timedelta(seconds=30)
    
    result = await db.execute(
        select(Agent).order_by(Agent.created_at.desc())
    )
    agents = result.scalars().all()
    
    # 动态计算真实在线状态
    agents_list = []
    for agent in agents:
        agent_dict = {
            "id": str(agent.id),
            "agent_id": agent.agent_id,
            "name": agent.name,
            "version": agent.version,
            "capabilities": agent.capabilities,
            "max_concurrent_tasks": agent.max_concurrent_tasks,
            "current_tasks": agent.current_tasks,
            "hostname": agent.hostname,
            "ip_address": agent.ip_address,
            "agent_metadata": agent.agent_metadata,
            "created_at": agent.created_at,
            "updated_at": agent.updated_at,
            "last_seen_at": agent.last_seen_at,
            # 基于心跳时间动态判断真实在线状态
            "status": "online" if (
                agent.last_seen_at and 
                agent.last_seen_at > online_threshold
            ) else "offline"
        }
        agents_list.append(agent_dict)
    
    return {"agents": agents_list}


@router.post("/{agent_id}/ping")
async def ping_agent(
    agent_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """ping Agent并强制更新状态"""
    # 检查Agent是否存在
    result = await db.execute(
        select(Agent).where(Agent.agent_id == agent_id)
    )
    agent = result.scalar_one_or_none()
    
    if not agent:
        raise HTTPException(status_code=404, detail="Agent not found")
    
    # 计算是否应该在线（基于最后心跳时间）
    online_threshold = datetime.now(timezone.utc) - timedelta(seconds=30)
    is_online = agent.last_seen_at and agent.last_seen_at > online_threshold
    
    # 强制更新agent状态
    new_status = "online" if is_online else "offline"
    
    await db.execute(
        update(Agent)
        .where(Agent.agent_id == agent_id)
        .values(status=new_status)
    )
    await db.commit()
    
    # 重新获取更新后的agent信息
    result = await db.execute(
        select(Agent).where(Agent.agent_id == agent_id)
    )
    updated_agent = result.scalar_one()
    
    return {
        "agent_id": agent_id,
        "status": new_status,
        "last_seen_at": updated_agent.last_seen_at,
        "is_reachable": is_online,
        "last_checked": datetime.now(timezone.utc)
    }


@router.post("/{agent_id}/command")
async def send_agent_command(
    agent_id: str,
    command: dict,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """向Agent发送远程控制命令（通过任务系统）"""
    # 验证Agent存在
    result = await db.execute(
        select(Agent).where(Agent.agent_id == agent_id)
    )
    agent = result.scalar_one_or_none()
    
    if not agent:
        raise HTTPException(status_code=404, detail="Agent not found")
    
    # 验证命令类型并映射到任务类型
    command_to_task_type = {
        "pause": "agent_pause",
        "resume": "agent_resume", 
        "stop": "agent_stop",
        "restart": "agent_restart",
        "cancel_tasks": "agent_cancel_tasks"
    }
    
    action = command.get("action")
    if action not in command_to_task_type:
        raise HTTPException(
            status_code=400, 
            detail=f"Invalid command. Valid commands: {list(command_to_task_type.keys())}"
        )
    
    task_type = command_to_task_type[action]
    
    # 创建控制任务
    task_id = str(uuid.uuid4())
    control_task = Task(
        task_id=task_id,
        task_type=task_type,
        target=agent_id,  # 目标是Agent ID
        parameters={
            "command": action,
            "agent_id": agent_id,
            "requested_by": str(current_user.id)
        },
        priority=4,  # 控制任务高优先级
        timeout=60,  # 1分钟超时
        retry_count=0,  # 控制任务不重试
        status="pending",
        created_by_user_id=current_user.id,
        created_at=datetime.now(timezone.utc)
    )
    
    db.add(control_task)
    await db.commit()
    await db.refresh(control_task)
    
    return {
        "task_id": task_id,
        "agent_id": agent_id,
        "command": action,
        "status": "task_created",
        "message": f"Control task '{action}' created for agent {agent_id}"
    }

@router.get("/{agent_id}", response_model=AgentInfo)
async def get_agent(
    agent_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取Agent详情"""
    result = await db.execute(
        select(Agent).where(Agent.agent_id == agent_id)
    )
    agent = result.scalar_one_or_none()
    
    if not agent:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Agent not found"
        )
    
    return agent

@router.delete("/{agent_id}")
async def delete_agent(
    agent_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(require_admin)
):
    """删除Agent"""
    result = await db.execute(
        select(Agent).where(Agent.agent_id == agent_id)
    )
    agent = result.scalar_one_or_none()
    
    if not agent:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Agent not found"
        )
    
    # 取消该Agent的所有任务
    await db.execute(
        update(Task)
        .where(Task.agent_id == agent_id, Task.status.in_(["assigned", "running"]))
        .values(status="cancelled", completed_at=datetime.now(timezone.utc))
    )
    
    # 删除Agent
    await db.delete(agent)
    await db.commit()
    
    return {"message": "Agent deleted successfully"}

@router.get("/stats/system", response_model=SystemStats)
async def get_system_stats(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取系统统计信息 - 基于真实心跳时间判断在线状态"""
    # 定义在线判断标准：最后心跳时间在30秒内（快速检测离线状态）
    online_threshold = datetime.now(timezone.utc) - timedelta(seconds=30)
    
    # Agent统计 - 基于实际心跳时间
    agent_stats = await db.execute(
        select(
            func.count(Agent.id).label("total"),
            func.count(Agent.id).filter(
                Agent.last_seen_at.isnot(None),
                Agent.last_seen_at > online_threshold
            ).label("online"),
            func.count(Agent.id).filter(
                Agent.last_seen_at.is_(None) |
                (Agent.last_seen_at <= online_threshold)
            ).label("offline")
        )
    )
    agent_data = agent_stats.first()
    
    # Task统计
    task_stats = await db.execute(
        select(
            func.count(Task.id).label("total"),
            func.count(Task.id).filter(Task.status == "pending").label("pending"),
            func.count(Task.id).filter(Task.status == "running").label("running"),
            func.count(Task.id).filter(Task.status == "completed").label("completed"),
            func.count(Task.id).filter(Task.status == "failed").label("failed")
        )
    )
    task_data = task_stats.first()
    
    # Workflow统计
    workflow_stats = await db.execute(
        select(
            func.count(WorkflowExecution.id).filter(WorkflowExecution.status == "running").label("active")
        )
    )
    workflow_data = workflow_stats.first()
    
    return SystemStats(
        total_agents=agent_data.total or 0,
        online_agents=agent_data.online or 0,
        offline_agents=agent_data.offline or 0,
        total_tasks=task_data.total or 0,
        pending_tasks=task_data.pending or 0,
        running_tasks=task_data.running or 0,
        completed_tasks=task_data.completed or 0,
        failed_tasks=task_data.failed or 0,
        active_workflows=workflow_data.active or 0
    )

async def update_workflow_status(db: AsyncSession, workflow_id: str):
    """更新工作流状态"""
    # 获取工作流的所有任务
    result = await db.execute(
        select(Task).where(Task.workflow_id == workflow_id)
    )
    tasks = result.scalars().all()
    
    if not tasks:
        return
    
    # 统计任务状态
    total_tasks = len(tasks)
    completed_tasks = sum(1 for task in tasks if task.status == "completed")
    failed_tasks = sum(1 for task in tasks if task.status == "failed")
    running_tasks = sum(1 for task in tasks if task.status == "running")
    
    progress = int((completed_tasks / total_tasks) * 100) if total_tasks > 0 else 0
    
    # 确定工作流状态
    if completed_tasks == total_tasks:
        workflow_status = "completed"
    elif failed_tasks > 0:
        workflow_status = "failed"
    elif running_tasks > 0:
        workflow_status = "running"
    else:
        workflow_status = "pending"
    
    # 更新工作流状态
    update_data = {
        "total_tasks": total_tasks,
        "completed_tasks": completed_tasks,
        "failed_tasks": failed_tasks,
        "running_tasks": running_tasks,
        "progress": progress,
        "status": workflow_status,
        "updated_at": datetime.now(timezone.utc)
    }
    
    if workflow_status == "completed":
        update_data["completed_at"] = datetime.now(timezone.utc)
    
    await db.execute(
        update(WorkflowExecution)
        .where(WorkflowExecution.execution_id == workflow_id)
        .values(**update_data)
    )

# ========== 基于密钥的新端点 ==========

@router.post("/register-with-key", response_model=AgentInfo)
async def register_agent_with_key(
    agent_data: AgentRegisterWithKeyRequest,
    db: AsyncSession = Depends(get_db)
):
    """Agent使用API密钥注册"""
    # 验证API密钥
    is_valid, agent_key = await AgentKeyService.validate_api_key(
        db=db, 
        api_key=agent_data.api_key
    )
    
    if not is_valid:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or expired API key"
        )
    
    # 检查密钥是否已绑定到其他agent
    if agent_key.agent_id and agent_key.agent_id != agent_data.agent_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="API key is already bound to another agent"
        )
    
    # 检查是否已经注册
    result = await db.execute(
        select(Agent).where(Agent.agent_id == agent_data.agent_id)
    )
    existing_agent = result.scalar_one_or_none()
    
    if existing_agent:
        # 更新现有Agent
        await db.execute(
            update(Agent)
            .where(Agent.agent_id == agent_data.agent_id)
            .values(
                name=agent_data.name,
                version=agent_data.version,
                capabilities=agent_data.capabilities,
                max_concurrent_tasks=agent_data.max_concurrent_tasks,
                hostname=agent_data.hostname,
                ip_address=agent_data.ip_address,
                agent_metadata=agent_data.metadata,
                status="online",
                last_seen_at=datetime.now(timezone.utc),
                updated_at=datetime.now(timezone.utc)
            )
        )
        
        # 绑定密钥到agent（如果尚未绑定）
        if not agent_key.agent_id:
            from models_dynamic import AgentKey
            await db.execute(
                update(AgentKey)
                .where(AgentKey.id == agent_key.id)
                .values(agent_id=agent_data.agent_id)
            )
        
        await db.commit()
        
        # 重新获取更新后的Agent
        result = await db.execute(
            select(Agent).where(Agent.agent_id == agent_data.agent_id)
        )
        agent = result.scalar_one()
    else:
        # 创建新Agent
        agent = Agent(
            agent_id=agent_data.agent_id,
            name=agent_data.name,
            version=agent_data.version,
            capabilities=agent_data.capabilities,
            max_concurrent_tasks=agent_data.max_concurrent_tasks,
            hostname=agent_data.hostname,
            ip_address=agent_data.ip_address,
            agent_metadata=agent_data.metadata,
            status="online",
            last_seen_at=datetime.now(timezone.utc)
        )
        db.add(agent)
        
        # 绑定密钥到agent（如果尚未绑定）
        if not agent_key.agent_id:
            from models_dynamic import AgentKey
            await db.execute(
                update(AgentKey)
                .where(AgentKey.id == agent_key.id)
                .values(agent_id=agent_data.agent_id)
            )
        
        await db.commit()
        await db.refresh(agent)
    
    return agent

@router.post("/heartbeat-with-key")
async def agent_heartbeat_with_key(
    heartbeat_data: AgentHeartbeatWithKeyRequest,
    db: AsyncSession = Depends(get_db)
):
    """Agent使用API密钥发送心跳"""
    # 验证API密钥
    is_valid, agent_key = await AgentKeyService.validate_api_key(
        db=db, 
        api_key=heartbeat_data.api_key
    )
    
    if not is_valid:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or expired API key"
        )
    
    # 验证agent_id与密钥绑定
    if agent_key.agent_id != heartbeat_data.agent_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="API key is not authorized for this agent"
        )
    
    # 查找Agent
    result = await db.execute(
        select(Agent).where(Agent.agent_id == heartbeat_data.agent_id)
    )
    agent = result.scalar_one_or_none()
    
    if not agent:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Agent not found"
        )
    
    # 更新Agent状态
    await db.execute(
        update(Agent)
        .where(Agent.agent_id == heartbeat_data.agent_id)
        .values(
            status=heartbeat_data.status,
            current_tasks=heartbeat_data.current_tasks,
            last_seen_at=datetime.now(timezone.utc),
            agent_metadata=heartbeat_data.system_info
        )
    )
    await db.commit()
    
    return {"message": "Heartbeat received", "status": "ok"}

@router.get("/tasks/poll-with-key", response_model=TaskPollResponse)
async def poll_tasks_with_key(
    api_key: str,
    agent_id: str,
    capabilities: str,  # comma-separated capabilities
    db: AsyncSession = Depends(get_db)
):
    """Agent使用API密钥轮询任务"""
    # 验证API密钥
    is_valid, agent_key = await AgentKeyService.validate_api_key(
        db=db, 
        api_key=api_key
    )
    
    if not is_valid:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or expired API key"
        )
    
    # 验证agent_id与密钥绑定
    if agent_key.agent_id != agent_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="API key is not authorized for this agent"
        )
    
    # 验证Agent是否存在
    result = await db.execute(
        select(Agent).where(Agent.agent_id == agent_id)
    )
    agent = result.scalar_one_or_none()
    
    if not agent:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Agent not found"
        )
    
    # 检查Agent是否有空闲槽位
    if agent.current_tasks >= agent.max_concurrent_tasks:
        return TaskPollResponse(
            has_task=False,
            message="Agent at maximum capacity"
        )
    
    # 解析Agent能力
    agent_capabilities = capabilities.split(',')
    
    # 查找符合条件的任务
    result = await db.execute(
        select(Task)
        .where(
            Task.status == "pending",
            Task.task_type.in_(agent_capabilities)
        )
        .order_by(Task.priority.desc(), Task.created_at.asc())
        .limit(1)
    )
    
    task = result.scalar_one_or_none()
    
    if not task:
        return TaskPollResponse(
            has_task=False,
            message="No tasks available"
        )
    
    # 检查任务依赖 (复用现有逻辑)
    dependencies = task.dependencies
    has_dependencies = False
    if dependencies:
        if isinstance(dependencies, str) and dependencies.lower() == "null":
            has_dependencies = False
        elif isinstance(dependencies, list) and len(dependencies) > 0:
            has_dependencies = True
            
    if has_dependencies:
        dependency_result = await db.execute(
            select(Task)
            .where(
                Task.task_id.in_(dependencies),
                Task.status != "completed"
            )
        )
        pending_dependencies = dependency_result.scalars().all()
        
        if pending_dependencies:
            return TaskPollResponse(
                has_task=False,
                message="Task dependencies not met"
            )
    
    # 分配任务给Agent
    await db.execute(
        update(Task)
        .where(Task.id == task.id)
        .values(
            status="assigned",
            agent_id=agent_id,
            assigned_at=datetime.now(timezone.utc)
        )
    )
    
    # 更新Agent当前任务数
    await db.execute(
        update(Agent)
        .where(Agent.agent_id == agent_id)
        .values(current_tasks=agent.current_tasks + 1)
    )
    
    await db.commit()
    
    # 构造任务定义
    task_def = TaskDefinition(
        task_id=task.task_id,
        task_type=task.task_type,
        target=task.target,
        parameters=task.parameters,
        priority=task.priority,
        timeout=task.timeout,
        retry_count=task.retry_count,
        dependencies=task.dependencies,
        workflow_id=task.workflow_id
    )
    
    return TaskPollResponse(
        has_task=True,
        task=task_def,
        message="Task assigned successfully"
    )