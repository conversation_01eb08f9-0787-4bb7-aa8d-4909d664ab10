from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete, func
from typing import List, Optional
from datetime import datetime, timedelta, timezone
import uuid

from database import get_db
from models_dynamic import Agent<PERSON><PERSON>, User
from schemas_agent_key import (
    AgentKeyCreate, AgentKeyResponse, AgentKeyInfo, 
    AgentKeyUpdate, AgentKeyValidationResponse
)
from auth import get_current_user, require_admin
from agent_key_service import AgentKeyService

router = APIRouter(prefix="/agent-keys", tags=["Agent Keys"])

@router.get("/", response_model=List[AgentKeyInfo])
async def list_agent_keys(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(require_admin),
    agent_id: Optional[str] = None,
    status_filter: Optional[str] = None,
    limit: int = 100,
    offset: int = 0
):
    """获取Agent密钥列表"""
    query = select(<PERSON><PERSON><PERSON>).order_by(AgentKey.created_at.desc())
    
    # 如果指定了agent_id，只返回该agent的密钥
    if agent_id:
        query = query.where(AgentKey.agent_id == agent_id)
    
    if status_filter:
        query = query.where(AgentKey.status == status_filter)
    
    query = query.limit(limit).offset(offset)
    
    result = await db.execute(query)
    keys = result.scalars().all()
    
    return [
        AgentKeyInfo(
            id=str(key.id),
            key_id=key.key_id,
            agent_id=key.agent_id,
            name=key.name,
            description=key.description,
            status=key.status,
            expires_at=key.expires_at,
            created_at=key.created_at,
            updated_at=key.updated_at,
            last_used_at=key.last_used_at,
            usage_count=key.usage_count,
            created_by_user_id=str(key.created_by_user_id) if key.created_by_user_id else None
        )
        for key in keys
    ]

@router.get("/stats")
async def get_key_stats(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(require_admin)
):
    """获取密钥统计信息"""
    # 统计各状态的密钥数量
    stats_result = await db.execute(
        select(
            func.count(AgentKey.id).label("total"),
            func.count(AgentKey.id).filter(AgentKey.status == "active").label("active"),
            func.count(AgentKey.id).filter(AgentKey.status == "suspended").label("suspended"),
            func.count(AgentKey.id).filter(AgentKey.status == "revoked").label("revoked"),
            func.count(AgentKey.id).filter(AgentKey.status == "expired").label("expired")
        )
    )
    stats = stats_result.first()
    
    # 统计即将过期的密钥（7天内）
    soon_expire_threshold = datetime.now(timezone.utc) + timedelta(days=7)
    soon_expire_result = await db.execute(
        select(func.count(AgentKey.id)).where(
            AgentKey.status == "active",
            AgentKey.expires_at.isnot(None),
            AgentKey.expires_at <= soon_expire_threshold
        )
    )
    soon_expire_count = soon_expire_result.scalar() or 0
    
    # 统计最近使用的密钥（24小时内）
    recent_use_threshold = datetime.now(timezone.utc) - timedelta(hours=24)
    recent_use_result = await db.execute(
        select(func.count(AgentKey.id)).where(
            AgentKey.last_used_at.isnot(None),
            AgentKey.last_used_at >= recent_use_threshold
        )
    )
    recent_use_count = recent_use_result.scalar() or 0
    
    return {
        "total_keys": stats.total or 0,
        "active_keys": stats.active or 0,
        "suspended_keys": stats.suspended or 0,
        "revoked_keys": stats.revoked or 0,
        "expired_keys": stats.expired or 0,
        "soon_expire_keys": soon_expire_count,
        "recently_used_keys": recent_use_count
    }

@router.post("/", response_model=AgentKeyResponse)
async def create_agent_key(
    key_data: AgentKeyCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(require_admin)
):
    """创建新的Agent密钥（未绑定状态）"""
    try:
        # 创建未绑定的密钥，agent_id为None
        key_data_unbound = AgentKeyCreate(
            name=key_data.name,
            description=key_data.description,
            expires_at=key_data.expires_at,
            agent_id=None  # 创建时不绑定Agent
        )
        
        api_key_response = await AgentKeyService.create_agent_key(
            db=db,
            key_data=key_data_unbound,
            created_by_user_id=current_user.id
        )
        return api_key_response
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建密钥失败: {str(e)}"
        )

@router.post("/{key_id}/revoke")
async def revoke_agent_key(
    key_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(require_admin)
):
    """撤销密钥"""
    result = await db.execute(
        select(AgentKey).where(AgentKey.key_id == key_id)
    )
    key = result.scalar_one_or_none()
    
    if not key:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="密钥不存在"
        )
    
    if key.status == "revoked":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="密钥已经被撤销"
        )
    
    await db.execute(
        update(AgentKey)
        .where(AgentKey.key_id == key_id)
        .values(
            status="revoked",
            updated_at=datetime.now(timezone.utc)
        )
    )
    await db.commit()
    
    return {"message": "密钥已撤销", "key_id": key_id}

@router.delete("/{key_id}")
async def delete_agent_key(
    key_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(require_admin)
):
    """删除密钥（物理删除）"""
    result = await db.execute(
        select(AgentKey).where(AgentKey.key_id == key_id)
    )
    key = result.scalar_one_or_none()
    
    if not key:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="密钥不存在"
        )
    
    # 检查密钥是否仍在使用中
    if key.agent_id and key.status == "active":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="无法删除正在使用的活跃密钥，请先撤销"
        )
    
    await db.execute(
        delete(AgentKey).where(AgentKey.key_id == key_id)
    )
    await db.commit()
    
    return {"message": "密钥已删除", "key_id": key_id}