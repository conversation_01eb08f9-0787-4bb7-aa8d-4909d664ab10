#!/usr/bin/env python3
"""
动态模型系统初始化脚本
将现有的固定模型迁移到动态模型系统
"""

import sys
import os
import asyncio

# Add backend to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'backend'))

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from models_dynamic import User, ModelType, ModelField, Base
from passlib.context import CryptContext

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def get_password_hash(password):
    return pwd_context.hash(password)

# Use sync version for initialization
SYNC_DATABASE_URL = "postgresql://postgres:postgres@localhost:5432/bounty_ams"

def create_default_dynamic_models(db, admin_user):
    """创建默认的动态模型类型"""
    
    # 检查是否已存在默认模型
    existing_models = db.query(ModelType).filter(ModelType.is_system == True).count()
    if existing_models > 0:
        print("Default dynamic models already exist")
        return
    
    # 1. 漏洞知识库模型
    vulnerability_model = ModelType(
        name="vulnerability_kb",
        display_name="漏洞知识库",
        description="安全漏洞信息管理",
        icon="🛡️",
        color="#DC2626",
        is_active=True,
        is_system=True,
        created_by_user_id=admin_user.id
    )
    db.add(vulnerability_model)
    db.flush()
    
    # 漏洞知识库字段
    vuln_fields = [
        ModelField(
            model_type_id=vulnerability_model.id,
            field_name="name",
            field_type="text",
            display_name="漏洞名称",
            description="漏洞的名称或标题",
            is_required=True,
            is_searchable=True,
            is_filterable=True,
            sort_order=0
        ),
        ModelField(
            model_type_id=vulnerability_model.id,
            field_name="vuln_id",
            field_type="text",
            display_name="漏洞ID",
            description="CVE ID或其他漏洞标识符",
            is_required=False,
            is_searchable=True,
            is_filterable=True,
            is_unique=True,
            validation_rules={"pattern": "^(CVE-|CNVD-|CNNVD-)?[0-9]{4}-[0-9]+$"},
            sort_order=1
        ),
        ModelField(
            model_type_id=vulnerability_model.id,
            field_name="description",
            field_type="textarea",
            display_name="漏洞描述",
            description="详细的漏洞描述",
            is_required=False,
            is_searchable=True,
            is_filterable=False,
            sort_order=2
        ),
        ModelField(
            model_type_id=vulnerability_model.id,
            field_name="severity",
            field_type="select",
            display_name="严重程度",
            description="漏洞的严重程度等级",
            is_required=False,
            is_searchable=False,
            is_filterable=True,
            field_options={
                "options": [
                    {"value": "Critical", "label": "严重"},
                    {"value": "High", "label": "高危"},
                    {"value": "Medium", "label": "中危"},
                    {"value": "Low", "label": "低危"},
                    {"value": "Info", "label": "信息"}
                ]
            },
            sort_order=3
        ),
        ModelField(
            model_type_id=vulnerability_model.id,
            field_name="affected_products",
            field_type="textarea",
            display_name="影响产品",
            description="受影响的产品列表（每行一个）",
            is_required=False,
            is_searchable=True,
            is_filterable=False,
            sort_order=4
        ),
        ModelField(
            model_type_id=vulnerability_model.id,
            field_name="poc_identifier",
            field_type="text",
            display_name="PoC标识符",
            description="概念验证代码的标识符",
            is_required=False,
            is_searchable=True,
            is_filterable=True,
            sort_order=5
        ),
        ModelField(
            model_type_id=vulnerability_model.id,
            field_name="cvss_score",
            field_type="number",
            display_name="CVSS评分",
            description="通用漏洞评分系统分数",
            is_required=False,
            is_searchable=False,
            is_filterable=True,
            validation_rules={"min": 0.0, "max": 10.0},
            sort_order=6
        ),
        ModelField(
            model_type_id=vulnerability_model.id,
            field_name="references",
            field_type="textarea",
            display_name="参考链接",
            description="相关参考资料链接（每行一个）",
            is_required=False,
            is_searchable=False,
            is_filterable=False,
            sort_order=7
        )
    ]
    
    for field in vuln_fields:
        db.add(field)
    
    # 2. 代理(Agent)模型
    agent_model = ModelType(
        name="agent",
        display_name="代理节点",
        description="分布式扫描代理管理",
        icon="🤖",
        color="#059669",
        is_active=True,
        is_system=True,
        created_by_user_id=admin_user.id
    )
    db.add(agent_model)
    db.flush()
    
    # 代理字段
    agent_fields = [
        ModelField(
            model_type_id=agent_model.id,
            field_name="name",
            field_type="text",
            display_name="代理名称",
            description="代理节点的名称",
            is_required=True,
            is_searchable=True,
            is_filterable=True,
            sort_order=0
        ),
        ModelField(
            model_type_id=agent_model.id,
            field_name="hostname",
            field_type="text",
            display_name="主机名",
            description="代理所在主机的名称",
            is_required=False,
            is_searchable=True,
            is_filterable=True,
            sort_order=1
        ),
        ModelField(
            model_type_id=agent_model.id,
            field_name="ip_address",
            field_type="text",
            display_name="IP地址",
            description="代理的IP地址",
            is_required=False,
            is_searchable=True,
            is_filterable=True,
            validation_rules={"pattern": "^(?:[0-9]{1,3}\\.){3}[0-9]{1,3}$"},
            sort_order=2
        ),
        ModelField(
            model_type_id=agent_model.id,
            field_name="status",
            field_type="select",
            display_name="状态",
            description="代理的当前状态",
            is_required=True,
            is_searchable=False,
            is_filterable=True,
            field_options={
                "options": [
                    {"value": "online", "label": "在线"},
                    {"value": "offline", "label": "离线"},
                    {"value": "busy", "label": "忙碌"},
                    {"value": "error", "label": "错误"}
                ]
            },
            default_value="offline",
            sort_order=3
        ),
        ModelField(
            model_type_id=agent_model.id,
            field_name="version",
            field_type="text",
            display_name="版本",
            description="代理软件版本",
            is_required=False,
            is_searchable=False,
            is_filterable=True,
            validation_rules={"pattern": "^v?[0-9]+\\.[0-9]+\\.[0-9]+"},
            sort_order=4
        ),
        ModelField(
            model_type_id=agent_model.id,
            field_name="capabilities",
            field_type="multi_select",
            display_name="功能",
            description="代理支持的功能",
            is_required=False,
            is_searchable=False,
            is_filterable=True,
            field_options={
                "options": [
                    {"value": "subdomain_enum", "label": "子域名枚举"},
                    {"value": "port_scan", "label": "端口扫描"},
                    {"value": "service_detect", "label": "服务识别"},
                    {"value": "vuln_scan", "label": "漏洞扫描"},
                    {"value": "web_crawl", "label": "Web爬取"},
                    {"value": "screenshot", "label": "截图"}
                ]
            },
            sort_order=5
        ),
        ModelField(
            model_type_id=agent_model.id,
            field_name="last_seen_at",
            field_type="datetime",
            display_name="最后在线时间",
            description="代理最后一次心跳时间",
            is_required=False,
            is_searchable=False,
            is_filterable=True,
            sort_order=6
        ),
        ModelField(
            model_type_id=agent_model.id,
            field_name="config",
            field_type="json",
            display_name="配置",
            description="代理的配置信息（JSON格式）",
            is_required=False,
            is_searchable=False,
            is_filterable=False,
            sort_order=7
        )
    ]
    
    for field in agent_fields:
        db.add(field)
    
    # 3. 任务模型
    task_model = ModelType(
        name="task",
        display_name="执行任务",
        description="代理执行任务管理",
        icon="📋",
        color="#7C3AED",
        is_active=True,
        is_system=True,
        created_by_user_id=admin_user.id
    )
    db.add(task_model)
    db.flush()
    
    # 任务字段
    task_fields = [
        ModelField(
            model_type_id=task_model.id,
            field_name="name",
            field_type="text",
            display_name="任务名称",
            description="任务的名称或标题",
            is_required=True,
            is_searchable=True,
            is_filterable=True,
            sort_order=0
        ),
        ModelField(
            model_type_id=task_model.id,
            field_name="task_type",
            field_type="select",
            display_name="任务类型",
            description="任务的类型",
            is_required=True,
            is_searchable=False,
            is_filterable=True,
            field_options={
                "options": [
                    {"value": "subdomain_enum", "label": "子域名枚举"},
                    {"value": "port_scan", "label": "端口扫描"},
                    {"value": "vuln_verify", "label": "漏洞验证"},
                    {"value": "web_crawl", "label": "Web爬取"},
                    {"value": "asset_discovery", "label": "资产发现"}
                ]
            },
            sort_order=1
        ),
        ModelField(
            model_type_id=task_model.id,
            field_name="target",
            field_type="text",
            display_name="目标",
            description="任务的目标（域名、IP等）",
            is_required=True,
            is_searchable=True,
            is_filterable=True,
            sort_order=2
        ),
        ModelField(
            model_type_id=task_model.id,
            field_name="status",
            field_type="select",
            display_name="状态",
            description="任务执行状态",
            is_required=True,
            is_searchable=False,
            is_filterable=True,
            field_options={
                "options": [
                    {"value": "pending", "label": "等待中"},
                    {"value": "running", "label": "执行中"},
                    {"value": "completed", "label": "已完成"},
                    {"value": "failed", "label": "失败"},
                    {"value": "cancelled", "label": "已取消"}
                ]
            },
            default_value="pending",
            sort_order=3
        ),
        ModelField(
            model_type_id=task_model.id,
            field_name="agent_id",
            field_type="text",
            display_name="执行代理ID",
            description="执行此任务的代理ID",
            is_required=False,
            is_searchable=False,
            is_filterable=True,
            sort_order=4
        ),
        ModelField(
            model_type_id=task_model.id,
            field_name="vulnerability_id",
            field_type="text",
            display_name="关联漏洞ID",
            description="关联的漏洞知识库ID（用于漏洞验证任务）",
            is_required=False,
            is_searchable=False,
            is_filterable=True,
            sort_order=5
        ),
        ModelField(
            model_type_id=task_model.id,
            field_name="priority",
            field_type="select",
            display_name="优先级",
            description="任务执行优先级",
            is_required=False,
            is_searchable=False,
            is_filterable=True,
            field_options={
                "options": [
                    {"value": "low", "label": "低"},
                    {"value": "normal", "label": "普通"},
                    {"value": "high", "label": "高"},
                    {"value": "urgent", "label": "紧急"}
                ]
            },
            default_value="normal",
            sort_order=6
        ),
        ModelField(
            model_type_id=task_model.id,
            field_name="parameters",
            field_type="json",
            display_name="任务参数",
            description="任务执行的参数配置（JSON格式）",
            is_required=False,
            is_searchable=False,
            is_filterable=False,
            sort_order=7
        ),
        ModelField(
            model_type_id=task_model.id,
            field_name="result",
            field_type="json",
            display_name="执行结果",
            description="任务执行的结果数据（JSON格式）",
            is_required=False,
            is_searchable=False,
            is_filterable=False,
            sort_order=8
        ),
        ModelField(
            model_type_id=task_model.id,
            field_name="started_at",
            field_type="datetime",
            display_name="开始时间",
            description="任务开始执行的时间",
            is_required=False,
            is_searchable=False,
            is_filterable=True,
            sort_order=9
        ),
        ModelField(
            model_type_id=task_model.id,
            field_name="completed_at",
            field_type="datetime",
            display_name="完成时间",
            description="任务完成的时间",
            is_required=False,
            is_searchable=False,
            is_filterable=True,
            sort_order=10
        ),
        ModelField(
            model_type_id=task_model.id,
            field_name="error_message",
            field_type="textarea",
            display_name="错误信息",
            description="任务失败时的错误信息",
            is_required=False,
            is_searchable=True,
            is_filterable=False,
            sort_order=11
        )
    ]
    
    for field in task_fields:
        db.add(field)
    
    db.commit()
    print("Default dynamic models created successfully:")
    print("- 漏洞知识库 (vulnerability_kb)")
    print("- 代理节点 (agent)")
    print("- 执行任务 (task)")


def create_admin_user_and_models():
    """创建管理员用户和默认动态模型"""
    engine = create_engine(SYNC_DATABASE_URL)
    Base.metadata.create_all(bind=engine)
    
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        # 检查管理员用户是否存在
        admin_user = db.query(User).filter(User.username == "admin").first()
        if admin_user:
            print("Admin user already exists")
        else:
            # 创建管理员用户
            hashed_password = get_password_hash("admin123")
            admin_user = User(
                username="admin",
                email="<EMAIL>",
                hashed_password=hashed_password,
                is_admin=True
            )
            db.add(admin_user)
            db.commit()
            db.refresh(admin_user)
            print("Admin user created successfully")
            print("Username: admin")
            print("Password: admin123")
        
        # 创建默认动态模型
        create_default_dynamic_models(db, admin_user)
        
    except Exception as e:
        print(f"Error: {e}")
        db.rollback()
    finally:
        db.close()


if __name__ == "__main__":
    print("Initializing dynamic model system...")
    create_admin_user_and_models()
    print("Dynamic model system initialization completed!")