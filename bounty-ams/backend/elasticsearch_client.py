from elasticsearch import AsyncElasticsearch
from config import settings
import logging

logger = logging.getLogger(__name__)

class ElasticsearchClient:
    def __init__(self):
        self.client = None
        self.connected = False
    
    async def connect(self):
        """Initialize Elasticsearch connection"""
        try:
            self.client = AsyncElasticsearch(
                hosts=[settings.ELASTICSEARCH_URL],
                timeout=30,
                max_retries=3,
                retry_on_timeout=True
            )
            
            # Test connection
            info = await self.client.info()
            logger.info(f"Connected to Elasticsearch: {info['version']['number']}")
            self.connected = True
            
            # Create indices if they don't exist
            await self.create_indices()
            
        except Exception as e:
            logger.error(f"Failed to connect to Elasticsearch: {e}")
            self.connected = False
            raise
    
    async def create_indices(self):
        """Create necessary indices with mappings"""
        # Assets index mapping
        assets_mapping = {
            "mappings": {
                "properties": {
                    "asset_type": {
                        "type": "keyword"
                    },
                    "asset_type_id": {
                        "type": "keyword"
                    },
                    "data": {
                        "type": "object",
                        "dynamic": True
                    },
                    "tags": {
                        "type": "keyword"
                    },
                    "source": {
                        "type": "keyword"
                    },
                    "created_at": {
                        "type": "date"
                    },
                    "updated_at": {
                        "type": "date"
                    }
                }
            }
        }
        
        # Create assets index
        if not await self.client.indices.exists(index="assets"):
            await self.client.indices.create(index="assets", body=assets_mapping)
            logger.info("Created assets index")
    
    async def disconnect(self):
        """Close Elasticsearch connection"""
        if self.client:
            await self.client.close()
            self.connected = False
    
    def get_client(self):
        """Get the Elasticsearch client"""
        if not self.connected:
            raise Exception("Elasticsearch not connected")
        return self.client

# Global instance
es_client = ElasticsearchClient()

async def get_es_client():
    """Dependency to get ES client"""
    if not es_client.connected:
        await es_client.connect()
    return es_client.get_client()