from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime
from enum import Enum

class AgentKeyStatus(str, Enum):
    active = "active"
    suspended = "suspended"
    revoked = "revoked"
    expired = "expired"

class AgentKeyCreate(BaseModel):
    name: str = Field(..., min_length=1, max_length=200)
    description: Optional[str] = None
    agent_id: Optional[str] = None
    expires_at: Optional[datetime] = None

class AgentKeyResponse(BaseModel):
    id: str
    key_id: str
    key_value: str  # Only shown once during creation
    agent_id: Optional[str]
    name: str
    description: Optional[str]
    status: AgentKeyStatus
    expires_at: Optional[datetime]
    created_at: datetime
    created_by_user_id: Optional[str]

class AgentKeyInfo(BaseModel):
    id: str
    key_id: str
    agent_id: Optional[str]
    name: str
    description: Optional[str]
    status: AgentKeyStatus
    expires_at: Optional[datetime]
    created_at: datetime
    updated_at: Optional[datetime]
    last_used_at: Optional[datetime]
    usage_count: int
    created_by_user_id: Optional[str]

class AgentKeyUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    status: Optional[AgentKeyStatus] = None
    expires_at: Optional[datetime] = None

class AgentKeyAuthRequest(BaseModel):
    api_key: str

class AgentKeyValidationResponse(BaseModel):
    valid: bool
    agent_id: Optional[str] = None
    key_info: Optional[AgentKeyInfo] = None
    error: Optional[str] = None

class AgentRegisterWithKeyRequest(BaseModel):
    api_key: str
    agent_id: str
    name: str
    version: str
    capabilities: List[str]
    max_concurrent_tasks: int = 3
    hostname: Optional[str] = None
    ip_address: Optional[str] = None
    metadata: Optional[dict] = None

class AgentHeartbeatWithKeyRequest(BaseModel):
    api_key: str
    agent_id: str
    status: str = "online"
    current_tasks: int = 0
    system_info: Optional[dict] = None