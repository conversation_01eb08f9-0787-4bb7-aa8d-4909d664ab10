#!/usr/bin/env python3
"""
为新的平台和项目创建相应的资产数据
"""

import asyncio
import json
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy import select, delete
from models_dynamic import ModelType, DynamicEntity
from config import settings
import random

# 创建异步数据库引擎
engine = create_async_engine(settings.DATABASE_URL)
async_session = async_sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)

async def create_realistic_assets():
    """为新的平台和项目创建现实的资产数据"""
    async with async_session() as session:
        try:
            # 获取模型类型
            enhanced_asset_model_result = await session.execute(
                select(ModelType).where(ModelType.name == 'enhanced_asset')
            )
            enhanced_asset_model = enhanced_asset_model_result.scalar_one_or_none()
            
            platform_model_result = await session.execute(
                select(ModelType).where(ModelType.name == 'platform')
            )
            platform_model = platform_model_result.scalar_one_or_none()
            
            project_model_result = await session.execute(
                select(ModelType).where(ModelType.name == 'project')
            )
            project_model = project_model_result.scalar_one_or_none()
            
            if not all([enhanced_asset_model, platform_model, project_model]):
                print("错误：找不到所需的模型类型")
                return
            
            # 清理现有资产数据
            print("清理现有资产数据...")
            await session.execute(delete(DynamicEntity).where(DynamicEntity.model_type_id == enhanced_asset_model.id))
            await session.commit()
            
            # 获取所有平台和项目
            platforms_result = await session.execute(
                select(DynamicEntity).where(DynamicEntity.model_type_id == platform_model.id)
            )
            platforms = platforms_result.scalars().all()
            
            projects_result = await session.execute(
                select(DynamicEntity).where(DynamicEntity.model_type_id == project_model.id)
            )
            projects = projects_result.scalars().all()
            
            print(f"找到 {len(platforms)} 个平台，{len(projects)} 个项目")
            
            # 为每个项目创建相应的资产
            all_assets = []
            confidence_levels = ["high", "medium", "low"]
            asset_types = ["domain", "subdomain", "url", "api_endpoint", "certificate"]
            
            for project in projects:
                project_data = project.entity_data
                project_name = project_data.get('name', '')
                company_name = project_data.get('company_name', '')
                platform_id = project_data.get('platform_id')
                project_id_str = str(project.id)  # 确保ID是字符串
                
                # 根据不同的项目创建不同的资产
                project_assets = []
                
                if "Shopify" in project_name:
                    project_assets = [
                        {
                            "platform_id": platform_id,
                            "project_id": project_id_str,
                            "asset_type": "domain",
                            "asset_value": "shopify.com",
                            "asset_host": "shopify.com",
                            "confidence": "high",
                            "status": "active",
                            "discovered_at": (datetime.now() - timedelta(days=random.randint(1, 30))).isoformat(),
                            "source_task_type": "domain_enum",
                            "tags": ["main_domain", "in_scope", "e-commerce"],
                            "metadata": {"ip": "************", "cdn": "Fastly"}
                        },
                        {
                            "platform_id": platform_id,
                            "project_id": project_id_str,
                            "asset_type": "subdomain",
                            "asset_value": "api.shopify.com",
                            "asset_host": "api.shopify.com",
                            "confidence": "high",
                            "status": "active",
                            "discovered_at": (datetime.now() - timedelta(days=random.randint(1, 30))).isoformat(),
                            "source_task_type": "subdomain_enum",
                            "tags": ["api", "in_scope", "critical"],
                            "metadata": {"services": ["HTTPS", "API"], "status_code": 200}
                        },
                        {
                            "platform_id": platform_id,
                            "project_id": project_id_str,
                            "asset_type": "subdomain",
                            "asset_value": "partners.shopify.com",
                            "asset_host": "partners.shopify.com",
                            "confidence": "high",
                            "status": "active",
                            "discovered_at": (datetime.now() - timedelta(days=random.randint(1, 30))).isoformat(),
                            "source_task_type": "subdomain_enum",
                            "tags": ["partners", "in_scope"],
                            "metadata": {"services": ["HTTPS"], "status_code": 200}
                        }
                    ]
                
                elif "Uber" in project_name:
                    project_assets = [
                        {
                            "platform_id": platform_id,
                            "project_id": project_id_str,
                            "asset_type": "domain",
                            "asset_value": "uber.com",
                            "asset_host": "uber.com",
                            "confidence": "high",
                            "status": "active",
                            "discovered_at": (datetime.now() - timedelta(days=random.randint(1, 30))).isoformat(),
                            "source_task_type": "domain_enum",
                            "tags": ["main_domain", "in_scope", "transportation"],
                            "metadata": {"ip": "***************", "cdn": "GitHub Pages"}
                        },
                        {
                            "platform_id": platform_id,
                            "project_id": project_id_str,
                            "asset_type": "subdomain",
                            "asset_value": "riders.uber.com",
                            "asset_host": "riders.uber.com",
                            "confidence": "high",
                            "status": "active",
                            "discovered_at": (datetime.now() - timedelta(days=random.randint(1, 30))).isoformat(),
                            "source_task_type": "subdomain_enum",
                            "tags": ["mobile", "in_scope"],
                            "metadata": {"services": ["HTTPS", "Mobile_API"], "status_code": 200}
                        }
                    ]
                
                elif "GitLab" in project_name:
                    project_assets = [
                        {
                            "platform_id": platform_id,
                            "project_id": project_id_str,
                            "asset_type": "domain",
                            "asset_value": "gitlab.com",
                            "asset_host": "gitlab.com",
                            "confidence": "high",
                            "status": "active",
                            "discovered_at": (datetime.now() - timedelta(days=random.randint(1, 30))).isoformat(),
                            "source_task_type": "domain_enum",
                            "tags": ["main_domain", "in_scope", "devops"],
                            "metadata": {"ip": "*************", "cdn": "Cloudflare"}
                        },
                        {
                            "platform_id": platform_id,
                            "project_id": project_id_str,
                            "asset_type": "subdomain",
                            "asset_value": "api.gitlab.com",
                            "asset_host": "api.gitlab.com",
                            "confidence": "high",
                            "status": "active",
                            "discovered_at": (datetime.now() - timedelta(days=random.randint(1, 30))).isoformat(),
                            "source_task_type": "subdomain_enum",
                            "tags": ["api", "in_scope", "critical"],
                            "metadata": {"services": ["HTTPS", "REST_API"], "status_code": 200}
                        }
                    ]
                
                elif "Coinbase" in project_name:
                    project_assets = [
                        {
                            "platform_id": platform_id,
                            "project_id": project_id_str,
                            "asset_type": "domain",
                            "asset_value": "coinbase.com",
                            "asset_host": "coinbase.com",
                            "confidence": "high",
                            "status": "active",
                            "discovered_at": (datetime.now() - timedelta(days=random.randint(1, 30))).isoformat(),
                            "source_task_type": "domain_enum",
                            "tags": ["main_domain", "in_scope", "crypto"],
                            "metadata": {"ip": "************", "cdn": "Cloudflare"}
                        },
                        {
                            "platform_id": platform_id,
                            "project_id": project_id_str,
                            "asset_type": "subdomain",
                            "asset_value": "pro.coinbase.com",
                            "asset_host": "pro.coinbase.com",
                            "confidence": "high",
                            "status": "active",
                            "discovered_at": (datetime.now() - timedelta(days=random.randint(1, 30))).isoformat(),
                            "source_task_type": "subdomain_enum",
                            "tags": ["trading", "in_scope", "pro"],
                            "metadata": {"services": ["HTTPS", "Trading_API"], "status_code": 200}
                        }
                    ]
                
                elif "Tesla" in project_name:
                    project_assets = [
                        {
                            "platform_id": platform_id,
                            "project_id": project_id_str,
                            "asset_type": "domain",
                            "asset_value": "tesla.com",
                            "asset_host": "tesla.com",
                            "confidence": "high",
                            "status": "active",
                            "discovered_at": (datetime.now() - timedelta(days=random.randint(1, 30))).isoformat(),
                            "source_task_type": "domain_enum",
                            "tags": ["main_domain", "in_scope", "automotive"],
                            "metadata": {"ip": "**************", "cdn": "Tesla CDN"}
                        },
                        {
                            "platform_id": platform_id,
                            "project_id": project_id_str,
                            "asset_type": "subdomain",
                            "asset_value": "supercharger.tesla.com",
                            "asset_host": "supercharger.tesla.com",
                            "confidence": "high",
                            "status": "active",
                            "discovered_at": (datetime.now() - timedelta(days=random.randint(1, 30))).isoformat(),
                            "source_task_type": "subdomain_enum",
                            "tags": ["charging", "in_scope", "iot"],
                            "metadata": {"services": ["HTTPS", "IoT_API"], "status_code": 200}
                        }
                    ]
                
                elif "Microsoft Edge" in project_name:
                    project_assets = [
                        {
                            "platform_id": platform_id,
                            "project_id": project_id_str,
                            "asset_type": "domain",
                            "asset_value": "microsoftedge.com",
                            "asset_host": "microsoftedge.com",
                            "confidence": "high",
                            "status": "active",
                            "discovered_at": (datetime.now() - timedelta(days=random.randint(1, 30))).isoformat(),
                            "source_task_type": "domain_enum",
                            "tags": ["main_domain", "in_scope", "browser"],
                            "metadata": {"ip": "************", "cdn": "Microsoft CDN"}
                        },
                        {
                            "platform_id": platform_id,
                            "project_id": project_id_str,
                            "asset_type": "subdomain",
                            "asset_value": "dev.microsoftedge.com",
                            "asset_host": "dev.microsoftedge.com",
                            "confidence": "medium",
                            "status": "active",
                            "discovered_at": (datetime.now() - timedelta(days=random.randint(1, 30))).isoformat(),
                            "source_task_type": "subdomain_enum",
                            "tags": ["dev", "in_scope", "browser"],
                            "metadata": {"services": ["HTTPS", "DEV_API"], "status_code": 200}
                        }
                    ]
                
                elif "Office 365" in project_name:
                    project_assets = [
                        {
                            "platform_id": platform_id,
                            "project_id": project_id_str,
                            "asset_type": "domain",
                            "asset_value": "office.com",
                            "asset_host": "office.com",
                            "confidence": "high",
                            "status": "active",
                            "discovered_at": (datetime.now() - timedelta(days=random.randint(1, 30))).isoformat(),
                            "source_task_type": "domain_enum",
                            "tags": ["main_domain", "in_scope", "productivity"],
                            "metadata": {"ip": "*************", "cdn": "Microsoft CDN"}
                        },
                        {
                            "platform_id": platform_id,
                            "project_id": project_id_str,
                            "asset_type": "subdomain",
                            "asset_value": "graph.microsoft.com",
                            "asset_host": "graph.microsoft.com",
                            "confidence": "high",
                            "status": "active",
                            "discovered_at": (datetime.now() - timedelta(days=random.randint(1, 30))).isoformat(),
                            "source_task_type": "subdomain_enum",
                            "tags": ["api", "in_scope", "graph"],
                            "metadata": {"services": ["HTTPS", "Graph_API"], "status_code": 200}
                        }
                    ]
                
                elif "Azure" in project_name:
                    project_assets = [
                        {
                            "platform_id": platform_id,
                            "project_id": project_id_str,
                            "asset_type": "domain",
                            "asset_value": "azure.com",
                            "asset_host": "azure.com",
                            "confidence": "high",
                            "status": "active",
                            "discovered_at": (datetime.now() - timedelta(days=random.randint(1, 30))).isoformat(),
                            "source_task_type": "domain_enum",
                            "tags": ["main_domain", "in_scope", "cloud"],
                            "metadata": {"ip": "************", "cdn": "Microsoft CDN"}
                        },
                        {
                            "platform_id": platform_id,
                            "project_id": project_id_str,
                            "asset_type": "subdomain",
                            "asset_value": "portal.azure.com",
                            "asset_host": "portal.azure.com",
                            "confidence": "high",
                            "status": "active",
                            "discovered_at": (datetime.now() - timedelta(days=random.randint(1, 30))).isoformat(),
                            "source_task_type": "subdomain_enum",
                            "tags": ["portal", "in_scope", "cloud"],
                            "metadata": {"services": ["HTTPS", "Portal_API"], "status_code": 200}
                        }
                    ]
                
                elif "Google Search" in project_name:
                    project_assets = [
                        {
                            "platform_id": platform_id,
                            "project_id": project_id_str,
                            "asset_type": "domain",
                            "asset_value": "google.com",
                            "asset_host": "google.com",
                            "confidence": "high",
                            "status": "active",
                            "discovered_at": (datetime.now() - timedelta(days=random.randint(1, 30))).isoformat(),
                            "source_task_type": "domain_enum",
                            "tags": ["main_domain", "in_scope", "search"],
                            "metadata": {"ip": "**************", "cdn": "Google CDN"}
                        },
                        {
                            "platform_id": platform_id,
                            "project_id": project_id_str,
                            "asset_type": "subdomain",
                            "asset_value": "www.google.com",
                            "asset_host": "www.google.com",
                            "confidence": "high",
                            "status": "active",
                            "discovered_at": (datetime.now() - timedelta(days=random.randint(1, 30))).isoformat(),
                            "source_task_type": "subdomain_enum",
                            "tags": ["www", "in_scope", "search"],
                            "metadata": {"services": ["HTTPS", "Search_API"], "status_code": 200}
                        }
                    ]
                
                elif "Facebook" in project_name:
                    project_assets = [
                        {
                            "platform_id": platform_id,
                            "project_id": project_id_str,
                            "asset_type": "domain",
                            "asset_value": "facebook.com",
                            "asset_host": "facebook.com",
                            "confidence": "high",
                            "status": "active",
                            "discovered_at": (datetime.now() - timedelta(days=random.randint(1, 30))).isoformat(),
                            "source_task_type": "domain_enum",
                            "tags": ["main_domain", "in_scope", "social"],
                            "metadata": {"ip": "***********", "cdn": "Facebook CDN"}
                        },
                        {
                            "platform_id": platform_id,
                            "project_id": project_id_str,
                            "asset_type": "subdomain",
                            "asset_value": "api.facebook.com",
                            "asset_host": "api.facebook.com",
                            "confidence": "high",
                            "status": "active",
                            "discovered_at": (datetime.now() - timedelta(days=random.randint(1, 30))).isoformat(),
                            "source_task_type": "subdomain_enum",
                            "tags": ["api", "in_scope", "critical"],
                            "metadata": {"services": ["HTTPS", "Graph_API"], "status_code": 200}
                        }
                    ]
                
                elif "Instagram" in project_name:
                    project_assets = [
                        {
                            "platform_id": platform_id,
                            "project_id": project_id_str,
                            "asset_type": "domain",
                            "asset_value": "instagram.com",
                            "asset_host": "instagram.com",
                            "confidence": "high",
                            "status": "active",
                            "discovered_at": (datetime.now() - timedelta(days=random.randint(1, 30))).isoformat(),
                            "source_task_type": "domain_enum",
                            "tags": ["main_domain", "in_scope", "social"],
                            "metadata": {"ip": "**************", "cdn": "Facebook CDN"}
                        },
                        {
                            "platform_id": platform_id,
                            "project_id": project_id_str,
                            "asset_type": "subdomain",
                            "asset_value": "api.instagram.com",
                            "asset_host": "api.instagram.com",
                            "confidence": "high",
                            "status": "active",
                            "discovered_at": (datetime.now() - timedelta(days=random.randint(1, 30))).isoformat(),
                            "source_task_type": "subdomain_enum",
                            "tags": ["api", "in_scope", "social"],
                            "metadata": {"services": ["HTTPS", "Instagram_API"], "status_code": 200}
                        }
                    ]
                
                else:
                    # 通用资产模板
                    domain_name = f"example-{project_id_str[:8]}.com"
                    project_assets = [
                        {
                            "platform_id": platform_id,
                            "project_id": project_id_str,
                            "asset_type": "domain",
                            "asset_value": domain_name,
                            "asset_host": domain_name,
                            "confidence": random.choice(confidence_levels),
                            "status": "active",
                            "discovered_at": (datetime.now() - timedelta(days=random.randint(1, 30))).isoformat(),
                            "source_task_type": "domain_enum",
                            "tags": ["main_domain", "in_scope"],
                            "metadata": {"ip": f"192.168.{random.randint(1, 255)}.{random.randint(1, 255)}"}
                        }
                    ]
                
                # 添加项目资产到总列表
                all_assets.extend(project_assets)
            
            # 批量创建资产
            print(f"创建 {len(all_assets)} 个资产...")
            created_assets = []
            for asset_data in all_assets:
                asset = DynamicEntity(
                    model_type_id=enhanced_asset_model.id,
                    entity_data=asset_data
                )
                session.add(asset)
                created_assets.append(asset)
            
            await session.commit()
            print(f"✅ 成功创建了 {len(created_assets)} 个资产")
            
            # 统计资产分布
            print("\n📊 资产分布统计：")
            asset_stats = {}
            for asset in created_assets:
                project_id = asset.entity_data.get('project_id')
                if project_id not in asset_stats:
                    asset_stats[project_id] = 0
                asset_stats[project_id] += 1
            
            for project in projects:
                project_id_str = str(project.id)
                count = asset_stats.get(project_id_str, 0)
                if count > 0:
                    print(f"  - {project.entity_data.get('name', 'Unknown')}: {count} 个资产")
            
            print(f"\n🎉 完成！总共创建了 {len(created_assets)} 个资产")
            
        except Exception as e:
            print(f"创建资产时发生错误: {e}")
            await session.rollback()

if __name__ == "__main__":
    asyncio.run(create_realistic_assets())