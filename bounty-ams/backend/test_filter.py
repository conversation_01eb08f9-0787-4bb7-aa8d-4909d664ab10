#!/usr/bin/env python3
"""
测试筛选功能
"""

import asyncio
import aiohttp

async def test_filter():
    base_url = "http://localhost:8000"
    
    async with aiohttp.ClientSession() as session:
        # 1. 登录
        login_data = {"username": "admin", "password": "password"}
        async with session.post(f"{base_url}/api/auth/login", json=login_data) as response:
            auth_data = await response.json()
            token = auth_data.get('access_token')
            
        headers = {"Authorization": f"Bearer {token}"}
        
        # 2. 测试无筛选
        print("测试无筛选:")
        async with session.get(f"{base_url}/api/dynamic-models/entities?model_type_id=e489cf55-09c3-48fe-a14b-93f3e63b3c8a", headers=headers) as response:
            data = await response.json()
            print(f"  总项目数: {len(data)}")
            
        # 3. 测试平台筛选
        print("\n测试平台筛选:")
        platform_id = "5eb256a3-96e6-4576-a113-ee846b61991b"
        async with session.get(f"{base_url}/api/dynamic-models/entities?model_type_id=e489cf55-09c3-48fe-a14b-93f3e63b3c8a&platform_id={platform_id}", headers=headers) as response:
            data = await response.json()
            print(f"  筛选后项目数: {len(data)}")
            if data:
                platform_ids = [item['entity_data']['platform_id'] for item in data[:3]]
                print(f"  前3个platform_id: {platform_ids}")

if __name__ == "__main__":
    asyncio.run(test_filter())