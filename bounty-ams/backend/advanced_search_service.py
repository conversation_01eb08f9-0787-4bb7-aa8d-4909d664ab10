"""
增强的Elasticsearch搜索服务
提供深度集成的搜索、聚合和数据分析功能
"""

from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta
from elasticsearch import AsyncElasticsearch
from elasticsearch.exceptions import NotFoundError, RequestError
import json
import logging
from dataclasses import dataclass

from query_builder import QueryBuilder, AdvancedQueryTemplates, QueryValidator
from data_pipeline import AssetDataProcessor

logger = logging.getLogger(__name__)

@dataclass
class SearchQuery:
    """搜索查询参数"""
    query: str = ""
    filters: Dict[str, Any] = None
    platform_id: Optional[str] = None
    project_id: Optional[str] = None
    asset_types: List[str] = None
    confidence: Optional[str] = None
    date_from: Optional[datetime] = None
    date_to: Optional[datetime] = None
    tags: List[str] = None
    sort_by: str = "discovered_at"
    sort_order: str = "desc"
    page: int = 1
    size: int = 20
    include_aggregations: bool = True

@dataclass
class SearchResult:
    """搜索结果"""
    total: int
    hits: List[Dict[str, Any]]
    aggregations: Dict[str, Any]
    query_time: float
    suggestions: List[str] = None

class AdvancedAssetSearchService:
    """高级资产搜索服务"""
    
    def __init__(self, es_client: AsyncElasticsearch):
        self.es = es_client
        self.index_patterns = {
            "assets": "enhanced_asset-*",
            "platforms": "dynamic_platform-*",
            "projects": "dynamic_project-*",
            "vulnerabilities": "dynamic_vulnerability_kb-*"
        }
    
    async def search_assets(self, search_query: SearchQuery) -> SearchResult:
        """
        高级资产搜索
        支持复杂查询、过滤、聚合分析
        """
        start_time = datetime.now()
        
        # 构建ES查询
        es_query = await self._build_search_query(search_query)
        
        try:
            response = await self.es.search(
                index=self.index_patterns["assets"],
                body=es_query,
                size=search_query.size,
                from_=(search_query.page - 1) * search_query.size,
                timeout="30s"
            )
            
            # 处理搜索结果
            total = response["hits"]["total"]["value"]
            hits = [
                {
                    "id": hit["_id"],
                    "source": hit["_source"],
                    "score": hit["_score"],
                    "highlight": hit.get("highlight", {})
                }
                for hit in response["hits"]["hits"]
            ]
            
            # 处理聚合结果
            aggregations = {}
            if search_query.include_aggregations and "aggregations" in response:
                aggregations = await self._process_aggregations(response["aggregations"])
            
            query_time = (datetime.now() - start_time).total_seconds()
            
            return SearchResult(
                total=total,
                hits=hits,
                aggregations=aggregations,
                query_time=query_time
            )
            
        except Exception as e:
            logger.error(f"搜索失败: {e}")
            raise
    
    async def _build_search_query(self, search_query: SearchQuery) -> Dict[str, Any]:
        """构建ES查询DSL - 使用QueryBuilder"""
        
        # 使用QueryBuilder构建查询
        builder = QueryBuilder()
        
        # 1. 全文搜索
        if search_query.query:
            builder.text_search(
                query=search_query.query,
                fields=[
                    "entity_data.asset_value^3",
                    "entity_data.asset_host^2",
                    "entity_data.tags^1.5",
                    "entity_data.metadata.*"
                ]
            )
        
        # 2. 平台和项目过滤
        if search_query.platform_id:
            builder.exact_match("entity_data.platform_id", search_query.platform_id)
        
        if search_query.project_id:
            builder.exact_match("entity_data.project_id", search_query.project_id)
        
        # 3. 资产类型过滤
        if search_query.asset_types:
            builder.exact_match("entity_data.asset_type", search_query.asset_types)
        
        # 4. 置信度过滤
        if search_query.confidence:
            builder.exact_match("entity_data.confidence", search_query.confidence)
        
        # 5. 日期范围过滤
        if search_query.date_from or search_query.date_to:
            builder.date_range(
                "entity_data.discovered_at",
                start=search_query.date_from,
                end=search_query.date_to
            )
        
        # 6. 标签过滤
        if search_query.tags:
            builder.exact_match("entity_data.tags", search_query.tags)
        
        # 7. 额外过滤器
        if search_query.filters:
            for field, value in search_query.filters.items():
                builder.exact_match(f"entity_data.{field}", value)
        
        # 8. 排序
        if search_query.sort_by:
            sort_field = f"entity_data.{search_query.sort_by}"
            builder.add_sort(sort_field, search_query.sort_order)
        
        # 9. 聚合查询
        if search_query.include_aggregations:
            await self._add_intelligent_aggregations(builder)
        
        # 10. 高亮
        builder.add_highlight({
            "entity_data.asset_value": {"fragment_size": 150},
            "entity_data.asset_host": {"fragment_size": 150},
            "entity_data.tags": {"fragment_size": 100}
        })
        
        # 构建最终查询
        query = builder.build(
            size=search_query.size,
            from_=(search_query.page - 1) * search_query.size
        )
        
        # 验证查询安全性
        is_valid, error_msg = QueryValidator.validate_query(query)
        if not is_valid:
            raise ValueError(f"查询验证失败: {error_msg}")
        
        return query
    
    async def _add_intelligent_aggregations(self, builder: QueryBuilder) -> None:
        """添加智能聚合功能"""
        
        # 基础统计聚合
        builder.terms_aggregation("platforms", "entity_data.platform_id", size=10)
        builder.terms_aggregation("projects", "entity_data.project_id", size=20)
        builder.terms_aggregation("asset_types", "entity_data.asset_type", size=10)
        builder.terms_aggregation("confidence_levels", "entity_data.confidence", size=5)
        builder.terms_aggregation("status_distribution", "entity_data.status", size=10)
        builder.terms_aggregation("discovery_sources", "entity_data.source_task_type", size=10)
        builder.terms_aggregation("tags_cloud", "entity_data.tags", size=50)
        
        # 时间序列聚合
        builder.date_histogram("discovery_timeline", "entity_data.discovered_at", "1d")
        builder.date_histogram("weekly_trend", "entity_data.discovered_at", "1w")
        builder.date_histogram("monthly_trend", "entity_data.discovered_at", "1M")
        
        # 高级嵌套聚合 - 平台项目关系分析
        builder.add_aggregation("platform_project_analysis", {
            "terms": {
                "field": "entity_data.platform_id",
                "size": 10
            },
            "aggs": {
                "projects": {
                    "terms": {
                        "field": "entity_data.project_id",
                        "size": 5
                    },
                    "aggs": {
                        "asset_types": {
                            "terms": {
                                "field": "entity_data.asset_type",
                                "size": 5
                            }
                        },
                        "confidence_dist": {
                            "terms": {
                                "field": "entity_data.confidence",
                                "size": 3
                            }
                        }
                    }
                },
                "total_assets": {
                    "cardinality": {
                        "field": "entity_data.asset_value.keyword"
                    }
                }
            }
        })
        
        # 风险评估聚合
        builder.add_aggregation("risk_analysis", {
            "filters": {
                "filters": {
                    "high_risk": {
                        "bool": {
                            "should": [
                                {"terms": {"entity_data.tags": ["vulnerable", "critical", "exposed"]}},
                                {"wildcard": {"entity_data.asset_value": "*admin*"}},
                                {"wildcard": {"entity_data.asset_value": "*api*"}},
                                {"term": {"entity_data.confidence": "low"}}
                            ],
                            "minimum_should_match": 1
                        }
                    },
                    "medium_risk": {
                        "bool": {
                            "should": [
                                {"terms": {"entity_data.tags": ["in_scope", "testing"]}},
                                {"wildcard": {"entity_data.asset_value": "*dev*"}},
                                {"wildcard": {"entity_data.asset_value": "*test*"}}
                            ],
                            "minimum_should_match": 1
                        }
                    },
                    "low_risk": {
                        "bool": {
                            "should": [
                                {"terms": {"entity_data.tags": ["verified", "safe"]}},
                                {"term": {"entity_data.confidence": "high"}}
                            ],
                            "minimum_should_match": 1
                        }
                    }
                }
            },
            "aggs": {
                "risk_by_platform": {
                    "terms": {
                        "field": "entity_data.platform_id",
                        "size": 10
                    }
                }
            }
        })
        
        # 技术栈分析聚合
        builder.add_aggregation("technology_analysis", {
            "filters": {
                "filters": {
                    "web_tech": {
                        "bool": {
                            "should": [
                                {"wildcard": {"entity_data.metadata.server": "*nginx*"}},
                                {"wildcard": {"entity_data.metadata.server": "*apache*"}},
                                {"wildcard": {"entity_data.metadata.technology": "*php*"}},
                                {"wildcard": {"entity_data.metadata.technology": "*nodejs*"}}
                            ]
                        }
                    },
                    "cms_tech": {
                        "bool": {
                            "should": [
                                {"wildcard": {"entity_data.metadata.cms": "*wordpress*"}},
                                {"wildcard": {"entity_data.metadata.cms": "*drupal*"}},
                                {"wildcard": {"entity_data.asset_value": "*wp-*"}}
                            ]
                        }
                    },
                    "cloud_services": {
                        "bool": {
                            "should": [
                                {"wildcard": {"entity_data.asset_value": "*.amazonaws.com"}},
                                {"wildcard": {"entity_data.asset_value": "*.azure.com"}},
                                {"wildcard": {"entity_data.asset_value": "*.googleusercontent.com"}}
                            ]
                        }
                    }
                }
            }
        })
        
        # 资产发现效率分析
        builder.add_aggregation("discovery_efficiency", {
            "terms": {
                "field": "entity_data.source_task_type",
                "size": 10
            },
            "aggs": {
                "daily_discovery": {
                    "date_histogram": {
                        "field": "entity_data.discovered_at",
                        "calendar_interval": "1d",
                        "min_doc_count": 1
                    }
                },
                "confidence_quality": {
                    "terms": {
                        "field": "entity_data.confidence",
                        "size": 3
                    }
                },
                "avg_discovery_time": {
                    "avg": {
                        "script": {
                            "source": "Math.random() * 1000"  # 模拟发现时间
                        }
                    }
                }
            }
        })
        
        # 资产相关性分析
        builder.add_aggregation("asset_correlation", {
            "terms": {
                "field": "entity_data.asset_host",
                "size": 20,
                "min_doc_count": 2
            },
            "aggs": {
                "related_assets": {
                    "terms": {
                        "field": "entity_data.asset_type",
                        "size": 5
                    }
                },
                "port_distribution": {
                    "terms": {
                        "script": {
                            "source": """
                            String value = doc['entity_data.asset_value'].value;
                            if (value != null && value.contains(':')) {
                                String[] parts = value.split(':');
                                if (parts.length > 1) {
                                    return parts[parts.length-1];
                                }
                            }
                            return 'unknown';
                            """
                        },
                        "size": 10
                    }
                }
            }
        })
    
    async def _process_aggregations(self, aggs: Dict[str, Any]) -> Dict[str, Any]:
        """处理聚合结果"""
        processed = {}
        
        for agg_name, agg_data in aggs.items():
            if "buckets" in agg_data:
                processed[agg_name] = [
                    {
                        "key": bucket["key"],
                        "doc_count": bucket["doc_count"],
                        **{k: v for k, v in bucket.items() if k not in ["key", "doc_count"]}
                    }
                    for bucket in agg_data["buckets"]
                ]
            else:
                processed[agg_name] = agg_data
        
        return processed
    
    async def advanced_security_analysis(self, platform_id: str = None, risk_level: str = "high") -> SearchResult:
        """
        高级安全分析搜索
        使用预定义模板进行安全风险分析
        """
        start_time = datetime.now()
        
        try:
            # 使用预定义安全分析模板
            builder = AdvancedQueryTemplates.asset_security_analysis(platform_id, risk_level)
            query = builder.build(size=100, from_=0)
            
            # 验证查询
            is_valid, error_msg = QueryValidator.validate_query(query)
            if not is_valid:
                raise ValueError(f"安全分析查询验证失败: {error_msg}")
            
            response = await self.es.search(
                index=self.index_patterns["assets"],
                body=query,
                timeout="30s"
            )
            
            # 处理搜索结果
            total = response["hits"]["total"]["value"]
            hits = [
                {
                    "id": hit["_id"],
                    "source": hit["_source"],
                    "score": hit["_score"],
                    "risk_score": self._calculate_risk_score(hit["_source"]),
                    "security_tags": self._extract_security_tags(hit["_source"])
                }
                for hit in response["hits"]["hits"]
            ]
            
            # 处理聚合结果
            aggregations = {}
            if "aggregations" in response:
                aggregations = await self._process_security_aggregations(response["aggregations"])
            
            query_time = (datetime.now() - start_time).total_seconds()
            
            return SearchResult(
                total=total,
                hits=hits,
                aggregations=aggregations,
                query_time=query_time
            )
            
        except Exception as e:
            logger.error(f"安全分析搜索失败: {e}")
            raise
    
    def _calculate_risk_score(self, asset_source: Dict[str, Any]) -> float:
        """
        计算资产风险评分
        """
        score = 0.0
        entity_data = asset_source.get("entity_data", {})
        
        # 基于置信度的风险评分
        confidence = entity_data.get("confidence", "medium")
        if confidence == "low":
            score += 0.4
        elif confidence == "medium":
            score += 0.2
        
        # 基于标签的风险评分
        tags = entity_data.get("tags", [])
        risk_tags = {"vulnerable": 0.5, "critical": 0.8, "exposed": 0.6, "admin": 0.3, "api": 0.2}
        for tag in tags:
            score += risk_tags.get(tag, 0)
        
        # 基于资产值的风险评分
        asset_value = entity_data.get("asset_value", "").lower()
        risk_patterns = ["admin", "api", "test", "dev", "staging", "internal"]
        for pattern in risk_patterns:
            if pattern in asset_value:
                score += 0.15
        
        return min(score, 1.0)
    
    def _extract_security_tags(self, asset_source: Dict[str, Any]) -> List[str]:
        """
        提取安全相关标签
        """
        entity_data = asset_source.get("entity_data", {})
        tags = entity_data.get("tags", [])
        asset_value = entity_data.get("asset_value", "").lower()
        
        security_tags = []
        
        # 从现有标签中提取安全标签
        security_keywords = ["vulnerable", "critical", "exposed", "secure", "verified", "high_risk"]
        security_tags.extend([tag for tag in tags if any(keyword in tag for keyword in security_keywords)])
        
        # 基于资产值自动生成安全标签
        if "admin" in asset_value:
            security_tags.append("admin_interface")
        if "api" in asset_value:
            security_tags.append("api_endpoint")
        if any(env in asset_value for env in ["test", "dev", "staging"]):
            security_tags.append("non_production")
        
        return list(set(security_tags))
    
    async def _process_security_aggregations(self, aggs: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理安全分析聚合结果
        """
        processed = await self._process_aggregations(aggs)
        
        # 计算总体风险评级
        if "risk_by_type" in processed:
            risk_distribution = processed["risk_by_type"]
            total_high_risk = sum(bucket["doc_count"] for bucket in risk_distribution if "high" in str(bucket["key"]).lower())
            total_assets = sum(bucket["doc_count"] for bucket in risk_distribution)
            
            processed["overall_risk_level"] = {
                "high_risk_assets": total_high_risk,
                "total_assets": total_assets,
                "risk_percentage": (total_high_risk / total_assets * 100) if total_assets > 0 else 0,
                "risk_level": "high" if (total_high_risk / total_assets) > 0.3 else "medium" if (total_high_risk / total_assets) > 0.1 else "low"
            }
        
        return processed

    async def get_asset_insights(self, platform_id: str = None, project_id: str = None) -> Dict[str, Any]:
        """
        获取资产洞察分析
        """
        query = {
            "size": 0,
            "query": {
                "bool": {
                    "filter": []
                }
            },
            "aggs": {
                "total_assets": {
                    "cardinality": {
                        "field": "entity_data.asset_value.keyword"
                    }
                },
                "asset_types_breakdown": {
                    "terms": {
                        "field": "entity_data.asset_type",
                        "size": 20
                    },
                    "aggs": {
                        "confidence_breakdown": {
                            "terms": {
                                "field": "entity_data.confidence",
                                "size": 5
                            }
                        }
                    }
                },
                "discovery_trend": {
                    "date_histogram": {
                        "field": "entity_data.discovered_at",
                        "calendar_interval": "week",
                        "min_doc_count": 1
                    }
                },
                "platform_coverage": {
                    "terms": {
                        "field": "entity_data.platform_id",
                        "size": 10
                    },
                    "aggs": {
                        "projects": {
                            "terms": {
                                "field": "entity_data.project_id",
                                "size": 5
                            }
                        }
                    }
                },
                "risk_assessment": {
                    "filters": {
                        "filters": {
                            "high_risk": {
                                "bool": {
                                    "should": [
                                        {"term": {"entity_data.tags": "vulnerable"}},
                                        {"term": {"entity_data.tags": "critical"}}
                                    ]
                                }
                            },
                            "medium_risk": {
                                "bool": {
                                    "should": [
                                        {"term": {"entity_data.confidence": "high"}},
                                        {"term": {"entity_data.tags": "in_scope"}}
                                    ]
                                }
                            },
                            "low_risk": {
                                "bool": {
                                    "should": [
                                        {"term": {"entity_data.confidence": "low"}},
                                        {"term": {"entity_data.tags": "tested"}}
                                    ]
                                }
                            }
                        }
                    }
                }
            }
        }
        
        # 添加平台/项目过滤
        if platform_id:
            query["query"]["bool"]["filter"].append({
                "term": {"entity_data.platform_id": platform_id}
            })
        
        if project_id:
            query["query"]["bool"]["filter"].append({
                "term": {"entity_data.project_id": project_id}
            })
        
        try:
            response = await self.es.search(
                index=self.index_patterns["assets"],
                body=query,
                timeout="30s"
            )
            
            return await self._process_aggregations(response["aggregations"])
            
        except Exception as e:
            logger.error(f"获取资产洞察失败: {e}")
            raise
    
    async def search_similar_assets(self, asset_id: str, size: int = 10) -> List[Dict[str, Any]]:
        """
        搜索相似资产
        """
        try:
            # 先获取原始资产
            original_asset = await self.es.get(
                index=self.index_patterns["assets"],
                id=asset_id
            )
            
            asset_data = original_asset["_source"]["entity_data"]
            
            # 构建相似性查询
            query = {
                "size": size,
                "query": {
                    "more_like_this": {
                        "fields": ["entity_data.asset_value", "entity_data.asset_host", "entity_data.tags"],
                        "like": [
                            {
                                "_index": original_asset["_index"],
                                "_id": asset_id
                            }
                        ],
                        "min_term_freq": 1,
                        "max_query_terms": 12
                    }
                }
            }
            
            response = await self.es.search(
                index=self.index_patterns["assets"],
                body=query
            )
            
            return [
                {
                    "id": hit["_id"],
                    "source": hit["_source"],
                    "score": hit["_score"]
                }
                for hit in response["hits"]["hits"]
            ]
            
        except Exception as e:
            logger.error(f"搜索相似资产失败: {e}")
            raise
    
    async def get_search_suggestions(self, query: str, field: str = "asset_value") -> List[str]:
        """
        获取搜索建议
        """
        try:
            es_query = {
                "suggest": {
                    "asset_suggest": {
                        "text": query,
                        "completion": {
                            "field": f"entity_data.{field}_suggest",
                            "size": 10,
                            "skip_duplicates": True
                        }
                    }
                }
            }
            
            response = await self.es.search(
                index=self.index_patterns["assets"],
                body=es_query
            )
            
            suggestions = []
            for suggestion in response["suggest"]["asset_suggest"][0]["options"]:
                suggestions.append(suggestion["text"])
            
            return suggestions
            
        except Exception as e:
            logger.error(f"获取搜索建议失败: {e}")
            return []
    
    async def bulk_update_assets(self, updates: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        批量更新资产
        """
        body = []
        
        for update in updates:
            # 更新操作
            body.append({
                "update": {
                    "_index": f"enhanced_asset-{datetime.now().strftime('%Y-%m')}",
                    "_id": update["id"]
                }
            })
            
            # 更新数据
            body.append({
                "doc": update["data"],
                "doc_as_upsert": True
            })
        
        try:
            response = await self.es.bulk(body=body)
            
            return {
                "updated": len([item for item in response["items"] if item["update"]["result"] == "updated"]),
                "created": len([item for item in response["items"] if item["update"]["result"] == "created"]),
                "errors": [item for item in response["items"] if "error" in item["update"]]
            }
            
        except Exception as e:
            logger.error(f"批量更新失败: {e}")
            raise
    
    async def create_asset_index_template(self):
        """
        创建资产索引模板
        """
        template = {
            "index_patterns": ["enhanced_asset-*"],
            "settings": {
                "number_of_shards": 3,
                "number_of_replicas": 1,
                "analysis": {
                    "analyzer": {
                        "asset_analyzer": {
                            "type": "custom",
                            "tokenizer": "standard",
                            "filter": ["lowercase", "stop", "snowball"]
                        }
                    }
                }
            },
            "mappings": {
                "properties": {
                    "model_type_id": {"type": "keyword"},
                    "created_at": {"type": "date"},
                    "updated_at": {"type": "date"},
                    "entity_data": {
                        "properties": {
                            "platform_id": {"type": "keyword"},
                            "project_id": {"type": "keyword"},
                            "asset_type": {"type": "keyword"},
                            "asset_value": {
                                "type": "text",
                                "analyzer": "asset_analyzer",
                                "fields": {
                                    "keyword": {"type": "keyword"},
                                    "suggest": {
                                        "type": "completion",
                                        "analyzer": "simple"
                                    }
                                }
                            },
                            "asset_host": {
                                "type": "text",
                                "analyzer": "asset_analyzer",
                                "fields": {
                                    "keyword": {"type": "keyword"}
                                }
                            },
                            "confidence": {"type": "keyword"},
                            "status": {"type": "keyword"},
                            "discovered_at": {"type": "date"},
                            "source_task_type": {"type": "keyword"},
                            "source_task_id": {"type": "keyword"},
                            "tags": {"type": "keyword"},
                            "metadata": {"type": "object", "dynamic": True}
                        }
                    }
                }
            }
        }
        
        try:
            await self.es.indices.put_template(
                name="enhanced_asset_template",
                body=template
            )
            logger.info("资产索引模板创建成功")
            
        except Exception as e:
            logger.error(f"创建索引模板失败: {e}")
            raise


class AssetDataProcessor:
    """资产数据处理器 - 集成数据清洗管道"""
    
    def __init__(self, search_service: AdvancedAssetSearchService):
        self.search_service = search_service
        # 集成专业数据处理管道
        from data_pipeline import AssetDataProcessor as AdvancedProcessor
        self.advanced_processor = AdvancedProcessor(search_service)
    
    async def clean_and_normalize_assets(self, raw_assets: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        清洗和标准化资产数据 - 使用高级数据管道
        """
        return await self.advanced_processor.clean_and_normalize_assets(raw_assets)
    
    async def deduplicate_assets(self, assets: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        去重资产 - 使用高级去重算法
        """
        return await self.advanced_processor.deduplicate_assets(assets)
    
    async def correlate_assets(self, assets: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        资产关联分析 - 使用高级关联算法
        """
        return await self.advanced_processor.correlate_assets(assets)
    
    async def process_and_index_assets(self, raw_assets: List[Dict[str, Any]], 
                                     auto_correlate: bool = True) -> Dict[str, Any]:
        """
        完整的资产处理和索引流程
        """
        try:
            # 1. 清洗和标准化
            cleaned_assets = await self.clean_and_normalize_assets(raw_assets)
            logger.info(f"清洗完成: {len(raw_assets)} -> {len(cleaned_assets)}")
            
            # 2. 去重
            unique_assets = await self.deduplicate_assets(cleaned_assets)
            logger.info(f"去重完成: {len(cleaned_assets)} -> {len(unique_assets)}")
            
            # 3. 关联分析（可选）
            correlations = {}
            if auto_correlate and unique_assets:
                correlations = await self.correlate_assets(unique_assets)
                logger.info(f"关联分析完成: 发现 {len(correlations.get('domain_groups', {}))} 个域名组")
            
            # 4. 批量索引到Elasticsearch
            if unique_assets:
                index_result = await self._bulk_index_assets(unique_assets)
                logger.info(f"索引完成: {index_result.get('indexed', 0)} 个资产")
            else:
                index_result = {"indexed": 0, "errors": []}
            
            return {
                "status": "success",
                "processing_summary": {
                    "original_count": len(raw_assets),
                    "cleaned_count": len(cleaned_assets),
                    "unique_count": len(unique_assets),
                    "indexed_count": index_result.get("indexed", 0),
                    "duplicates_removed": len(cleaned_assets) - len(unique_assets),
                    "errors_count": len(index_result.get("errors", []))
                },
                "correlations": correlations,
                "index_result": index_result
            }
            
        except Exception as e:
            logger.error(f"资产处理失败: {e}")
            raise
    
    async def _bulk_index_assets(self, assets: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        批量索引资产到Elasticsearch
        """
        if not assets:
            return {"indexed": 0, "errors": []}
        
        # 准备批量索引数据
        bulk_data = []
        current_month = datetime.now().strftime("%Y-%m")
        
        for asset in assets:
            # 索引操作
            bulk_data.append({
                "index": {
                    "_index": f"enhanced_asset-{current_month}",
                    "_id": self._generate_asset_id(asset)
                }
            })
            
            # 文档数据
            doc_data = {
                "model_type_id": "asset",
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat(),
                "entity_data": asset
            }
            bulk_data.append(doc_data)
        
        try:
            response = await self.search_service.es.bulk(body=bulk_data)
            
            # 处理响应
            indexed_count = 0
            errors = []
            
            for item in response["items"]:
                if "index" in item:
                    if item["index"].get("status") in [200, 201]:
                        indexed_count += 1
                    elif "error" in item["index"]:
                        errors.append(item["index"]["error"])
            
            return {
                "indexed": indexed_count,
                "errors": errors,
                "took": response.get("took", 0)
            }
            
        except Exception as e:
            logger.error(f"批量索引失败: {e}")
            return {"indexed": 0, "errors": [str(e)]}
    
    def _generate_asset_id(self, asset: Dict[str, Any]) -> str:
        """
        生成资产唯一ID
        """
        import hashlib
        
        # 构建唯一标识符
        identifier_parts = [
            asset.get("platform_id", ""),
            asset.get("project_id", ""),
            asset.get("asset_type", ""),
            asset.get("asset_value", "")
        ]
        
        identifier = "|".join(str(part) for part in identifier_parts)
        return hashlib.md5(identifier.encode()).hexdigest()
    
    async def get_processing_statistics(self) -> Dict[str, Any]:
        """
        获取数据处理统计信息
        """
        try:
            # 获取索引统计
            stats = await self.search_service.es.indices.stats(
                index=self.search_service.index_patterns["assets"]
            )
            
            # 获取资产洞察
            insights = await self.search_service.get_asset_insights()
            
            return {
                "index_stats": {
                    "total_docs": stats["_all"]["total"]["docs"]["count"],
                    "total_size": stats["_all"]["total"]["store"]["size_in_bytes"],
                    "indices_count": len(stats["indices"])
                },
                "asset_insights": insights,
                "processing_health": "healthy"
            }
            
        except Exception as e:
            logger.error(f"获取处理统计失败: {e}")
            return {
                "index_stats": {},
                "asset_insights": {},
                "processing_health": "unhealthy",
                "error": str(e)
            }