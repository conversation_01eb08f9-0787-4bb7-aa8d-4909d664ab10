"""
自定义仪表板数据服务
提供针对Elasticsearch的可配置聚合查询功能，支持自定义仪表板组件的数据需求
"""

from elasticsearch import AsyncElasticsearch
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import json
import logging

logger = logging.getLogger(__name__)

class DashboardDataService:
    """仪表板数据服务，支持可配置的ES聚合查询"""
    
    def __init__(self, es_client: AsyncElasticsearch):
        self.es_client = es_client
        self.index_pattern = "enhanced_asset-*"
    
    async def execute_widget_query(self, widget_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行组件查询并返回格式化的数据
        
        Args:
            widget_config: 组件配置，包含查询类型、参数等
            
        Returns:
            格式化的查询结果数据
        """
        query_type = widget_config.get("query_type", "aggregation")
        
        if query_type == "aggregation":
            return await self._execute_aggregation_query(widget_config)
        elif query_type == "search":
            return await self._execute_search_query(widget_config)
        elif query_type == "custom":
            return await self._execute_custom_query(widget_config)
        else:
            raise ValueError(f"不支持的查询类型: {query_type}")
    
    async def _execute_aggregation_query(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """执行聚合查询"""
        agg_type = config.get("aggregation_type", "terms")
        field = config.get("field", "entity_data.asset_type.keyword")
        size = config.get("size", 10)
        time_range = config.get("time_range", "30d")
        
        # 构建基础查询
        query_body = {
            "size": 0,
            "query": self._build_time_range_query(time_range)
        }
        
        # 添加聚合
        if agg_type == "terms":
            query_body["aggs"] = {
                "data": {
                    "terms": {
                        "field": field,
                        "size": size
                    }
                }
            }
        elif agg_type == "date_histogram":
            interval = config.get("interval", "day")
            query_body["aggs"] = {
                "data": {
                    "date_histogram": {
                        "field": field,
                        "calendar_interval": interval,
                        "format": "yyyy-MM-dd"
                    }
                }
            }
        elif agg_type == "stats":
            query_body["aggs"] = {
                "data": {
                    "stats": {
                        "field": field
                    }
                }
            }
        
        # 执行查询
        response = await self.es_client.search(
            index=self.index_pattern,
            body=query_body
        )
        
        # 格式化结果
        return self._format_aggregation_result(response, agg_type, config)
    
    async def _execute_search_query(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """执行搜索查询"""
        query = config.get("query", "*")
        size = config.get("size", 100)
        filters = config.get("filters", {})
        
        # 构建查询
        query_body = {
            "size": size,
            "query": {
                "bool": {
                    "must": []
                }
            }
        }
        
        # 添加主查询
        if query and query != "*":
            query_body["query"]["bool"]["must"].append({
                "multi_match": {
                    "query": query,
                    "fields": [
                        "entity_data.asset_value",
                        "entity_data.asset_host",
                        "entity_data.platform_id"
                    ]
                }
            })
        
        # 添加过滤器
        for filter_field, filter_value in filters.items():
            if filter_value:
                query_body["query"]["bool"]["must"].append({
                    "term": {f"entity_data.{filter_field}.keyword": filter_value}
                })
        
        # 如果没有查询条件，使用match_all
        if not query_body["query"]["bool"]["must"]:
            query_body["query"] = {"match_all": {}}
        
        # 执行查询
        response = await self.es_client.search(
            index=self.index_pattern,
            body=query_body
        )
        
        # 格式化结果
        return self._format_search_result(response, config)
    
    async def _execute_custom_query(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """执行自定义ES查询"""
        custom_query = config.get("custom_query", {})
        
        if not custom_query:
            raise ValueError("自定义查询配置为空")
        
        # 执行查询
        response = await self.es_client.search(
            index=self.index_pattern,
            body=custom_query
        )
        
        return self._format_custom_result(response, config)
    
    def _build_time_range_query(self, time_range: str) -> Dict[str, Any]:
        """构建时间范围查询"""
        if not time_range or time_range == "all":
            return {"match_all": {}}
        
        return {
            "range": {
                "entity_data.discovered_at": {
                    "gte": f"now-{time_range}"
                }
            }
        }
    
    def _format_aggregation_result(self, response: Dict[str, Any], agg_type: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """格式化聚合查询结果"""
        aggs = response.get("aggregations", {})
        data_agg = aggs.get("data", {})
        
        if agg_type == "terms":
            buckets = data_agg.get("buckets", [])
            return {
                "type": "terms",
                "data": [
                    {"name": bucket["key"], "value": bucket["doc_count"]}
                    for bucket in buckets
                ],
                "total": response["hits"]["total"]["value"]
            }
        elif agg_type == "date_histogram":
            buckets = data_agg.get("buckets", [])
            return {
                "type": "time_series",
                "data": [
                    {"date": bucket["key_as_string"], "value": bucket["doc_count"]}
                    for bucket in buckets
                ],
                "total": response["hits"]["total"]["value"]
            }
        elif agg_type == "stats":
            stats = data_agg
            return {
                "type": "statistics",
                "data": {
                    "count": stats.get("count", 0),
                    "min": stats.get("min", 0),
                    "max": stats.get("max", 0),
                    "avg": stats.get("avg", 0),
                    "sum": stats.get("sum", 0)
                },
                "total": response["hits"]["total"]["value"]
            }
        
        return {"type": "unknown", "data": [], "total": 0}
    
    def _format_search_result(self, response: Dict[str, Any], config: Dict[str, Any]) -> Dict[str, Any]:
        """格式化搜索查询结果"""
        hits = response.get("hits", {}).get("hits", [])
        
        formatted_hits = []
        for hit in hits:
            source = hit["_source"]
            entity_data = source.get("entity_data", {})
            
            formatted_hits.append({
                "id": hit["_id"],
                "asset_type": entity_data.get("asset_type"),
                "asset_value": entity_data.get("asset_value"),
                "asset_host": entity_data.get("asset_host"),
                "confidence": entity_data.get("confidence"),
                "status": entity_data.get("status"),
                "platform_id": entity_data.get("platform_id"),
                "project_id": entity_data.get("project_id"),
                "discovered_at": entity_data.get("discovered_at"),
                "tags": entity_data.get("tags", []),
                "score": hit.get("_score", 0)
            })
        
        return {
            "type": "search_results",
            "data": formatted_hits,
            "total": response["hits"]["total"]["value"]
        }
    
    def _format_custom_result(self, response: Dict[str, Any], config: Dict[str, Any]) -> Dict[str, Any]:
        """格式化自定义查询结果"""
        format_type = config.get("result_format", "raw")
        
        if format_type == "raw":
            return {
                "type": "custom_raw",
                "data": response,
                "total": response.get("hits", {}).get("total", {}).get("value", 0)
            }
        elif format_type == "aggregation":
            return self._format_aggregation_result(response, "terms", config)
        elif format_type == "search":
            return self._format_search_result(response, config)
        
        return {"type": "custom", "data": response, "total": 0}
    
    async def get_asset_overview(self) -> Dict[str, Any]:
        """获取资产概览统计"""
        query_body = {
            "size": 0,
            "aggs": {
                "total_assets": {
                    "value_count": {
                        "field": "entity_data.asset_value.keyword"
                    }
                },
                "by_type": {
                    "terms": {
                        "field": "entity_data.asset_type.keyword",
                        "size": 10
                    }
                },
                "by_confidence": {
                    "terms": {
                        "field": "entity_data.confidence.keyword",
                        "size": 5
                    }
                },
                "by_platform": {
                    "terms": {
                        "field": "entity_data.platform_id.keyword",
                        "size": 10
                    }
                },
                "recent_discoveries": {
                    "date_histogram": {
                        "field": "entity_data.discovered_at",
                        "calendar_interval": "day",
                        "format": "yyyy-MM-dd"
                    }
                }
            }
        }
        
        response = await self.es_client.search(
            index=self.index_pattern,
            body=query_body
        )
        
        aggs = response.get("aggregations", {})
        
        return {
            "total_assets": response["hits"]["total"]["value"],
            "asset_types": [
                {"type": bucket["key"], "count": bucket["doc_count"]}
                for bucket in aggs.get("by_type", {}).get("buckets", [])
            ],
            "confidence_levels": [
                {"level": bucket["key"], "count": bucket["doc_count"]}
                for bucket in aggs.get("by_confidence", {}).get("buckets", [])
            ],
            "platforms": [
                {"platform": bucket["key"], "count": bucket["doc_count"]}
                for bucket in aggs.get("by_platform", {}).get("buckets", [])
            ],
            "timeline": [
                {"date": bucket["key_as_string"], "count": bucket["doc_count"]}
                for bucket in aggs.get("recent_discoveries", {}).get("buckets", [])
            ]
        }
    
    async def get_risk_analysis(self) -> Dict[str, Any]:
        """获取风险分析数据"""
        # 这里可以实现复杂的风险评分逻辑
        # 目前返回基础的置信度和资产类型分析
        
        query_body = {
            "size": 0,
            "aggs": {
                "risk_by_type": {
                    "terms": {
                        "field": "entity_data.asset_type.keyword"
                    },
                    "aggs": {
                        "avg_confidence": {
                            "terms": {
                                "field": "entity_data.confidence.keyword"
                            }
                        }
                    }
                },
                "high_risk_assets": {
                    "filter": {
                        "terms": {
                            "entity_data.confidence.keyword": ["high"]
                        }
                    },
                    "aggs": {
                        "by_type": {
                            "terms": {
                                "field": "entity_data.asset_type.keyword"
                            }
                        }
                    }
                }
            }
        }
        
        response = await self.es_client.search(
            index=self.index_pattern,
            body=query_body
        )
        
        return {
            "risk_distribution": response.get("aggregations", {}),
            "total_analyzed": response["hits"]["total"]["value"]
        }

# 预定义的组件配置模板
WIDGET_TEMPLATES = {
    "asset_type_pie": {
        "query_type": "aggregation",
        "aggregation_type": "terms",
        "field": "entity_data.asset_type.keyword",
        "size": 10,
        "time_range": "30d"
    },
    "confidence_distribution": {
        "query_type": "aggregation", 
        "aggregation_type": "terms",
        "field": "entity_data.confidence.keyword",
        "size": 5,
        "time_range": "30d"
    },
    "discovery_timeline": {
        "query_type": "aggregation",
        "aggregation_type": "date_histogram",
        "field": "entity_data.discovered_at",
        "interval": "day",
        "time_range": "30d"
    },
    "platform_distribution": {
        "query_type": "aggregation",
        "aggregation_type": "terms",
        "field": "entity_data.platform_id.keyword",
        "size": 10,
        "time_range": "30d"
    },
    "total_assets_count": {
        "query_type": "aggregation",
        "aggregation_type": "stats",
        "field": "entity_data.asset_value.keyword",
        "time_range": "all"
    }
}