#!/usr/bin/env python3
"""
添加Platform和Project动态模型
通过动态模型系统添加平台和项目管理功能
"""

import sys
import os
import asyncio

# Add backend to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'backend'))

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from models_dynamic import User, ModelType, ModelField, Base
from passlib.context import CryptContext

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# Use sync version for initialization
SYNC_DATABASE_URL = "postgresql://postgres:postgres@localhost:5432/bounty_ams"

def create_platform_project_models(db, admin_user):
    """通过动态模型系统创建Platform和Project模型"""
    
    print("开始创建Platform和Project动态模型...")
    
    # 1. 平台模型 (Platform)
    platform_model = ModelType(
        name="platform",
        display_name="漏洞平台",
        description="漏洞赏金平台管理（HackerOne、MSRC、Bugcrowd等）",
        icon="🌐",
        color="#1E40AF",
        is_active=True,
        is_system=True,
        created_by_user_id=admin_user.id
    )
    db.add(platform_model)
    db.flush()
    
    # 平台字段定义
    platform_fields = [
        ModelField(
            model_type_id=platform_model.id,
            field_name="name",
            field_type="text",
            display_name="平台名称",
            description="平台的唯一标识名称",
            is_required=True,
            is_searchable=True,
            is_filterable=True,
            is_unique=True,
            validation_rules={"pattern": "^[a-z0-9_]+$", "minLength": 2, "maxLength": 50},
            sort_order=0
        ),
        ModelField(
            model_type_id=platform_model.id,
            field_name="display_name",
            field_type="text",
            display_name="显示名称",
            description="平台的显示名称",
            is_required=True,
            is_searchable=True,
            is_filterable=True,
            validation_rules={"minLength": 2, "maxLength": 100},
            sort_order=1
        ),
        ModelField(
            model_type_id=platform_model.id,
            field_name="platform_type",
            field_type="select",
            display_name="平台类型",
            description="平台的类型分类",
            is_required=True,
            is_searchable=False,
            is_filterable=True,
            field_options={
                "options": [
                    {"value": "bug_bounty", "label": "漏洞赏金"},
                    {"value": "vendor_vdp", "label": "厂商VDP"},
                    {"value": "government", "label": "政府部门"},
                    {"value": "private", "label": "私人项目"},
                    {"value": "crowdsourced", "label": "众包平台"},
                    {"value": "other", "label": "其他"}
                ]
            },
            sort_order=2
        ),
        ModelField(
            model_type_id=platform_model.id,
            field_name="website_url",
            field_type="text",
            display_name="官网地址",
            description="平台的官方网站地址",
            is_required=False,
            is_searchable=False,
            is_filterable=False,
            validation_rules={"pattern": "^https?://.*"},
            sort_order=3
        ),
        ModelField(
            model_type_id=platform_model.id,
            field_name="api_base_url",
            field_type="text",
            display_name="API基础地址",
            description="平台API的基础URL",
            is_required=False,
            is_searchable=False,
            is_filterable=False,
            validation_rules={"pattern": "^https?://.*"},
            sort_order=4
        ),
        ModelField(
            model_type_id=platform_model.id,
            field_name="api_config",
            field_type="json",
            display_name="API配置",
            description="API认证和配置信息（JSON格式）",
            is_required=False,
            is_searchable=False,
            is_filterable=False,
            sort_order=5
        ),
        ModelField(
            model_type_id=platform_model.id,
            field_name="description",
            field_type="textarea",
            display_name="平台描述",
            description="平台的详细描述信息",
            is_required=False,
            is_searchable=True,
            is_filterable=False,
            sort_order=6
        ),
        ModelField(
            model_type_id=platform_model.id,
            field_name="status",
            field_type="select",
            display_name="状态",
            description="平台的使用状态",
            is_required=True,
            is_searchable=False,
            is_filterable=True,
            field_options={
                "options": [
                    {"value": "active", "label": "活跃"},
                    {"value": "inactive", "label": "不活跃"},
                    {"value": "maintenance", "label": "维护中"},
                    {"value": "deprecated", "label": "已废弃"}
                ]
            },
            default_value="active",
            sort_order=7
        ),
        ModelField(
            model_type_id=platform_model.id,
            field_name="contact_info",
            field_type="json",
            display_name="联系信息",
            description="平台联系方式（JSON格式）",
            is_required=False,
            is_searchable=False,
            is_filterable=False,
            sort_order=8
        ),
        ModelField(
            model_type_id=platform_model.id,
            field_name="supported_asset_types",
            field_type="multi_select",
            display_name="支持的资产类型",
            description="平台支持的资产类型",
            is_required=False,
            is_searchable=False,
            is_filterable=True,
            field_options={
                "options": [
                    {"value": "domain", "label": "域名"},
                    {"value": "subdomain", "label": "子域名"},
                    {"value": "ip", "label": "IP地址"},
                    {"value": "url", "label": "URL"},
                    {"value": "mobile_app", "label": "移动应用"},
                    {"value": "api", "label": "API端点"},
                    {"value": "source_code", "label": "源代码"}
                ]
            },
            sort_order=9
        )
    ]
    
    for field in platform_fields:
        db.add(field)
    
    # 2. 项目模型 (Project)
    project_model = ModelType(
        name="project",
        display_name="漏洞项目",
        description="具体的漏洞赏金项目或程序管理",
        icon="📁",
        color="#0D9488",
        is_active=True,
        is_system=True,
        created_by_user_id=admin_user.id
    )
    db.add(project_model)
    db.flush()
    
    # 项目字段定义
    project_fields = [
        ModelField(
            model_type_id=project_model.id,
            field_name="name",
            field_type="text",
            display_name="项目名称",
            description="项目的名称或标识",
            is_required=True,
            is_searchable=True,
            is_filterable=True,
            validation_rules={"minLength": 2, "maxLength": 200},
            sort_order=0
        ),
        ModelField(
            model_type_id=project_model.id,
            field_name="platform_id",
            field_type="text",
            display_name="所属平台ID",
            description="项目所属的平台ID（关联到platform模型）",
            is_required=True,
            is_searchable=False,
            is_filterable=True,
            sort_order=1
        ),
        ModelField(
            model_type_id=project_model.id,
            field_name="external_id",
            field_type="text",
            display_name="外部项目ID",
            description="在外部平台中的项目ID",
            is_required=False,
            is_searchable=True,
            is_filterable=True,
            sort_order=2
        ),
        ModelField(
            model_type_id=project_model.id,
            field_name="company_name",
            field_type="text",
            display_name="公司名称",
            description="项目所属的公司或组织名称",
            is_required=False,
            is_searchable=True,
            is_filterable=True,
            validation_rules={"maxLength": 200},
            sort_order=3
        ),
        ModelField(
            model_type_id=project_model.id,
            field_name="program_type",
            field_type="select",
            display_name="项目类型",
            description="漏洞项目的类型",
            is_required=True,
            is_searchable=False,
            is_filterable=True,
            field_options={
                "options": [
                    {"value": "public", "label": "公开项目"},
                    {"value": "private", "label": "私有项目"},
                    {"value": "invite_only", "label": "邀请制"},
                    {"value": "vdp", "label": "漏洞披露"},
                    {"value": "bounty", "label": "赏金项目"}
                ]
            },
            sort_order=4
        ),
        ModelField(
            model_type_id=project_model.id,
            field_name="status",
            field_type="select",
            display_name="项目状态",
            description="项目的当前状态",
            is_required=True,
            is_searchable=False,
            is_filterable=True,
            field_options={
                "options": [
                    {"value": "active", "label": "活跃"},
                    {"value": "paused", "label": "暂停"},
                    {"value": "closed", "label": "关闭"},
                    {"value": "archived", "label": "归档"}
                ]
            },
            default_value="active",
            sort_order=5
        ),
        ModelField(
            model_type_id=project_model.id,
            field_name="scope",
            field_type="json",
            display_name="项目范围",
            description="项目的测试范围定义（JSON格式）",
            is_required=False,
            is_searchable=False,
            is_filterable=False,
            sort_order=6
        ),
        ModelField(
            model_type_id=project_model.id,
            field_name="reward_range",
            field_type="text",
            display_name="奖励范围",
            description="项目的奖励金额范围",
            is_required=False,
            is_searchable=False,
            is_filterable=False,
            validation_rules={"maxLength": 100},
            sort_order=7
        ),
        ModelField(
            model_type_id=project_model.id,
            field_name="description",
            field_type="textarea",
            display_name="项目描述",
            description="项目的详细描述信息",
            is_required=False,
            is_searchable=True,
            is_filterable=False,
            sort_order=8
        ),
        ModelField(
            model_type_id=project_model.id,
            field_name="contact_email",
            field_type="email",
            display_name="联系邮箱",
            description="项目的联系邮箱",
            is_required=False,
            is_searchable=False,
            is_filterable=False,
            sort_order=9
        ),
        ModelField(
            model_type_id=project_model.id,
            field_name="started_at",
            field_type="date",
            display_name="开始日期",
            description="项目开始的日期",
            is_required=False,
            is_searchable=False,
            is_filterable=True,
            sort_order=10
        ),
        ModelField(
            model_type_id=project_model.id,
            field_name="ended_at",
            field_type="date",
            display_name="结束日期",
            description="项目结束的日期",
            is_required=False,
            is_searchable=False,
            is_filterable=True,
            sort_order=11
        ),
        ModelField(
            model_type_id=project_model.id,
            field_name="tags",
            field_type="multi_select",
            display_name="标签",
            description="项目标签分类",
            is_required=False,
            is_searchable=False,
            is_filterable=True,
            field_options={
                "options": [
                    {"value": "web", "label": "Web应用"},
                    {"value": "mobile", "label": "移动应用"},
                    {"value": "api", "label": "API"},
                    {"value": "iot", "label": "物联网"},
                    {"value": "blockchain", "label": "区块链"},
                    {"value": "infrastructure", "label": "基础设施"},
                    {"value": "social_engineering", "label": "社会工程"},
                    {"value": "physical", "label": "物理安全"}
                ]
            },
            sort_order=12
        ),
        ModelField(
            model_type_id=project_model.id,
            field_name="priority",
            field_type="select",
            display_name="优先级",
            description="项目的优先级",
            is_required=False,
            is_searchable=False,
            is_filterable=True,
            field_options={
                "options": [
                    {"value": "high", "label": "高"},
                    {"value": "medium", "label": "中"},
                    {"value": "low", "label": "低"}
                ]
            },
            default_value="medium",
            sort_order=13
        ),
        ModelField(
            model_type_id=project_model.id,
            field_name="notes",
            field_type="textarea",
            display_name="备注",
            description="项目的额外备注信息",
            is_required=False,
            is_searchable=True,
            is_filterable=False,
            sort_order=14
        )
    ]
    
    for field in project_fields:
        db.add(field)
    
    # 3. 更新现有的资产类型，添加platform_id和project_id字段
    # 由于这是动态模型，我们需要为现有的资产类型添加关联字段
    # 这里我们可以创建一个增强版的资产模型
    
    enhanced_asset_model = ModelType(
        name="enhanced_asset",
        display_name="增强资产",
        description="带有平台和项目关联的资产管理",
        icon="🎯",
        color="#DC2626",
        is_active=True,
        is_system=True,
        created_by_user_id=admin_user.id
    )
    db.add(enhanced_asset_model)
    db.flush()
    
    # 增强资产字段定义
    enhanced_asset_fields = [
        ModelField(
            model_type_id=enhanced_asset_model.id,
            field_name="platform_id",
            field_type="text",
            display_name="所属平台ID",
            description="资产所属的平台ID（关联到platform模型）",
            is_required=True,
            is_searchable=False,
            is_filterable=True,
            sort_order=0
        ),
        ModelField(
            model_type_id=enhanced_asset_model.id,
            field_name="project_id",
            field_type="text",
            display_name="所属项目ID",
            description="资产所属的项目ID（关联到project模型）",
            is_required=True,
            is_searchable=False,
            is_filterable=True,
            sort_order=1
        ),
        ModelField(
            model_type_id=enhanced_asset_model.id,
            field_name="asset_type",
            field_type="select",
            display_name="资产类型",
            description="资产的类型分类",
            is_required=True,
            is_searchable=False,
            is_filterable=True,
            field_options={
                "options": [
                    {"value": "domain", "label": "域名"},
                    {"value": "subdomain", "label": "子域名"},
                    {"value": "ip", "label": "IP地址"},
                    {"value": "url", "label": "URL"},
                    {"value": "port", "label": "端口"},
                    {"value": "service", "label": "服务"},
                    {"value": "api_endpoint", "label": "API端点"},
                    {"value": "mobile_app", "label": "移动应用"},
                    {"value": "certificate", "label": "证书"}
                ]
            },
            sort_order=2
        ),
        ModelField(
            model_type_id=enhanced_asset_model.id,
            field_name="asset_value",
            field_type="text",
            display_name="资产值",
            description="资产的具体值（域名、IP、URL等）",
            is_required=True,
            is_searchable=True,
            is_filterable=True,
            validation_rules={"minLength": 1, "maxLength": 500},
            sort_order=3
        ),
        ModelField(
            model_type_id=enhanced_asset_model.id,
            field_name="asset_host",
            field_type="text",
            display_name="主机",
            description="资产的主机信息",
            is_required=False,
            is_searchable=True,
            is_filterable=True,
            sort_order=4
        ),
        ModelField(
            model_type_id=enhanced_asset_model.id,
            field_name="confidence",
            field_type="select",
            display_name="置信度",
            description="资产发现的置信度",
            is_required=False,
            is_searchable=False,
            is_filterable=True,
            field_options={
                "options": [
                    {"value": "high", "label": "高"},
                    {"value": "medium", "label": "中"},
                    {"value": "low", "label": "低"}
                ]
            },
            default_value="medium",
            sort_order=5
        ),
        ModelField(
            model_type_id=enhanced_asset_model.id,
            field_name="status",
            field_type="select",
            display_name="状态",
            description="资产的当前状态",
            is_required=False,
            is_searchable=False,
            is_filterable=True,
            field_options={
                "options": [
                    {"value": "active", "label": "活跃"},
                    {"value": "inactive", "label": "不活跃"},
                    {"value": "verified", "label": "已验证"},
                    {"value": "false_positive", "label": "误报"}
                ]
            },
            default_value="active",
            sort_order=6
        ),
        ModelField(
            model_type_id=enhanced_asset_model.id,
            field_name="discovered_at",
            field_type="datetime",
            display_name="发现时间",
            description="资产被发现的时间",
            is_required=False,
            is_searchable=False,
            is_filterable=True,
            sort_order=7
        ),
        ModelField(
            model_type_id=enhanced_asset_model.id,
            field_name="source_task_type",
            field_type="select",
            display_name="发现来源",
            description="发现资产的任务类型",
            is_required=False,
            is_searchable=False,
            is_filterable=True,
            field_options={
                "options": [
                    {"value": "subdomain_enum", "label": "子域名枚举"},
                    {"value": "port_scan", "label": "端口扫描"},
                    {"value": "web_crawl", "label": "Web爬取"},
                    {"value": "certificate_transparency", "label": "证书透明度"},
                    {"value": "manual", "label": "手动添加"},
                    {"value": "api_import", "label": "API导入"}
                ]
            },
            sort_order=8
        ),
        ModelField(
            model_type_id=enhanced_asset_model.id,
            field_name="source_task_id",
            field_type="text",
            display_name="源任务ID",
            description="发现此资产的任务ID",
            is_required=False,
            is_searchable=True,
            is_filterable=True,
            sort_order=9
        ),
        ModelField(
            model_type_id=enhanced_asset_model.id,
            field_name="tags",
            field_type="multi_select",
            display_name="标签",
            description="资产标签",
            is_required=False,
            is_searchable=False,
            is_filterable=True,
            field_options={
                "options": [
                    {"value": "in_scope", "label": "在范围内"},
                    {"value": "out_of_scope", "label": "超出范围"},
                    {"value": "critical", "label": "关键资产"},
                    {"value": "tested", "label": "已测试"},
                    {"value": "vulnerable", "label": "存在漏洞"},
                    {"value": "protected", "label": "受保护"}
                ]
            },
            sort_order=10
        ),
        ModelField(
            model_type_id=enhanced_asset_model.id,
            field_name="metadata",
            field_type="json",
            display_name="元数据",
            description="资产的额外元数据信息（JSON格式）",
            is_required=False,
            is_searchable=False,
            is_filterable=False,
            sort_order=11
        )
    ]
    
    for field in enhanced_asset_fields:
        db.add(field)
    
    db.commit()
    print("✅ Platform和Project动态模型创建成功!")
    print("- 漏洞平台 (platform)")
    print("- 漏洞项目 (project)")
    print("- 增强资产 (enhanced_asset)")
    print("\n📋 下一步操作:")
    print("1. 在前端Models页面中查看新创建的模型")
    print("2. 创建一些平台实例（如HackerOne、MSRC等）")
    print("3. 为每个平台创建对应的项目")
    print("4. 将资产数据迁移到enhanced_asset模型中")


def create_sample_platform_data(db):
    """创建示例平台和项目数据"""
    print("\n🔧 创建示例平台和项目数据...")
    
    # 这里需要通过动态模型的API来创建数据
    # 由于我们在初始化脚本中，先不创建示例数据
    # 用户可以通过前端界面来创建
    
    print("✅ 可以通过前端界面创建示例数据")
    print("建议的平台数据:")
    print("- HackerOne (bug_bounty)")
    print("- Microsoft MSRC (vendor_vdp)")
    print("- Google VRP (vendor_vdp)")
    print("- Bugcrowd (bug_bounty)")
    print("- Intigriti (bug_bounty)")


def main():
    """主函数"""
    engine = create_engine(SYNC_DATABASE_URL)
    Base.metadata.create_all(bind=engine)
    
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        # 获取管理员用户
        admin_user = db.query(User).filter(User.username == "admin").first()
        if not admin_user:
            print("❌ 管理员用户不存在，请先运行 init_dynamic_models.py")
            return
        
        # 检查是否已经创建过Platform模型
        existing_platform = db.query(ModelType).filter(ModelType.name == "platform").first()
        if existing_platform:
            print("⚠️  Platform模型已存在，跳过创建")
            return
        
        # 创建Platform和Project模型
        create_platform_project_models(db, admin_user)
        
        # 创建示例数据提示
        create_sample_platform_data(db)
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        db.rollback()
    finally:
        db.close()


if __name__ == "__main__":
    print("🚀 开始添加Platform和Project动态模型...")
    main()
    print("✅ Platform和Project动态模型添加完成!")