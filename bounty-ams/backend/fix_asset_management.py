#!/usr/bin/env python3
"""
修复资产管理系统的数据一致性和关联问题
"""

import asyncio
import json
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy import select, delete, and_, or_
from elasticsearch import AsyncElasticsearch
from elasticsearch.exceptions import NotFoundError

from models_dynamic import ModelType, DynamicEntity, User
from config import settings
from elasticsearch_client import get_es_client

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建异步数据库引擎
engine = create_async_engine(settings.DATABASE_URL)
async_session = async_sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)

class AssetManagementFixer:
    """资产管理系统修复器"""
    
    def __init__(self):
        self.es_client = None
        self.db_session = None
        self.stats = {
            'total_es_assets': 0,
            'synced_to_postgres': 0,
            'fixed_associations': 0,
            'failed_syncs': 0,
            'errors': []
        }
    
    async def initialize(self):
        """初始化连接"""
        self.es_client = await get_es_client()
        self.db_session = async_session()
        
    async def fix_all_issues(self):
        """修复所有问题"""
        logger.info("开始修复资产管理系统...")
        
        await self.initialize()
        
        try:
            # 1. 同步Elasticsearch中的资产到PostgreSQL
            await self.sync_es_assets_to_postgres()
            
            # 2. 修复资产的平台项目关联
            await self.fix_platform_project_associations()
            
            # 3. 创建缺失的索引和映射
            await self.create_missing_indexes()
            
            # 4. 验证数据一致性
            await self.verify_data_consistency()
            
            # 5. 输出修复报告
            await self.generate_report()
            
        except Exception as e:
            logger.error(f"修复过程中出现错误: {e}")
            self.stats['errors'].append(str(e))
        
        finally:
            await self.cleanup()
    
    async def sync_es_assets_to_postgres(self):
        """同步Elasticsearch中的资产到PostgreSQL"""
        logger.info("开始同步Elasticsearch资产到PostgreSQL...")
        
        # 获取所有资产相关的模型类型
        asset_model_types = await self.get_asset_model_types()
        
        # 搜索Elasticsearch中的所有资产
        es_assets = await self.search_all_es_assets()
        self.stats['total_es_assets'] = len(es_assets)
        
        logger.info(f"在Elasticsearch中发现 {len(es_assets)} 个资产")
        
        for asset in es_assets:
            try:
                await self.sync_single_asset_to_postgres(asset, asset_model_types)
                self.stats['synced_to_postgres'] += 1
            except Exception as e:
                self.stats['failed_syncs'] += 1
                self.stats['errors'].append(f"同步资产失败 {asset.get('_id', 'unknown')}: {str(e)}")
        
        await self.db_session.commit()
        logger.info(f"成功同步 {self.stats['synced_to_postgres']} 个资产")
    
    async def get_asset_model_types(self) -> Dict[str, ModelType]:
        """获取所有资产相关的模型类型"""
        # 获取或创建enhanced_asset模型类型
        enhanced_asset_result = await self.db_session.execute(
            select(ModelType).where(ModelType.name == 'enhanced_asset')
        )
        enhanced_asset_model = enhanced_asset_result.scalar_one_or_none()
        
        if not enhanced_asset_model:
            # 创建enhanced_asset模型类型
            enhanced_asset_model = await self.create_enhanced_asset_model()
        
        # 获取其他可能的资产模型类型
        exclude_models = ['platform', 'project', 'vulnerability_kb', 'agent', 'task', 'customer_info']
        model_types_result = await self.db_session.execute(
            select(ModelType).where(
                ModelType.is_active == True,
                ~ModelType.name.in_(exclude_models)
            )
        )
        model_types = model_types_result.scalars().all()
        
        return {mt.name: mt for mt in model_types}
    
    async def create_enhanced_asset_model(self) -> ModelType:
        """创建enhanced_asset模型类型"""
        from models_dynamic import ModelField
        
        # 获取admin用户
        admin_result = await self.db_session.execute(
            select(User).where(User.is_admin == True).limit(1)
        )
        admin_user = admin_result.scalar_one_or_none()
        
        if not admin_user:
            raise Exception("找不到管理员用户")
        
        # 创建enhanced_asset模型类型
        enhanced_asset_model = ModelType(
            name="enhanced_asset",
            display_name="增强资产",
            description="带有平台和项目关联的资产管理",
            icon="🎯",
            color="#DC2626",
            is_active=True,
            is_system=True,
            created_by_user_id=admin_user.id
        )
        
        self.db_session.add(enhanced_asset_model)
        await self.db_session.flush()
        
        # 创建字段
        fields = [
            ModelField(
                model_type_id=enhanced_asset_model.id,
                field_name="platform_id",
                field_type="text",
                display_name="所属平台ID",
                is_required=False,
                is_searchable=True,
                is_filterable=True,
                sort_order=0
            ),
            ModelField(
                model_type_id=enhanced_asset_model.id,
                field_name="project_id",
                field_type="text",
                display_name="所属项目ID",
                is_required=False,
                is_searchable=True,
                is_filterable=True,
                sort_order=1
            ),
            ModelField(
                model_type_id=enhanced_asset_model.id,
                field_name="asset_type",
                field_type="text",
                display_name="资产类型",
                is_required=True,
                is_searchable=True,
                is_filterable=True,
                sort_order=2
            ),
            ModelField(
                model_type_id=enhanced_asset_model.id,
                field_name="asset_value",
                field_type="text",
                display_name="资产值",
                is_required=True,
                is_searchable=True,
                is_filterable=True,
                sort_order=3
            ),
            ModelField(
                model_type_id=enhanced_asset_model.id,
                field_name="confidence",
                field_type="text",
                display_name="置信度",
                is_required=False,
                is_searchable=True,
                is_filterable=True,
                sort_order=4
            ),
            ModelField(
                model_type_id=enhanced_asset_model.id,
                field_name="status",
                field_type="text",
                display_name="状态",
                is_required=False,
                is_searchable=True,
                is_filterable=True,
                sort_order=5
            ),
        ]
        
        for field in fields:
            self.db_session.add(field)
        
        await self.db_session.commit()
        return enhanced_asset_model
    
    async def search_all_es_assets(self) -> List[Dict[str, Any]]:
        """搜索Elasticsearch中的所有资产"""
        assets = []
        
        # 搜索不同的索引模式
        index_patterns = [
            "enhanced_asset-*",
            "assets-*",
            "dynamic_*"
        ]
        
        for pattern in index_patterns:
            try:
                response = await self.es_client.search(
                    index=pattern,
                    body={
                        "query": {"match_all": {}},
                        "size": 10000,  # 增加大小以获取更多结果
                        "sort": [{"_id": {"order": "asc"}}]
                    },
                    ignore=[404]  # 忽略索引不存在的错误
                )
                
                if response and 'hits' in response:
                    for hit in response['hits']['hits']:
                        # 添加索引信息
                        hit['_index_pattern'] = pattern
                        assets.append(hit)
                        
            except Exception as e:
                logger.warning(f"搜索索引 {pattern} 时出错: {e}")
        
        return assets
    
    async def sync_single_asset_to_postgres(self, es_asset: Dict[str, Any], model_types: Dict[str, ModelType]):
        """将单个Elasticsearch资产同步到PostgreSQL"""
        asset_source = es_asset.get('_source', {})
        asset_id = es_asset.get('_id')
        
        # 检查是否已经存在
        existing_result = await self.db_session.execute(
            select(DynamicEntity).where(DynamicEntity.es_doc_id == asset_id)
        )
        existing_entity = existing_result.scalar_one_or_none()
        
        if existing_entity:
            logger.debug(f"资产 {asset_id} 已存在于PostgreSQL")
            return
        
        # 确定模型类型
        model_type = self.determine_model_type(asset_source, model_types)
        
        # 提取和标准化实体数据
        entity_data = self.extract_entity_data(asset_source)
        
        # 创建DynamicEntity
        entity = DynamicEntity(
            model_type_id=model_type.id,
            entity_data=entity_data,
            es_index=es_asset.get('_index'),
            es_doc_id=asset_id,
            created_by_user_id=model_type.created_by_user_id
        )
        
        self.db_session.add(entity)
        logger.debug(f"同步资产 {asset_id} 到PostgreSQL")
    
    def determine_model_type(self, asset_source: Dict[str, Any], model_types: Dict[str, ModelType]) -> ModelType:
        """确定资产的模型类型"""
        # 检查是否有明确的模型类型标识
        if 'model_type_id' in asset_source:
            model_type_id = asset_source['model_type_id']
            for mt in model_types.values():
                if str(mt.id) == str(model_type_id):
                    return mt
        
        # 检查entity_data中的资产类型
        entity_data = asset_source.get('entity_data', {})
        if 'asset_type' in entity_data or 'asset_value' in entity_data:
            return model_types.get('enhanced_asset')
        
        # 检查传统的资产字段
        if any(field in asset_source for field in ['asset_type', 'asset_value', 'domain', 'ip']):
            return model_types.get('enhanced_asset')
        
        # 默认使用enhanced_asset
        return model_types.get('enhanced_asset')
    
    def extract_entity_data(self, asset_source: Dict[str, Any]) -> Dict[str, Any]:
        """提取和标准化实体数据"""
        entity_data = {}
        
        # 如果已经有entity_data，直接使用
        if 'entity_data' in asset_source:
            entity_data = asset_source['entity_data'].copy()
        else:
            # 从根级别字段提取
            for field in ['asset_type', 'asset_value', 'asset_host', 'confidence', 'status', 
                         'platform_id', 'project_id', 'discovered_at', 'tags', 'metadata']:
                if field in asset_source:
                    entity_data[field] = asset_source[field]
        
        # 确保必需字段存在
        if 'asset_type' not in entity_data:
            entity_data['asset_type'] = asset_source.get('asset_type', 'unknown')
        
        if 'asset_value' not in entity_data:
            # 尝试从各种字段推断asset_value
            for field in ['asset_value', 'domain', 'ip', 'url', 'host', 'value']:
                if field in asset_source:
                    entity_data['asset_value'] = asset_source[field]
                    break
        
        # 设置默认值
        if 'confidence' not in entity_data:
            entity_data['confidence'] = 'medium'
        
        if 'status' not in entity_data:
            entity_data['status'] = 'active'
        
        if 'discovered_at' not in entity_data:
            entity_data['discovered_at'] = datetime.now().isoformat()
        
        return entity_data
    
    async def fix_platform_project_associations(self):
        """修复资产的平台项目关联"""
        logger.info("修复资产的平台项目关联...")
        
        # 获取所有平台和项目
        platforms = await self.get_all_platforms()
        projects = await self.get_all_projects()
        
        # 获取所有资产
        assets_result = await self.db_session.execute(
            select(DynamicEntity).where(
                DynamicEntity.model_type_id.in_(
                    select(ModelType.id).where(~ModelType.name.in_(['platform', 'project']))
                )
            )
        )
        assets = assets_result.scalars().all()
        
        for asset in assets:
            try:
                entity_data = asset.entity_data.copy()
                updated = False
                
                # 修复平台关联
                if not entity_data.get('platform_id') and platforms:
                    # 尝试从资产值推断平台
                    platform_id = self.infer_platform_from_asset(entity_data, platforms)
                    if platform_id:
                        entity_data['platform_id'] = platform_id
                        updated = True
                
                # 修复项目关联
                if not entity_data.get('project_id') and projects:
                    # 尝试从资产值和平台推断项目
                    project_id = self.infer_project_from_asset(entity_data, projects)
                    if project_id:
                        entity_data['project_id'] = project_id
                        updated = True
                
                if updated:
                    asset.entity_data = entity_data
                    self.stats['fixed_associations'] += 1
                    logger.debug(f"修复资产 {asset.id} 的关联")
                
            except Exception as e:
                self.stats['errors'].append(f"修复资产关联失败 {asset.id}: {str(e)}")
        
        await self.db_session.commit()
        logger.info(f"修复了 {self.stats['fixed_associations']} 个资产的关联")
    
    async def get_all_platforms(self) -> List[DynamicEntity]:
        """获取所有平台"""
        platform_model_result = await self.db_session.execute(
            select(ModelType).where(ModelType.name == 'platform')
        )
        platform_model = platform_model_result.scalar_one_or_none()
        
        if not platform_model:
            return []
        
        platforms_result = await self.db_session.execute(
            select(DynamicEntity).where(DynamicEntity.model_type_id == platform_model.id)
        )
        return platforms_result.scalars().all()
    
    async def get_all_projects(self) -> List[DynamicEntity]:
        """获取所有项目"""
        project_model_result = await self.db_session.execute(
            select(ModelType).where(ModelType.name == 'project')
        )
        project_model = project_model_result.scalar_one_or_none()
        
        if not project_model:
            return []
        
        projects_result = await self.db_session.execute(
            select(DynamicEntity).where(DynamicEntity.model_type_id == project_model.id)
        )
        return projects_result.scalars().all()
    
    def infer_platform_from_asset(self, entity_data: Dict[str, Any], platforms: List[DynamicEntity]) -> Optional[str]:
        """从资产推断平台"""
        asset_value = entity_data.get('asset_value', '').lower()
        
        # 简单的推断逻辑
        for platform in platforms:
            platform_data = platform.entity_data
            platform_name = platform_data.get('name', '').lower()
            
            if platform_name in asset_value:
                return str(platform.id)
        
        # 如果只有一个平台，默认使用它
        if len(platforms) == 1:
            return str(platforms[0].id)
        
        return None
    
    def infer_project_from_asset(self, entity_data: Dict[str, Any], projects: List[DynamicEntity]) -> Optional[str]:
        """从资产推断项目"""
        asset_value = entity_data.get('asset_value', '').lower()
        platform_id = entity_data.get('platform_id')
        
        if not platform_id:
            return None
        
        # 查找同一平台下的项目
        platform_projects = [p for p in projects if p.entity_data.get('platform_id') == platform_id]
        
        for project in platform_projects:
            project_data = project.entity_data
            project_name = project_data.get('name', '').lower()
            company_name = project_data.get('company_name', '').lower()
            
            if project_name in asset_value or company_name in asset_value:
                return str(project.id)
        
        # 如果该平台下只有一个项目，默认使用它
        if len(platform_projects) == 1:
            return str(platform_projects[0].id)
        
        return None
    
    async def create_missing_indexes(self):
        """创建缺失的Elasticsearch索引和映射"""
        logger.info("创建缺失的Elasticsearch索引...")
        
        # 创建enhanced_asset索引模板
        template = {
            "index_patterns": ["enhanced_asset-*"],
            "settings": {
                "number_of_shards": 1,
                "number_of_replicas": 0,
                "refresh_interval": "1s"
            },
            "mappings": {
                "properties": {
                    "model_type_id": {"type": "keyword"},
                    "created_at": {"type": "date"},
                    "updated_at": {"type": "date"},
                    "entity_data": {
                        "properties": {
                            "platform_id": {"type": "keyword"},
                            "project_id": {"type": "keyword"},
                            "asset_type": {"type": "keyword"},
                            "asset_value": {
                                "type": "text",
                                "fields": {"keyword": {"type": "keyword"}}
                            },
                            "asset_host": {
                                "type": "text",
                                "fields": {"keyword": {"type": "keyword"}}
                            },
                            "confidence": {"type": "keyword"},
                            "status": {"type": "keyword"},
                            "discovered_at": {"type": "date"},
                            "tags": {"type": "keyword"},
                            "metadata": {"type": "object", "dynamic": True}
                        }
                    }
                }
            }
        }
        
        try:
            await self.es_client.indices.put_index_template(
                name="enhanced_asset_template",
                body=template
            )
            logger.info("创建enhanced_asset索引模板成功")
        except Exception as e:
            logger.error(f"创建索引模板失败: {e}")
    
    async def verify_data_consistency(self):
        """验证数据一致性"""
        logger.info("验证数据一致性...")
        
        # 检查PostgreSQL中的资产数量
        postgres_count_result = await self.db_session.execute(
            select(DynamicEntity).where(
                DynamicEntity.model_type_id.in_(
                    select(ModelType.id).where(~ModelType.name.in_(['platform', 'project']))
                )
            )
        )
        postgres_count = len(postgres_count_result.scalars().all())
        
        # 检查Elasticsearch中的资产数量
        es_count = 0
        try:
            response = await self.es_client.count(
                index="enhanced_asset-*,assets-*",
                ignore=[404]
            )
            es_count = response.get('count', 0)
        except Exception as e:
            logger.warning(f"检查Elasticsearch数量失败: {e}")
        
        logger.info(f"PostgreSQL中的资产数量: {postgres_count}")
        logger.info(f"Elasticsearch中的资产数量: {es_count}")
        
        # 检查关联完整性
        assets_with_platform = await self.db_session.execute(
            select(DynamicEntity).where(
                and_(
                    DynamicEntity.model_type_id.in_(
                        select(ModelType.id).where(~ModelType.name.in_(['platform', 'project']))
                    ),
                    DynamicEntity.entity_data.op('->>')('platform_id').isnot(None)
                )
            )
        )
        platform_linked_count = len(assets_with_platform.scalars().all())
        
        assets_with_project = await self.db_session.execute(
            select(DynamicEntity).where(
                and_(
                    DynamicEntity.model_type_id.in_(
                        select(ModelType.id).where(~ModelType.name.in_(['platform', 'project']))
                    ),
                    DynamicEntity.entity_data.op('->>')('project_id').isnot(None)
                )
            )
        )
        project_linked_count = len(assets_with_project.scalars().all())
        
        logger.info(f"关联到平台的资产数量: {platform_linked_count}")
        logger.info(f"关联到项目的资产数量: {project_linked_count}")
    
    async def generate_report(self):
        """生成修复报告"""
        logger.info("="*50)
        logger.info("资产管理系统修复报告")
        logger.info("="*50)
        logger.info(f"Elasticsearch中发现的资产总数: {self.stats['total_es_assets']}")
        logger.info(f"成功同步到PostgreSQL: {self.stats['synced_to_postgres']}")
        logger.info(f"修复的关联数量: {self.stats['fixed_associations']}")
        logger.info(f"同步失败数量: {self.stats['failed_syncs']}")
        
        if self.stats['errors']:
            logger.error("修复过程中的错误:")
            for error in self.stats['errors']:
                logger.error(f"  - {error}")
        
        logger.info("="*50)
        logger.info("修复完成！")
    
    async def cleanup(self):
        """清理资源"""
        if self.db_session:
            await self.db_session.close()
        if self.es_client:
            await self.es_client.close()

async def main():
    """主函数"""
    fixer = AssetManagementFixer()
    await fixer.fix_all_issues()

if __name__ == "__main__":
    asyncio.run(main()) 