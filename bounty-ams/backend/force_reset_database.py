#!/usr/bin/env python3
"""
Database setup script with force reset
"""

import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

# Database connection parameters
DB_HOST = "localhost"
DB_PORT = 5432
DB_USER = "postgres"
DB_PASSWORD = "postgres"
DB_NAME = "bounty_ams"

def force_reset_database():
    """Force drop and recreate the database"""
    try:
        # Connect to PostgreSQL server (not to the specific database)
        conn = psycopg2.connect(
            host=DB_HOST,
            port=DB_PORT,
            user=DB_USER,
            password=DB_PASSWORD,
            database="postgres"  # Connect to default database
        )
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor()
        
        # Terminate all connections to the database
        cursor.execute(f"""
            SELECT pg_terminate_backend(pg_stat_activity.pid)
            FROM pg_stat_activity
            WHERE pg_stat_activity.datname = '{DB_NAME}'
              AND pid <> pg_backend_pid()
        """)
        print(f"✓ Terminated active connections to {DB_NAME}")
        
        # Drop the database if it exists
        cursor.execute(f"DROP DATABASE IF EXISTS {DB_NAME}")
        print(f"✓ Database {DB_NAME} dropped")
        
        # Create the database
        cursor.execute(f"CREATE DATABASE {DB_NAME}")
        print(f"✓ Database {DB_NAME} created")
        
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Error resetting database: {e}")
        return False

if __name__ == "__main__":
    print("Force resetting Bounty AMS database...")
    success = force_reset_database()
    if success:
        print("🎉 Database reset completed successfully!")
    else:
        print("❌ Database reset failed!")