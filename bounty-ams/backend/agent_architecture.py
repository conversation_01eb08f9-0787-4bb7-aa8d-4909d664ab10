"""
Agent Communication System Architecture

1. Agent Management Layer
   - Agent Registration & Discovery
   - Health Monitoring & Heartbeat
   - Capability Reporting
   - Load Balancing

2. Task & Workflow Engine
   - Task Definition & Queuing
   - Workflow Orchestration
   - Task Dependencies & Chaining
   - Result Aggregation

3. Communication Protocol
   - REST API for registration
   - WebSocket for real-time communication
   - Message Queue for task distribution
   - Redis for state management

4. Agent Types & Capabilities
   - Subdomain Discovery (subfinder, amass)
   - Port Scanning (nmap, naabu)
   - Service Detection (httpx, nuclei)
   - Vulnerability Testing (PoC execution)
   - Asset Fingerprinting

5. Workflow Templates
   - Full Domain Reconnaissance
   - Quick Port Scan
   - Vulnerability Assessment
   - Custom Workflows
"""

from enum import Enum
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
import json
from datetime import datetime

class AgentCapability(Enum):
    """Agent功能枚举"""
    SUBDOMAIN_DISCOVERY = "subdomain_discovery"
    PORT_SCANNING = "port_scanning"
    SERVICE_DETECTION = "service_detection"
    VULNERABILITY_TESTING = "vulnerability_testing"
    WEB_CRAWLING = "web_crawling"
    ASSET_FINGERPRINTING = "asset_fingerprinting"
    DNS_RESOLUTION = "dns_resolution"
    SCREENSHOT = "screenshot"
    HOST_COLLISION = "host_collision"

class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"
    ASSIGNED = "assigned"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    SKIPPED = "skipped"

class TaskPriority(Enum):
    """任务优先级"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4

@dataclass
class AgentInfo:
    """Agent信息"""
    agent_id: str
    name: str
    version: str
    capabilities: List[AgentCapability]
    max_concurrent_tasks: int = 3
    current_tasks: int = 0
    last_heartbeat: Optional[datetime] = None
    metadata: Dict[str, Any] = None

@dataclass
class TaskDefinition:
    """任务定义"""
    task_id: str
    task_type: AgentCapability
    target: str
    parameters: Dict[str, Any]
    priority: TaskPriority = TaskPriority.NORMAL
    timeout: int = 300  # seconds
    retry_count: int = 3
    dependencies: List[str] = None  # 依赖的任务ID列表
    workflow_id: Optional[str] = None

@dataclass
class TaskResult:
    """任务结果"""
    task_id: str
    agent_id: str
    status: TaskStatus
    result_data: Dict[str, Any]
    error_message: Optional[str] = None
    execution_time: Optional[float] = None
    assets_discovered: List[Dict[str, Any]] = None

@dataclass
class WorkflowTemplate:
    """工作流模板"""
    workflow_id: str
    name: str
    description: str
    tasks: List[TaskDefinition]
    variables: Dict[str, Any] = None

# 预定义工作流模板
WORKFLOW_TEMPLATES = {
    "full_domain_recon": WorkflowTemplate(
        workflow_id="full_domain_recon",
        name="完整域名侦察",
        description="从主域名开始的完整资产发现流程",
        tasks=[
            # 1. 子域名发现
            TaskDefinition(
                task_id="subdomain_discovery",
                task_type=AgentCapability.SUBDOMAIN_DISCOVERY,
                target="{{domain}}",
                parameters={
                    "tools": ["subfinder", "amass", "assetfinder"],
                    "wordlist": "default",
                    "recursive": True
                },
                priority=TaskPriority.HIGH
            ),
            # 2. DNS解析
            TaskDefinition(
                task_id="dns_resolution",
                task_type=AgentCapability.DNS_RESOLUTION,
                target="{{subdomain_discovery.subdomains}}",
                parameters={
                    "resolve_type": ["A", "AAAA", "CNAME"],
                    "timeout": 5
                },
                dependencies=["subdomain_discovery"],
                priority=TaskPriority.HIGH
            ),
            # 3. 存活性检测
            TaskDefinition(
                task_id="service_detection",
                task_type=AgentCapability.SERVICE_DETECTION,
                target="{{dns_resolution.resolved_ips}}",
                parameters={
                    "ports": ["80", "443", "8080", "8443"],
                    "timeout": 10,
                    "follow_redirects": True
                },
                dependencies=["dns_resolution"],
                priority=TaskPriority.HIGH
            ),
            # 4. 端口扫描
            TaskDefinition(
                task_id="port_scanning",
                task_type=AgentCapability.PORT_SCANNING,
                target="{{dns_resolution.resolved_ips}}",
                parameters={
                    "ports": "top1000",
                    "scan_type": "syn",
                    "threads": 1000
                },
                dependencies=["dns_resolution"],
                priority=TaskPriority.NORMAL
            ),
            # 5. Web指纹识别
            TaskDefinition(
                task_id="web_fingerprinting",
                task_type=AgentCapability.ASSET_FINGERPRINTING,
                target="{{service_detection.web_services}}",
                parameters={
                    "fingerprint_tools": ["httpx", "whatweb"],
                    "screenshot": True,
                    "tech_detection": True
                },
                dependencies=["service_detection"],
                priority=TaskPriority.NORMAL
            ),
            # 6. Host碰撞检测
            TaskDefinition(
                task_id="host_collision",
                task_type=AgentCapability.HOST_COLLISION,
                target="{{dns_resolution.resolved_ips}}",
                parameters={
                    "wordlist": "common_hosts",
                    "timeout": 5
                },
                dependencies=["dns_resolution"],
                priority=TaskPriority.LOW
            ),
            # 7. 漏洞扫描
            TaskDefinition(
                task_id="vulnerability_testing",
                task_type=AgentCapability.VULNERABILITY_TESTING,
                target="{{service_detection.web_services}}",
                parameters={
                    "nuclei_templates": ["cves", "exposures", "technologies"],
                    "severity": ["critical", "high", "medium"]
                },
                dependencies=["service_detection", "web_fingerprinting"],
                priority=TaskPriority.HIGH
            )
        ]
    ),
    
    "quick_port_scan": WorkflowTemplate(
        workflow_id="quick_port_scan",
        name="快速端口扫描",
        description="对目标进行快速端口扫描和服务识别",
        tasks=[
            TaskDefinition(
                task_id="port_scanning",
                task_type=AgentCapability.PORT_SCANNING,
                target="{{target}}",
                parameters={
                    "ports": "top100",
                    "scan_type": "syn",
                    "threads": 500
                },
                priority=TaskPriority.HIGH
            ),
            TaskDefinition(
                task_id="service_detection",
                task_type=AgentCapability.SERVICE_DETECTION,
                target="{{port_scanning.open_ports}}",
                parameters={
                    "timeout": 5
                },
                dependencies=["port_scanning"],
                priority=TaskPriority.HIGH
            )
        ]
    ),
    
    "vulnerability_assessment": WorkflowTemplate(
        workflow_id="vulnerability_assessment",
        name="漏洞评估",
        description="对已知服务进行漏洞评估",
        tasks=[
            TaskDefinition(
                task_id="vulnerability_testing",
                task_type=AgentCapability.VULNERABILITY_TESTING,
                target="{{target}}",
                parameters={
                    "nuclei_templates": ["cves", "exposures"],
                    "severity": ["critical", "high"],
                    "custom_payloads": True
                },
                priority=TaskPriority.URGENT
            )
        ]
    )
}

class AgentCommunicationProtocol:
    """Agent通信协议定义"""
    
    # Agent注册
    REGISTER_ENDPOINT = "/api/agents/register"
    HEARTBEAT_ENDPOINT = "/api/agents/heartbeat"
    TASK_POLL_ENDPOINT = "/api/agents/tasks/poll"
    TASK_RESULT_ENDPOINT = "/api/agents/tasks/result"
    
    # WebSocket事件
    WS_TASK_ASSIGNED = "task_assigned"
    WS_TASK_CANCELLED = "task_cancelled"
    WS_AGENT_SHUTDOWN = "agent_shutdown"
    
    # 消息格式
    @staticmethod
    def format_task_message(task: TaskDefinition) -> Dict[str, Any]:
        return {
            "type": "task_assignment",
            "task": asdict(task),
            "timestamp": datetime.utcnow().isoformat()
        }
    
    @staticmethod
    def format_result_message(result: TaskResult) -> Dict[str, Any]:
        return {
            "type": "task_result",
            "result": asdict(result),
            "timestamp": datetime.utcnow().isoformat()
        }

# Agent SDK消息示例
AGENT_SDK_EXAMPLES = {
    "registration": {
        "agent_id": "agent-go-001",
        "name": "Go Recon Agent",
        "version": "1.0.0",
        "capabilities": [
            "subdomain_discovery",
            "port_scanning",
            "dns_resolution"
        ],
        "max_concurrent_tasks": 5,
        "metadata": {
            "os": "linux",
            "arch": "amd64",
            "tools": ["subfinder", "naabu", "dnsx"]
        }
    },
    
    "task_result": {
        "task_id": "task-001",
        "agent_id": "agent-go-001",
        "status": "completed",
        "execution_time": 45.6,
        "result_data": {
            "subdomains": [
                "api.example.com",
                "admin.example.com",
                "test.example.com"
            ],
            "tools_used": ["subfinder", "amass"],
            "total_found": 3
        },
        "assets_discovered": [
            {
                "type": "subdomain",
                "value": "api.example.com",
                "source": "subfinder",
                "metadata": {
                    "parent_domain": "example.com",
                    "discovery_time": "2024-07-11T10:30:00Z"
                }
            }
        ]
    }
}

if __name__ == "__main__":
    # 输出架构文档
    print("Agent Communication System - Architecture Design")
    print("=" * 60)
    
    print("\n🎯 Core Components:")
    print("1. Agent Management & Registration")
    print("2. Task Queue & Distribution")
    print("3. Workflow Engine & Orchestration")
    print("4. Result Aggregation & Asset Discovery")
    print("5. Multi-language SDK Support")
    
    print(f"\n📋 Supported Capabilities: {len(AgentCapability)} types")
    for cap in AgentCapability:
        print(f"  - {cap.value}")
    
    print(f"\n🔄 Predefined Workflows: {len(WORKFLOW_TEMPLATES)}")
    for wf_id, wf in WORKFLOW_TEMPLATES.items():
        print(f"  - {wf.name}: {len(wf.tasks)} tasks")
    
    print("\n✅ Architecture design completed!")