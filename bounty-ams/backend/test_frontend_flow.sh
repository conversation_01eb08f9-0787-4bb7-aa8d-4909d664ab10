#!/bin/bash

echo "=== 测试前端认证和agents获取流程 ==="

# 步骤1: 模拟登录获取token
echo "1. 模拟登录..."
LOGIN_RESPONSE=$(curl -s -X POST "http://localhost:5174/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"password"}')

TOKEN=$(echo $LOGIN_RESPONSE | jq -r '.access_token')
echo "   Token获取成功: ${TOKEN:0:30}..."

# 步骤2: 模拟获取用户信息
echo "2. 获取用户信息..."
USER_RESPONSE=$(curl -s -X GET "http://localhost:5174/api/auth/me" \
  -H "Authorization: Bearer $TOKEN")

USER_ID=$(echo $USER_RESPONSE | jq -r '.id')
USERNAME=$(echo $USER_RESPONSE | jq -r '.username')
echo "   用户信息: $USERNAME ($USER_ID)"

# 步骤3: 获取agents列表
echo "3. 获取agents列表..."
AGENTS_RESPONSE=$(curl -s -X GET "http://localhost:5174/api/agents/" \
  -H "Authorization: Bearer $TOKEN")

# 解析agents数据
AGENT_COUNT=$(echo $AGENTS_RESPONSE | jq '.agents | length')
echo "   发现 $AGENT_COUNT 个agents"

# 显示每个agent的详细信息
echo "4. Agent详细信息:"
echo $AGENTS_RESPONSE | jq -r '.agents[] | "   - \(.name) (\(.agent_id)): \(.status)"'

echo ""
echo "=== 前端应该显示的数据 ==="
echo "Total agents: $AGENT_COUNT"
echo "Online agents: $(echo $AGENTS_RESPONSE | jq '[.agents[] | select(.status == "online")] | length')"
echo "Offline agents: $(echo $AGENTS_RESPONSE | jq '[.agents[] | select(.status == "offline")] | length')"

echo ""
echo "如果前端agents页面仍然显示为空，问题可能在于："
echo "1. 前端localStorage中没有正确的token"
echo "2. 前端页面有JavaScript错误"
echo "3. 前端认证状态不正确"
echo ""
echo "请在浏览器console中检查以下调试信息："
echo "- 'Auth debug - Token exists'"
echo "- 'API Response:'"
echo "- 'Parsed agents data:'"