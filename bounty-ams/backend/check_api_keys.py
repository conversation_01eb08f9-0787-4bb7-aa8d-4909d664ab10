#!/usr/bin/env python3
"""
Check existing API keys
"""
import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import select
from database import get_db
from models_dynamic import <PERSON><PERSON><PERSON>

async def check_api_keys():
    """查看现有API密钥"""
    
    async for db in get_db():
        try:
            result = await db.execute(select(AgentKey))
            keys = result.scalars().all()
            
            print(f"Found {len(keys)} API keys:")
            for key in keys:
                print(f"  - Key ID: {key.key_id}")
                print(f"    Agent ID: {key.agent_id}")
                print(f"    Name: {key.name}")
                print(f"    Status: {key.status}")
                print(f"    Created: {key.created_at}")
                print()
            
            break
        except Exception as e:
            print(f"❌ Error: {e}")
            raise

if __name__ == "__main__":
    asyncio.run(check_api_keys())