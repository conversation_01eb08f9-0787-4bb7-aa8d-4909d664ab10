import secrets
import string
import hashlib
from datetime import datetime, timed<PERSON><PERSON>
from typing import Op<PERSON>, <PERSON>, <PERSON><PERSON>
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete
from models_dynamic import <PERSON><PERSON><PERSON>, User
from schemas_agent_key import <PERSON><PERSON><PERSON><PERSON><PERSON>, AgentKeyResponse, AgentKeyInfo, AgentKeyStatus
import uuid

class AgentKeyService:
    """Agent密钥管理服务"""
    
    @staticmethod
    def generate_api_key() -> Tu<PERSON>[str, str]:
        """
        生成API密钥
        返回: (密钥明文, 密钥ID)
        """
        # 生成随机字符串
        alphabet = string.ascii_lowercase + string.digits
        random_part = ''.join(secrets.choice(alphabet) for _ in range(32))
        
        # 构造密钥格式: bams_agent_ak_<32位随机字符>
        api_key = f"bams_agent_ak_{random_part}"
        
        # 生成密钥ID (用于识别和存储)
        key_id = f"ak_{secrets.token_hex(16)}"
        
        return api_key, key_id
    
    @staticmethod
    def hash_api_key(api_key: str) -> str:
        """生成API密钥的哈希值"""
        return hashlib.sha256(api_key.encode()).hexdigest()
    
    @staticmethod
    def verify_api_key(api_key: str, key_hash: str) -> bool:
        """验证API密钥"""
        return AgentKeyService.hash_api_key(api_key) == key_hash
    
    @staticmethod
    async def create_agent_key(
        db: AsyncSession,
        key_data: AgentKeyCreate,
        created_by_user_id: uuid.UUID
    ) -> AgentKeyResponse:
        """创建Agent密钥"""
        
        # 生成密钥
        api_key, key_id = AgentKeyService.generate_api_key()
        key_hash = AgentKeyService.hash_api_key(api_key)
        
        # 创建数据库记录
        agent_key = AgentKey(
            key_id=key_id,
            key_hash=key_hash,
            agent_id=key_data.agent_id,
            name=key_data.name,
            description=key_data.description,
            expires_at=key_data.expires_at,
            created_by_user_id=created_by_user_id,
            status="active"
        )
        
        db.add(agent_key)
        await db.commit()
        await db.refresh(agent_key)
        
        return AgentKeyResponse(
            id=str(agent_key.id),
            key_id=agent_key.key_id,
            key_value=api_key,  # 只在创建时返回明文密钥
            agent_id=agent_key.agent_id,
            name=agent_key.name,
            description=agent_key.description,
            status=AgentKeyStatus(agent_key.status),
            expires_at=agent_key.expires_at,
            created_at=agent_key.created_at,
            created_by_user_id=str(agent_key.created_by_user_id) if agent_key.created_by_user_id else None
        )
    
    @staticmethod
    async def validate_api_key(
        db: AsyncSession,
        api_key: str
    ) -> Tuple[bool, Optional[AgentKey]]:
        """验证API密钥并更新使用统计"""
        
        if not api_key.startswith("bams_agent_ak_"):
            return False, None
        
        key_hash = AgentKeyService.hash_api_key(api_key)
        
        # 查找密钥
        result = await db.execute(
            select(AgentKey).where(
                AgentKey.key_hash == key_hash,
                AgentKey.status == "active"
            )
        )
        agent_key = result.scalar_one_or_none()
        
        if not agent_key:
            return False, None
        
        # 检查过期时间
        if agent_key.expires_at and agent_key.expires_at < datetime.utcnow():
            # 自动标记为过期
            await db.execute(
                update(AgentKey)
                .where(AgentKey.id == agent_key.id)
                .values(status="expired")
            )
            await db.commit()
            return False, None
        
        # 更新使用统计
        await db.execute(
            update(AgentKey)
            .where(AgentKey.id == agent_key.id)
            .values(
                last_used_at=datetime.utcnow(),
                usage_count=AgentKey.usage_count + 1
            )
        )
        await db.commit()
        
        return True, agent_key
    
    @staticmethod
    async def list_agent_keys(
        db: AsyncSession,
        agent_id: Optional[str] = None,
        status: Optional[str] = None
    ) -> List[AgentKeyInfo]:
        """获取密钥列表"""
        query = select(AgentKey)
        
        if agent_id:
            query = query.where(AgentKey.agent_id == agent_id)
        
        if status:
            query = query.where(AgentKey.status == status)
        
        query = query.order_by(AgentKey.created_at.desc())
        
        result = await db.execute(query)
        agent_keys = result.scalars().all()
        
        return [
            AgentKeyInfo(
                id=str(key.id),
                key_id=key.key_id,
                agent_id=key.agent_id,
                name=key.name,
                description=key.description,
                status=AgentKeyStatus(key.status),
                expires_at=key.expires_at,
                created_at=key.created_at,
                updated_at=key.updated_at,
                last_used_at=key.last_used_at,
                usage_count=key.usage_count,
                created_by_user_id=str(key.created_by_user_id) if key.created_by_user_id else None
            )
            for key in agent_keys
        ]
    
    @staticmethod
    async def revoke_agent_key(
        db: AsyncSession,
        key_id: str
    ) -> bool:
        """撤销密钥"""
        result = await db.execute(
            update(AgentKey)
            .where(AgentKey.key_id == key_id)
            .values(
                status="revoked",
                updated_at=datetime.utcnow()
            )
        )
        await db.commit()
        return result.rowcount > 0
    
    @staticmethod
    async def delete_agent_key(
        db: AsyncSession,
        key_id: str
    ) -> bool:
        """删除密钥"""
        result = await db.execute(
            delete(AgentKey).where(AgentKey.key_id == key_id)
        )
        await db.commit()
        return result.rowcount > 0
    
    @staticmethod
    async def get_agent_key_info(
        db: AsyncSession,
        key_id: str
    ) -> Optional[AgentKeyInfo]:
        """获取密钥详情"""
        result = await db.execute(
            select(AgentKey).where(AgentKey.key_id == key_id)
        )
        agent_key = result.scalar_one_or_none()
        
        if not agent_key:
            return None
        
        return AgentKeyInfo(
            id=str(agent_key.id),
            key_id=agent_key.key_id,
            agent_id=agent_key.agent_id,
            name=agent_key.name,
            description=agent_key.description,
            status=AgentKeyStatus(agent_key.status),
            expires_at=agent_key.expires_at,
            created_at=agent_key.created_at,
            updated_at=agent_key.updated_at,
            last_used_at=agent_key.last_used_at,
            usage_count=agent_key.usage_count,
            created_by_user_id=str(agent_key.created_by_user_id) if agent_key.created_by_user_id else None
        )