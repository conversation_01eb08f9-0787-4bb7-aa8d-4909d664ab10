"""
增强搜索相关的Pydantic模型
"""

from pydantic import BaseModel, Field, validator
from typing import Optional, List, Dict, Any, Union
from datetime import datetime
from enum import Enum

class AssetType(str, Enum):
    """资产类型枚举"""
    DOMAIN = "domain"
    SUBDOMAIN = "subdomain"
    IP = "ip"
    URL = "url"
    PORT = "port"
    SERVICE = "service"
    API_ENDPOINT = "api_endpoint"
    MOBILE_APP = "mobile_app"
    CERTIFICATE = "certificate"

class ConfidenceLevel(str, Enum):
    """置信度枚举"""
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"

class SortOrder(str, Enum):
    """排序顺序枚举"""
    ASC = "asc"
    DESC = "desc"

class AdvancedSearchRequest(BaseModel):
    """高级搜索请求模型"""
    query: Optional[str] = Field(None, description="搜索查询字符串")
    filters: Optional[Dict[str, Any]] = Field(None, description="额外过滤条件")
    platform_id: Optional[str] = Field(None, description="平台ID")
    project_id: Optional[str] = Field(None, description="项目ID")
    asset_types: Optional[List[AssetType]] = Field(None, description="资产类型列表")
    confidence: Optional[ConfidenceLevel] = Field(None, description="置信度")
    date_from: Optional[datetime] = Field(None, description="开始日期")
    date_to: Optional[datetime] = Field(None, description="结束日期")
    tags: Optional[List[str]] = Field(None, description="标签列表")
    sort_by: Optional[str] = Field("discovered_at", description="排序字段")
    sort_order: Optional[SortOrder] = Field(SortOrder.DESC, description="排序顺序")
    page: Optional[int] = Field(1, ge=1, description="页码")
    size: Optional[int] = Field(20, ge=1, le=1000, description="每页大小")
    include_aggregations: Optional[bool] = Field(True, description="是否包含聚合数据")

    @validator('date_to')
    def validate_date_range(cls, v, values):
        if v and 'date_from' in values and values['date_from']:
            if v < values['date_from']:
                raise ValueError('结束日期不能早于开始日期')
        return v

class SearchHit(BaseModel):
    """搜索结果命中项"""
    id: str = Field(..., description="文档ID")
    source: Dict[str, Any] = Field(..., description="原始数据")
    score: float = Field(..., description="相关性得分")
    highlight: Optional[Dict[str, List[str]]] = Field(None, description="高亮片段")

class AggregationBucket(BaseModel):
    """聚合桶模型"""
    key: Union[str, int, float] = Field(..., description="桶键")
    doc_count: int = Field(..., description="文档数量")
    sub_aggregations: Optional[Dict[str, Any]] = Field(None, description="子聚合")

class AdvancedSearchResponse(BaseModel):
    """高级搜索响应模型"""
    total: int = Field(..., description="总结果数")
    hits: List[SearchHit] = Field(..., description="搜索结果")
    aggregations: Optional[Dict[str, Any]] = Field(None, description="聚合数据")
    query_time: float = Field(..., description="查询耗时（秒）")
    page: int = Field(..., description="当前页码")
    size: int = Field(..., description="每页大小")
    suggestions: Optional[List[str]] = Field(None, description="搜索建议")

class PlatformCreate(BaseModel):
    """创建平台请求模型"""
    name: str = Field(..., min_length=2, max_length=50, description="平台名称")
    display_name: str = Field(..., min_length=2, max_length=100, description="显示名称")
    platform_type: str = Field(..., description="平台类型")
    website_url: Optional[str] = Field(None, description="官网地址")
    api_base_url: Optional[str] = Field(None, description="API基础地址")
    api_config: Optional[Dict[str, Any]] = Field(None, description="API配置")
    description: Optional[str] = Field(None, description="平台描述")
    status: str = Field("active", description="状态")
    contact_info: Optional[Dict[str, Any]] = Field(None, description="联系信息")
    supported_asset_types: Optional[List[str]] = Field(None, description="支持的资产类型")

class PlatformUpdate(BaseModel):
    """更新平台请求模型"""
    display_name: Optional[str] = Field(None, min_length=2, max_length=100, description="显示名称")
    platform_type: Optional[str] = Field(None, description="平台类型")
    website_url: Optional[str] = Field(None, description="官网地址")
    api_base_url: Optional[str] = Field(None, description="API基础地址")
    api_config: Optional[Dict[str, Any]] = Field(None, description="API配置")
    description: Optional[str] = Field(None, description="平台描述")
    status: Optional[str] = Field(None, description="状态")
    contact_info: Optional[Dict[str, Any]] = Field(None, description="联系信息")
    supported_asset_types: Optional[List[str]] = Field(None, description="支持的资产类型")

class ProjectCreate(BaseModel):
    """创建项目请求模型"""
    name: str = Field(..., min_length=2, max_length=200, description="项目名称")
    platform_id: str = Field(..., description="所属平台ID")
    external_id: Optional[str] = Field(None, description="外部项目ID")
    company_name: Optional[str] = Field(None, max_length=200, description="公司名称")
    program_type: str = Field(..., description="项目类型")
    status: str = Field("active", description="项目状态")
    scope: Optional[Dict[str, Any]] = Field(None, description="项目范围")
    reward_range: Optional[str] = Field(None, max_length=100, description="奖励范围")
    description: Optional[str] = Field(None, description="项目描述")
    contact_email: Optional[str] = Field(None, description="联系邮箱")
    started_at: Optional[datetime] = Field(None, description="开始日期")
    ended_at: Optional[datetime] = Field(None, description="结束日期")
    tags: Optional[List[str]] = Field(None, description="标签")
    priority: str = Field("medium", description="优先级")
    notes: Optional[str] = Field(None, description="备注")

class ProjectUpdate(BaseModel):
    """更新项目请求模型"""
    name: Optional[str] = Field(None, min_length=2, max_length=200, description="项目名称")
    external_id: Optional[str] = Field(None, description="外部项目ID")
    company_name: Optional[str] = Field(None, max_length=200, description="公司名称")
    program_type: Optional[str] = Field(None, description="项目类型")
    status: Optional[str] = Field(None, description="项目状态")
    scope: Optional[Dict[str, Any]] = Field(None, description="项目范围")
    reward_range: Optional[str] = Field(None, max_length=100, description="奖励范围")
    description: Optional[str] = Field(None, description="项目描述")
    contact_email: Optional[str] = Field(None, description="联系邮箱")
    started_at: Optional[datetime] = Field(None, description="开始日期")
    ended_at: Optional[datetime] = Field(None, description="结束日期")
    tags: Optional[List[str]] = Field(None, description="标签")
    priority: Optional[str] = Field(None, description="优先级")
    notes: Optional[str] = Field(None, description="备注")

class EnhancedAssetCreate(BaseModel):
    """创建增强资产请求模型"""
    platform_id: str = Field(..., description="所属平台ID")
    project_id: str = Field(..., description="所属项目ID")
    asset_type: AssetType = Field(..., description="资产类型")
    asset_value: str = Field(..., min_length=1, max_length=500, description="资产值")
    asset_host: Optional[str] = Field(None, description="主机")
    confidence: ConfidenceLevel = Field(ConfidenceLevel.MEDIUM, description="置信度")
    status: str = Field("active", description="状态")
    discovered_at: Optional[datetime] = Field(None, description="发现时间")
    source_task_type: Optional[str] = Field(None, description="发现来源")
    source_task_id: Optional[str] = Field(None, description="源任务ID")
    tags: Optional[List[str]] = Field(None, description="标签")
    metadata: Optional[Dict[str, Any]] = Field(None, description="元数据")

class EnhancedAssetUpdate(BaseModel):
    """更新增强资产请求模型"""
    asset_type: Optional[AssetType] = Field(None, description="资产类型")
    asset_value: Optional[str] = Field(None, min_length=1, max_length=500, description="资产值")
    asset_host: Optional[str] = Field(None, description="主机")
    confidence: Optional[ConfidenceLevel] = Field(None, description="置信度")
    status: Optional[str] = Field(None, description="状态")
    tags: Optional[List[str]] = Field(None, description="标签")
    metadata: Optional[Dict[str, Any]] = Field(None, description="元数据")

class BulkUpdateRequest(BaseModel):
    """批量更新请求模型"""
    asset_ids: List[str] = Field(..., description="资产ID列表")
    updates: Dict[str, Any] = Field(..., description="更新数据")

class AssetProcessRequest(BaseModel):
    """资产处理请求模型"""
    raw_assets: List[Dict[str, Any]] = Field(..., description="原始资产数据")
    clean_options: Optional[Dict[str, Any]] = Field(None, description="清洗选项")
    normalize_options: Optional[Dict[str, Any]] = Field(None, description="标准化选项")
    enhance_options: Optional[Dict[str, Any]] = Field(None, description="增强选项")

class AssetProcessResponse(BaseModel):
    """资产处理响应模型"""
    processed_assets: List[Dict[str, Any]] = Field(..., description="处理后的资产")
    original_count: int = Field(..., description="原始数量")
    cleaned_count: int = Field(..., description="清洗后数量")
    unique_count: int = Field(..., description="去重后数量")
    duplicates_removed: int = Field(..., description="移除的重复数量")
    processing_time: float = Field(..., description="处理耗时")

class AssetInsights(BaseModel):
    """资产洞察模型"""
    total_assets: int = Field(..., description="总资产数")
    asset_types_breakdown: List[Dict[str, Any]] = Field(..., description="资产类型分布")
    discovery_trend: List[Dict[str, Any]] = Field(..., description="发现趋势")
    platform_coverage: List[Dict[str, Any]] = Field(..., description="平台覆盖")
    risk_assessment: Dict[str, Any] = Field(..., description="风险评估")
    generated_at: datetime = Field(..., description="生成时间")

class SimilarAssetsResponse(BaseModel):
    """相似资产响应模型"""
    similar_assets: List[Dict[str, Any]] = Field(..., description="相似资产列表")
    similarity_scores: List[float] = Field(..., description="相似度分数")
    total: int = Field(..., description="总数")

class SearchSuggestionsResponse(BaseModel):
    """搜索建议响应模型"""
    suggestions: List[str] = Field(..., description="搜索建议")
    query: str = Field(..., description="原始查询")
    field: str = Field(..., description="搜索字段")

class ExportRequest(BaseModel):
    """导出请求模型"""
    search_params: AdvancedSearchRequest = Field(..., description="搜索参数")
    export_format: str = Field("json", description="导出格式")
    export_fields: Optional[List[str]] = Field(None, description="导出字段")
    include_metadata: bool = Field(True, description="是否包含元数据")

class ExportResponse(BaseModel):
    """导出响应模型"""
    export_format: str = Field(..., description="导出格式")
    total_exported: int = Field(..., description="导出总数")
    file_url: Optional[str] = Field(None, description="文件URL")
    data: Optional[List[Dict[str, Any]]] = Field(None, description="导出数据")
    exported_at: datetime = Field(..., description="导出时间")

class HealthCheckResponse(BaseModel):
    """健康检查响应模型"""
    status: str = Field(..., description="健康状态")
    elasticsearch: Dict[str, Any] = Field(..., description="Elasticsearch状态")
    timestamp: datetime = Field(..., description="检查时间")

class ErrorResponse(BaseModel):
    """错误响应模型"""
    error: str = Field(..., description="错误类型")
    message: str = Field(..., description="错误消息")
    details: Optional[Dict[str, Any]] = Field(None, description="错误详情")
    timestamp: datetime = Field(..., description="错误时间")

class QueryBuilderRequest(BaseModel):
    """查询构建器请求模型"""
    text_search: Optional[Dict[str, Any]] = Field(None, description="全文搜索配置")
    filters: Optional[List[Dict[str, Any]]] = Field(None, description="过滤器配置")
    aggregations: Optional[List[Dict[str, Any]]] = Field(None, description="聚合配置")
    sort: Optional[List[Dict[str, Any]]] = Field(None, description="排序配置")
    size: Optional[int] = Field(20, ge=1, le=10000, description="结果数量")
    from_: Optional[int] = Field(0, ge=0, alias="from", description="起始位置")

class SecurityAnalysisRequest(BaseModel):
    """安全分析请求模型"""
    platform_id: Optional[str] = Field(None, description="平台ID")
    risk_level: str = Field("high", pattern="^(high|medium|low)$", description="风险级别")
    include_recommendations: bool = Field(True, description="是否包含修复建议")

class SecurityAnalysisResponse(BaseModel):
    """安全分析响应模型"""
    risk_assets: List[Dict[str, Any]] = Field(..., description="风险资产列表")
    risk_statistics: Dict[str, Any] = Field(..., description="风险统计")
    threat_vectors: Dict[str, Any] = Field(..., description="威胁向量")
    recommendations: Optional[List[str]] = Field(None, description="修复建议")
    overall_risk_level: str = Field(..., description="总体风险级别")

class DataProcessingRequest(BaseModel):
    """数据处理请求模型"""
    raw_assets: List[Dict[str, Any]] = Field(..., description="原始资产数据")
    auto_correlate: bool = Field(True, description="是否进行关联分析")
    auto_index: bool = Field(True, description="是否自动索引")
    processing_options: Optional[Dict[str, Any]] = Field(None, description="处理选项")

class DataProcessingResponse(BaseModel):
    """数据处理响应模型"""
    status: str = Field(..., description="处理状态")
    processing_summary: Dict[str, Any] = Field(..., description="处理摘要")
    correlations: Optional[Dict[str, Any]] = Field(None, description="关联分析结果")
    index_result: Optional[Dict[str, Any]] = Field(None, description="索引结果")
    processed_assets: Optional[List[Dict[str, Any]]] = Field(None, description="处理后资产")

class CorrelationAnalysisResponse(BaseModel):
    """关联分析响应模型"""
    correlation_type: str = Field(..., description="关联类型")
    total_assets_analyzed: int = Field(..., description="分析的资产总数")
    correlations: Dict[str, Any] = Field(..., description="关联结果")
    analysis_scope: Dict[str, Any] = Field(..., description="分析范围")

class RiskAssessmentResponse(BaseModel):
    """风险评估响应模型"""
    overall_risk_level: str = Field(..., description="总体风险级别")
    risk_score: float = Field(..., description="风险评分")
    asset_statistics: Dict[str, Any] = Field(..., description="资产统计")
    threat_vectors: Dict[str, Any] = Field(..., description="威胁向量")
    insights: Dict[str, Any] = Field(..., description="资产洞察")
    remediation_recommendations: Optional[Dict[str, Any]] = Field(None, description="修复建议")

class ProcessingStatisticsResponse(BaseModel):
    """处理统计响应模型"""
    index_stats: Dict[str, Any] = Field(..., description="索引统计")
    asset_insights: Dict[str, Any] = Field(..., description="资产洞察")
    processing_health: str = Field(..., description="处理健康状态")
    error: Optional[str] = Field(None, description="错误信息")

class IntelligenceFilter(BaseModel):
    """情报过滤器模型"""
    field: str = Field(..., description="过滤字段")
    operator: str = Field(..., pattern="^(eq|ne|gt|lt|gte|lte|in|nin|regex|wildcard)$", description="操作符")
    value: Union[str, int, float, List[Any]] = Field(..., description="过滤值")

class IntelligenceQuery(BaseModel):
    """情报查询模型"""
    query_type: str = Field(..., pattern="^(security|domain|correlation|inventory)$", description="查询类型")
    filters: Optional[List[IntelligenceFilter]] = Field(None, description="过滤条件")
    aggregation_level: str = Field("detailed", pattern="^(summary|detailed|comprehensive)$", description="聚合级别")
    time_range: Optional[Dict[str, datetime]] = Field(None, description="时间范围")

class ThreatIntelligenceResponse(BaseModel):
    """威胁情报响应模型"""
    threat_level: str = Field(..., description="威胁级别")
    threat_indicators: List[Dict[str, Any]] = Field(..., description="威胁指标")
    attack_vectors: List[str] = Field(..., description="攻击向量")
    mitigation_strategies: List[str] = Field(..., description="缓解策略")
    intelligence_sources: List[str] = Field(..., description="情报来源")

# 为了向后兼容，添加一些别名
AssetSearchRequest = AdvancedSearchRequest
AssetSearchResponse = AdvancedSearchResponse