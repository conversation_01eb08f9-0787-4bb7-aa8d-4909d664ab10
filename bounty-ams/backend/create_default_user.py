#!/usr/bin/env python3
"""
Create default admin user for testing
"""
import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import get_db
from models_dynamic import User
from sqlalchemy import select
import bcrypt

async def create_default_user():
    """Create default admin user"""
    print("Creating default admin user...")
    
    async for db in get_db():
        try:
            # Check if admin user exists
            result = await db.execute(
                select(User).where(User.username == "admin")
            )
            existing_user = result.scalar_one_or_none()
            
            if existing_user:
                print("Admin user already exists")
                return
            
            # Hash password
            password = "password"
            hashed_password = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt())
            
            # Create admin user
            admin_user = User(
                username="admin",
                email="<EMAIL>",
                hashed_password=hashed_password.decode('utf-8'),
                is_admin=True,
                is_active=True
            )
            
            db.add(admin_user)
            await db.commit()
            
            print(f"✓ Created admin user: username='admin', password='password'")
            
        except Exception as e:
            print(f"✗ Error creating admin user: {e}")
            import traceback
            traceback.print_exc()
        finally:
            await db.close()

if __name__ == "__main__":
    asyncio.run(create_default_user())