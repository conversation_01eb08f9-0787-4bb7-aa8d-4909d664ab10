from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
from uuid import UUID
from enum import Enum

class AgentCapability(str, Enum):
    """Agent功能枚举"""
    SUBDOMAIN_DISCOVERY = "subdomain_discovery"
    PORT_SCANNING = "port_scanning"
    SERVICE_DETECTION = "service_detection"
    VULNERABILITY_TESTING = "vulnerability_testing"
    WEB_CRAWLING = "web_crawling"
    ASSET_FINGERPRINTING = "asset_fingerprinting"
    DNS_RESOLUTION = "dns_resolution"
    SCREENSHOT = "screenshot"
    HOST_COLLISION = "host_collision"
    # Agent控制任务类型
    AGENT_PAUSE = "agent_pause"
    AGENT_RESUME = "agent_resume"
    AGENT_STOP = "agent_stop"
    AGENT_RESTART = "agent_restart"
    AGENT_CANCEL_TASKS = "agent_cancel_tasks"

class AgentStatus(str, Enum):
    """Agent状态"""
    ONLINE = "online"
    OFFLINE = "offline"
    BUSY = "busy"
    ERROR = "error"
    MAINTENANCE = "maintenance"

class TaskStatus(str, Enum):
    """任务状态"""
    PENDING = "pending"
    ASSIGNED = "assigned"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    SKIPPED = "skipped"

class TaskPriority(int, Enum):
    """任务优先级"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4

# Agent相关schemas
class AgentRegisterRequest(BaseModel):
    """Agent注册请求"""
    agent_id: str = Field(..., description="唯一的Agent标识符")
    name: str = Field(..., description="Agent名称")
    version: str = Field(..., description="Agent版本")
    capabilities: List[AgentCapability] = Field(..., description="Agent支持的功能列表")
    max_concurrent_tasks: int = Field(default=3, ge=1, le=20, description="最大并发任务数")
    hostname: Optional[str] = Field(None, description="主机名")
    ip_address: Optional[str] = Field(None, description="IP地址")
    metadata: Optional[Dict[str, Any]] = Field(None, description="额外元数据")

class AgentHeartbeatRequest(BaseModel):
    """Agent心跳请求"""
    agent_id: str = Field(..., description="Agent标识符")
    status: AgentStatus = Field(..., description="当前状态")
    current_tasks: int = Field(default=0, ge=0, description="当前执行任务数")
    system_info: Optional[Dict[str, Any]] = Field(None, description="系统信息")

class AgentInfo(BaseModel):
    """Agent信息响应"""
    id: UUID
    agent_id: str
    name: str
    version: str
    capabilities: List[str]
    max_concurrent_tasks: int
    current_tasks: int
    status: str
    hostname: Optional[str]
    ip_address: Optional[str]
    last_seen_at: Optional[datetime]
    created_at: datetime
    agent_metadata: Optional[Dict[str, Any]]  # Changed from metadata to agent_metadata
    
    class Config:
        from_attributes = True

# 任务相关schemas
class TaskDefinition(BaseModel):
    """任务定义"""
    task_id: str = Field(..., description="任务ID")
    task_type: AgentCapability = Field(..., description="任务类型")
    target: str = Field(..., description="目标（域名、IP等）")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="任务参数")
    priority: TaskPriority = Field(default=TaskPriority.NORMAL, description="优先级")
    timeout: int = Field(default=300, ge=1, le=3600, description="超时时间（秒）")
    retry_count: int = Field(default=3, ge=0, le=10, description="重试次数")
    dependencies: Optional[List[str]] = Field(None, description="依赖的任务ID列表")
    workflow_id: Optional[str] = Field(None, description="所属工作流ID")
    preferred_agent_id: Optional[str] = Field(None, description="首选Agent ID")

class TaskResult(BaseModel):
    """任务结果"""
    task_id: str = Field(..., description="任务ID")
    agent_id: str = Field(..., description="执行Agent ID")
    status: TaskStatus = Field(..., description="任务状态")
    result_data: Dict[str, Any] = Field(default_factory=dict, description="结果数据")
    error_message: Optional[str] = Field(None, description="错误信息")
    execution_time: Optional[float] = Field(None, ge=0, description="执行时间（秒）")
    assets_discovered: Optional[List[Dict[str, Any]]] = Field(None, description="发现的资产")
    started_at: Optional[datetime] = Field(None, description="开始时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")

class TaskInfo(BaseModel):
    """任务信息"""
    id: UUID
    task_id: str
    task_type: str
    target: str
    parameters: Dict[str, Any]
    priority: int
    status: str
    agent_id: Optional[str]
    workflow_id: Optional[str]
    timeout: int
    retry_count: int
    current_retry: int
    dependencies: Optional[List[str]]
    result_data: Optional[Dict[str, Any]]
    error_message: Optional[str]
    execution_time: Optional[float]
    assets_discovered: Optional[List[Dict[str, Any]]]
    created_at: datetime
    assigned_at: Optional[datetime]
    started_at: Optional[datetime]
    completed_at: Optional[datetime]
    created_by_user_id: Optional[UUID]
    
    class Config:
        from_attributes = True

# 工作流相关schemas
class WorkflowTemplate(BaseModel):
    """工作流模板"""
    workflow_id: str = Field(..., description="工作流ID")
    name: str = Field(..., description="工作流名称")
    description: str = Field(..., description="工作流描述")
    tasks: List[TaskDefinition] = Field(..., description="任务列表")
    variables: Optional[Dict[str, Any]] = Field(None, description="工作流变量")

class WorkflowExecution(BaseModel):
    """工作流执行"""
    execution_id: str = Field(..., description="执行ID")
    workflow_id: str = Field(..., description="工作流模板ID")
    target: str = Field(..., description="目标")
    variables: Optional[Dict[str, Any]] = Field(None, description="执行变量")
    priority: TaskPriority = Field(default=TaskPriority.NORMAL, description="优先级")

class WorkflowStatus(BaseModel):
    """工作流状态"""
    execution_id: str
    workflow_id: str
    status: str
    target: str
    total_tasks: int
    completed_tasks: int
    failed_tasks: int
    running_tasks: int
    progress: float
    started_at: datetime
    updated_at: datetime
    completed_at: Optional[datetime]
    tasks: List[TaskInfo]

# Agent任务轮询响应
class TaskPollResponse(BaseModel):
    """任务轮询响应"""
    has_task: bool = Field(..., description="是否有任务")
    task: Optional[TaskDefinition] = Field(None, description="任务定义")
    message: Optional[str] = Field(None, description="消息")

# 系统统计
class SystemStats(BaseModel):
    """系统统计"""
    total_agents: int
    online_agents: int
    offline_agents: int
    total_tasks: int
    pending_tasks: int
    running_tasks: int
    completed_tasks: int
    failed_tasks: int
    active_workflows: int

# 资产发现结果
class AssetDiscovery(BaseModel):
    """资产发现结果"""
    asset_type: str = Field(..., description="资产类型")
    value: str = Field(..., description="资产值")
    source: str = Field(..., description="发现来源")
    confidence: float = Field(default=1.0, ge=0, le=1, description="置信度")
    metadata: Optional[Dict[str, Any]] = Field(None, description="元数据")
    parent_asset: Optional[str] = Field(None, description="父资产")
    discovery_time: datetime = Field(default_factory=datetime.utcnow, description="发现时间")

# WebSocket消息
class WebSocketMessage(BaseModel):
    """WebSocket消息"""
    type: str = Field(..., description="消息类型")
    data: Dict[str, Any] = Field(..., description="消息数据")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="时间戳")

# 错误响应
class ErrorResponse(BaseModel):
    """错误响应"""
    error: str = Field(..., description="错误类型")
    message: str = Field(..., description="错误消息")
    details: Optional[Dict[str, Any]] = Field(None, description="错误详情")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="时间戳")