#!/usr/bin/env python3
"""
创建示例平台和项目数据
"""

import asyncio
import json
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from models_dynamic import ModelType, DynamicEntity
from config import settings

# 创建异步数据库引擎
engine = create_async_engine(settings.DATABASE_URL)
async_session = async_sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)

async def create_sample_data():
    """创建示例平台和项目数据"""
    async with async_session() as session:
        try:
            # 获取平台和项目模型类型
            from sqlalchemy import select
            platform_model_result = await session.execute(
                select(ModelType).where(ModelType.name == 'platform')
            )
            platform_model = platform_model_result.scalar_one_or_none()
            
            project_model_result = await session.execute(
                select(ModelType).where(ModelType.name == 'project')
            )
            project_model = project_model_result.scalar_one_or_none()
            
            if not platform_model or not project_model:
                print("错误：找不到平台或项目模型类型，请先运行 init_platform_project_models.py")
                return
            
            # 创建示例平台
            platforms_data = [
                {
                    "name": "hackerone",
                    "display_name": "HackerOne",
                    "platform_type": "bug_bounty",
                    "website_url": "https://hackerone.com",
                    "api_base_url": "https://api.hackerone.com",
                    "description": "全球领先的漏洞赏金平台",
                    "status": "active",
                    "supported_asset_types": ["domain", "subdomain", "url", "api_endpoint"]
                },
                {
                    "name": "msrc",
                    "display_name": "Microsoft MSRC",
                    "platform_type": "vendor_vdp",
                    "website_url": "https://msrc.microsoft.com",
                    "description": "微软安全响应中心",
                    "status": "active",
                    "supported_asset_types": ["domain", "subdomain", "url", "api_endpoint"]
                },
                {
                    "name": "bugcrowd",
                    "display_name": "Bugcrowd",
                    "platform_type": "bug_bounty",
                    "website_url": "https://bugcrowd.com",
                    "api_base_url": "https://api.bugcrowd.com",
                    "description": "知名的漏洞赏金平台",
                    "status": "active",
                    "supported_asset_types": ["domain", "subdomain", "url", "api_endpoint"]
                }
            ]
            
            created_platforms = []
            for platform_data in platforms_data:
                platform = DynamicEntity(
                    model_type_id=platform_model.id,
                    entity_data=platform_data
                )
                session.add(platform)
                created_platforms.append(platform)
            
            await session.commit()
            
            # 刷新对象以获取ID
            for platform in created_platforms:
                await session.refresh(platform)
            
            print(f"创建了 {len(created_platforms)} 个平台")
            
            # 为平台创建项目
            projects_data = [
                # HackerOne 项目
                {
                    "name": "Shopify Bug Bounty",
                    "platform_id": str(created_platforms[0].id),  # 转换为字符串
                    "external_id": "shopify",
                    "company_name": "Shopify",
                    "program_type": "public",
                    "status": "active",
                    "scope": {
                        "in_scope": ["*.shopify.com", "shopify.com"],
                        "out_of_scope": ["internal.shopify.com"]
                    },
                    "reward_range": "$100 - $25,000",
                    "description": "Shopify公开漏洞赏金项目",
                    "tags": ["web", "api", "e-commerce"],
                    "priority": "high"
                },
                {
                    "name": "Uber Bug Bounty",
                    "platform_id": str(created_platforms[0].id),  # 转换为字符串
                    "external_id": "uber",
                    "company_name": "Uber",
                    "program_type": "public",
                    "status": "active",
                    "scope": {
                        "in_scope": ["*.uber.com", "uber.com"],
                        "out_of_scope": ["internal.uber.com"]
                    },
                    "reward_range": "$500 - $10,000",
                    "description": "Uber公开漏洞赏金项目",
                    "tags": ["web", "mobile", "api"],
                    "priority": "high"
                },
                # MSRC 项目
                {
                    "name": "Microsoft Edge",
                    "platform_id": str(created_platforms[1].id),  # 转换为字符串
                    "external_id": "edge",
                    "company_name": "Microsoft",
                    "program_type": "vdp",
                    "status": "active",
                    "scope": {
                        "in_scope": ["*.microsoftedge.com", "edge.microsoft.com"],
                        "out_of_scope": ["internal.microsoft.com"]
                    },
                    "description": "Microsoft Edge浏览器漏洞披露项目",
                    "tags": ["browser", "web", "security"],
                    "priority": "high"
                },
                {
                    "name": "Microsoft Office",
                    "platform_id": str(created_platforms[1].id),  # 转换为字符串
                    "external_id": "office",
                    "company_name": "Microsoft",
                    "program_type": "vdp",
                    "status": "active",
                    "scope": {
                        "in_scope": ["*.office.com", "office.microsoft.com"],
                        "out_of_scope": ["internal.microsoft.com"]
                    },
                    "description": "Microsoft Office产品漏洞披露项目",
                    "tags": ["office", "web", "productivity"],
                    "priority": "medium"
                },
                # Bugcrowd 项目
                {
                    "name": "Tesla Bug Bounty",
                    "platform_id": str(created_platforms[2].id),  # 转换为字符串
                    "external_id": "tesla",
                    "company_name": "Tesla",
                    "program_type": "private",
                    "status": "active",
                    "scope": {
                        "in_scope": ["*.tesla.com", "tesla.com"],
                        "out_of_scope": ["internal.tesla.com"]
                    },
                    "reward_range": "$1,000 - $15,000",
                    "description": "Tesla私有漏洞赏金项目",
                    "tags": ["automotive", "web", "iot"],
                    "priority": "high"
                }
            ]
            
            created_projects = []
            for project_data in projects_data:
                project = DynamicEntity(
                    model_type_id=project_model.id,
                    entity_data=project_data
                )
                session.add(project)
                created_projects.append(project)
            
            await session.commit()
            print(f"创建了 {len(created_projects)} 个项目")
            
            # 打印创建的数据信息
            print("\n创建的平台：")
            for platform in created_platforms:
                print(f"  - {platform.entity_data['display_name']} (ID: {platform.id})")
            
            print("\n创建的项目：")
            for project in created_projects:
                platform = next((p for p in created_platforms if str(p.id) == project.entity_data['platform_id']), None)
                if platform:
                    print(f"  - {project.entity_data['name']} (ID: {project.id}) - 平台: {platform.entity_data['display_name']}")
                else:
                    print(f"  - {project.entity_data['name']} (ID: {project.id}) - 平台: 未找到")
            
            print("\n示例数据创建完成！")
            
        except Exception as e:
            print(f"创建示例数据时发生错误: {e}")
            await session.rollback()

if __name__ == "__main__":
    asyncio.run(create_sample_data())