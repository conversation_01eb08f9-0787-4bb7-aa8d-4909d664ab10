"""
Agent System API Tests

Test the Agent communication system endpoints
"""
import asyncio
import httpx
from datetime import datetime
import json

BASE_URL = "http://localhost:8000"

async def test_agent_system():
    """测试Agent系统的完整流程"""
    
    async with httpx.AsyncClient() as client:
        print("🚀 开始测试Agent系统...")
        
        # 1. 首先注册用户和获取token
        print("\n1. 用户注册和登录...")
        
        # 注册测试用户
        register_data = {
            "username": "testuser",
            "email": "<EMAIL>", 
            "password": "testpass",
            "is_admin": True
        }
        
        response = await client.post(f"{BASE_URL}/api/auth/register", json=register_data)
        if response.status_code == 400:
            print("用户已存在，直接登录")
        else:
            print(f"注册结果: {response.status_code}")
        
        # 登录获取token
        login_data = {
            "username": "testuser",
            "password": "testpass"
        }
        
        response = await client.post(f"{BASE_URL}/api/auth/login", json=login_data)
        if response.status_code != 200:
            print(f"登录失败: {response.status_code} - {response.text}")
            return
        
        token_data = response.json()
        token = token_data["access_token"]
        headers = {"Authorization": f"Bearer {token}"}
        
        print("✅ 登录成功")
        
        # 2. 测试Agent注册
        print("\n2. 测试Agent注册...")
        
        agent_data = {
            "agent_id": "test-agent-001",
            "name": "Test Recon Agent",
            "version": "1.0.0",
            "capabilities": ["subdomain_discovery", "port_scanning", "service_detection"],
            "max_concurrent_tasks": 3,
            "hostname": "test-host",
            "ip_address": "127.0.0.1",
            "metadata": {
                "os": "linux",
                "arch": "amd64",
                "tools": ["subfinder", "naabu", "httpx"]
            }
        }
        
        response = await client.post(f"{BASE_URL}/api/agents/register", json=agent_data)
        print(f"Agent注册结果: {response.status_code}")
        if response.status_code == 200:
            agent_info = response.json()
            print(f"Agent ID: {agent_info['agent_id']}")
            print(f"Agent状态: {agent_info['status']}")
            print("✅ Agent注册成功")
        else:
            print(f"❌ Agent注册失败: {response.text}")
            return
        
        # 3. 测试Agent心跳
        print("\n3. 测试Agent心跳...")
        
        heartbeat_data = {
            "agent_id": "test-agent-001",
            "status": "online",
            "current_tasks": 0,
            "system_info": {
                "cpu_usage": 25.5,
                "memory_usage": 512,
                "uptime": 3600
            }
        }
        
        response = await client.post(f"{BASE_URL}/api/agents/heartbeat", json=heartbeat_data)
        print(f"心跳结果: {response.status_code}")
        if response.status_code == 200:
            print("✅ 心跳成功")
        else:
            print(f"❌ 心跳失败: {response.text}")
        
        # 4. 测试创建任务
        print("\n4. 测试创建任务...")
        
        # Generate unique task ID using timestamp
        task_id = f"task-{datetime.now().strftime('%Y%m%d%H%M%S')}"
        
        task_data = {
            "task_id": task_id,
            "task_type": "subdomain_discovery",
            "target": "example.com",
            "parameters": {
                "tools": ["subfinder", "amass"],
                "wordlist": "default"
            },
            "priority": 3,
            "timeout": 300,
            "retry_count": 3
        }
        
        response = await client.post(f"{BASE_URL}/api/tasks/", json=task_data, headers=headers)
        print(f"任务创建结果: {response.status_code}")
        if response.status_code == 200:
            task_info = response.json()
            print(f"任务ID: {task_info['task_id']}")
            print(f"任务状态: {task_info['status']}")
            print("✅ 任务创建成功")
        else:
            print(f"❌ 任务创建失败: {response.text}")
            return
        
        # 5. 测试Agent任务轮询
        print("\n5. 测试Agent任务轮询...")
        
        poll_params = {
            "agent_id": "test-agent-001",
            "capabilities": "subdomain_discovery,port_scanning,service_detection"
        }
        
        response = await client.get(f"{BASE_URL}/api/agents/tasks/poll", params=poll_params)
        print(f"任务轮询结果: {response.status_code}")
        if response.status_code == 200:
            poll_result = response.json()
            print(f"有任务: {poll_result['has_task']}")
            if poll_result['has_task']:
                task = poll_result['task']
                print(f"分配的任务: {task['task_id']}")
                print(f"任务类型: {task['task_type']}")
                print(f"目标: {task['target']}")
                print("✅ 任务轮询成功")
            else:
                print("当前没有任务")
        else:
            print(f"❌ 任务轮询失败: {response.text}")
        
        # 6. 模拟任务执行并提交结果
        print("\n6. 测试任务结果提交...")
        
        # 先提交运行状态
        running_result = {
            "task_id": task_id,
            "agent_id": "test-agent-001",
            "status": "running",
            "result_data": {},
            "started_at": datetime.utcnow().isoformat()
        }
        
        response = await client.post(f"{BASE_URL}/api/agents/tasks/result", json=running_result)
        print(f"运行状态提交: {response.status_code}")
        
        # 等待一下模拟执行时间
        await asyncio.sleep(1)
        
        # 提交完成结果
        completed_result = {
            "task_id": task_id,
            "agent_id": "test-agent-001",
            "status": "completed",
            "result_data": {
                "subdomains": [
                    "www.example.com",
                    "mail.example.com",
                    "api.example.com"
                ],
                "tools_used": ["subfinder"],
                "total_found": 3
            },
            "execution_time": 45.6,
            "assets_discovered": [
                {
                    "type": "subdomain",
                    "value": "www.example.com",
                    "source": "subfinder",
                    "metadata": {
                        "parent_domain": "example.com"
                    }
                },
                {
                    "type": "subdomain", 
                    "value": "mail.example.com",
                    "source": "subfinder",
                    "metadata": {
                        "parent_domain": "example.com"
                    }
                }
            ],
            "completed_at": datetime.utcnow().isoformat()
        }
        
        response = await client.post(f"{BASE_URL}/api/agents/tasks/result", json=completed_result)
        print(f"完成结果提交: {response.status_code}")
        if response.status_code == 200:
            print("✅ 任务结果提交成功")
        else:
            print(f"❌ 任务结果提交失败: {response.text}")
        
        # 7. 测试工作流执行
        print("\n7. 测试工作流执行...")
        
        # 获取工作流模板
        response = await client.get(f"{BASE_URL}/api/workflows/templates", headers=headers)
        print(f"工作流模板获取: {response.status_code}")
        if response.status_code == 200:
            templates = response.json()
            print(f"可用模板数: {len(templates)}")
            for template in templates:
                print(f"  - {template['name']}: {len(template['tasks'])}个任务")
        
        # 执行工作流
        workflow_data = {
            "execution_id": f"workflow-{datetime.now().strftime('%Y%m%d%H%M%S')}",
            "workflow_id": "quick_port_scan",
            "target": "192.168.1.1",
            "priority": 2,
            "variables": {
                "scan_type": "quick"
            }
        }
        
        response = await client.post(f"{BASE_URL}/api/workflows/execute", json=workflow_data, headers=headers)
        print(f"工作流执行: {response.status_code}")
        if response.status_code == 200:
            workflow_status = response.json()
            print(f"工作流ID: {workflow_status['execution_id']}")
            print(f"总任务数: {workflow_status['total_tasks']}")
            print("✅ 工作流执行成功")
        else:
            print(f"❌ 工作流执行失败: {response.text}")
        
        # 8. 测试系统统计
        print("\n8. 测试系统统计...")
        
        response = await client.get(f"{BASE_URL}/api/agents/stats/system", headers=headers)
        print(f"系统统计获取: {response.status_code}")
        if response.status_code == 200:
            stats = response.json()
            print(f"在线Agent数: {stats['online_agents']}")
            print(f"总任务数: {stats['total_tasks']}")
            print(f"待处理任务: {stats['pending_tasks']}")
            print(f"运行中任务: {stats['running_tasks']}")
            print(f"已完成任务: {stats['completed_tasks']}")
            print("✅ 系统统计获取成功")
        
        # 9. 获取Agent列表
        print("\n9. 测试Agent列表获取...")
        
        response = await client.get(f"{BASE_URL}/api/agents/", headers=headers)
        print(f"Agent列表获取: {response.status_code}")
        if response.status_code == 200:
            agents = response.json()
            print(f"Agent总数: {len(agents)}")
            for agent in agents:
                print(f"  - {agent['name']} ({agent['agent_id']}): {agent['status']}")
                print(f"    能力: {', '.join(agent['capabilities'])}")
                print(f"    当前任务: {agent['current_tasks']}/{agent['max_concurrent_tasks']}")
            print("✅ Agent列表获取成功")
        
        print("\n🎉 Agent系统测试完成!")
        print("\n📊 测试总结:")
        print("  ✅ Agent注册功能正常")
        print("  ✅ Agent心跳机制正常")
        print("  ✅ 任务创建和分发正常")
        print("  ✅ 任务轮询机制正常") 
        print("  ✅ 任务结果提交正常")
        print("  ✅ 工作流执行正常")
        print("  ✅ 系统统计功能正常")
        print("\n🚀 Agent通信系统已就绪!")

if __name__ == "__main__":
    asyncio.run(test_agent_system())