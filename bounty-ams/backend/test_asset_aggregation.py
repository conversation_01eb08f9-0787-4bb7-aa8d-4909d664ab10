#!/usr/bin/env python3
"""
Test script for asset aggregation functionality
"""
import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import get_db
from models_dynamic import Task
from asset_aggregation import process_task_assets_on_completion
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
import json

async def test_asset_processing():
    """Test asset processing from a completed task"""
    print("Testing asset aggregation system...")
    
    # Get database session
    async for db in get_db():
        try:
            # Find a completed task with assets
            result = await db.execute(
                select(Task).where(
                    Task.status == "completed",
                    Task.assets_discovered.is_not(None)
                ).limit(1)
            )
            task = result.scalar_one_or_none()
            
            if not task:
                print("No completed tasks with assets found")
                # Create a mock task for testing
                mock_assets = [
                    {"type": "subdomain", "value": "test.example.com", "host": "example.com", "confidence": "high"},
                    {"type": "port", "value": "80", "host": "example.com", "confidence": "medium"},
                    {"type": "url", "value": "http://test.example.com", "host": "test.example.com", "confidence": "high"}
                ]
                
                # Create a test task
                from models_dynamic import get_or_create_model
                TaskModel = await get_or_create_model(db, "Task")
                
                new_task = TaskModel(
                    task_id="test-asset-aggregation-001",
                    task_type="subdomain_discovery",
                    target="example.com",
                    status="completed",
                    agent_id="test-agent",
                    workflow_id="test-workflow",
                    assets_discovered=mock_assets,
                    result_data={"message": "Test task for asset aggregation"}
                )
                
                db.add(new_task)
                await db.commit()
                print(f"Created test task: {new_task.task_id}")
                
                # Process the test task
                processed_assets = await process_task_assets_on_completion(new_task.task_id, db)
                print(f"Processed {processed_assets} assets from test task")
                
            else:
                print(f"Found task with assets: {task.task_id}")
                print(f"Task type: {task.task_type}")
                print(f"Assets discovered: {len(task.assets_discovered) if task.assets_discovered else 0}")
                
                if task.assets_discovered:
                    print("Sample asset data:")
                    for i, asset in enumerate(task.assets_discovered[:3]):
                        print(f"  {i+1}. {asset}")
                
                # Process the task assets
                processed_assets = await process_task_assets_on_completion(task.task_id, db)
                print(f"Processed {processed_assets} assets from task {task.task_id}")
            
            print("✓ Asset aggregation test completed successfully")
            
        except Exception as e:
            print(f"✗ Error during asset processing test: {e}")
            import traceback
            traceback.print_exc()
        finally:
            await db.close()

if __name__ == "__main__":
    asyncio.run(test_asset_processing())