#!/usr/bin/env python3
"""
Simple Agent Registration Test
"""
import requests
import json

BASE_URL = "http://localhost:8000"

def test_agent_registration():
    """Test Agent registration"""
    
    # Test data
    agent_data = {
        "agent_id": "test-agent-simple",
        "name": "Simple Test Agent",
        "version": "1.0.0",
        "capabilities": ["subdomain_discovery"],
        "max_concurrent_tasks": 1,
        "hostname": "localhost",
        "ip_address": "127.0.0.1",
        "metadata": {"test": "true"}
    }
    
    print("Testing Agent Registration...")
    print(f"URL: {BASE_URL}/api/agents/register")
    print(f"Data: {json.dumps(agent_data, indent=2)}")
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/agents/register",
            json=agent_data,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            print("✅ Agent registration successful!")
            return True
        else:
            print(f"❌ Agent registration failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    test_agent_registration()