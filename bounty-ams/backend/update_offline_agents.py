#!/usr/bin/env python3
"""
Update offline agent status
"""
import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import datetime, timedelta
from sqlalchemy import select, update
from database import get_db
from models_dynamic import Agent

async def update_offline_agents():
    """更新离线agents的状态"""
    
    # 定义在线判断标准：最后心跳时间在5分钟内
    online_threshold = datetime.utcnow() - timedelta(minutes=5)
    
    print(f"Current UTC time: {datetime.utcnow()}")
    print(f"Online threshold (5 minutes ago): {online_threshold}")
    print("="*60)
    
    async for db in get_db():
        try:
            # 更新所有离线的agents状态为offline
            result = await db.execute(
                update(Agent)
                .where(
                    (Agent.last_seen_at.is_(None)) |
                    (Agent.last_seen_at <= online_threshold)
                )
                .values(status="offline")
            )
            
            await db.commit()
            print(f"✅ Updated {result.rowcount} agents to offline status")
            
            # 查看现在的agents状态
            result = await db.execute(select(Agent))
            agents = result.scalars().all()
            
            print(f"\nCurrent agents status:")
            for agent in agents:
                last_seen = agent.last_seen_at
                if last_seen:
                    time_diff = datetime.utcnow() - last_seen.replace(tzinfo=None)
                    status = "online" if time_diff < timedelta(minutes=5) else "offline"
                else:
                    status = "offline"
                    time_diff = "Never"
                
                print(f"  - {agent.name} ({agent.agent_id})")
                print(f"    DB Status: {agent.status}")
                print(f"    Last seen: {last_seen}")
                print(f"    Time diff: {time_diff}")
                print(f"    Real status: {status}")
                print()
            
            break
        except Exception as e:
            print(f"❌ Error: {e}")
            await db.rollback()
            raise

if __name__ == "__main__":
    asyncio.run(update_offline_agents())