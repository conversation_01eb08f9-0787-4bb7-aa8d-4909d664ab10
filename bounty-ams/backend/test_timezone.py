#!/usr/bin/env python3
"""
Test timezone handling in heartbeat
"""
import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import datetime, timezone
from sqlalchemy import select
from database import get_db
from models_dynamic import Agent

async def test_timezone_handling():
    """测试时区处理"""
    
    print(f"Python datetime.utcnow(): {datetime.utcnow()}")
    print(f"Python datetime.now(timezone.utc): {datetime.now(timezone.utc)}")
    print()
    
    async for db in get_db():
        try:
            # 查看agent的last_seen_at
            result = await db.execute(
                select(Agent).where(Agent.agent_id == "go-agent-code")
            )
            agent = result.scalar_one_or_none()
            
            if agent:
                print(f"Agent last_seen_at from DB: {agent.last_seen_at}")
                print(f"Agent last_seen_at type: {type(agent.last_seen_at)}")
                if agent.last_seen_at:
                    print(f"Has timezone info: {agent.last_seen_at.tzinfo is not None}")
            else:
                print("Agent not found")
            
            break
        except Exception as e:
            print(f"❌ Error: {e}")
            raise

if __name__ == "__main__":
    asyncio.run(test_timezone_handling())