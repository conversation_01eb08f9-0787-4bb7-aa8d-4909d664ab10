#!/usr/bin/env python3
"""
资产管理系统 V2.0 - ES为主的架构
主要特性：
1. Elasticsearch作为主要数据存储
2. PostgreSQL仅存储元数据
3. 统一的数据处理管道
4. 支持数据清洗和去重
5. Kibana集成支持
"""

import asyncio
import json
import hashlib
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_
from elasticsearch import AsyncElasticsearch
from elasticsearch.helpers import async_bulk

from models_dynamic import ModelType, DynamicEntity, User
from elasticsearch_client import get_es_client
from database import get_db
import logging

logger = logging.getLogger(__name__)

class DataSource(Enum):
    """数据源类型"""
    IMPORT = "import"
    AGENT_DISCOVERY = "agent_discovery"
    MANUAL = "manual"
    API_SYNC = "api_sync"

@dataclass
class AssetProcessingResult:
    """资产处理结果"""
    total_processed: int = 0
    success_count: int = 0
    duplicate_count: int = 0
    error_count: int = 0
    errors: List[str] = None
    
    def __post_init__(self):
        if self.errors is None:
            self.errors = []

class AssetManagerV2:
    """资产管理器 V2.0 - ES为主的架构"""
    
    def __init__(self, es_client: AsyncElasticsearch, db_session: AsyncSession):
        self.es_client = es_client
        self.db_session = db_session
        
        # ES索引配置
        self.asset_index_pattern = "assets-{date}"  # assets-2025-01
        self.asset_template_name = "assets-template"
        
        # 数据清洗配置
        self.dedup_fields = ["asset_type", "asset_value", "asset_host"]
        self.required_fields = ["asset_type", "asset_value"]
        
    async def initialize(self):
        """初始化ES索引模板和映射"""
        await self._create_asset_index_template()
    
    async def _create_asset_index_template(self):
        """创建资产索引模板"""
        template = {
            "index_patterns": ["assets-*"],
            "template": {
                "settings": {
                    "number_of_shards": 3,
                    "number_of_replicas": 1,
                    "refresh_interval": "5s",
                    "max_result_window": 50000,
                    "analysis": {
                        "analyzer": {
                            "asset_analyzer": {
                                "type": "custom",
                                "tokenizer": "standard",
                                "filter": ["lowercase", "stop", "snowball"]
                            },
                            "domain_analyzer": {
                                "type": "custom", 
                                "tokenizer": "keyword",
                                "filter": ["lowercase"]
                            }
                        }
                    }
                },
                "mappings": {
                    "properties": {
                        # 核心资产字段
                        "asset_id": {"type": "keyword"},  # 唯一标识
                        "asset_type": {"type": "keyword"},
                        "asset_value": {
                            "type": "text",
                            "analyzer": "asset_analyzer",
                            "fields": {
                                "keyword": {"type": "keyword"},
                                "domain": {"type": "text", "analyzer": "domain_analyzer"}
                            }
                        },
                        "asset_host": {
                            "type": "text",
                            "analyzer": "domain_analyzer",
                            "fields": {"keyword": {"type": "keyword"}}
                        },
                        
                        # 关联信息
                        "platform_id": {"type": "keyword"},
                        "project_id": {"type": "keyword"},
                        "platform_name": {"type": "keyword"},
                        "project_name": {"type": "keyword"},
                        
                        # 状态和元数据
                        "status": {"type": "keyword"},
                        "confidence": {"type": "keyword"},
                        "tags": {"type": "keyword"},
                        "source": {"type": "keyword"},
                        "source_task_type": {"type": "keyword"},
                        
                        # 时间字段
                        "discovered_at": {"type": "date"},
                        "last_seen_at": {"type": "date"},
                        "created_at": {"type": "date"},
                        "updated_at": {"type": "date"},
                        
                        # 扩展数据
                        "metadata": {
                            "type": "object",
                            "dynamic": True
                        },
                        "raw_data": {
                            "type": "object",
                            "enabled": False  # 不索引，仅存储
                        },
                        
                        # 数据处理标记
                        "is_duplicate": {"type": "boolean"},
                        "duplicate_of": {"type": "keyword"},
                        "processing_status": {"type": "keyword"},
                        "data_version": {"type": "integer"}
                    }
                }
            }
        }
        
        try:
            await self.es_client.indices.put_index_template(
                name=self.asset_template_name,
                body=template
            )
            print(f"✅ 创建资产索引模板: {self.asset_template_name}")
        except Exception as e:
            print(f"❌ 创建索引模板失败: {e}")
    
    async def process_assets(
        self,
        raw_assets: List[Dict[str, Any]],
        platform_id: Optional[str] = None,
        project_id: Optional[str] = None,
        source: DataSource = DataSource.IMPORT,
        auto_clean: bool = True
    ) -> AssetProcessingResult:
        """
        处理资产数据的主要入口
        
        Args:
            raw_assets: 原始资产数据
            platform_id: 平台ID
            project_id: 项目ID  
            source: 数据源类型
            auto_clean: 是否自动清洗
        """
        result = AssetProcessingResult()
        result.total_processed = len(raw_assets)
        
        # 1. 数据清洗和标准化
        if auto_clean:
            cleaned_assets = await self._clean_and_normalize_assets(raw_assets)
        else:
            cleaned_assets = raw_assets
        
        # 2. 获取平台项目信息
        platform_info, project_info = await self._get_platform_project_info(platform_id, project_id)
        
        # 3. 转换为ES文档格式
        es_docs = []
        for i, asset_data in enumerate(cleaned_assets):
            try:
                doc = await self._convert_to_es_document(
                    asset_data, platform_info, project_info, source
                )
                if doc:
                    es_docs.append(doc)
                    result.success_count += 1
                else:
                    result.error_count += 1
                    result.errors.append(f"记录 {i}: 转换失败")
            except Exception as e:
                result.error_count += 1
                result.errors.append(f"记录 {i}: {str(e)}")
        
        # 4. 去重处理
        if auto_clean:
            es_docs, dup_count = await self._deduplicate_assets(es_docs)
            result.duplicate_count = dup_count
        
        # 5. 批量写入ES
        if es_docs:
            await self._bulk_index_assets(es_docs)
        
        return result
    
    async def _clean_and_normalize_assets(self, raw_assets: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """数据清洗和标准化"""
        cleaned = []
        
        for asset in raw_assets:
            try:
                # 基础数据验证
                if not self._validate_asset_data(asset):
                    continue
                
                # 字段标准化
                normalized = self._normalize_asset_fields(asset)
                
                # 数据类型转换
                typed = self._convert_data_types(normalized)
                
                cleaned.append(typed)
                
            except Exception as e:
                print(f"清洗资产数据失败: {e}")
                continue
        
        return cleaned
    
    def _validate_asset_data(self, asset: Dict[str, Any]) -> bool:
        """验证资产数据的基本要求"""
        # 检查必需字段
        for field in self.required_fields:
            if not asset.get(field):
                return False
        
        # 检查asset_value是否有效
        asset_value = asset.get("asset_value", "").strip()
        if len(asset_value) < 2:
            return False
        
        return True
    
    def _normalize_asset_fields(self, asset: Dict[str, Any]) -> Dict[str, Any]:
        """字段标准化和重映射"""
        normalized = asset.copy()
        
        # 字段重映射
        field_mapping = {
            "domain": "asset_value",
            "subdomain": "asset_value", 
            "host": "asset_host",
            "ip": "asset_value",
            "url": "asset_value",
            "endpoint": "asset_value"
        }
        
        for old_field, new_field in field_mapping.items():
            if old_field in normalized and new_field not in normalized:
                normalized[new_field] = normalized.pop(old_field)
        
        # 标准化asset_type
        asset_type = normalized.get("asset_type", "").lower()
        type_mapping = {
            "web": "url",
            "website": "url", 
            "api": "api_endpoint",
            "endpoint": "api_endpoint",
            "hostname": "domain"
        }
        if asset_type in type_mapping:
            normalized["asset_type"] = type_mapping[asset_type]
        
        # 标准化域名和IP
        if normalized.get("asset_type") in ["domain", "subdomain"]:
            asset_value = normalized.get("asset_value", "").lower().strip()
            # 移除协议前缀
            if asset_value.startswith(("http://", "https://")):
                asset_value = asset_value.split("://", 1)[1]
            # 移除路径
            if "/" in asset_value:
                asset_value = asset_value.split("/")[0]
            normalized["asset_value"] = asset_value
            normalized["asset_host"] = asset_value
        
        return normalized
    
    def _convert_data_types(self, asset: Dict[str, Any]) -> Dict[str, Any]:
        """数据类型转换"""
        converted = asset.copy()
        
        # 时间字段处理
        time_fields = ["discovered_at", "last_seen_at", "created_at"]
        for field in time_fields:
            if field in converted:
                converted[field] = self._parse_datetime(converted[field])
        
        # 标签处理
        if "tags" in converted:
            tags = converted["tags"]
            if isinstance(tags, str):
                # 分割字符串标签
                converted["tags"] = [tag.strip() for tag in tags.split(",") if tag.strip()]
            elif not isinstance(tags, list):
                converted["tags"] = [str(tags)]
        
        return converted
    
    def _parse_datetime(self, dt_value: Any) -> str:
        """解析时间字段"""
        if isinstance(dt_value, str):
            try:
                # 尝试解析ISO格式
                parsed = datetime.fromisoformat(dt_value.replace("Z", "+00:00"))
                return parsed.isoformat()
            except:
                pass
        
        # 默认使用当前时间
        return datetime.utcnow().isoformat()
    
    async def _get_platform_project_info(self, platform_id: Optional[str], project_id: Optional[str]) -> Tuple[Optional[Dict], Optional[Dict]]:
        """获取平台和项目信息"""
        platform_info = None
        project_info = None
        
        if platform_id:
            try:
                # 从PostgreSQL获取平台信息
                platform_model = await self._get_model_by_name("platform")
                if platform_model:
                    platform_result = await self.db_session.execute(
                        select(DynamicEntity).where(
                            and_(
                                DynamicEntity.model_type_id == platform_model.id,
                                DynamicEntity.id == platform_id
                            )
                        )
                    )
                    platform_entity = platform_result.scalar_one_or_none()
                    if platform_entity:
                        platform_info = {
                            "id": str(platform_entity.id),
                            "name": platform_entity.entity_data.get("name"),
                            "display_name": platform_entity.entity_data.get("display_name")
                        }
            except Exception as e:
                print(f"获取平台信息失败: {e}")
        
        if project_id:
            try:
                # 从PostgreSQL获取项目信息
                project_model = await self._get_model_by_name("project")
                if project_model:
                    project_result = await self.db_session.execute(
                        select(DynamicEntity).where(
                            and_(
                                DynamicEntity.model_type_id == project_model.id,
                                DynamicEntity.id == project_id
                            )
                        )
                    )
                    project_entity = project_result.scalar_one_or_none()
                    if project_entity:
                        project_info = {
                            "id": str(project_entity.id),
                            "name": project_entity.entity_data.get("name"),
                            "display_name": project_entity.entity_data.get("display_name")
                        }
            except Exception as e:
                print(f"获取项目信息失败: {e}")
        
        return platform_info, project_info
    
    async def _get_model_by_name(self, model_name: str) -> Optional[ModelType]:
        """根据名称获取模型类型"""
        try:
            result = await self.db_session.execute(
                select(ModelType).where(ModelType.name == model_name)
            )
            return result.scalar_one_or_none()
        except Exception:
            return None
    
    async def _convert_to_es_document(
        self,
        asset_data: Dict[str, Any],
        platform_info: Optional[Dict],
        project_info: Optional[Dict],
        source: DataSource
    ) -> Optional[Dict[str, Any]]:
        """转换为ES文档格式"""
        try:
            # 生成唯一资产ID
            asset_id = self._generate_asset_id(asset_data)
            
            # 构建ES文档
            doc = {
                "asset_id": asset_id,
                "asset_type": asset_data.get("asset_type"),
                "asset_value": asset_data.get("asset_value"),
                "asset_host": asset_data.get("asset_host", ""),
                
                # 关联信息
                "platform_id": platform_info["id"] if platform_info else None,
                "project_id": project_info["id"] if project_info else None,
                "platform_name": platform_info["name"] if platform_info else None,
                "project_name": project_info["name"] if project_info else None,
                
                # 状态信息
                "status": asset_data.get("status", "active"),
                "confidence": asset_data.get("confidence", "medium"),
                "tags": asset_data.get("tags", []),
                "source": source.value,
                "source_task_type": asset_data.get("source_task_type", "unknown"),
                
                # 时间信息
                "discovered_at": asset_data.get("discovered_at", datetime.utcnow().isoformat()),
                "last_seen_at": asset_data.get("last_seen_at", datetime.utcnow().isoformat()),
                "created_at": datetime.utcnow().isoformat(),
                "updated_at": datetime.utcnow().isoformat(),
                
                # 元数据
                "metadata": asset_data.get("metadata", {}),
                "raw_data": asset_data,  # 保留原始数据
                
                # 处理标记
                "is_duplicate": False,
                "processing_status": "processed",
                "data_version": 1
            }
            
            return doc
            
        except Exception as e:
            print(f"转换ES文档失败: {e}")
            return None
    
    def _generate_asset_id(self, asset_data: Dict[str, Any]) -> str:
        """生成唯一的资产ID"""
        # 使用关键字段生成ID
        key_parts = [
            asset_data.get("asset_type", ""),
            asset_data.get("asset_value", ""),
            asset_data.get("asset_host", "")
        ]
        
        # 标准化处理
        normalized_parts = []
        for part in key_parts:
            if part:
                normalized_parts.append(str(part).lower().strip())
        
        # 生成MD5哈希
        id_string = "|".join(normalized_parts)
        return hashlib.md5(id_string.encode()).hexdigest()
    
    async def _deduplicate_assets(self, assets: List[Dict[str, Any]]) -> Tuple[List[Dict[str, Any]], int]:
        """去重处理"""
        seen_ids = set()
        deduplicated = []
        duplicate_count = 0
        
        for asset in assets:
            asset_id = asset["asset_id"]
            
            if asset_id in seen_ids:
                duplicate_count += 1
                asset["is_duplicate"] = True
                asset["duplicate_of"] = asset_id
            else:
                seen_ids.add(asset_id)
                deduplicated.append(asset)
        
        return deduplicated, duplicate_count
    
    async def _bulk_index_assets(self, assets: List[Dict[str, Any]]):
        """批量索引资产到ES"""
        if not assets:
            return
        
        # 确定索引名称（按月分割）
        index_name = self.asset_index_pattern.format(
            date=datetime.utcnow().strftime("%Y-%m")
        )
        
        # 准备批量操作
        actions = []
        for asset in assets:
            action = {
                "_index": index_name,
                "_id": asset["asset_id"],
                "_source": asset
            }
            actions.append(action)
        
        # 执行批量索引
        try:
            success, failed = await async_bulk(
                client=self.es_client,
                actions=actions,
                chunk_size=1000,
                max_chunk_bytes=10 * 1024 * 1024  # 10MB
            )
            print(f"✅ 成功索引 {success} 个资产到 {index_name}")
            if failed:
                print(f"❌ 失败 {len(failed)} 个资产")
        except Exception as e:
            print(f"❌ 批量索引失败: {e}")
    
    async def search_assets(
        self,
        query: str = "*",
        filters: Dict[str, Any] = None,
        sort_by: str = "discovered_at",
        sort_order: str = "desc",
        page: int = 1,
        size: int = 20,
        include_aggregations: bool = False
    ) -> Dict[str, Any]:
        """统一的资产搜索接口"""
        
        # 构建ES查询
        es_query = {
            "query": self._build_search_query(query, filters),
            "sort": [{sort_by: {"order": sort_order}}],
            "from": (page - 1) * size,
            "size": size
        }
        
        # 添加聚合
        if include_aggregations:
            es_query["aggs"] = self._build_aggregations()
        
        try:
            # 执行搜索（跨所有资产索引）
            response = await self.es_client.search(
                index="assets-*",
                body=es_query
            )
            
            # 处理结果
            hits = []
            for hit in response["hits"]["hits"]:
                asset = hit["_source"]
                asset["_id"] = hit["_id"]
                asset["_score"] = hit["_score"]
                hits.append(asset)
            
            result = {
                "total": response["hits"]["total"]["value"],
                "hits": hits,
                "page": page,
                "size": size
            }
            
            if include_aggregations and "aggregations" in response:
                result["aggregations"] = response["aggregations"]
            
            return result
            
        except Exception as e:
            print(f"搜索失败: {e}")
            return {"total": 0, "hits": [], "page": page, "size": size}
    
    def _build_search_query(self, query: str, filters: Dict[str, Any] = None) -> Dict[str, Any]:
        """构建ES搜索查询"""
        if not filters:
            filters = {}
        
        must_clauses = []
        filter_clauses = []
        
        # 主查询
        if query and query != "*":
            must_clauses.append({
                "multi_match": {
                    "query": query,
                    "fields": [
                        "asset_value^3",
                        "asset_host^2",
                        "tags^1.5",
                        "platform_name^2",
                        "project_name^2",
                        "metadata.*"
                    ],
                    "type": "best_fields",
                    "fuzziness": "AUTO"
                }
            })
        
        # 过滤条件
        for field, value in filters.items():
            if value:
                if isinstance(value, list):
                    filter_clauses.append({"terms": {field: value}})
                else:
                    filter_clauses.append({"term": {field: value}})
        
        # 排除重复项
        filter_clauses.append({"term": {"is_duplicate": False}})
        
        query_dsl = {"bool": {}}
        if must_clauses:
            query_dsl["bool"]["must"] = must_clauses
        if filter_clauses:
            query_dsl["bool"]["filter"] = filter_clauses
        
        if not must_clauses and not filter_clauses:
            return {"match_all": {}}
        
        return query_dsl
    
    def _build_aggregations(self) -> Dict[str, Any]:
        """构建聚合查询"""
        return {
            "asset_types": {
                "terms": {"field": "asset_type", "size": 20}
            },
            "platforms": {
                "terms": {"field": "platform_name.keyword", "size": 20}
            },
            "projects": {
                "terms": {"field": "project_name.keyword", "size": 20}
            },
            "confidence_levels": {
                "terms": {"field": "confidence", "size": 10}
            },
            "discovery_timeline": {
                "date_histogram": {
                    "field": "discovered_at",
                    "calendar_interval": "day",
                    "min_doc_count": 1
                }
            }
        }


# 使用示例和API接口
async def create_asset_manager(db_session: AsyncSession = None) -> AssetManagerV2:
    """创建资产管理器实例"""
    es_client = await get_es_client()
    if db_session is None:
        # 这里需要在路由中传入db_session
        raise ValueError("db_session is required")
    
    manager = AssetManagerV2(es_client, db_session)
    await manager.initialize()
    
    return manager


# API路由集成示例
async def process_imported_assets(
    raw_assets: List[Dict[str, Any]],
    platform_id: Optional[str] = None,
    project_id: Optional[str] = None
) -> Dict[str, Any]:
    """处理导入的资产数据"""
    
    manager = await create_asset_manager()
    
    result = await manager.process_assets(
        raw_assets=raw_assets,
        platform_id=platform_id,
        project_id=project_id,
        source=DataSource.IMPORT,
        auto_clean=True
    )
    
    return {
        "status": "success",
        "total_processed": result.total_processed,
        "success_count": result.success_count,
        "duplicate_count": result.duplicate_count,
        "error_count": result.error_count,
        "errors": result.errors[:10]  # 只返回前10个错误
    }

print("✅ 资产管理系统 V2.0 架构已创建")
print("\n🎯 主要特性:")
print("- Elasticsearch作为主数据存储")
print("- 统一的数据处理管道")
print("- 自动数据清洗和去重")
print("- 支持大数据量处理")
print("- Kibana集成友好")
print("- PostgreSQL存储元数据") 