"""
Database Migration Script for Agent System

Creates the necessary tables for Agent communication system
"""
import asyncio
from sqlalchemy import text
from database import engine

async def create_agent_tables():
    """创建Agent系统相关的数据库表"""
    
    # 分别定义每个SQL命令
    create_agents_table = """
    CREATE TABLE IF NOT EXISTS agents (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        agent_id VARCHAR(255) NOT NULL UNIQUE,
        name VARCHAR(255) NOT NULL,
        version VARCHAR(50) NOT NULL,
        capabilities JSONB NOT NULL,
        max_concurrent_tasks INTEGER DEFAULT 3,
        current_tasks INTEGER DEFAULT 0,
        status VARCHAR(20) DEFAULT 'offline',
        hostname VARCHAR(255),
        ip_address VARCHAR(45),
        last_seen_at TIMESTAMP WITH TIME ZONE,
        agent_metadata JSONB,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE
    )
    """
    
    agents_indexes = [
        "CREATE INDEX IF NOT EXISTS idx_agents_agent_id ON agents(agent_id)",
        "CREATE INDEX IF NOT EXISTS idx_agents_status ON agents(status)",
        "CREATE INDEX IF NOT EXISTS idx_agents_last_seen ON agents(last_seen_at)"
    ]
    
    create_tasks_table = """
    CREATE TABLE IF NOT EXISTS tasks (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        task_id VARCHAR(255) NOT NULL UNIQUE,
        task_type VARCHAR(50) NOT NULL,
        target VARCHAR(500) NOT NULL,
        parameters JSONB NOT NULL,
        priority INTEGER DEFAULT 2,
        status VARCHAR(20) DEFAULT 'pending',
        agent_id VARCHAR(255) REFERENCES agents(agent_id),
        workflow_id VARCHAR(255),
        timeout INTEGER DEFAULT 300,
        retry_count INTEGER DEFAULT 3,
        current_retry INTEGER DEFAULT 0,
        dependencies JSONB,
        result_data JSONB,
        error_message TEXT,
        execution_time INTEGER,
        assets_discovered JSONB,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        assigned_at TIMESTAMP WITH TIME ZONE,
        started_at TIMESTAMP WITH TIME ZONE,
        completed_at TIMESTAMP WITH TIME ZONE,
        created_by_user_id UUID REFERENCES users(id)
    )
    """
    
    tasks_indexes = [
        "CREATE INDEX IF NOT EXISTS idx_tasks_task_id ON tasks(task_id)",
        "CREATE INDEX IF NOT EXISTS idx_tasks_status ON tasks(status)",
        "CREATE INDEX IF NOT EXISTS idx_tasks_task_type ON tasks(task_type)",
        "CREATE INDEX IF NOT EXISTS idx_tasks_agent_id ON tasks(agent_id)",
        "CREATE INDEX IF NOT EXISTS idx_tasks_workflow_id ON tasks(workflow_id)",
        "CREATE INDEX IF NOT EXISTS idx_tasks_priority ON tasks(priority)",
        "CREATE INDEX IF NOT EXISTS idx_tasks_created_at ON tasks(created_at)"
    ]
    
    create_workflow_executions_table = """
    CREATE TABLE IF NOT EXISTS workflow_executions (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        execution_id VARCHAR(255) NOT NULL UNIQUE,
        workflow_id VARCHAR(255) NOT NULL,
        target VARCHAR(500) NOT NULL,
        status VARCHAR(20) DEFAULT 'pending',
        priority INTEGER DEFAULT 2,
        variables JSONB,
        total_tasks INTEGER DEFAULT 0,
        completed_tasks INTEGER DEFAULT 0,
        failed_tasks INTEGER DEFAULT 0,
        running_tasks INTEGER DEFAULT 0,
        progress INTEGER DEFAULT 0,
        started_at TIMESTAMP WITH TIME ZONE,
        completed_at TIMESTAMP WITH TIME ZONE,
        created_by_user_id UUID REFERENCES users(id),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE
    )
    """
    
    workflow_indexes = [
        "CREATE INDEX IF NOT EXISTS idx_workflow_executions_execution_id ON workflow_executions(execution_id)",
        "CREATE INDEX IF NOT EXISTS idx_workflow_executions_workflow_id ON workflow_executions(workflow_id)",
        "CREATE INDEX IF NOT EXISTS idx_workflow_executions_status ON workflow_executions(status)",
        "CREATE INDEX IF NOT EXISTS idx_workflow_executions_created_at ON workflow_executions(created_at)"
    ]
    
    try:
        async with engine.begin() as conn:
            print("🔄 开始创建Agent系统数据库表...")
            
            # 创建Agents表
            print("创建agents表...")
            await conn.execute(text(create_agents_table))
            
            # 创建agents表索引
            for index_sql in agents_indexes:
                await conn.execute(text(index_sql))
            
            print("✅ agents表创建完成")
            
            # 创建Tasks表
            print("创建tasks表...")
            await conn.execute(text(create_tasks_table))
            
            # 创建tasks表索引
            for index_sql in tasks_indexes:
                await conn.execute(text(index_sql))
            
            print("✅ tasks表创建完成")
            
            # 创建WorkflowExecutions表
            print("创建workflow_executions表...")
            await conn.execute(text(create_workflow_executions_table))
            
            # 创建workflow_executions表索引
            for index_sql in workflow_indexes:
                await conn.execute(text(index_sql))
            
            print("✅ workflow_executions表创建完成")
            
            print("\n🎉 所有Agent系统表创建完成!")
            
    except Exception as e:
        print(f"❌ 数据库表创建失败: {e}")
        raise

async def verify_tables():
    """验证表是否创建成功"""
    verify_sql = """
    SELECT table_name 
    FROM information_schema.tables 
    WHERE table_schema = 'public' 
    AND table_name IN ('agents', 'tasks', 'workflow_executions')
    ORDER BY table_name;
    """
    
    try:
        async with engine.begin() as conn:
            result = await conn.execute(text(verify_sql))
            tables = [row[0] for row in result.fetchall()]
            
            print(f"\n📋 已创建的Agent系统表: {tables}")
            
            expected_tables = ['agents', 'tasks', 'workflow_executions']
            missing_tables = [table for table in expected_tables if table not in tables]
            
            if missing_tables:
                print(f"❌ 缺少的表: {missing_tables}")
            else:
                print("✅ 所有Agent系统表都已创建")
                
            return len(missing_tables) == 0
            
    except Exception as e:
        print(f"❌ 表验证失败: {e}")
        return False

async def main():
    """主函数"""
    print("🚀 Agent系统数据库迁移开始...")
    
    # 创建表
    await create_agent_tables()
    
    # 验证表
    success = await verify_tables()
    
    if success:
        print("\n🎉 Agent系统数据库迁移成功完成!")
        print("\n📝 数据库结构说明:")
        print("  - agents: Agent注册和状态管理")
        print("  - tasks: 任务定义和执行状态")
        print("  - workflow_executions: 工作流执行记录")
        print("\n🔗 表关系:")
        print("  - tasks.agent_id -> agents.agent_id")
        print("  - tasks.workflow_id -> workflow_executions.execution_id")
        print("  - tasks.created_by_user_id -> users.id")
        print("  - workflow_executions.created_by_user_id -> users.id")
        print("\n🚀 现在可以启动Agent通信系统了!")
    else:
        print("\n❌ 数据库迁移失败，请检查错误信息")

if __name__ == "__main__":
    asyncio.run(main())