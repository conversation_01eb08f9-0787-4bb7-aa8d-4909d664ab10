#!/usr/bin/env python3
"""
Bounty AMS 系统诊断脚本
检查前端和后端的所有关键服务状态
"""

import asyncio
import aiohttp
import subprocess
import sys
import os
from typing import Dict, List, Any
import json
import time
from datetime import datetime

class SystemDiagnostics:
    def __init__(self):
        self.results = {}
        self.errors = []
        
    async def check_database_connection(self) -> Dict[str, Any]:
        """检查PostgreSQL数据库连接"""
        try:
            # 尝试连接数据库
            result = subprocess.run(
                ["psql", "-h", "localhost", "-U", "postgres", "-d", "bounty_ams", "-c", "SELECT 1;"],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                return {"status": "healthy", "message": "数据库连接正常"}
            else:
                return {"status": "error", "message": f"数据库连接失败: {result.stderr}"}
                
        except subprocess.TimeoutExpired:
            return {"status": "timeout", "message": "数据库连接超时"}
        except Exception as e:
            return {"status": "error", "message": f"数据库检查异常: {str(e)}"}
    
    async def check_elasticsearch_connection(self) -> Dict[str, Any]:
        """检查Elasticsearch连接"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get("http://localhost:9200/_cluster/health", timeout=5) as response:
                    if response.status == 200:
                        data = await response.json()
                        return {
                            "status": "healthy",
                            "cluster_status": data.get("status", "unknown"),
                            "number_of_nodes": data.get("number_of_nodes", 0),
                            "message": "Elasticsearch连接正常"
                        }
                    else:
                        return {"status": "error", "message": f"Elasticsearch返回状态码: {response.status}"}
        except asyncio.TimeoutError:
            return {"status": "timeout", "message": "Elasticsearch连接超时"}
        except Exception as e:
            return {"status": "error", "message": f"Elasticsearch检查异常: {str(e)}"}
    
    async def check_redis_connection(self) -> Dict[str, Any]:
        """检查Redis连接"""
        try:
            result = subprocess.run(
                ["redis-cli", "-h", "localhost", "-p", "6379", "ping"],
                capture_output=True,
                text=True,
                timeout=5
            )
            
            if result.returncode == 0 and "PONG" in result.stdout:
                return {"status": "healthy", "message": "Redis连接正常"}
            else:
                return {"status": "error", "message": f"Redis连接失败: {result.stderr}"}
                
        except subprocess.TimeoutExpired:
            return {"status": "timeout", "message": "Redis连接超时"}
        except Exception as e:
            return {"status": "error", "message": f"Redis检查异常: {str(e)}"}
    
    async def check_backend_api(self) -> Dict[str, Any]:
        """检查后端API状态"""
        try:
            async with aiohttp.ClientSession() as session:
                # 检查健康检查端点
                async with session.get("http://localhost:8000/health", timeout=5) as response:
                    if response.status == 200:
                        data = await response.json()
                        return {"status": "healthy", "message": "后端API正常", "data": data}
                    else:
                        return {"status": "error", "message": f"后端API返回状态码: {response.status}"}
        except asyncio.TimeoutError:
            return {"status": "timeout", "message": "后端API连接超时"}
        except Exception as e:
            return {"status": "error", "message": f"后端API检查异常: {str(e)}"}
    
    async def check_frontend_service(self) -> Dict[str, Any]:
        """检查前端服务状态"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get("http://localhost:5173", timeout=5) as response:
                    if response.status == 200:
                        return {"status": "healthy", "message": "前端服务正常"}
                    else:
                        return {"status": "error", "message": f"前端服务返回状态码: {response.status}"}
        except asyncio.TimeoutError:
            return {"status": "timeout", "message": "前端服务连接超时"}
        except Exception as e:
            return {"status": "error", "message": f"前端服务检查异常: {str(e)}"}
    
    def check_running_processes(self) -> Dict[str, Any]:
        """检查相关进程是否运行"""
        try:
            result = subprocess.run(
                ["ps", "aux"],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            processes = result.stdout
            running_services = {
                "postgresql": "postgres" in processes,
                "elasticsearch": "elasticsearch" in processes or "java" in processes,
                "redis": "redis-server" in processes,
                "node_vite": "vite" in processes or "node" in processes,
                "python_fastapi": "python" in processes and "main.py" in processes
            }
            
            return {"status": "info", "running_services": running_services}
            
        except Exception as e:
            return {"status": "error", "message": f"进程检查异常: {str(e)}"}
    
    def check_ports_listening(self) -> Dict[str, Any]:
        """检查关键端口是否在监听"""
        try:
            result = subprocess.run(
                ["netstat", "-tlnp"],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            output = result.stdout
            ports_status = {
                "5432": ":5432" in output,  # PostgreSQL
                "9200": ":9200" in output,  # Elasticsearch  
                "6379": ":6379" in output,  # Redis
                "8000": ":8000" in output,  # Backend API
                "5173": ":5173" in output   # Frontend Vite
            }
            
            return {"status": "info", "ports_listening": ports_status}
            
        except Exception as e:
            return {"status": "error", "message": f"端口检查异常: {str(e)}"}
    
    async def check_api_endpoints(self) -> Dict[str, Any]:
        """检查关键API端点"""
        endpoints = [
            "/health",
            "/api/auth/me",
            "/api/agents/",
            "/api/tasks",
            "/api/workflows",
            "/api/discovered-assets/stats",
            "/api/dynamic-models/types"
        ]
        
        endpoint_status = {}
        
        try:
            async with aiohttp.ClientSession() as session:
                for endpoint in endpoints:
                    try:
                        url = f"http://localhost:8000{endpoint}"
                        async with session.get(url, timeout=3) as response:
                            endpoint_status[endpoint] = {
                                "status": response.status,
                                "reachable": True
                            }
                    except Exception as e:
                        endpoint_status[endpoint] = {
                            "status": "error",
                            "reachable": False,
                            "error": str(e)
                        }
        except Exception as e:
            return {"status": "error", "message": f"API端点检查异常: {str(e)}"}
        
        return {"status": "info", "endpoints": endpoint_status}
    
    async def run_full_diagnosis(self) -> Dict[str, Any]:
        """执行完整的系统诊断"""
        print("🔍 开始系统诊断...")
        
        # 并行执行所有检查
        tasks = [
            ("数据库连接", self.check_database_connection()),
            ("Elasticsearch连接", self.check_elasticsearch_connection()),
            ("Redis连接", self.check_redis_connection()),
            ("后端API", self.check_backend_api()),
            ("前端服务", self.check_frontend_service()),
            ("API端点", self.check_api_endpoints())
        ]
        
        results = {}
        for name, task in tasks:
            print(f"  检查 {name}...")
            try:
                result = await task
                results[name] = result
                
                # 打印结果
                if result["status"] == "healthy":
                    print(f"  ✅ {name}: {result['message']}")
                elif result["status"] == "timeout":
                    print(f"  ⏱️  {name}: {result['message']}")
                else:
                    print(f"  ❌ {name}: {result['message']}")
                    
            except Exception as e:
                results[name] = {"status": "error", "message": f"检查失败: {str(e)}"}
                print(f"  ❌ {name}: 检查失败 - {str(e)}")
        
        # 同步检查（不需要async）
        print("  检查运行进程...")
        results["运行进程"] = self.check_running_processes()
        
        print("  检查端口监听...")
        results["端口监听"] = self.check_ports_listening()
        
        # 生成诊断报告
        report = {
            "timestamp": datetime.now().isoformat(),
            "overall_status": self.determine_overall_status(results),
            "detailed_results": results,
            "recommendations": self.generate_recommendations(results)
        }
        
        return report
    
    def determine_overall_status(self, results: Dict[str, Any]) -> str:
        """确定整体系统状态"""
        critical_services = ["数据库连接", "后端API"]
        healthy_count = 0
        critical_healthy = 0
        
        for service, result in results.items():
            if result.get("status") == "healthy":
                healthy_count += 1
                if service in critical_services:
                    critical_healthy += 1
        
        if critical_healthy == len(critical_services):
            if healthy_count >= len(results) * 0.8:
                return "healthy"
            else:
                return "degraded"
        else:
            return "unhealthy"
    
    def generate_recommendations(self, results: Dict[str, Any]) -> List[str]:
        """生成修复建议"""
        recommendations = []
        
        # 检查数据库
        if results.get("数据库连接", {}).get("status") != "healthy":
            recommendations.append("启动PostgreSQL数据库服务: sudo systemctl start postgresql")
        
        # 检查Elasticsearch
        if results.get("Elasticsearch连接", {}).get("status") != "healthy":
            recommendations.append("启动Elasticsearch服务: sudo systemctl start elasticsearch")
        
        # 检查Redis
        if results.get("Redis连接", {}).get("status") != "healthy":
            recommendations.append("启动Redis服务: sudo systemctl start redis")
        
        # 检查后端API
        if results.get("后端API", {}).get("status") != "healthy":
            recommendations.append("启动后端服务: cd backend && python main.py")
        
        # 检查前端服务
        if results.get("前端服务", {}).get("status") != "healthy":
            recommendations.append("启动前端服务: cd frontend && npm run dev")
        
        return recommendations

async def main():
    diagnostics = SystemDiagnostics()
    report = await diagnostics.run_full_diagnosis()
    
    # 保存诊断报告
    with open("system_diagnosis_report.json", "w", encoding="utf-8") as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print(f"\n📊 系统诊断完成!")
    print(f"整体状态: {report['overall_status']}")
    print(f"详细报告已保存到: system_diagnosis_report.json")
    
    if report['recommendations']:
        print("\n🔧 修复建议:")
        for i, rec in enumerate(report['recommendations'], 1):
            print(f"  {i}. {rec}")
    
    return report

if __name__ == "__main__":
    asyncio.run(main()) 